const config = {
    apiBaseUrl: 'http://localhost:3001/api'
};

// 测试总体考勤率API
async function testOverallAttendanceRate() {
    try {
        const response = await fetch(`${config.apiBaseUrl}/attendance/overall-rate?year=2025&month=2`, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const result = await response.json();
            console.log('总体考勤率API测试结果:', result);
        } else {
            console.error('总体考勤率API测试失败:', response.status, response.statusText);
        }
    } catch (error) {
        console.error('总体考勤率API测试异常:', error);
    }
}

// 测试员工考勤数据API
async function testEmployeeAttendance() {
    try {
        const response = await fetch(`${config.apiBaseUrl}/attendance/employee-attendance?employeeId=001&name=张三&year=2025&month=2`, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const result = await response.json();
            console.log('员工考勤数据API测试结果:', result);
        } else {
            console.error('员工考勤数据API测试失败:', response.status, response.statusText);
        }
    } catch (error) {
        console.error('员工考勤数据API测试异常:', error);
    }
}

// 运行测试
async function runTests() {
    console.log('开始测试考勤API...');
    
    console.log('\n1. 测试总体考勤率API:');
    await testOverallAttendanceRate();
    
    console.log('\n2. 测试员工考勤数据API:');
    await testEmployeeAttendance();
    
    console.log('\n测试完成!');
}

// 在Node.js环境中运行
if (typeof window === 'undefined') {
    // Node.js环境
    const fetch = require('node-fetch');
    runTests();
} else {
    // 浏览器环境
    window.testAttendanceAPIs = runTests;
} 