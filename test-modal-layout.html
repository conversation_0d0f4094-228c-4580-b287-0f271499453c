<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考勤配置模态框布局测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f2f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1890ff;
        }
        .test-button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background-color: #40a9ff;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #f0f5ff;
            border: 1px solid #adc6ff;
            color: #2f54eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>考勤配置模态框布局修复测试</h1>
        
        <div class="test-info">
            <h3>修复内容</h3>
            <ul>
                <li><strong>模态框尺寸</strong>: 宽度增加到1000px，高度减少到50vh</li>
                <li><strong>开关按钮</strong>: 统一尺寸为20px高度，44px宽度</li>
                <li><strong>底部按钮</strong>: 保存和取消按钮强制在同一行显示</li>
                <li><strong>布局优化</strong>: 所有开关按钮对齐，间距统一</li>
            </ul>
        </div>

        <div>
            <h3>测试步骤</h3>
            <ol>
                <li>点击下面的按钮打开考勤配置管理页面</li>
                <li>点击任意配置的"编辑"按钮</li>
                <li>检查模态框是否：
                    <ul>
                        <li>宽度合适（1000px）</li>
                        <li>高度不会过高（最大50vh）</li>
                        <li>所有开关按钮尺寸统一</li>
                        <li>保存和取消按钮在同一行</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div>
            <button class="test-button" onclick="openAttendanceConfig()">
                打开考勤配置管理页面
            </button>
            <button class="test-button" onclick="checkFrontendStatus()">
                检查前端服务状态
            </button>
            <button class="test-button" onclick="clearCache()">
                清除浏览器缓存提示
            </button>
        </div>

        <div id="status" class="status info" style="display: none;"></div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function openAttendanceConfig() {
            const url = 'http://localhost:3002/admin/attendance-config';
            showStatus('正在打开考勤配置管理页面...', 'info');
            
            // 打开新窗口
            const newWindow = window.open(url, '_blank');
            
            if (newWindow) {
                showStatus('页面已在新窗口中打开。请在新窗口中测试模态框布局。', 'success');
            } else {
                showStatus('无法打开新窗口，请手动访问: ' + url, 'error');
            }
        }

        async function checkFrontendStatus() {
            try {
                showStatus('正在检查前端服务状态...', 'info');
                const response = await fetch('http://localhost:3002', { method: 'HEAD' });
                if (response.ok) {
                    showStatus('前端服务运行正常 (端口3002)', 'success');
                } else {
                    showStatus('前端服务响应异常', 'error');
                }
            } catch (error) {
                showStatus('无法连接到前端服务，请确保服务已启动', 'error');
            }
        }

        function clearCache() {
            showStatus('请按以下步骤清除缓存:\n1. 按 Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac) 强制刷新\n2. 或打开开发者工具 (F12)，在Network标签中勾选"Disable cache"', 'info');
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkFrontendStatus();
        };
    </script>
</body>
</html> 