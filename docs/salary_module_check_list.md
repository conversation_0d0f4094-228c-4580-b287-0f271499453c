# 薪资模块检查清单

本文档提供了一个详细的检查清单，用于验证薪资计算功能的正确性，特别关注以下几点：
1. 当用户首次使用薪资模块时，所有薪资相关金额应该显示为默认值
2. 薪资详单，薪资表单中关于学历和语言调整系数和金额的一致性显示
3. 根据学历和语言系数调整的基本工资值的正确性（分为三种情况，调整系数大于1，等于1和小于1）

## 1. 首次使用薪资模块检查

### 1.1 薪资列表页面检查
- [ ] 所有员工的基本工资显示为0
- [ ] 所有员工的岗位工资显示为0
- [ ] 所有员工的管理津贴显示为0
- [ ] 所有员工的绩效奖金显示为0
- [ ] 所有员工的餐补显示为0
- [ ] 所有员工的通讯补贴显示为0
- [ ] 所有员工的应发工资显示为0
- [ ] 所有员工的实发工资显示为0

### 1.2 薪资详情页面检查
- [ ] 基本工资显示为0
- [ ] 岗位工资显示为0
- [ ] 管理津贴显示为0
- [ ] 绩效奖金显示为0
- [ ] 餐补显示为0
- [ ] 通讯补贴显示为0
- [ ] 特殊津贴显示为0
- [ ] 社保扣除显示为0
- [ ] 个税扣除显示为0
- [ ] 专项附加扣除显示为0
- [ ] 应发工资显示为0
- [ ] 实发工资显示为0
- [ ] 学历系数显示为1.00
- [ ] 语言系数显示为1.00
- [ ] 绩效系数显示为1.00
- [ ] 社保缴费基数显示为0
- [ ] 个税扣缴基数显示为0
- [ ] 基本工资计算公式显示为"0"

### 1.3 薪资表单检查
- [ ] 基本工资显示为0
- [ ] 岗位工资输入框为空或0
- [ ] 管理津贴输入框为空或0
- [ ] 绩效奖金显示为0
- [ ] 餐补显示为0
- [ ] 通讯补贴显示为0
- [ ] 特殊津贴输入框为空或0
- [ ] 社保扣除显示为0
- [ ] 个税扣除显示为0
- [ ] 专项附加扣除输入框为空或0
- [ ] 应发工资显示为0
- [ ] 实发工资显示为0
- [ ] 学历系数显示为1.00
- [ ] 语言系数显示为1.00
- [ ] 绩效系数显示为1.00
- [ ] 社保缴费基数显示为0
- [ ] 基本工资计算公式显示为"0"

## 2. 学历和语言系数调整检查

### 2.1 学历系数大于1的情况
#### 2.1.1 设置和计算
- [ ] 选择一个员工，设置学历为"硕士（985/211 院校）"
- [ ] 设置语言水平为"一般"
- [ ] 点击计算薪资按钮

#### 2.1.2 薪资表单检查
- [ ] 学历系数显示大于1（例如1.20）
- [ ] 学历调整金额为正数（例如+700）
- [ ] 语言系数显示为1.00
- [ ] 语言调整金额为0
- [ ] 基本工资计算公式正确显示：3500 + 700 + 0 = 4200
- [ ] 调整后的基本工资为4200

#### 2.1.3 保存后薪资详情检查
- [ ] 学历系数显示与表单一致（例如1.20）
- [ ] 语言系数显示与表单一致（例如1.00）
- [ ] 基本工资计算公式与表单一致：3500 + 700 + 0 = 4200
- [ ] 调整后的基本工资与表单一致（例如4200）

### 2.2 学历系数等于1的情况
#### 2.2.1 设置和计算
- [ ] 选择一个员工，设置学历为"本科（普通院校）"
- [ ] 设置语言水平为"一般"
- [ ] 点击计算薪资按钮

#### 2.2.2 薪资表单检查
- [ ] 学历系数显示为1.00
- [ ] 学历调整金额为0
- [ ] 语言系数显示为1.00
- [ ] 语言调整金额为0
- [ ] 基本工资计算公式正确显示：3500 + 0 + 0 = 3500
- [ ] 调整后的基本工资为3500

#### 2.2.3 保存后薪资详情检查
- [ ] 学历系数显示与表单一致（例如1.00）
- [ ] 语言系数显示与表单一致（例如1.00）
- [ ] 基本工资计算公式与表单一致：3500 + 0 + 0 = 3500
- [ ] 调整后的基本工资与表单一致（例如3500）

### 2.3 学历系数小于1的情况
#### 2.3.1 设置和计算
- [ ] 选择一个员工，设置学历为"大专及以下"
- [ ] 设置语言水平为"一般"
- [ ] 点击计算薪资按钮

#### 2.3.2 薪资表单检查
- [ ] 学历系数显示小于1（例如0.80）
- [ ] 学历调整金额为负数（例如-700）
- [ ] 语言系数显示为1.00
- [ ] 语言调整金额为0
- [ ] 基本工资计算公式正确显示：3500 - 700 + 0 = 2800
- [ ] 调整后的基本工资为2800

#### 2.3.3 保存后薪资详情检查
- [ ] 学历系数显示与表单一致（例如0.80）
- [ ] 语言系数显示与表单一致（例如1.00）
- [ ] 基本工资计算公式与表单一致：3500 - 700 + 0 = 2800
- [ ] 调整后的基本工资与表单一致（例如2800）

## 3. 重置薪资检查

### 3.1 单个员工重置
- [ ] 选择一个已计算薪资的员工，点击编辑薪资
- [ ] 在薪资表单中点击重置薪资按钮
- [ ] 确认薪资表单中所有金额都重置为0，所有系数都重置为1.00
- [ ] 保存后，确认薪资列表中该员工的所有薪资相关金额都显示为0
- [ ] 打开该员工的薪资详情页面，确认所有薪资相关金额都显示为0，所有系数都显示为1.00

### 3.2 批量重置
- [ ] 在薪资列表页面，点击重置所有薪资按钮
- [ ] 确认所有员工的薪资相关金额都显示为0
- [ ] 随机选择几个员工，打开其薪资详情页面，确认所有薪资相关金额都显示为0，所有系数都显示为1.00

## 4. 非全日制学历检查

### 4.1 设置和计算
- [ ] 选择一个员工，设置第一学历为"本科"，学历类型为"非全日制"
- [ ] 设置最高学历为"硕士"，学历类型为"非全日制"
- [ ] 设置语言水平为"一般"
- [ ] 点击计算薪资按钮

### 4.2 薪资表单检查
- [ ] 学历显示为"大专及以下"
- [ ] 学历系数显示小于1（例如0.80）
- [ ] 学历调整金额为负数（例如-700）
- [ ] 基本工资计算公式正确显示：3500 - 700 + 0 = 2800
- [ ] 调整后的基本工资为2800

### 4.3 保存后薪资详情检查
- [ ] 学历系数显示与表单一致（例如0.80）
- [ ] 基本工资计算公式与表单一致：3500 - 700 + 0 = 2800
- [ ] 调整后的基本工资与表单一致（例如2800）

## 检查结果记录

| 检查项目 | 通过状态 | 备注 |
|---------|---------|------|
| 1.1 薪资列表页面检查 | □ 通过 □ 不通过 | |
| 1.2 薪资详情页面检查 | □ 通过 □ 不通过 | |
| 1.3 薪资表单检查 | □ 通过 □ 不通过 | |
| 2.1 学历系数大于1的情况 | □ 通过 □ 不通过 | |
| 2.2 学历系数等于1的情况 | □ 通过 □ 不通过 | |
| 2.3 学历系数小于1的情况 | □ 通过 □ 不通过 | |
| 3.1 单个员工重置 | □ 通过 □ 不通过 | |
| 3.2 批量重置 | □ 通过 □ 不通过 | |
| 4.1 非全日制学历检查 | □ 通过 □ 不通过 | |

## 问题记录

| 问题描述 | 问题位置 | 修复状态 |
|---------|---------|---------|
| | | □ 已修复 □ 未修复 |
| | | □ 已修复 □ 未修复 |
| | | □ 已修复 □ 未修复 |
