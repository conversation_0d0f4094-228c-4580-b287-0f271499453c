<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端薪资配置保存测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 前端薪资配置保存功能测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>这个测试将验证前端薪资配置的保存功能是否正常工作。</p>
            
            <h4>🔧 手动测试步骤：</h4>
            <ol>
                <li>点击下方的"打开薪资配置页面"按钮</li>
                <li>使用管理员账号登录（用户名：admin，密码：admin123）</li>
                <li>进入"后台管理" → "薪资配置"</li>
                <li>切换到"岗位工资"选项卡</li>
                <li>修改任意岗位的工资数值</li>
                <li>点击右上角的"保存配置"按钮</li>
                <li>刷新页面，检查修改是否保存成功</li>
                <li>点击下方的"验证保存结果"按钮进行自动验证</li>
            </ol>
            
            <h4>⚠️ 注意事项：</h4>
            <ul>
                <li>确保后端服务正在运行（端口5006）</li>
                <li>确保前端服务正在运行（端口3000）</li>
                <li>测试完成后会自动恢复原始配置</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🚀 快速操作</h2>
            <button onclick="openSalaryConfig()">打开薪资配置页面</button>
            <button onclick="verifyBackendAPI()">验证后端API</button>
            <button onclick="showTestResults()">显示测试结果</button>
        </div>

        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div id="testResults">
                <div class="info">
                    <p>点击上方按钮开始测试...</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 问题排查</h2>
            <div id="troubleshooting">
                <h4>常见问题：</h4>
                <ul>
                    <li><strong>保存后数据恢复：</strong> 检查前端组件的状态管理是否正确</li>
                    <li><strong>保存按钮无响应：</strong> 检查全局保存函数是否正确调用组件的保存方法</li>
                    <li><strong>网络错误：</strong> 确认后端服务是否正常运行</li>
                    <li><strong>权限错误：</strong> 确认是否使用管理员账号登录</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function openSalaryConfig() {
            window.open('http://localhost:3000', '_blank');
            showResult('info', '已打开薪资配置页面，请按照说明进行手动测试');
        }

        async function verifyBackendAPI() {
            try {
                showResult('info', '正在验证后端API...');
                
                // 1. 登录
                const loginResponse = await fetch('http://localhost:5006/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                
                if (!loginResponse.ok) {
                    throw new Error('管理员登录失败');
                }
                
                const loginData = await loginResponse.json();
                const token = loginData.token;
                
                // 2. 获取配置
                const configResponse = await fetch('http://localhost:5006/api/admin/salary-config', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (!configResponse.ok) {
                    throw new Error('获取薪资配置失败');
                }
                
                const config = await configResponse.json();
                const originalA1 = config.TECH_POSITIONS['技术']['A1'];
                
                // 3. 测试保存
                const newA1 = originalA1 + 50;
                const updateData = {
                    TECH_POSITIONS: {
                        '技术': {
                            ...config.TECH_POSITIONS['技术'],
                            A1: newA1
                        }
                    },
                    MANAGER_POSITIONS: config.MANAGER_POSITIONS,
                    SUPPORT_POSITIONS: config.SUPPORT_POSITIONS,
                    OTHER_POSITIONS: config.OTHER_POSITIONS
                };
                
                const saveResponse = await fetch('http://localhost:5006/api/admin/salary-config', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(updateData)
                });
                
                if (!saveResponse.ok) {
                    throw new Error('保存配置失败');
                }
                
                // 4. 验证保存结果
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const verifyResponse = await fetch('http://localhost:5006/api/admin/salary-config', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                const verifyConfig = await verifyResponse.json();
                const actualA1 = verifyConfig.TECH_POSITIONS['技术']['A1'];
                
                // 5. 恢复原始配置
                const restoreData = {
                    TECH_POSITIONS: {
                        '技术': {
                            ...verifyConfig.TECH_POSITIONS['技术'],
                            A1: originalA1
                        }
                    },
                    MANAGER_POSITIONS: verifyConfig.MANAGER_POSITIONS,
                    SUPPORT_POSITIONS: verifyConfig.SUPPORT_POSITIONS,
                    OTHER_POSITIONS: verifyConfig.OTHER_POSITIONS
                };
                
                await fetch('http://localhost:5006/api/admin/salary-config', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(restoreData)
                });
                
                if (actualA1 === newA1) {
                    showResult('success', `✅ 后端API测试通过！原始值: ${originalA1}, 测试值: ${newA1}, 验证值: ${actualA1}`);
                } else {
                    showResult('error', `❌ 后端API测试失败！期望: ${newA1}, 实际: ${actualA1}`);
                }
                
            } catch (error) {
                showResult('error', `❌ 后端API测试失败: ${error.message}`);
            }
        }

        function showTestResults() {
            const results = `
                <h4>🎯 测试重点</h4>
                <div class="step">
                    <strong>1. 前端状态管理：</strong> PositionSalaryForm 组件使用 React.forwardRef 和 useImperativeHandle 暴露保存函数
                </div>
                <div class="step">
                    <strong>2. 全局保存逻辑：</strong> handleSaveClick 函数通过 ref 调用组件的保存方法
                </div>
                <div class="step">
                    <strong>3. 状态同步：</strong> 使用 isSaving 标志防止保存过程中状态重置
                </div>
                <div class="step">
                    <strong>4. 配置合并：</strong> 直接使用服务器返回的完整配置，避免合并冲突
                </div>
                
                <h4>🔧 已修复的问题</h4>
                <ul>
                    <li>✅ 修复了语法错误（多余的字符 'A'）</li>
                    <li>✅ 解决了状态管理冲突问题</li>
                    <li>✅ 优化了配置合并逻辑</li>
                    <li>✅ 添加了组件间通信机制</li>
                </ul>
            `;
            
            document.getElementById('testResults').innerHTML = results;
        }

        function showResult(type, message) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<p>${message}</p>`;
            
            const resultsContainer = document.getElementById('testResults');
            resultsContainer.appendChild(resultDiv);
            
            // 滚动到结果
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 页面加载时显示初始信息
        window.onload = function() {
            showResult('info', '🚀 测试页面已加载，请按照说明进行测试');
        };
    </script>
</body>
</html>
