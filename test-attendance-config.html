<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考勤配置API测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button.danger {
            background-color: #ff4d4f;
        }
        button.danger:hover {
            background-color: #ff7875;
        }
        pre {
            background-color: #f6f8fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .info { color: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>考勤配置API测试</h1>
        <p class="info">测试前端修复后的考勤配置管理功能</p>
        
        <div class="test-section">
            <h3>1. 获取所有配置</h3>
            <button onclick="getAllConfigs()">获取配置列表</button>
            <pre id="getAllResult"></pre>
        </div>

        <div class="test-section">
            <h3>2. 获取默认配置</h3>
            <button onclick="getDefaultConfig()">获取默认配置</button>
            <pre id="getDefaultResult"></pre>
        </div>

        <div class="test-section">
            <h3>3. 创建测试配置</h3>
            <button onclick="createTestConfig()">创建标准配置</button>
            <button onclick="createFlexibleConfig()">创建弹性配置</button>
            <pre id="createResult"></pre>
        </div>

        <div class="test-section">
            <h3>4. 配置管理</h3>
            <button onclick="setDefaultConfig()">设置默认配置</button>
            <button onclick="toggleActiveConfig()">切换启用状态</button>
            <pre id="manageResult"></pre>
        </div>

        <div class="test-section">
            <h3>5. 删除测试配置</h3>
            <button class="danger" onclick="deleteTestConfigs()">删除测试配置</button>
            <pre id="deleteResult"></pre>
        </div>

        <div class="test-section">
            <h3>6. 前端页面测试</h3>
            <p>请访问前端页面进行手动测试：</p>
            <a href="http://localhost:3000/admin/attendance-config" target="_blank" rel="noopener">
                <button>打开考勤配置管理页面</button>
            </a>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/attendance-config';
        let testConfigIds = [];

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML = `<span class="${className}">[${timestamp}] ${message}</span>`;
        }

        async function getAllConfigs() {
            try {
                const response = await fetch(API_BASE);
                const result = await response.json();
                log('getAllResult', JSON.stringify(result, null, 2), 'success');
            } catch (error) {
                log('getAllResult', `错误: ${error.message}`, 'error');
            }
        }

        async function getDefaultConfig() {
            try {
                const response = await fetch(`${API_BASE}/default`);
                const result = await response.json();
                log('getDefaultResult', JSON.stringify(result, null, 2), 'success');
            } catch (error) {
                log('getDefaultResult', `错误: ${error.message}`, 'error');
            }
        }

        async function createTestConfig() {
            const config = {
                name: "标准工作制测试",
                description: "测试用标准工作制配置",
                workDays: [1, 2, 3, 4, 5],
                standardWorkTime: {
                    startTime: "08:30",
                    endTime: "17:30"
                },
                flexibleTime: {
                    enabled: false,
                    earliestStartTime: "08:00",
                    latestStartTime: "09:00",
                    minWorkHours: 8
                },
                lateEarlyRules: {
                    lateTolerance: 5,
                    earlyTolerance: 5,
                    severeLateTime: 30
                },
                lunchBreak: {
                    enabled: true,
                    startTime: "12:00",
                    endTime: "13:00",
                    duration: 60
                },
                overtimeRules: {
                    enabled: true,
                    weekdayOvertimeStart: "17:30",
                    minOvertimeMinutes: 30,
                    requireApproval: false
                },
                isActive: true
            };

            try {
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });
                const result = await response.json();
                if (result.success && result.data) {
                    testConfigIds.push(result.data._id);
                }
                log('createResult', JSON.stringify(result, null, 2), result.success ? 'success' : 'error');
            } catch (error) {
                log('createResult', `错误: ${error.message}`, 'error');
            }
        }

        async function createFlexibleConfig() {
            const config = {
                name: "弹性工作制测试",
                description: "测试用弹性工作制配置",
                workDays: [1, 2, 3, 4, 5],
                standardWorkTime: {
                    startTime: "09:00",
                    endTime: "18:00"
                },
                flexibleTime: {
                    enabled: true,
                    earliestStartTime: "08:00",
                    latestStartTime: "10:00",
                    minWorkHours: 8
                },
                lateEarlyRules: {
                    lateTolerance: 10,
                    earlyTolerance: 10,
                    severeLateTime: 60
                },
                lunchBreak: {
                    enabled: true,
                    startTime: "12:00",
                    endTime: "13:00",
                    duration: 60
                },
                overtimeRules: {
                    enabled: true,
                    weekdayOvertimeStart: "18:00",
                    minOvertimeMinutes: 30,
                    requireApproval: true
                },
                isActive: true
            };

            try {
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });
                const result = await response.json();
                if (result.success && result.data) {
                    testConfigIds.push(result.data._id);
                }
                log('createResult', JSON.stringify(result, null, 2), result.success ? 'success' : 'error');
            } catch (error) {
                log('createResult', `错误: ${error.message}`, 'error');
            }
        }

        async function setDefaultConfig() {
            if (testConfigIds.length === 0) {
                log('manageResult', '请先创建测试配置', 'error');
                return;
            }

            const configId = testConfigIds[0];
            try {
                const response = await fetch(`${API_BASE}/${configId}/set-default`, {
                    method: 'POST'
                });
                const result = await response.json();
                log('manageResult', JSON.stringify(result, null, 2), result.success ? 'success' : 'error');
            } catch (error) {
                log('manageResult', `错误: ${error.message}`, 'error');
            }
        }

        async function toggleActiveConfig() {
            if (testConfigIds.length === 0) {
                log('manageResult', '请先创建测试配置', 'error');
                return;
            }

            const configId = testConfigIds[0];
            try {
                const response = await fetch(`${API_BASE}/${configId}/toggle-active`, {
                    method: 'POST'
                });
                const result = await response.json();
                log('manageResult', JSON.stringify(result, null, 2), result.success ? 'success' : 'error');
            } catch (error) {
                log('manageResult', `错误: ${error.message}`, 'error');
            }
        }

        async function deleteTestConfigs() {
            if (testConfigIds.length === 0) {
                log('deleteResult', '没有测试配置需要删除', 'info');
                return;
            }

            const results = [];
            for (const configId of testConfigIds) {
                try {
                    const response = await fetch(`${API_BASE}/${configId}`, {
                        method: 'DELETE'
                    });
                    const result = await response.json();
                    results.push({ configId, result });
                } catch (error) {
                    results.push({ configId, error: error.message });
                }
            }
            
            testConfigIds = [];
            log('deleteResult', JSON.stringify(results, null, 2), 'success');
        }

        // 页面加载时自动获取配置列表
        window.onload = function() {
            getAllConfigs();
        };
    </script>
</body>
</html> 
</html> 