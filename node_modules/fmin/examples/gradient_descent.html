<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Gradient Descent Example</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="<PERSON>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css"/>
<style>

.ticks {
  font: 10px sans-serif;
}

.track,
.track-inset,
.track-overlay {
  stroke-linecap: round;
}

.track {
  stroke: #000;
  stroke-opacity: 0.3;
  stroke-width: 10px;
}

.track-inset {
  stroke: #ddd;
  stroke-width: 8px;
}

.track-overlay {
  pointer-events: stroke;
  stroke-width: 50px;
  cursor: crosshair;
}

.handle {
  fill: #fff;
  stroke: #000;
  stroke-opacity: 0.5;
  stroke-width: 1.25px;
}
</style>

</head>

<body>
<div class="container">
<div class = "row"><div class="col-md-6 col-md-offset-3">

<div id ="gd" >
<div style="text-align:center"><div style="display:inline-block;">
<h4>
    <div class="btn-group">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
          <span class="function_sig">
                \(f(x, y) \)
            </span>
            <span class="caret"></span>
        </button>
        <ul class="dropdown-menu" role="menu">
            <li><a class="function_flower">
                \(x^2 + y^2 + x \sin y + y \sin x \)
            </a></li>
            <li><a class="function_himmelblau">
                \((x^2 + y - 11)^2 + (x+y^2 -7)^2\)
            </a></li>
            <li><a class="function_banana">
                \((1-x)^2 + 100 (y - x^2) ^2\)
            </a></li>
            <li><a class="function_matyas">
                \(.26 (x^2 + y^2)  + .48 x y \)
            </a></li>
        </ul>
    </div>
    <span>\(=\)</span>
    <span class="function_label">
        \((x^2 + y - 11)^2 + (x+y^2 -7)^2\)
    </span>
</h4>
</div></div>

<div id="vis"></div>

<div class="row">
<form class="form-inline" role="form">
    <div class="form-group col-xs-12 col-md-12">
        <div style="text-align:center"><div style="display:inline-block;">
            <label class="r-only" for="learningrate">Learning Rate \(\alpha\)
                <span id="learningratevalue">= 0.01</span>
            </label>
        </div></div>
        <div id="learningrate"></div>
    </div>
</form>
</div>
<div style="text-align:center"><div style="display:inline-block;">
  <div class="checkbox">
    <label>
      <input id="linesearchcheck" type="checkbox"> Use Line Search
    </label>
  </div>
</div>
<div style="text-align:center"><div style="display:inline-block;">
<div class="row">
    <span class ="iterations"></span>
</div>
</div></div>
</div>
</div>
</div>


</body>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.1.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3/4.2.3/d3.min.js"></script>
<script src="../build/fmin.js"></script>
<script src="../build/fmin_vis.js"></script>

<script>
var gd_plot = new fmin_vis.GradientContour(d3.select("#gd"));
</script>

<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    showMathMenu: false,
    extensions: ["tex2jax.js"],
    jax: ["input/TeX", "output/HTML-CSS"],
    tex2jax: {
      inlineMath: [ ['$','$'], ["\\(","\\)"] ],
      displayMath: [ ['$$','$$'], ["\\[","\\]"] ],
      processEscapes: true
    },
    "HTML-CSS": { availableFonts: ["TeX"] }
  });
</script>
<script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
</html>
