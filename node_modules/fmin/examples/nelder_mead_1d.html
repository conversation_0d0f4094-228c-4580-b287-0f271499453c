<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title><PERSON><PERSON><PERSON> in One Dimension</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="<PERSON>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css"/>
<style>

.ticks {
  font: 10px sans-serif;
}

.track,
.track-inset,
.track-overlay {
  stroke-linecap: round;
}

.track {
  stroke: #000;
  stroke-opacity: 0.3;
  stroke-width: 10px;
}

.track-inset {
  stroke: #ddd;
  stroke-width: 8px;
}

.track-overlay {
  pointer-events: stroke;
  stroke-width: 50px;
  cursor: crosshair;
}

.handle {
  fill: #fff;
  stroke: #000;
  stroke-opacity: 0.5;
  stroke-width: 1.25px;
}

</style>
</head>

<body>
<div class="container">
<div class = "row"><div class="col-md-6 col-md-offset-3">

<div id ="simplex1d" >
<div style="text-align:center"><div style="display:inline-block;">
<h4>

        <div class="btn-group">
            <button id="fx" type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                <span>
                \(f(x)\)
                </span>
                <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" role="menu">
                <li><a class="function_smooth">
                    \(log{(1 + \left|x\right|^{2+\sin x})}\)
                </a></li>
                <li><a class="function_noisy">
                    \((2+\frac{\sin{50x}}{50})  ({\arctan x})^2\)
                </a></li>
                <li><a class="function_floor">
                    \(\left|\left\lfloor x \right\rfloor - 50\right|\)
                </a></li>
            </ul>
        </div>
       <span>
            \(=\)
        </span>
      <span class="function_label">
            \(\log{(1 + \left|x\right|^{2+\sin x})}\)
        </span>
</h4>
</div></div>

<div id="vis"></div>


<div class="row">
<form class="form-inline" role="form">
    <div class="form-group col-xs-6 col-md-6">
        <div style="text-align:center"><div style="display:inline-block;">
            <label class="r-only" for="contraction">Contraction \(\psi\)
                <span id="contractionvalue">= 1.0x</span>
            </label>
        </div></div>
        <div id="contraction"></div>
    </div>
    <div class="form-group col-xs-6 col-md-6">
        <div style="text-align:center"><div style="display:inline-block;">
            <label class="r-only" for="expansion">Expansion \(\chi\)
                <span id="expansionvalue">= 1.0x</span>
            </label>
        </div></div>
        <div id="expansion"></div>
    </div>
    <!--
    <div class="form-group col-xs-4 col-md-4">
        <div style="text-align:center"><div style="display:inline-block;">
            <label class="r-only" for="nonzerodelta">Nonzero \(\Delta\)</label>
        </div></div>
        <div id="nonzerodelta"></div>
    </div>
    -->
</form>
</div>
<div class="row">
    <div style="text-align:center"><div style="display:inline-block;">
        <div class ="iterations"></div>
    </div></div>
</div>
<div class="row">
</div>
</div>
</div>
</div>

</body>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.1.1/jquery.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3/4.2.3/d3.min.js"></script>
<script src="../build/fmin.js"></script>
<script src="../build/fmin_vis.js"></script>

<script>
var nelder_mead_1d_plot = new fmin_vis.NelderMead1d(d3.select("#simplex1d"));
</script>

<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    showMathMenu: false,
    extensions: ["tex2jax.js"],
    jax: ["input/TeX", "output/HTML-CSS"],
    tex2jax: {
      inlineMath: [ ['$','$'], ["\\(","\\)"] ],
      displayMath: [ ['$$','$$'], ["\\[","\\]"] ],
      processEscapes: true
    },
    "HTML-CSS": { availableFonts: ["TeX"] }
  });
</script>
<script  src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
</html>
