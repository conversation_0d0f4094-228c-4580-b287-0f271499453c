!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(r.fmin=r.fmin||{})}(this,function(r){"use strict";function e(r,e,i,f){f=f||{};var x=f.maxIterations||100,t=f.tolerance||1e-10,n=r(e),a=r(i),s=i-e;if(n*a>0)throw"Initial bisect points must have opposite signs";if(0===n)return e;if(0===a)return i;for(var o=0;x>o;++o){s/=2;var c=e+s,l=r(c);if(l*n>=0&&(e=c),Math.abs(s)<t||0===l)return c}return e+s}function i(r){for(var e=new Array(r),i=0;r>i;++i)e[i]=0;return e}function f(r,e){return i(r).map(function(){return i(e)})}function x(r,e){for(var i=0,f=0;f<r.length;++f)i+=r[f]*e[f];return i}function t(r){return Math.sqrt(x(r,r))}function n(r,e,i){for(var f=0;f<e.length;++f)r[f]=e[f]*i}function a(r,e,i,f,x){for(var t=0;t<r.length;++t)r[t]=e*i[t]+f*x[t]}function s(r,e,i){function f(r){for(var e=0;e<r.length;e++)v[h][e]=r[e];v[h].fx=r.fx}i=i||{};var x,t=i.maxIterations||200*e.length,n=i.nonZeroDelta||1.05,s=i.zeroDelta||.001,o=i.minErrorDelta||1e-6,c=i.minErrorDelta||1e-5,l=void 0!==i.rho?i.rho:1,p=void 0!==i.chi?i.chi:2,u=void 0!==i.psi?i.psi:-.5,m=void 0!==i.sigma?i.sigma:.5,h=e.length,v=new Array(h+1);v[0]=e,v[0].fx=r(e),v[0].id=0;for(var d=0;h>d;++d){var g=e.slice();g[d]=g[d]?g[d]*n:s,v[d+1]=g,v[d+1].fx=r(g),v[d+1].id=d+1}for(var y=function(r,e){return r.fx-e.fx},b=e.slice(),M=e.slice(),D=e.slice(),I=e.slice(),w=0;t>w;++w){if(v.sort(y),i.history){var k=v.map(function(r){var e=r.slice();return e.fx=r.fx,e.id=r.id,e});k.sort(function(r,e){return r.id-e.id}),i.history.push({x:v[0].slice(),fx:v[0].fx,simplex:k})}for(x=0,d=0;h>d;++d)x=Math.max(x,Math.abs(v[0][d]-v[1][d]));if(Math.abs(v[0].fx-v[h].fx)<o&&c>x)break;for(d=0;h>d;++d){b[d]=0;for(var z=0;h>z;++z)b[d]+=v[z][d];b[d]/=h}var R=v[h];if(a(M,1+l,b,-l,R),M.fx=r(M),M.fx<v[0].fx)a(I,1+p,b,-p,R),I.fx=r(I),f(I.fx<M.fx?I:M);else if(M.fx>=v[h-1].fx){var j=!1;if(M.fx>R.fx?(a(D,1+u,b,-u,R),D.fx=r(D),D.fx<R.fx?f(D):j=!0):(a(D,1-u*l,b,u*l,R),D.fx=r(D),D.fx<M.fx?f(D):j=!0),j){if(m>=1)break;for(d=1;d<v.length;++d)a(v[d],1-m,v[0],m,v[d]),v[d].fx=r(v[d])}}else f(M)}return v.sort(y),{fx:v[0].fx,x:v[0]}}function o(r,e,i,f,t,n,s){function o(o,u,h){for(var v=0;16>v;++v)if(t=(o+u)/2,a(f.x,1,i.x,t,e),p=f.fx=r(f.x,f.fxprime),m=x(f.fxprime,e),p>c+n*t*l||p>=h)u=t;else{if(Math.abs(m)<=-s*l)return t;m*(u-o)>=0&&(u=o),o=t,h=p}return 0}var c=i.fx,l=x(i.fxprime,e),p=c,u=c,m=l,h=0;t=t||1,n=n||1e-6,s=s||.1;for(var v=0;10>v;++v){if(a(f.x,1,i.x,t,e),p=f.fx=r(f.x,f.fxprime),m=x(f.fxprime,e),p>c+n*t*l||v&&p>=u)return o(h,t,u);if(Math.abs(m)<=-s*l)return t;if(m>=0)return o(t,h,p);u=p,h=t,t*=2}return t}function c(r,e,i){var f,s,c,l={x:e.slice(),fx:0,fxprime:e.slice()},p={x:e.slice(),fx:0,fxprime:e.slice()},u=e.slice(),m=1;i=i||{},c=i.maxIterations||20*e.length,l.fx=r(l.x,l.fxprime),f=l.fxprime.slice(),n(f,l.fxprime,-1);for(var h=0;c>h;++h){if(m=o(r,f,l,p,m),i.history&&i.history.push({x:l.x.slice(),fx:l.fx,fxprime:l.fxprime.slice(),alpha:m}),m){a(u,1,p.fxprime,-1,l.fxprime);var v=x(l.fxprime,l.fxprime),d=Math.max(0,x(u,p.fxprime)/v);a(f,d,f,-1,p.fxprime),s=l,l=p,p=s}else n(f,l.fxprime,-1);if(t(l.fxprime)<=1e-5)break}return i.history&&i.history.push({x:l.x.slice(),fx:l.fx,fxprime:l.fxprime.slice(),alpha:m}),l}function l(r,e,i){i=i||{};for(var f=i.maxIterations||100*e.length,x=i.learnRate||.001,n={x:e.slice(),fx:0,fxprime:e.slice()},s=0;f>s&&(n.fx=r(n.x,n.fxprime),i.history&&i.history.push({x:n.x.slice(),fx:n.fx,fxprime:n.fxprime.slice()}),a(n.x,1,n.x,-x,n.fxprime),!(t(n.fxprime)<=1e-5));++s);return n}function p(r,e,i){i=i||{};var f,x={x:e.slice(),fx:0,fxprime:e.slice()},a={x:e.slice(),fx:0,fxprime:e.slice()},s=i.maxIterations||100*e.length,c=i.learnRate||1,l=e.slice(),p=i.c1||.001,u=i.c2||.1,m=[];if(i.history){var h=r;r=function(r,e){return m.push(r.slice()),h(r,e)}}x.fx=r(x.x,x.fxprime);for(var v=0;s>v&&(n(l,x.fxprime,-1),c=o(r,l,x,a,c,p,u),i.history&&(i.history.push({x:x.x.slice(),fx:x.fx,fxprime:x.fxprime.slice(),functionCalls:m,learnRate:c,alpha:c}),m=[]),f=x,x=a,a=f,!(0===c||t(x.fxprime)<1e-5));++v);return x}r.bisect=e,r.nelderMead=s,r.conjugateGradient=c,r.gradientDescent=l,r.gradientDescentLineSearch=p,r.zeros=i,r.zerosM=f,r.norm2=t,r.weightedSum=a,r.scale=n});