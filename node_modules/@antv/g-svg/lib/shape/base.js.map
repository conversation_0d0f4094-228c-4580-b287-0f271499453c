{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/shape/base.ts"], "names": [], "mappings": ";;;AAAA,uCAA6C;AAI7C,mCAA+D;AAC/D,mCAAwC;AACxC,qCAA8C;AAC9C,wCAA2C;AAC3C,+BAAiC;AACjC,kCAA6B;AAC7B,uCAA6C;AAE7C;IAAwB,qCAAa;IAArC;QAAA,qEAuOC;QAtOC,UAAI,GAAW,KAAK,CAAC;QACrB,aAAO,GAAY,KAAK,CAAC;QACzB,eAAS,GAAY,KAAK,CAAC;;IAoO7B,CAAC;IAlOC,mCAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,QAAQ;QACR,6CACK,KAAK,KACR,SAAS,EAAE,CAAC,EACZ,eAAe,EAAE,CAAC,EAClB,aAAa,EAAE,CAAC,EAChB,WAAW,EAAE,CAAC,IACd;IACJ,CAAC;IAED,4BAA4B;IAC5B,oCAAgB,GAAhB,UAAiB,WAAuB;QACtC,iBAAM,gBAAgB,YAAC,WAAW,CAAC,CAAC;QACpC,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClC,sBAAsB;QACtB,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACpC,IAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;SACjC;IACH,CAAC;IAED,gCAAY,GAAZ;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,gCAAY,GAAZ;QACE,OAAO,eAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,kCAAc,GAAd,UAAe,UAAsB;QACnC,qBAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;IAED,iCAAa,GAAb;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,2CAA2C;QAC3C,IAAI,EAAE,EAAE;YACN,IAAI,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;SACrB;aAAM;YACL,IAAM,UAAU,GAAG,sBAAa,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;YACnD,IAAI,UAAU,EAAE;gBACd,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;aACzB;SACF;QACD,IAAI,IAAI,EAAE;YACA,IAAA,CAAC,GAAuB,IAAI,EAA3B,EAAE,CAAC,GAAoB,IAAI,EAAxB,EAAE,KAAK,GAAa,IAAI,MAAjB,EAAE,MAAM,GAAK,IAAI,OAAT,CAAU;YACrC,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACzC,IAAM,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;YAChC,IAAM,IAAI,GAAG,CAAC,GAAG,SAAS,CAAC;YAC3B,IAAM,IAAI,GAAG,CAAC,GAAG,SAAS,CAAC;YAC3B,IAAM,IAAI,GAAG,CAAC,GAAG,KAAK,GAAG,SAAS,CAAC;YACnC,IAAM,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS,CAAC;YACpC,OAAO;gBACL,CAAC,EAAE,IAAI;gBACP,CAAC,EAAE,IAAI;gBACP,IAAI,MAAA;gBACJ,IAAI,MAAA;gBACJ,IAAI,MAAA;gBACJ,IAAI,MAAA;gBACJ,KAAK,EAAE,KAAK,GAAG,SAAS;gBACxB,MAAM,EAAE,MAAM,GAAG,SAAS;aAC3B,CAAC;SACH;QACD,OAAO;YACL,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,CAAC;YACR,MAAM,EAAE,CAAC;SACV,CAAC;IACJ,CAAC;IAED,0BAAM,GAAN;QACQ,IAAA,KAAsB,IAAI,CAAC,IAAI,EAAE,EAA/B,IAAI,UAAA,EAAE,SAAS,eAAgB,CAAC;QACxC,OAAO,CAAC,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC;IACnE,CAAC;IAED,4BAAQ,GAAR;QACQ,IAAA,KAA0B,IAAI,CAAC,IAAI,EAAE,EAAnC,MAAM,YAAA,EAAE,WAAW,iBAAgB,CAAC;QAC5C,OAAO,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC;IACnD,CAAC;IAED,wBAAI,GAAJ,UAAK,OAAO,EAAE,WAAW;QACvB,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;YACzB,IAAI,EAAE,EAAE;gBACN,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;aAC/B;SACF;aAAM;YACL,IAAI,CAAC,EAAE,EAAE;gBACP,eAAS,CAAC,IAAI,CAAC,CAAC;aACjB;YACD,aAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAClC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YACzC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;SAC7B;IACH,CAAC;IAED;;;;;OAKG;IACH,8BAAU,GAAV,UAAW,OAAa,EAAE,WAAwB,IAAG,CAAC;IAEtD,kBAAkB;IAClB,iCAAa,GAAb,UAAc,OAAO,EAAE,WAAY;QACjC,IAAM,KAAK,GAAG,WAAW,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACjC,IAAA,IAAI,GAA4E,KAAK,KAAjF,EAAE,SAAS,GAAiE,KAAK,UAAtE,EAAE,MAAM,GAAyD,KAAK,OAA9D,EAAE,WAAW,GAA4C,KAAK,YAAjD,EAAE,WAAW,GAA+B,KAAK,YAApC,EAAE,aAAa,GAAgB,KAAK,cAArB,EAAE,SAAS,GAAK,KAAK,UAAV,CAAW;QAC9F,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,0DAA0D;YAC1D,IAAI,CAAC,WAAW,EAAE;gBAChB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,IAAI,SAAS,CAAC,CAAC;aACpD;iBAAM,IAAI,MAAM,IAAI,KAAK,EAAE;gBAC1B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;aACvC;iBAAM,IAAI,WAAW,IAAI,KAAK,EAAE;gBAC/B,4BAA4B;gBAC5B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;aAC5C;YACD,IAAI,WAAW,EAAE;gBACf,EAAE,CAAC,YAAY,CAAC,uBAAY,CAAC,aAAa,CAAC,EAAE,WAAW,CAAC,CAAC;aAC3D;SACF;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,GAAG,CAAC,EAAE;YACnC,IAAI,CAAC,WAAW,EAAE;gBAChB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,WAAW,CAAC,CAAC;aAC1D;iBAAM,IAAI,QAAQ,IAAI,KAAK,EAAE;gBAC5B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;aAC3C;iBAAM,IAAI,aAAa,IAAI,KAAK,EAAE;gBACjC,8BAA8B;gBAC9B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;aAChD;YACD,IAAI,aAAa,EAAE;gBACjB,EAAE,CAAC,YAAY,CAAC,uBAAY,CAAC,eAAe,CAAC,EAAE,aAAa,CAAC,CAAC;aAC/D;YACD,IAAI,SAAS,EAAE;gBACb,EAAE,CAAC,YAAY,CAAC,uBAAY,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,CAAC;aACvD;SACF;IACH,CAAC;IAED,6BAAS,GAAT,UAAU,OAAO,EAAE,IAAI,EAAE,KAAK;QAC5B,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,KAAK,EAAE;YACV,4CAA4C;YAC5C,EAAE,CAAC,YAAY,CAAC,uBAAY,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;YAC5C,OAAO;SACR;QACD,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACtC,IAAI,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACzC,IAAI,CAAC,EAAE,EAAE;gBACP,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aACjC;YACD,EAAE,CAAC,YAAY,CAAC,uBAAY,CAAC,IAAI,CAAC,EAAE,UAAQ,EAAE,MAAG,CAAC,CAAC;SACpD;aAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACzC,IAAI,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACxC,IAAI,CAAC,EAAE,EAAE;gBACP,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;aAChC;YACD,EAAE,CAAC,YAAY,CAAC,uBAAY,CAAC,IAAI,CAAC,EAAE,UAAQ,EAAE,MAAG,CAAC,CAAC;SACpD;aAAM;YACL,EAAE,CAAC,YAAY,CAAC,uBAAY,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;SAC5C;IACH,CAAC;IAED,0BAAM,GAAN,UAAO,OAAO,EAAE,WAAY;QAC1B,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACpB,IAAA,KAA4D,WAAW,IAAI,KAAK,EAA9E,aAAa,mBAAA,EAAE,aAAa,mBAAA,EAAE,UAAU,gBAAA,EAAE,WAAW,iBAAyB,CAAC;QACvF,IAAI,aAAa,IAAI,aAAa,IAAI,UAAU,IAAI,WAAW,EAAE;YAC/D,eAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SAC1B;IACH,CAAC;IAED,6BAAS,GAAT,UAAU,WAAY;QACpB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAA,MAAM,GAAK,CAAA,WAAW,IAAI,KAAK,CAAA,OAAzB,CAA0B;QACxC,IAAI,MAAM,EAAE;YACV,kBAAY,CAAC,IAAI,CAAC,CAAC;SACpB;IACH,CAAC;IAED,6BAAS,GAAT,UAAU,IAAY,EAAE,IAAY;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,iCAAa,GAAb,UAAc,IAAY,EAAE,IAAY;QACtC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,CAAC;QACtD,IAAM,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACjC,IAAM,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;QAChC,IAAM,OAAO,GAAG,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC5D,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE;YACtC,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,mCAAe,GAAf;QACQ,IAAA,KAAiC,IAAI,CAAC,KAAK,EAAzC,SAAS,eAAA,EAAE,eAAe,qBAAe,CAAC;QAClD,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACnB,OAAO,SAAS,GAAG,eAAe,CAAC;SACpC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IACH,gBAAC;AAAD,CAAC,AAvOD,CAAwB,sBAAa,GAuOpC;AAED,kBAAe,SAAS,CAAC"}