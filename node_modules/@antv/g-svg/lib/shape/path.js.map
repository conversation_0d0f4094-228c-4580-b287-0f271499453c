{"version": 3, "file": "path.js", "sourceRoot": "", "sources": ["../../src/shape/path.ts"], "names": [], "mappings": ";;;AAKA,mCAAqD;AACrD,wCAA2C;AAC3C,+BAA+B;AAE/B;IAAmB,gCAAS;IAA5B;QAAA,qEA8EC;QA7EC,UAAI,GAAW,MAAM,CAAC;QACtB,aAAO,GAAY,IAAI,CAAC;QACxB,eAAS,GAAY,IAAI,CAAC;;IA2E5B,CAAC;IAzEC,8BAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,6CACK,KAAK,KACR,UAAU,EAAE,KAAK,EACjB,QAAQ,EAAE,KAAK,IACf;IACJ,CAAC;IAED,yBAAU,GAAV,UAAW,OAAO,EAAE,WAAW;QAA/B,iBAmBC;QAlBC,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,WAAI,CAAC,WAAW,IAAI,KAAK,EAAE,UAAC,KAAK,EAAE,IAAI;YACrC,IAAI,IAAI,KAAK,MAAM,IAAI,cAAO,CAAC,KAAK,CAAC,EAAE;gBACrC,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/C;iBAAM,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,UAAU,EAAE;gBACvD,IAAI,KAAK,EAAE;oBACT,IAAM,EAAE,GAAG,eAAQ,CAAC,KAAK,CAAC;wBACxB,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,uBAAY,CAAC,IAAI,CAAC,CAAC;wBAC7C,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,uBAAY,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvD,EAAE,CAAC,YAAY,CAAC,uBAAY,CAAC,IAAI,CAAC,EAAE,UAAQ,EAAE,MAAG,CAAC,CAAC;iBACpD;qBAAM;oBACL,EAAE,CAAC,eAAe,CAAC,uBAAY,CAAC,IAAI,CAAC,CAAC,CAAC;iBACxC;aACF;iBAAM,IAAI,uBAAY,CAAC,IAAI,CAAC,EAAE;gBAC7B,EAAE,CAAC,YAAY,CAAC,uBAAY,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;aAC5C;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,0BAAW,GAAX,UAAY,KAAK;QACf,IAAM,QAAQ,GAAG,KAAK;aACnB,GAAG,CAAC,UAAC,IAAI;YACR,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,CAAC;QACZ,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC5B,OAAO,EAAE,CAAC;SACX;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACH,6BAAc,GAAd;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,uBAAQ,GAAR,UAAS,KAAa;QACpB,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,8CAA8C;QAC9C,IAAI,WAAW,KAAK,CAAC,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;QACD,IAAM,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACnE,OAAO,KAAK;YACV,CAAC,CAAC;gBACE,CAAC,EAAE,KAAK,CAAC,CAAC;gBACV,CAAC,EAAE,KAAK,CAAC,CAAC;aACX;YACH,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IACH,WAAC;AAAD,CAAC,AA9ED,CAAmB,cAAS,GA8E3B;AAED,kBAAe,IAAI,CAAC"}