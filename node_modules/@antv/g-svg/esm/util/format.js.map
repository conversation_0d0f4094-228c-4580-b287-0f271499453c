{"version": 3, "file": "format.js", "sourceRoot": "", "sources": ["../../src/util/format.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAErD,IAAM,SAAS,GAAG,gCAAgC,CAAC;AACnD,IAAM,QAAQ,GAAG,WAAW,CAAC;AAE7B,MAAM,UAAU,WAAW,CAAC,MAAM;IAChC,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;QACnB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SAC/B;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACpB,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SACrB;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACf,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACpB,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SAChB;aAAM;YACL,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACf,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACf,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACf,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SAChB;KACF;SAAM;QACL,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC;KAC5B;IACD,OAAO;QACL,EAAE,IAAA;QACF,EAAE,IAAA;QACF,EAAE,IAAA;QACF,EAAE,IAAA;KACH,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,IAAI;IAC5B,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;IAClB,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QACjB,OAAO,IAAI,CAAC;KACb;IAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,EAAE,UAAC,IAAI,EAAE,KAAK;YACrB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC5B,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;aACf;YACD,IAAI,CAAC,IAAI,EAAE,UAAC,GAAG,EAAE,CAAC;gBAChB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACf,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;iBAChB;YACH,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;KACb;AACH,CAAC"}