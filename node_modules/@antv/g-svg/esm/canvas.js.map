{"version": 3, "file": "canvas.js", "sourceRoot": "", "sources": ["../src/canvas.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,cAAc,EAAU,MAAM,cAAc,CAAC;AAGtD,OAAO,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACnD,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AACvD,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AACjC,OAAO,KAAK,MAAM,SAAS,CAAC;AAC5B,OAAO,IAAI,MAAM,QAAQ,CAAC;AAE1B;IAAqB,0BAAc;IACjC,gBAAY,GAAG;eACb,wCACK,GAAG,KACN,QAAQ,EAAE,IAAI;YACd,sBAAsB;YACtB,QAAQ,EAAE,KAAK,IACf;IACJ,CAAC;IAED,6BAAY,GAAZ;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,6BAAY,GAAZ;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,yDAAyD;IACzD,yBAAQ,GAAR,UAAS,CAAS,EAAE,CAAS,EAAE,EAAS;QACtC,IAAI,MAAM,GAAY,EAAE,CAAC,MAAM,IAAa,EAAE,CAAC,UAAU,CAAC;QAC1D,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YAClC,IAAI,QAAM,GAAY,MAAM,CAAC,UAAU,CAAC;YACxC,OAAO,QAAM,IAAI,CAAC,aAAa,CAAC,QAAM,CAAC,OAAO,CAAC,EAAE;gBAC/C,QAAM,GAAY,QAAM,CAAC,UAAU,CAAC;aACrC;YACD,MAAM,GAAG,QAAM,CAAC;SACjB;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,MAAM,EAA1B,CAA0B,CAAW,CAAC;IACpE,CAAC;IAED,cAAc;IACd,0BAAS,GAAT;QACE,IAAM,OAAO,GAAG,gBAAgB,CAAC,KAAK,CAAkB,CAAC;QACzD,IAAM,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;QAClC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAG,CAAC,CAAC;QACtD,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAG,CAAC,CAAC;QACxD,gBAAgB;QAChB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC7B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,+BAAc,GAAd,UAAe,UAAsB;QACnC,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,UAAU,KAAK,MAAM,EAAE;YACzB,IAAM,UAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACtC,IAAI,UAAQ,IAAI,UAAQ,CAAC,MAAM,EAAE;gBAC/B,OAAO,CAAC,IAAI,EAAE,UAAC,CAAW,EAAE,CAAW;oBACrC,OAAO,UAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,UAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,CAAC,CAAC,CAAC;aACJ;SACF;aAAM,IAAI,UAAU,KAAK,OAAO,EAAE;YACjC,2BAA2B;YAC3B,IAAI,EAAE,EAAE;gBACN,YAAY;gBACZ,EAAE,CAAC,SAAS,GAAG,EAAE,CAAC;gBAClB,IAAM,MAAM,GAAG,OAAO,CAAC,EAAE,CAAC;gBAC1B,aAAa;gBACb,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC;gBACtB,wBAAwB;gBACxB,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;aACxB;SACF;aAAM,IAAI,UAAU,KAAK,QAAQ,EAAE;YAClC,YAAY,CAAC,IAAI,CAAC,CAAC;SACpB;aAAM,IAAI,UAAU,KAAK,MAAM,EAAE;YAChC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACxB;aAAM,IAAI,UAAU,KAAK,YAAY,EAAE;YACtC,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,KAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAG,CAAC,CAAC;YACjD,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAG,CAAC,CAAC;SACpD;IACH,CAAC;IAED,gBAAgB;IAChB,qBAAI,GAAJ;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAgB,CAAC;QAClD,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACvB,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;SACjC;IACH,CAAC;IACH,aAAC;AAAD,CAAC,AAtFD,CAAqB,cAAc,GAsFlC;AAED,eAAe,MAAM,CAAC"}