{"version": 3, "file": "image.js", "sourceRoot": "", "sources": ["../../src/shape/image.ts"], "names": [], "mappings": "AAAA;;;GAGG;;AAEH,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAC3C,OAAO,SAAS,MAAM,QAAQ,CAAC;AAE/B;IAAoB,yBAAS;IAA7B;QAAA,qEAqEC;QApEC,UAAI,GAAW,OAAO,CAAC;QACvB,aAAO,GAAY,KAAK,CAAC;QACzB,eAAS,GAAY,KAAK,CAAC;;IAkE7B,CAAC;IAhEC,+BAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,6BACK,KAAK,KACR,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,KAAK,EAAE,CAAC,EACR,MAAM,EAAE,CAAC,IACT;IACJ,CAAC;IAED,0BAAU,GAAV,UAAW,OAAO,EAAE,WAAW;QAA/B,iBAUC;QATC,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,WAAW,IAAI,KAAK,EAAE,UAAC,KAAK,EAAE,IAAI;YACrC,IAAI,IAAI,KAAK,KAAK,EAAE;gBAClB,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC3B;iBAAM,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;gBAC7B,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;aAC5C;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,uBAAO,GAAP,UAAQ,IAAY,EAAE,KAAU;QAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACzB,IAAI,IAAI,KAAK,KAAK,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACvB;IACH,CAAC;IAED,yBAAS,GAAT,UAAU,GAAG;QACX,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACjB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SAC9B;aAAM,IAAI,GAAG,YAAa,MAAc,CAAC,KAAK,EAAE;YAC/C,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBAChB,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBACpC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;aAC/B;YACD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACjB,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;gBACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;aACjC;YACD,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;SAClC;aAAM,IAAI,GAAG,YAAY,WAAW,IAAI,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE;YAC1G,aAAa;YACb,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;SAC1C;aAAM,IAAI,GAAG,YAAY,SAAS,EAAE;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,KAAG,GAAG,CAAC,KAAO,CAAC,CAAC;YAC7C,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAG,GAAG,CAAC,MAAQ,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBAChB,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,KAAG,GAAG,CAAC,KAAO,CAAC,CAAC;gBACzC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;aAC/B;YACD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACjB,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAG,GAAG,CAAC,MAAQ,CAAC,CAAC;gBAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;aACjC;YACD,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;SAC7C;IACH,CAAC;IACH,YAAC;AAAD,CAAC,AArED,CAAoB,SAAS,GAqE5B;AAED,eAAe,KAAK,CAAC"}