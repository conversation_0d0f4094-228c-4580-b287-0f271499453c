{"version": 3, "file": "line.js", "sourceRoot": "", "sources": ["../../src/shape/line.ts"], "names": [], "mappings": ";AAAA;;;GAGG;AACH,OAAO,EAAE,IAAI,IAAI,QAAQ,EAAE,MAAM,cAAc,CAAC;AAChD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAC3C,OAAO,SAAS,MAAM,QAAQ,CAAC;AAE/B;IAAmB,wBAAS;IAA5B;QAAA,qEAuDC;QAtDC,UAAI,GAAW,MAAM,CAAC;QACtB,aAAO,GAAY,KAAK,CAAC;QACzB,eAAS,GAAY,IAAI,CAAC;;IAoD5B,CAAC;IAlDC,8BAAe,GAAf;QACE,IAAM,KAAK,GAAG,iBAAM,eAAe,WAAE,CAAC;QACtC,6BACK,KAAK,KACR,EAAE,EAAE,CAAC,EACL,EAAE,EAAE,CAAC,EACL,EAAE,EAAE,CAAC,EACL,EAAE,EAAE,CAAC,EACL,UAAU,EAAE,KAAK,EACjB,QAAQ,EAAE,KAAK,IACf;IACJ,CAAC;IAED,yBAAU,GAAV,UAAW,OAAO,EAAE,WAAW;QAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,WAAW,IAAI,KAAK,EAAE,UAAC,KAAK,EAAE,IAAI;YACrC,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,UAAU,EAAE;gBAChD,IAAI,KAAK,EAAE;oBACT,IAAM,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC;wBACxB,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;wBAC7C,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvD,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,UAAQ,EAAE,MAAG,CAAC,CAAC;iBACpD;qBAAM;oBACL,EAAE,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;iBACxC;aACF;iBAAM,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;gBAC7B,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;aAC5C;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,6BAAc,GAAd;QACQ,IAAA,KAAqB,IAAI,CAAC,IAAI,EAAE,EAA9B,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAgB,CAAC;QACvC,OAAO,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,uBAAQ,GAAR,UAAS,KAAa;QACd,IAAA,KAAqB,IAAI,CAAC,IAAI,EAAE,EAA9B,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAgB,CAAC;QACvC,OAAO,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IACH,WAAC;AAAD,CAAC,AAvDD,CAAmB,SAAS,GAuD3B;AAED,eAAe,IAAI,CAAC"}