!function(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.G=n():t.G=n()}(window,(function(){return function(t){var n={};function e(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,e),i.l=!0,i.exports}return e.m=t,e.c=n,e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:r})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,n){if(1&n&&(t=e(t)),8&n)return t;if(4&n&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(e.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&n&&"string"!=typeof t)for(var i in t)e.d(r,i,function(n){return t[n]}.bind(null,i));return r},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},e.p="",e(e.s=40)}([function(t,n,e){"use strict";e.d(n,"g",(function(){return y})),e.d(n,"j",(function(){return h})),e.d(n,"k",(function(){return p})),e.d(n,"m",(function(){return m})),e.d(n,"o",(function(){return x})),e.d(n,"q",(function(){return _})),e.d(n,"b",(function(){return c})),e.d(n,"d",(function(){return a})),e.d(n,"e",(function(){return u})),e.d(n,"f",(function(){return g})),e.d(n,"h",(function(){return s})),e.d(n,"i",(function(){return v})),e.d(n,"a",(function(){return f})),e.d(n,"c",(function(){return P})),e.d(n,"l",(function(){return O})),e.d(n,"p",(function(){return j})),e.d(n,"n",(function(){return T}));var r=function(t){return null!==t&&"function"!=typeof t&&isFinite(t.length)},i={}.toString,o=function(t,n){return i.call(t)==="[object "+n+"]"},a=function(t){return o(t,"Function")},u=function(t){return null==t},c=function(t){return Array.isArray?Array.isArray(t):o(t,"Array")},s=function(t){var n=typeof t;return null!==t&&"object"===n||"function"===n};var f=function(t,n){if(t)if(c(t))for(var e=0,r=t.length;e<r&&!1!==n(t[e],e);e++);else if(s(t))for(var i in t)if(t.hasOwnProperty(i)&&!1===n(t[i],i))break};Object.keys;var l=function(t){return"object"==typeof t&&null!==t};var h=function(t){if(c(t))return t.reduce((function(t,n){return Math.max(t,n)}),t[0])},p=function(t){if(c(t))return t.reduce((function(t,n){return Math.min(t,n)}),t[0])},d=Array.prototype,v=(d.splice,d.indexOf,Array.prototype.splice,function(t){return o(t,"String")});Object.prototype.hasOwnProperty;var g=function(t){return o(t,"Number")};Number.isInteger&&Number.isInteger;function y(t,n,e){return void 0===e&&(e=1e-5),Math.abs(t-n)<e}var m=function(t,n){return(t%n+n)%n},b=(Math.PI,parseInt,Math.PI/180),x=function(t){return b*t},M=Object.values?function(t){return Object.values(t)}:function(t){var n=[];return f(t,(function(e,r){a(t)&&"prototype"===r||n.push(e)})),n},w=function(t){return u(t)?"":t.toString()};var _=function(t){var n=w(t);return n.charAt(0).toUpperCase()+n.substring(1)};Object.prototype;function A(t,n){for(var e in n)n.hasOwnProperty(e)&&"constructor"!==e&&void 0!==n[e]&&(t[e]=n[e])}function O(t,n,e,r){return n&&A(t,n),e&&A(t,e),r&&A(t,r),t}Object.prototype.hasOwnProperty;var S=function(t,n){if(t===n)return!0;if(!t||!n)return!1;if(v(t)||v(n))return!1;if(r(t)||r(n)){if(t.length!==n.length)return!1;for(var e=!0,i=0;i<t.length&&(e=S(t[i],n[i]));i++);return e}if(l(t)||l(n)){var o=Object.keys(t),a=Object.keys(n);if(o.length!==a.length)return!1;for(e=!0,i=0;i<o.length&&(e=S(t[o[i]],n[o[i]]));i++);return e}return!1},P=S,C=(Object.prototype.hasOwnProperty,{}),j=function(t){return C[t=t||"g"]?C[t]+=1:C[t]=1,t+C[t]},T=function(){};var k,E=e(6);(function(t,n){if(!a(t))throw new TypeError("Expected a function");var e=function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var o=n?n.apply(this,r):r[0],a=e.cache;if(a.has(o))return a.get(o);var u=t.apply(this,r);return a.set(o,u),u};e.cache=new Map})((function(t,n){void 0===n&&(n={});var e=n.fontSize,r=n.fontFamily,i=n.fontWeight,o=n.fontStyle,a=n.fontVariant;return k||(k=document.createElement("canvas").getContext("2d")),k.font=[o,a,i,e+"px",r].join(" "),k.measureText(v(t)?t:"").width}),(function(t,n){return void 0===n&&(n={}),Object(E.b)([t],M(n)).join("")})),function(){function t(){this.map={}}t.prototype.has=function(t){return void 0!==this.map[t]},t.prototype.get=function(t,n){var e=this.map[t];return void 0===e?n:e},t.prototype.set=function(t,n){this.map[t]=n},t.prototype.clear=function(){this.map={}},t.prototype.delete=function(t){delete this.map[t]},t.prototype.size=function(){return Object.keys(this.map).length}}()},function(t,n,e){"use strict";e.d(n,"j",(function(){return i})),e.d(n,"c",(function(){return o})),e.d(n,"g",(function(){return a})),e.d(n,"b",(function(){return u}));var r=e(0);function i(t,n){var e=t.indexOf(n);-1!==e&&t.splice(e,1)}e.d(n,"e",(function(){return r.e})),e.d(n,"d",(function(){return r.d})),e.d(n,"h",(function(){return r.i})),e.d(n,"f",(function(){return r.h})),e.d(n,"i",(function(){return r.l})),e.d(n,"a",(function(){return r.a})),e.d(n,"k",(function(){return r.q}));var o="undefined"!=typeof window&&void 0!==window.document;function a(t,n){if(t.isCanvas())return!0;for(var e=n.getParent(),r=!1;e;){if(e===t){r=!0;break}e=e.getParent()}return r}function u(t){return t.cfg.visible&&t.cfg.capture}},function(t,n,e){"use strict";e.d(n,"a",(function(){return r})),e.d(n,"b",(function(){return i}));var r={};e.r(r),e.d(r,"create",(function(){return u})),e.d(r,"fromMat4",(function(){return c})),e.d(r,"clone",(function(){return s})),e.d(r,"copy",(function(){return f})),e.d(r,"fromValues",(function(){return l})),e.d(r,"set",(function(){return h})),e.d(r,"identity",(function(){return p})),e.d(r,"transpose",(function(){return d})),e.d(r,"invert",(function(){return v})),e.d(r,"adjoint",(function(){return g})),e.d(r,"determinant",(function(){return y})),e.d(r,"multiply",(function(){return m})),e.d(r,"translate",(function(){return b})),e.d(r,"rotate",(function(){return x})),e.d(r,"scale",(function(){return M})),e.d(r,"fromTranslation",(function(){return w})),e.d(r,"fromRotation",(function(){return _})),e.d(r,"fromScaling",(function(){return A})),e.d(r,"fromMat2d",(function(){return O})),e.d(r,"fromQuat",(function(){return S})),e.d(r,"normalFromMat4",(function(){return P})),e.d(r,"projection",(function(){return C})),e.d(r,"str",(function(){return j})),e.d(r,"frob",(function(){return T})),e.d(r,"add",(function(){return k})),e.d(r,"subtract",(function(){return E})),e.d(r,"multiplyScalar",(function(){return B})),e.d(r,"multiplyScalarAndAdd",(function(){return I})),e.d(r,"exactEquals",(function(){return D})),e.d(r,"equals",(function(){return N})),e.d(r,"mul",(function(){return F})),e.d(r,"sub",(function(){return L}));var i={};e.r(i),e.d(i,"create",(function(){return R})),e.d(i,"clone",(function(){return G})),e.d(i,"fromValues",(function(){return V})),e.d(i,"copy",(function(){return W})),e.d(i,"set",(function(){return q})),e.d(i,"add",(function(){return X})),e.d(i,"subtract",(function(){return $})),e.d(i,"multiply",(function(){return Y})),e.d(i,"divide",(function(){return z})),e.d(i,"ceil",(function(){return H})),e.d(i,"floor",(function(){return Q})),e.d(i,"min",(function(){return U})),e.d(i,"max",(function(){return Z})),e.d(i,"round",(function(){return K})),e.d(i,"scale",(function(){return J})),e.d(i,"scaleAndAdd",(function(){return tt})),e.d(i,"distance",(function(){return nt})),e.d(i,"squaredDistance",(function(){return et})),e.d(i,"length",(function(){return rt})),e.d(i,"squaredLength",(function(){return it})),e.d(i,"negate",(function(){return ot})),e.d(i,"inverse",(function(){return at})),e.d(i,"normalize",(function(){return ut})),e.d(i,"dot",(function(){return ct})),e.d(i,"cross",(function(){return st})),e.d(i,"lerp",(function(){return ft})),e.d(i,"random",(function(){return lt})),e.d(i,"transformMat2",(function(){return ht})),e.d(i,"transformMat2d",(function(){return pt})),e.d(i,"transformMat3",(function(){return dt})),e.d(i,"transformMat4",(function(){return vt})),e.d(i,"rotate",(function(){return gt})),e.d(i,"angle",(function(){return yt})),e.d(i,"zero",(function(){return mt})),e.d(i,"str",(function(){return bt})),e.d(i,"exactEquals",(function(){return xt})),e.d(i,"equals",(function(){return Mt})),e.d(i,"len",(function(){return _t})),e.d(i,"sub",(function(){return At})),e.d(i,"mul",(function(){return Ot})),e.d(i,"div",(function(){return St})),e.d(i,"dist",(function(){return Pt})),e.d(i,"sqrDist",(function(){return Ct})),e.d(i,"sqrLen",(function(){return jt})),e.d(i,"forEach",(function(){return Tt}));var o="undefined"!=typeof Float32Array?Float32Array:Array,a=Math.random;Math.PI;function u(){var t=new o(9);return o!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[5]=0,t[6]=0,t[7]=0),t[0]=1,t[4]=1,t[8]=1,t}function c(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[4],t[4]=n[5],t[5]=n[6],t[6]=n[8],t[7]=n[9],t[8]=n[10],t}function s(t){var n=new o(9);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n}function f(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t}function l(t,n,e,r,i,a,u,c,s){var f=new o(9);return f[0]=t,f[1]=n,f[2]=e,f[3]=r,f[4]=i,f[5]=a,f[6]=u,f[7]=c,f[8]=s,f}function h(t,n,e,r,i,o,a,u,c,s){return t[0]=n,t[1]=e,t[2]=r,t[3]=i,t[4]=o,t[5]=a,t[6]=u,t[7]=c,t[8]=s,t}function p(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function d(t,n){if(t===n){var e=n[1],r=n[2],i=n[5];t[1]=n[3],t[2]=n[6],t[3]=e,t[5]=n[7],t[6]=r,t[7]=i}else t[0]=n[0],t[1]=n[3],t[2]=n[6],t[3]=n[1],t[4]=n[4],t[5]=n[7],t[6]=n[2],t[7]=n[5],t[8]=n[8];return t}function v(t,n){var e=n[0],r=n[1],i=n[2],o=n[3],a=n[4],u=n[5],c=n[6],s=n[7],f=n[8],l=f*a-u*s,h=-f*o+u*c,p=s*o-a*c,d=e*l+r*h+i*p;return d?(d=1/d,t[0]=l*d,t[1]=(-f*r+i*s)*d,t[2]=(u*r-i*a)*d,t[3]=h*d,t[4]=(f*e-i*c)*d,t[5]=(-u*e+i*o)*d,t[6]=p*d,t[7]=(-s*e+r*c)*d,t[8]=(a*e-r*o)*d,t):null}function g(t,n){var e=n[0],r=n[1],i=n[2],o=n[3],a=n[4],u=n[5],c=n[6],s=n[7],f=n[8];return t[0]=a*f-u*s,t[1]=i*s-r*f,t[2]=r*u-i*a,t[3]=u*c-o*f,t[4]=e*f-i*c,t[5]=i*o-e*u,t[6]=o*s-a*c,t[7]=r*c-e*s,t[8]=e*a-r*o,t}function y(t){var n=t[0],e=t[1],r=t[2],i=t[3],o=t[4],a=t[5],u=t[6],c=t[7],s=t[8];return n*(s*o-a*c)+e*(-s*i+a*u)+r*(c*i-o*u)}function m(t,n,e){var r=n[0],i=n[1],o=n[2],a=n[3],u=n[4],c=n[5],s=n[6],f=n[7],l=n[8],h=e[0],p=e[1],d=e[2],v=e[3],g=e[4],y=e[5],m=e[6],b=e[7],x=e[8];return t[0]=h*r+p*a+d*s,t[1]=h*i+p*u+d*f,t[2]=h*o+p*c+d*l,t[3]=v*r+g*a+y*s,t[4]=v*i+g*u+y*f,t[5]=v*o+g*c+y*l,t[6]=m*r+b*a+x*s,t[7]=m*i+b*u+x*f,t[8]=m*o+b*c+x*l,t}function b(t,n,e){var r=n[0],i=n[1],o=n[2],a=n[3],u=n[4],c=n[5],s=n[6],f=n[7],l=n[8],h=e[0],p=e[1];return t[0]=r,t[1]=i,t[2]=o,t[3]=a,t[4]=u,t[5]=c,t[6]=h*r+p*a+s,t[7]=h*i+p*u+f,t[8]=h*o+p*c+l,t}function x(t,n,e){var r=n[0],i=n[1],o=n[2],a=n[3],u=n[4],c=n[5],s=n[6],f=n[7],l=n[8],h=Math.sin(e),p=Math.cos(e);return t[0]=p*r+h*a,t[1]=p*i+h*u,t[2]=p*o+h*c,t[3]=p*a-h*r,t[4]=p*u-h*i,t[5]=p*c-h*o,t[6]=s,t[7]=f,t[8]=l,t}function M(t,n,e){var r=e[0],i=e[1];return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=i*n[3],t[4]=i*n[4],t[5]=i*n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t}function w(t,n){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=n[0],t[7]=n[1],t[8]=1,t}function _(t,n){var e=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=e,t[2]=0,t[3]=-e,t[4]=r,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function A(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=0,t[4]=n[1],t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function O(t,n){return t[0]=n[0],t[1]=n[1],t[2]=0,t[3]=n[2],t[4]=n[3],t[5]=0,t[6]=n[4],t[7]=n[5],t[8]=1,t}function S(t,n){var e=n[0],r=n[1],i=n[2],o=n[3],a=e+e,u=r+r,c=i+i,s=e*a,f=r*a,l=r*u,h=i*a,p=i*u,d=i*c,v=o*a,g=o*u,y=o*c;return t[0]=1-l-d,t[3]=f-y,t[6]=h+g,t[1]=f+y,t[4]=1-s-d,t[7]=p-v,t[2]=h-g,t[5]=p+v,t[8]=1-s-l,t}function P(t,n){var e=n[0],r=n[1],i=n[2],o=n[3],a=n[4],u=n[5],c=n[6],s=n[7],f=n[8],l=n[9],h=n[10],p=n[11],d=n[12],v=n[13],g=n[14],y=n[15],m=e*u-r*a,b=e*c-i*a,x=e*s-o*a,M=r*c-i*u,w=r*s-o*u,_=i*s-o*c,A=f*v-l*d,O=f*g-h*d,S=f*y-p*d,P=l*g-h*v,C=l*y-p*v,j=h*y-p*g,T=m*j-b*C+x*P+M*S-w*O+_*A;return T?(T=1/T,t[0]=(u*j-c*C+s*P)*T,t[1]=(c*S-a*j-s*O)*T,t[2]=(a*C-u*S+s*A)*T,t[3]=(i*C-r*j-o*P)*T,t[4]=(e*j-i*S+o*O)*T,t[5]=(r*S-e*C-o*A)*T,t[6]=(v*_-g*w+y*M)*T,t[7]=(g*x-d*_-y*b)*T,t[8]=(d*w-v*x+y*m)*T,t):null}function C(t,n,e){return t[0]=2/n,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/e,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t}function j(t){return"mat3("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+")"}function T(t){return Math.hypot(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8])}function k(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t[2]=n[2]+e[2],t[3]=n[3]+e[3],t[4]=n[4]+e[4],t[5]=n[5]+e[5],t[6]=n[6]+e[6],t[7]=n[7]+e[7],t[8]=n[8]+e[8],t}function E(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t[4]=n[4]-e[4],t[5]=n[5]-e[5],t[6]=n[6]-e[6],t[7]=n[7]-e[7],t[8]=n[8]-e[8],t}function B(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t[2]=n[2]*e,t[3]=n[3]*e,t[4]=n[4]*e,t[5]=n[5]*e,t[6]=n[6]*e,t[7]=n[7]*e,t[8]=n[8]*e,t}function I(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t[2]=n[2]+e[2]*r,t[3]=n[3]+e[3]*r,t[4]=n[4]+e[4]*r,t[5]=n[5]+e[5]*r,t[6]=n[6]+e[6]*r,t[7]=n[7]+e[7]*r,t[8]=n[8]+e[8]*r,t}function D(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]&&t[8]===n[8]}function N(t,n){var e=t[0],r=t[1],i=t[2],o=t[3],a=t[4],u=t[5],c=t[6],s=t[7],f=t[8],l=n[0],h=n[1],p=n[2],d=n[3],v=n[4],g=n[5],y=n[6],m=n[7],b=n[8];return Math.abs(e-l)<=1e-6*Math.max(1,Math.abs(e),Math.abs(l))&&Math.abs(r-h)<=1e-6*Math.max(1,Math.abs(r),Math.abs(h))&&Math.abs(i-p)<=1e-6*Math.max(1,Math.abs(i),Math.abs(p))&&Math.abs(o-d)<=1e-6*Math.max(1,Math.abs(o),Math.abs(d))&&Math.abs(a-v)<=1e-6*Math.max(1,Math.abs(a),Math.abs(v))&&Math.abs(u-g)<=1e-6*Math.max(1,Math.abs(u),Math.abs(g))&&Math.abs(c-y)<=1e-6*Math.max(1,Math.abs(c),Math.abs(y))&&Math.abs(s-m)<=1e-6*Math.max(1,Math.abs(s),Math.abs(m))&&Math.abs(f-b)<=1e-6*Math.max(1,Math.abs(f),Math.abs(b))}Math.hypot||(Math.hypot=function(){for(var t=0,n=arguments.length;n--;)t+=arguments[n]*arguments[n];return Math.sqrt(t)});var F=m,L=E;function R(){var t=new o(2);return o!=Float32Array&&(t[0]=0,t[1]=0),t}function G(t){var n=new o(2);return n[0]=t[0],n[1]=t[1],n}function V(t,n){var e=new o(2);return e[0]=t,e[1]=n,e}function W(t,n){return t[0]=n[0],t[1]=n[1],t}function q(t,n,e){return t[0]=n,t[1]=e,t}function X(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t}function $(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t}function Y(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t}function z(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t}function H(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t}function Q(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t}function U(t,n,e){return t[0]=Math.min(n[0],e[0]),t[1]=Math.min(n[1],e[1]),t}function Z(t,n,e){return t[0]=Math.max(n[0],e[0]),t[1]=Math.max(n[1],e[1]),t}function K(t,n){return t[0]=Math.round(n[0]),t[1]=Math.round(n[1]),t}function J(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t}function tt(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t}function nt(t,n){var e=n[0]-t[0],r=n[1]-t[1];return Math.hypot(e,r)}function et(t,n){var e=n[0]-t[0],r=n[1]-t[1];return e*e+r*r}function rt(t){var n=t[0],e=t[1];return Math.hypot(n,e)}function it(t){var n=t[0],e=t[1];return n*n+e*e}function ot(t,n){return t[0]=-n[0],t[1]=-n[1],t}function at(t,n){return t[0]=1/n[0],t[1]=1/n[1],t}function ut(t,n){var e=n[0],r=n[1],i=e*e+r*r;return i>0&&(i=1/Math.sqrt(i)),t[0]=n[0]*i,t[1]=n[1]*i,t}function ct(t,n){return t[0]*n[0]+t[1]*n[1]}function st(t,n,e){var r=n[0]*e[1]-n[1]*e[0];return t[0]=t[1]=0,t[2]=r,t}function ft(t,n,e,r){var i=n[0],o=n[1];return t[0]=i+r*(e[0]-i),t[1]=o+r*(e[1]-o),t}function lt(t,n){n=n||1;var e=2*a()*Math.PI;return t[0]=Math.cos(e)*n,t[1]=Math.sin(e)*n,t}function ht(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[2]*i,t[1]=e[1]*r+e[3]*i,t}function pt(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[2]*i+e[4],t[1]=e[1]*r+e[3]*i+e[5],t}function dt(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[3]*i+e[6],t[1]=e[1]*r+e[4]*i+e[7],t}function vt(t,n,e){var r=n[0],i=n[1];return t[0]=e[0]*r+e[4]*i+e[12],t[1]=e[1]*r+e[5]*i+e[13],t}function gt(t,n,e,r){var i=n[0]-e[0],o=n[1]-e[1],a=Math.sin(r),u=Math.cos(r);return t[0]=i*u-o*a+e[0],t[1]=i*a+o*u+e[1],t}function yt(t,n){var e=t[0],r=t[1],i=n[0],o=n[1],a=Math.sqrt(e*e+r*r)*Math.sqrt(i*i+o*o),u=a&&(e*i+r*o)/a;return Math.acos(Math.min(Math.max(u,-1),1))}function mt(t){return t[0]=0,t[1]=0,t}function bt(t){return"vec2("+t[0]+", "+t[1]+")"}function xt(t,n){return t[0]===n[0]&&t[1]===n[1]}function Mt(t,n){var e=t[0],r=t[1],i=n[0],o=n[1];return Math.abs(e-i)<=1e-6*Math.max(1,Math.abs(e),Math.abs(i))&&Math.abs(r-o)<=1e-6*Math.max(1,Math.abs(r),Math.abs(o))}var wt,_t=rt,At=$,Ot=Y,St=z,Pt=nt,Ct=et,jt=it,Tt=(wt=R(),function(t,n,e,r,i,o){var a,u;for(n||(n=2),e||(e=0),u=r?Math.min(r*n+e,t.length):t.length,a=e;a<u;a+=n)wt[0]=t[a],wt[1]=t[a+1],i(wt,wt,o),t[a]=wt[0],t[a+1]=wt[1];return t})},function(t,n,e){"use strict";e.r(n),e.d(n,"contains",(function(){return i})),e.d(n,"includes",(function(){return i})),e.d(n,"difference",(function(){return a})),e.d(n,"find",(function(){return m})),e.d(n,"findIndex",(function(){return b})),e.d(n,"firstValue",(function(){return x})),e.d(n,"flatten",(function(){return M})),e.d(n,"flattenDeep",(function(){return _})),e.d(n,"getRange",(function(){return S})),e.d(n,"pull",(function(){return T})),e.d(n,"pullAt",(function(){return E})),e.d(n,"reduce",(function(){return B})),e.d(n,"remove",(function(){return I})),e.d(n,"sortBy",(function(){return N})),e.d(n,"union",(function(){return L})),e.d(n,"uniq",(function(){return F})),e.d(n,"valuesOfKey",(function(){return R})),e.d(n,"head",(function(){return G})),e.d(n,"last",(function(){return V})),e.d(n,"startsWith",(function(){return W})),e.d(n,"endsWith",(function(){return q})),e.d(n,"filter",(function(){return o})),e.d(n,"every",(function(){return X})),e.d(n,"some",(function(){return $})),e.d(n,"group",(function(){return Q})),e.d(n,"groupBy",(function(){return z})),e.d(n,"groupToMap",(function(){return H})),e.d(n,"getWrapBehavior",(function(){return U})),e.d(n,"wrapBehavior",(function(){return Z})),e.d(n,"number2color",(function(){return J})),e.d(n,"parseRadius",(function(){return tt})),e.d(n,"clamp",(function(){return nt})),e.d(n,"fixedBase",(function(){return et})),e.d(n,"isDecimal",(function(){return it})),e.d(n,"isEven",(function(){return ot})),e.d(n,"isInteger",(function(){return at})),e.d(n,"isNegative",(function(){return ut})),e.d(n,"isNumberEqual",(function(){return ct})),e.d(n,"isOdd",(function(){return st})),e.d(n,"isPositive",(function(){return ft})),e.d(n,"max",(function(){return A})),e.d(n,"maxBy",(function(){return lt})),e.d(n,"min",(function(){return O})),e.d(n,"minBy",(function(){return ht})),e.d(n,"mod",(function(){return pt})),e.d(n,"toDegree",(function(){return vt})),e.d(n,"toInteger",(function(){return gt})),e.d(n,"toRadian",(function(){return mt})),e.d(n,"forIn",(function(){return bt})),e.d(n,"has",(function(){return xt})),e.d(n,"hasKey",(function(){return Mt})),e.d(n,"hasValue",(function(){return _t})),e.d(n,"keys",(function(){return d})),e.d(n,"isMatch",(function(){return v})),e.d(n,"values",(function(){return wt})),e.d(n,"lowerCase",(function(){return Ot})),e.d(n,"lowerFirst",(function(){return St})),e.d(n,"substitute",(function(){return Pt})),e.d(n,"upperCase",(function(){return Ct})),e.d(n,"upperFirst",(function(){return jt})),e.d(n,"getType",(function(){return kt})),e.d(n,"isArguments",(function(){return Et})),e.d(n,"isArray",(function(){return l})),e.d(n,"isArrayLike",(function(){return r})),e.d(n,"isBoolean",(function(){return Bt})),e.d(n,"isDate",(function(){return It})),e.d(n,"isError",(function(){return Dt})),e.d(n,"isFunction",(function(){return s})),e.d(n,"isFinite",(function(){return Nt})),e.d(n,"isNil",(function(){return f})),e.d(n,"isNull",(function(){return Ft})),e.d(n,"isNumber",(function(){return rt})),e.d(n,"isObject",(function(){return h})),e.d(n,"isObjectLike",(function(){return g})),e.d(n,"isPlainObject",(function(){return y})),e.d(n,"isPrototype",(function(){return Rt})),e.d(n,"isRegExp",(function(){return Gt})),e.d(n,"isString",(function(){return D})),e.d(n,"isType",(function(){return c})),e.d(n,"isUndefined",(function(){return Vt})),e.d(n,"isElement",(function(){return Wt})),e.d(n,"requestAnimationFrame",(function(){return qt})),e.d(n,"clearAnimationFrame",(function(){return Xt})),e.d(n,"augment",(function(){return zt})),e.d(n,"clone",(function(){return Qt})),e.d(n,"debounce",(function(){return Ut})),e.d(n,"memoize",(function(){return Zt})),e.d(n,"deepMix",(function(){return Jt})),e.d(n,"each",(function(){return p})),e.d(n,"extend",(function(){return tn})),e.d(n,"indexOf",(function(){return nn})),e.d(n,"isEmpty",(function(){return rn})),e.d(n,"isEqual",(function(){return an})),e.d(n,"isEqualWith",(function(){return un})),e.d(n,"map",(function(){return cn})),e.d(n,"mapValues",(function(){return fn})),e.d(n,"mix",(function(){return Yt})),e.d(n,"assign",(function(){return Yt})),e.d(n,"get",(function(){return ln})),e.d(n,"set",(function(){return hn})),e.d(n,"pick",(function(){return dn})),e.d(n,"omit",(function(){return vn})),e.d(n,"throttle",(function(){return gn})),e.d(n,"toArray",(function(){return yn})),e.d(n,"toString",(function(){return At})),e.d(n,"uniqueId",(function(){return bn})),e.d(n,"noop",(function(){return xn})),e.d(n,"identity",(function(){return Mn})),e.d(n,"size",(function(){return wn})),e.d(n,"measureTextWidth",(function(){return On})),e.d(n,"getEllipsisText",(function(){return Sn})),e.d(n,"Cache",(function(){return Pn}));var r=function(t){return null!==t&&"function"!=typeof t&&isFinite(t.length)},i=function(t,n){return!!r(t)&&t.indexOf(n)>-1},o=function(t,n){if(!r(t))return t;for(var e=[],i=0;i<t.length;i++){var o=t[i];n(o,i)&&e.push(o)}return e},a=function(t,n){return void 0===n&&(n=[]),o(t,(function(t){return!i(n,t)}))},u={}.toString,c=function(t,n){return u.call(t)==="[object "+n+"]"},s=function(t){return c(t,"Function")},f=function(t){return null==t},l=function(t){return Array.isArray?Array.isArray(t):c(t,"Array")},h=function(t){var n=typeof t;return null!==t&&"object"===n||"function"===n};var p=function(t,n){if(t)if(l(t))for(var e=0,r=t.length;e<r&&!1!==n(t[e],e);e++);else if(h(t))for(var i in t)if(t.hasOwnProperty(i)&&!1===n(t[i],i))break},d=Object.keys?function(t){return Object.keys(t)}:function(t){var n=[];return p(t,(function(e,r){s(t)&&"prototype"===r||n.push(r)})),n};var v=function(t,n){var e=d(n),r=e.length;if(f(t))return!r;for(var i=0;i<r;i+=1){var o=e[i];if(n[o]!==t[o]||!(o in t))return!1}return!0},g=function(t){return"object"==typeof t&&null!==t},y=function(t){if(!g(t)||!c(t,"Object"))return!1;if(null===Object.getPrototypeOf(t))return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return Object.getPrototypeOf(t)===n};var m=function(t,n){if(!l(t))return null;var e;if(s(n)&&(e=n),y(n)&&(e=function(t){return v(t,n)}),e)for(var r=0;r<t.length;r+=1)if(e(t[r]))return t[r];return null};var b=function(t,n,e){void 0===e&&(e=0);for(var r=e;r<t.length;r++)if(n(t[r],r))return r;return-1},x=function(t,n){for(var e=null,r=0;r<t.length;r++){var i=t[r][n];if(!f(i)){e=l(i)?i[0]:i;break}}return e},M=function(t){if(!l(t))return[];for(var n=[],e=0;e<t.length;e++)n=n.concat(t[e]);return n},w=function(t,n){if(void 0===n&&(n=[]),l(t))for(var e=0;e<t.length;e+=1)w(t[e],n);else n.push(t);return n},_=w,A=function(t){if(l(t))return t.reduce((function(t,n){return Math.max(t,n)}),t[0])},O=function(t){if(l(t))return t.reduce((function(t,n){return Math.min(t,n)}),t[0])},S=function(t){var n=t.filter((function(t){return!isNaN(t)}));if(!n.length)return{min:0,max:0};if(l(t[0])){for(var e=[],r=0;r<t.length;r++)e=e.concat(t[r]);n=e}var i=A(n);return{min:O(n),max:i}},P=Array.prototype,C=P.splice,j=P.indexOf,T=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];for(var r=0;r<n.length;r++)for(var i=n[r],o=-1;(o=j.call(t,i))>-1;)C.call(t,o,1);return t},k=Array.prototype.splice,E=function(t,n){if(!r(t))return[];for(var e=t?n.length:0,i=e-1;e--;){var o=void 0,a=n[e];e!==i&&a===o||(o=a,k.call(t,a,1))}return t},B=function(t,n,e){if(!l(t)&&!y(t))return t;var r=e;return p(t,(function(t,e){r=n(r,t,e)})),r},I=function(t,n){var e=[];if(!r(t))return e;for(var i=-1,o=[],a=t.length;++i<a;){var u=t[i];n(u,i,t)&&(e.push(u),o.push(i))}return E(t,o),e},D=function(t){return c(t,"String")};var N=function(t,n){var e;if(s(n))e=function(t,e){return n(t)-n(e)};else{var r=[];D(n)?r.push(n):l(n)&&(r=n),e=function(t,n){for(var e=0;e<r.length;e+=1){var i=r[e];if(t[i]>n[i])return 1;if(t[i]<n[i])return-1}return 0}}return t.sort(e),t};function F(t,n){void 0===n&&(n=new Map);var e=[];if(Array.isArray(t))for(var r=0,i=t.length;r<i;r++){var o=t[r];n.has(o)||(e.push(o),n.set(o,!0))}return e}var L=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return F([].concat.apply([],t))},R=function(t,n){for(var e=[],r={},i=0;i<t.length;i++){var o=t[i][n];if(!f(o)){l(o)||(o=[o]);for(var a=0;a<o.length;a++){var u=o[a];r[u]||(e.push(u),r[u]=!0)}}}return e};function G(t){if(r(t))return t[0]}function V(t){if(r(t)){return t[t.length-1]}}var W=function(t,n){return!(!l(t)&&!D(t))&&t[0]===n};var q=function(t,n){return!(!l(t)&&!D(t))&&t[t.length-1]===n},X=function(t,n){for(var e=0;e<t.length;e++)if(!n(t[e],e))return!1;return!0},$=function(t,n){for(var e=0;e<t.length;e++)if(n(t[e],e))return!0;return!1},Y=Object.prototype.hasOwnProperty;var z=function(t,n){if(!n||!l(t))return{};for(var e,r={},i=s(n)?n:function(t){return t[n]},o=0;o<t.length;o++){var a=t[o];e=i(a),Y.call(r,e)?r[e].push(a):r[e]=[a]}return r};function H(t,n){if(!n)return{0:t};if(!s(n)){var e=l(n)?n:n.replace(/\s+/g,"").split("*");n=function(t){for(var n="_",r=0,i=e.length;r<i;r++)n+=t[e[r]]&&t[e[r]].toString();return n}}return z(t,n)}var Q=function(t,n){if(!n)return[t];var e=H(t,n),r=[];for(var i in e)r.push(e[i]);return r};var U=function(t,n){return t["_wrap_"+n]};var Z=function(t,n){if(t["_wrap_"+n])return t["_wrap_"+n];var e=function(e){t[n](e)};return t["_wrap_"+n]=e,e},K={};var J=function(t){var n=K[t];if(!n){for(var e=t.toString(16),r=e.length;r<6;r++)e="0"+e;n="#"+e,K[t]=n}return n};var tt=function(t){var n=0,e=0,r=0,i=0;return l(t)?1===t.length?n=e=r=i=t[0]:2===t.length?(n=r=t[0],e=i=t[1]):3===t.length?(n=t[0],e=i=t[1],r=t[2]):(n=t[0],e=t[1],r=t[2],i=t[3]):n=e=r=i=t,{r1:n,r2:e,r3:r,r4:i}},nt=function(t,n,e){return t<n?n:t>e?e:t},et=function(t,n){var e=n.toString(),r=e.indexOf(".");if(-1===r)return Math.round(t);var i=e.substr(r+1).length;return i>20&&(i=20),parseFloat(t.toFixed(i))},rt=function(t){return c(t,"Number")},it=function(t){return rt(t)&&t%1!=0},ot=function(t){return rt(t)&&t%2==0},at=Number.isInteger?Number.isInteger:function(t){return rt(t)&&t%1==0},ut=function(t){return rt(t)&&t<0};function ct(t,n,e){return void 0===e&&(e=1e-5),Math.abs(t-n)<e}var st=function(t){return rt(t)&&t%2!=0},ft=function(t){return rt(t)&&t>0},lt=function(t,n){if(l(t)){for(var e,r=-1/0,i=0;i<t.length;i++){var o=t[i],a=s(n)?n(o):o[n];a>r&&(e=o,r=a)}return e}},ht=function(t,n){if(l(t)){for(var e,r=1/0,i=0;i<t.length;i++){var o=t[i],a=s(n)?n(o):o[n];a<r&&(e=o,r=a)}return e}},pt=function(t,n){return(t%n+n)%n},dt=180/Math.PI,vt=function(t){return dt*t},gt=parseInt,yt=Math.PI/180,mt=function(t){return yt*t},bt=p,xt=function(t,n){return t.hasOwnProperty(n)},Mt=xt,wt=Object.values?function(t){return Object.values(t)}:function(t){var n=[];return p(t,(function(e,r){s(t)&&"prototype"===r||n.push(e)})),n},_t=function(t,n){return i(wt(t),n)},At=function(t){return f(t)?"":t.toString()},Ot=function(t){return At(t).toLowerCase()},St=function(t){var n=At(t);return n.charAt(0).toLowerCase()+n.substring(1)};var Pt=function(t,n){return t&&n?t.replace(/\\?\{([^{}]+)\}/g,(function(t,e){return"\\"===t.charAt(0)?t.slice(1):void 0===n[e]?"":n[e]})):t},Ct=function(t){return At(t).toUpperCase()},jt=function(t){var n=At(t);return n.charAt(0).toUpperCase()+n.substring(1)},Tt={}.toString,kt=function(t){return Tt.call(t).replace(/^\[object /,"").replace(/]$/,"")},Et=function(t){return c(t,"Arguments")},Bt=function(t){return c(t,"Boolean")},It=function(t){return c(t,"Date")},Dt=function(t){return c(t,"Error")},Nt=function(t){return rt(t)&&isFinite(t)},Ft=function(t){return null===t},Lt=Object.prototype,Rt=function(t){var n=t&&t.constructor;return t===("function"==typeof n&&n.prototype||Lt)},Gt=function(t){return c(t,"RegExp")},Vt=function(t){return void 0===t},Wt=function(t){return t instanceof Element||t instanceof HTMLDocument};function qt(t){return(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||function(t){return setTimeout(t,16)})(t)}function Xt(t){(window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.msCancelAnimationFrame||clearTimeout)(t)}function $t(t,n){for(var e in n)n.hasOwnProperty(e)&&"constructor"!==e&&void 0!==n[e]&&(t[e]=n[e])}function Yt(t,n,e,r){return n&&$t(t,n),e&&$t(t,e),r&&$t(t,r),t}var zt=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var e=t[0],r=1;r<t.length;r++){var i=t[r];s(i)&&(i=i.prototype),Yt(e.prototype,i)}},Ht=function(t){if("object"!=typeof t||null===t)return t;var n;if(l(t)){n=[];for(var e=0,r=t.length;e<r;e++)"object"==typeof t[e]&&null!=t[e]?n[e]=Ht(t[e]):n[e]=t[e]}else for(var i in n={},t)"object"==typeof t[i]&&null!=t[i]?n[i]=Ht(t[i]):n[i]=t[i];return n},Qt=Ht;var Ut=function(t,n,e){var r;return function(){var i=this,o=arguments,a=function(){r=null,e||t.apply(i,o)},u=e&&!r;clearTimeout(r),r=setTimeout(a,n),u&&t.apply(i,o)}},Zt=function(t,n){if(!s(t))throw new TypeError("Expected a function");var e=function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var o=n?n.apply(this,r):r[0],a=e.cache;if(a.has(o))return a.get(o);var u=t.apply(this,r);return a.set(o,u),u};return e.cache=new Map,e};function Kt(t,n,e,r){for(var i in e=e||0,r=r||5,n)if(n.hasOwnProperty(i)){var o=n[i];null!==o&&y(o)?(y(t[i])||(t[i]={}),e<r?Kt(t[i],o,e+1,r):t[i]=n[i]):l(o)?(t[i]=[],t[i]=t[i].concat(o)):void 0!==o&&(t[i]=o)}}var Jt=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];for(var r=0;r<n.length;r+=1)Kt(t,n[r]);return t},tn=function(t,n,e,r){s(n)||(e=n,n=t,t=function(){});var i=Object.create?function(t,n){return Object.create(t,{constructor:{value:n}})}:function(t,n){function e(){}e.prototype=t;var r=new e;return r.constructor=n,r},o=i(n.prototype,t);return t.prototype=Yt(o,t.prototype),t.superclass=i(n.prototype,n),Yt(o,e),Yt(t,r),t},nn=function(t,n){if(!r(t))return-1;var e=Array.prototype.indexOf;if(e)return e.call(t,n);for(var i=-1,o=0;o<t.length;o++)if(t[o]===n){i=o;break}return i},en=Object.prototype.hasOwnProperty;var rn=function(t){if(f(t))return!0;if(r(t))return!t.length;var n=kt(t);if("Map"===n||"Set"===n)return!t.size;if(Rt(t))return!Object.keys(t).length;for(var e in t)if(en.call(t,e))return!1;return!0},on=function(t,n){if(t===n)return!0;if(!t||!n)return!1;if(D(t)||D(n))return!1;if(r(t)||r(n)){if(t.length!==n.length)return!1;for(var e=!0,i=0;i<t.length&&(e=on(t[i],n[i]));i++);return e}if(g(t)||g(n)){var o=Object.keys(t),a=Object.keys(n);if(o.length!==a.length)return!1;for(e=!0,i=0;i<o.length&&(e=on(t[o[i]],n[o[i]]));i++);return e}return!1},an=on,un=function(t,n,e){return s(e)?!!e(t,n):an(t,n)},cn=function(t,n){if(!r(t))return t;for(var e=[],i=0;i<t.length;i++){var o=t[i];e.push(n(o,i))}return e},sn=function(t){return t},fn=function(t,n){void 0===n&&(n=sn);var e={};return h(t)&&!f(t)&&Object.keys(t).forEach((function(r){e[r]=n(t[r],r)})),e},ln=function(t,n,e){for(var r=0,i=D(n)?n.split("."):n;t&&r<i.length;)t=t[i[r++]];return void 0===t||r<i.length?e:t},hn=function(t,n,e){var r=t,i=D(n)?n.split("."):n;return i.forEach((function(t,n){n<i.length-1?(h(r[t])||(r[t]=rt(i[n+1])?[]:{}),r=r[t]):r[t]=e})),t},pn=Object.prototype.hasOwnProperty,dn=function(t,n){if(null===t||!y(t))return{};var e={};return p(n,(function(n){pn.call(t,n)&&(e[n]=t[n])})),e},vn=function(t,n){return B(t,(function(t,e,r){return n.includes(r)||(t[r]=e),t}),{})},gn=function(t,n,e){var r,i,o,a,u=0;e||(e={});var c=function(){u=!1===e.leading?0:Date.now(),r=null,a=t.apply(i,o),r||(i=o=null)},s=function(){var s=Date.now();u||!1!==e.leading||(u=s);var f=n-(s-u);return i=this,o=arguments,f<=0||f>n?(r&&(clearTimeout(r),r=null),u=s,a=t.apply(i,o),r||(i=o=null)):r||!1===e.trailing||(r=setTimeout(c,f)),a};return s.cancel=function(){clearTimeout(r),u=0,r=i=o=null},s},yn=function(t){return r(t)?Array.prototype.slice.call(t):[]},mn={},bn=function(t){return mn[t=t||"g"]?mn[t]+=1:mn[t]=1,t+mn[t]},xn=function(){},Mn=function(t){return t};function wn(t){return f(t)?0:r(t)?t.length:Object.keys(t).length}var _n,An=e(4),On=Zt((function(t,n){void 0===n&&(n={});var e=n.fontSize,r=n.fontFamily,i=n.fontWeight,o=n.fontStyle,a=n.fontVariant;return _n||(_n=document.createElement("canvas").getContext("2d")),_n.font=[o,a,i,e+"px",r].join(" "),_n.measureText(D(t)?t:"").width}),(function(t,n){return void 0===n&&(n={}),Object(An.__spreadArrays)([t],wt(n)).join("")})),Sn=function(t,n,e,r){void 0===r&&(r="...");var i,o,a=On(r,e),u=D(t)?t:At(t),c=n,s=[];if(On(t,e)<=n)return t;for(;i=u.substr(0,16),!((o=On(i,e))+a>c&&o>c);)if(s.push(i),c-=o,!(u=u.substr(16)))return s.join("");for(;i=u.substr(0,1),!((o=On(i,e))+a>c);)if(s.push(i),c-=o,!(u=u.substr(1)))return s.join("");return""+s.join("")+r},Pn=function(){function t(){this.map={}}return t.prototype.has=function(t){return void 0!==this.map[t]},t.prototype.get=function(t,n){var e=this.map[t];return void 0===e?n:e},t.prototype.set=function(t,n){this.map[t]=n},t.prototype.clear=function(){this.map={}},t.prototype.delete=function(t){delete this.map[t]},t.prototype.size=function(){return Object.keys(this.map).length},t}()},function(t,n,e){"use strict";e.r(n),e.d(n,"__extends",(function(){return i})),e.d(n,"__assign",(function(){return o})),e.d(n,"__rest",(function(){return a})),e.d(n,"__decorate",(function(){return u})),e.d(n,"__param",(function(){return c})),e.d(n,"__metadata",(function(){return s})),e.d(n,"__awaiter",(function(){return f})),e.d(n,"__generator",(function(){return l})),e.d(n,"__createBinding",(function(){return h})),e.d(n,"__exportStar",(function(){return p})),e.d(n,"__values",(function(){return d})),e.d(n,"__read",(function(){return v})),e.d(n,"__spread",(function(){return g})),e.d(n,"__spreadArrays",(function(){return y})),e.d(n,"__spreadArray",(function(){return m})),e.d(n,"__await",(function(){return b})),e.d(n,"__asyncGenerator",(function(){return x})),e.d(n,"__asyncDelegator",(function(){return M})),e.d(n,"__asyncValues",(function(){return w})),e.d(n,"__makeTemplateObject",(function(){return _})),e.d(n,"__importStar",(function(){return O})),e.d(n,"__importDefault",(function(){return S})),e.d(n,"__classPrivateFieldGet",(function(){return P})),e.d(n,"__classPrivateFieldSet",(function(){return C})),e.d(n,"__classPrivateFieldIn",(function(){return j}));var r=function(t,n){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e])})(t,n)};function i(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=t}r(t,n),t.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)}var o=function(){return(o=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};function a(t,n){var e={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&n.indexOf(r)<0&&(e[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)n.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(e[r[i]]=t[r[i]])}return e}function u(t,n,e,r){var i,o=arguments.length,a=o<3?n:null===r?r=Object.getOwnPropertyDescriptor(n,e):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,n,e,r);else for(var u=t.length-1;u>=0;u--)(i=t[u])&&(a=(o<3?i(a):o>3?i(n,e,a):i(n,e))||a);return o>3&&a&&Object.defineProperty(n,e,a),a}function c(t,n){return function(e,r){n(e,r,t)}}function s(t,n){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,n)}function f(t,n,e,r){return new(e||(e=Promise))((function(i,o){function a(t){try{c(r.next(t))}catch(t){o(t)}}function u(t){try{c(r.throw(t))}catch(t){o(t)}}function c(t){var n;t.done?i(t.value):(n=t.value,n instanceof e?n:new e((function(t){t(n)}))).then(a,u)}c((r=r.apply(t,n||[])).next())}))}function l(t,n){var e,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(u){return function(c){return function(u){if(e)throw new TypeError("Generator is already executing.");for(;o&&(o=0,u[0]&&(a=0)),a;)try{if(e=1,r&&(i=2&u[0]?r.return:u[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,u[1])).done)return i;switch(r=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==u[0]&&2!==u[0])){a=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){a.label=u[1];break}if(6===u[0]&&a.label<i[1]){a.label=i[1],i=u;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(u);break}i[2]&&a.ops.pop(),a.trys.pop();continue}u=n.call(t,a)}catch(t){u=[6,t],r=0}finally{e=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,c])}}}var h=Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]};function p(t,n){for(var e in t)"default"===e||Object.prototype.hasOwnProperty.call(n,e)||h(n,t,e)}function d(t){var n="function"==typeof Symbol&&Symbol.iterator,e=n&&t[n],r=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function v(t,n){var e="function"==typeof Symbol&&t[Symbol.iterator];if(!e)return t;var r,i,o=e.call(t),a=[];try{for(;(void 0===n||n-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(e=o.return)&&e.call(o)}finally{if(i)throw i.error}}return a}function g(){for(var t=[],n=0;n<arguments.length;n++)t=t.concat(v(arguments[n]));return t}function y(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;var r=Array(t),i=0;for(n=0;n<e;n++)for(var o=arguments[n],a=0,u=o.length;a<u;a++,i++)r[i]=o[a];return r}function m(t,n,e){if(e||2===arguments.length)for(var r,i=0,o=n.length;i<o;i++)!r&&i in n||(r||(r=Array.prototype.slice.call(n,0,i)),r[i]=n[i]);return t.concat(r||Array.prototype.slice.call(n))}function b(t){return this instanceof b?(this.v=t,this):new b(t)}function x(t,n,e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=e.apply(t,n||[]),o=[];return r={},a("next"),a("throw"),a("return"),r[Symbol.asyncIterator]=function(){return this},r;function a(t){i[t]&&(r[t]=function(n){return new Promise((function(e,r){o.push([t,n,e,r])>1||u(t,n)}))})}function u(t,n){try{(e=i[t](n)).value instanceof b?Promise.resolve(e.value.v).then(c,s):f(o[0][2],e)}catch(t){f(o[0][3],t)}var e}function c(t){u("next",t)}function s(t){u("throw",t)}function f(t,n){t(n),o.shift(),o.length&&u(o[0][0],o[0][1])}}function M(t){var n,e;return n={},r("next"),r("throw",(function(t){throw t})),r("return"),n[Symbol.iterator]=function(){return this},n;function r(r,i){n[r]=t[r]?function(n){return(e=!e)?{value:b(t[r](n)),done:"return"===r}:i?i(n):n}:i}}function w(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,e=t[Symbol.asyncIterator];return e?e.call(t):(t=d(t),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(e){n[e]=t[e]&&function(n){return new Promise((function(r,i){(function(t,n,e,r){Promise.resolve(r).then((function(n){t({value:n,done:e})}),n)})(r,i,(n=t[e](n)).done,n.value)}))}}}function _(t,n){return Object.defineProperty?Object.defineProperty(t,"raw",{value:n}):t.raw=n,t}var A=Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n};function O(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&h(n,t,e);return A(n,t),n}function S(t){return t&&t.__esModule?t:{default:t}}function P(t,n,e,r){if("a"===e&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof n?t!==n||!r:!n.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===e?r:"a"===e?r.call(t):r?r.value:n.get(t)}function C(t,n,e,r,i){if("m"===r)throw new TypeError("Private method is not writable");if("a"===r&&!i)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof n?t!==n||!i:!n.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(t,e):i?i.value=e:n.set(t,e),e}function j(t,n){if(null===n||"object"!=typeof n&&"function"!=typeof n)throw new TypeError("Cannot use 'in' operator on non-object");return"function"==typeof t?n===t:t.has(n)}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.EVENTS=n.SVG_ATTR_MAP=n.SHAPE_TO_TAGS=void 0,n.SHAPE_TO_TAGS={rect:"path",circle:"circle",line:"line",path:"path",marker:"path",text:"text",polyline:"polyline",polygon:"polygon",image:"image",ellipse:"ellipse",dom:"foreignObject"},n.SVG_ATTR_MAP={opacity:"opacity",fillStyle:"fill",fill:"fill",fillOpacity:"fill-opacity",strokeStyle:"stroke",strokeOpacity:"stroke-opacity",stroke:"stroke",x:"x",y:"y",r:"r",rx:"rx",ry:"ry",width:"width",height:"height",x1:"x1",x2:"x2",y1:"y1",y2:"y2",lineCap:"stroke-linecap",lineJoin:"stroke-linejoin",lineWidth:"stroke-width",lineDash:"stroke-dasharray",lineDashOffset:"stroke-dashoffset",miterLimit:"stroke-miterlimit",font:"font",fontSize:"font-size",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",fontFamily:"font-family",startArrow:"marker-start",endArrow:"marker-end",path:"d",class:"class",id:"id",style:"style",preserveAspectRatio:"preserveAspectRatio"},n.EVENTS=["click","mousedown","mouseup","dblclick","contextmenu","mouseenter","mouseleave","mouseover","mouseout","mousemove","wheel"]},function(t,n,e){"use strict";e.d(n,"a",(function(){return i})),e.d(n,"b",(function(){return o}));var r=function(t,n){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e])})(t,n)};function i(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=t}r(t,n),t.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)}Object.create;function o(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;var r=Array(t),i=0;for(n=0;n<e;n++)for(var o=arguments[n],a=0,u=o.length;a<u;a++,i++)r[i]=o[a];return r}Object.create},function(t,n,e){"use strict";function r(t,n){var e=[],r=t[0],i=t[1],o=t[2],a=t[3],u=t[4],c=t[5],s=t[6],f=t[7],l=t[8],h=n[0],p=n[1],d=n[2],v=n[3],g=n[4],y=n[5],m=n[6],b=n[7],x=n[8];return e[0]=h*r+p*a+d*s,e[1]=h*i+p*u+d*f,e[2]=h*o+p*c+d*l,e[3]=v*r+g*a+y*s,e[4]=v*i+g*u+y*f,e[5]=v*o+g*c+y*l,e[6]=m*r+b*a+x*s,e[7]=m*i+b*u+x*f,e[8]=m*o+b*c+x*l,e}function i(t,n){var e=[],r=n[0],i=n[1];return e[0]=t[0]*r+t[3]*i+t[6],e[1]=t[1]*r+t[4]*i+t[7],e}function o(t){var n=[],e=t[0],r=t[1],i=t[2],o=t[3],a=t[4],u=t[5],c=t[6],s=t[7],f=t[8],l=f*a-u*s,h=-f*o+u*c,p=s*o-a*c,d=e*l+r*h+i*p;return d?(d=1/d,n[0]=l*d,n[1]=(-f*r+i*s)*d,n[2]=(u*r-i*a)*d,n[3]=h*d,n[4]=(f*e-i*c)*d,n[5]=(-u*e+i*o)*d,n[6]=p*d,n[7]=(-s*e+r*c)*d,n[8]=(a*e-r*o)*d,n):null}e.d(n,"b",(function(){return r})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){return o}))},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(13),o=e(14),a=e(9),u=e(22),c=e(5),s=e(15),f=e(23),l=e(13),h=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="svg",n.canFill=!1,n.canStroke=!1,n}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{lineWidth:1,lineAppendWidth:0,strokeOpacity:1,fillOpacity:1})},n.prototype.afterAttrsChange=function(n){t.prototype.afterAttrsChange.call(this,n);var e=this.get("canvas");if(e&&e.get("autoDraw")){var r=e.get("context");this.draw(r,n)}},n.prototype.getShapeBase=function(){return s},n.prototype.getGroupBase=function(){return f.default},n.prototype.onCanvasChange=function(t){(0,u.refreshElement)(this,t)},n.prototype.calculateBBox=function(){var t=this.get("el"),n=null;if(t)n=t.getBBox();else{var e=(0,l.getBBoxMethod)(this.get("type"));e&&(n=e(this))}if(n){var r=n.x,i=n.y,o=n.width,a=n.height,u=this.getHitLineWidth(),c=u/2,s=r-c,f=i-c;return{x:s,y:f,minX:s,minY:f,maxX:r+o+c,maxY:i+a+c,width:o+u,height:a+u}}return{x:0,y:0,minX:0,minY:0,maxX:0,maxY:0,width:0,height:0}},n.prototype.isFill=function(){var t=this.attr(),n=t.fill,e=t.fillStyle;return(n||e||this.isClipShape())&&this.canFill},n.prototype.isStroke=function(){var t=this.attr(),n=t.stroke,e=t.strokeStyle;return(n||e)&&this.canStroke},n.prototype.draw=function(t,n){var e=this.get("el");this.get("destroyed")?e&&e.parentNode.removeChild(e):(e||(0,a.createDom)(this),(0,o.setClip)(this,t),this.createPath(t,n),this.shadow(t,n),this.strokeAndFill(t,n),this.transform(n))},n.prototype.createPath=function(t,n){},n.prototype.strokeAndFill=function(t,n){var e=n||this.attr(),r=e.fill,i=e.fillStyle,o=e.stroke,a=e.strokeStyle,u=e.fillOpacity,s=e.strokeOpacity,f=e.lineWidth,l=this.get("el");this.canFill&&(n?"fill"in e?this._setColor(t,"fill",r):"fillStyle"in e&&this._setColor(t,"fill",i):this._setColor(t,"fill",r||i),u&&l.setAttribute(c.SVG_ATTR_MAP.fillOpacity,u)),this.canStroke&&f>0&&(n?"stroke"in e?this._setColor(t,"stroke",o):"strokeStyle"in e&&this._setColor(t,"stroke",a):this._setColor(t,"stroke",o||a),s&&l.setAttribute(c.SVG_ATTR_MAP.strokeOpacity,s),f&&l.setAttribute(c.SVG_ATTR_MAP.lineWidth,f))},n.prototype._setColor=function(t,n,e){var r=this.get("el");if(e)if(e=e.trim(),/^[r,R,L,l]{1}[\s]*\(/.test(e))(i=t.find("gradient",e))||(i=t.addGradient(e)),r.setAttribute(c.SVG_ATTR_MAP[n],"url(#".concat(i,")"));else if(/^[p,P]{1}[\s]*\(/.test(e)){var i;(i=t.find("pattern",e))||(i=t.addPattern(e)),r.setAttribute(c.SVG_ATTR_MAP[n],"url(#".concat(i,")"))}else r.setAttribute(c.SVG_ATTR_MAP[n],e);else r.setAttribute(c.SVG_ATTR_MAP[n],"none")},n.prototype.shadow=function(t,n){var e=this.attr(),r=n||e,i=r.shadowOffsetX,a=r.shadowOffsetY,u=r.shadowBlur,c=r.shadowColor;(i||a||u||c)&&(0,o.setShadow)(this,t)},n.prototype.transform=function(t){var n=this.attr();(t||n).matrix&&(0,o.setTransform)(this)},n.prototype.isInShape=function(t,n){return this.isPointInPath(t,n)},n.prototype.isPointInPath=function(t,n){var e=this.get("el"),r=this.get("canvas").get("el").getBoundingClientRect(),i=t+r.left,o=n+r.top,a=document.elementFromPoint(i,o);return!(!a||!a.isEqualNode(e))},n.prototype.getHitLineWidth=function(){var t=this.attrs,n=t.lineWidth,e=t.lineAppendWidth;return this.isStroke()?n+e:0},n}(i.AbstractShape);n.default=h},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.moveTo=n.sortDom=n.createDom=n.createSVGElement=void 0;var r=e(3),i=e(5);function o(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}n.createSVGElement=o,n.createDom=function(t){var n=i.SHAPE_TO_TAGS[t.type],e=t.getParent();if(!n)throw new Error("the type ".concat(t.type," is not supported by svg"));var r=o(n);if(t.get("id")&&(r.id=t.get("id")),t.set("el",r),t.set("attrs",{}),e){var a=e.get("el");a||(a=e.createDom(),e.set("el",a)),a.appendChild(r)}return r},n.sortDom=function(t,n){var e=t.get("el"),i=(0,r.toArray)(e.children).sort(n),o=document.createDocumentFragment();i.forEach((function(t){o.appendChild(t)})),e.appendChild(o)},n.moveTo=function(t,n){var e=t.parentNode,r=Array.from(e.childNodes).filter((function(t){return 1===t.nodeType&&"defs"!==t.nodeName.toLowerCase()})),i=r[n],o=r.indexOf(t);if(i){if(o>n)e.insertBefore(t,i);else if(o<n){var a=r[n+1];a?e.insertBefore(t,a):e.appendChild(t)}}else e.appendChild(t)}},function(t,n,e){"use strict";e.r(n),e.d(n,"Quad",(function(){return k})),e.d(n,"Cubic",(function(){return F})),e.d(n,"Arc",(function(){return X})),e.d(n,"Line",(function(){return O})),e.d(n,"Polygon",(function(){return K})),e.d(n,"Polyline",(function(){return U})),e.d(n,"Util",(function(){return r}));var r={};e.r(r),e.d(r,"distance",(function(){return y})),e.d(r,"isNumberEqual",(function(){return m})),e.d(r,"getBBoxByArray",(function(){return b})),e.d(r,"getBBoxRange",(function(){return x})),e.d(r,"piMod",(function(){return M}));var i={}.toString,o=function(t,n){return i.call(t)==="[object "+n+"]"},a=function(t){return o(t,"Function")},u=function(t){return Array.isArray?Array.isArray(t):o(t,"Array")},c=function(t){var n=typeof t;return null!==t&&"object"===n||"function"===n};var s=function(t,n){if(t)if(u(t))for(var e=0,r=t.length;e<r&&!1!==n(t[e],e);e++);else if(c(t))for(var i in t)if(t.hasOwnProperty(i)&&!1===n(t[i],i))break};Object.keys;var f=function(t){if(u(t))return t.reduce((function(t,n){return Math.max(t,n)}),t[0])},l=function(t){if(u(t))return t.reduce((function(t,n){return Math.min(t,n)}),t[0])},h=Array.prototype,p=(h.splice,h.indexOf,Array.prototype.splice,function(t){return o(t,"String")});Object.prototype.hasOwnProperty;var d=function(t){return o(t,"Number")};Number.isInteger&&Number.isInteger;Math.PI,parseInt,Math.PI;var v=Object.values?function(t){return Object.values(t)}:function(t){var n=[];return s(t,(function(e,r){a(t)&&"prototype"===r||n.push(e)})),n};Object.prototype;Object.prototype.hasOwnProperty;Object.prototype.hasOwnProperty;Object.create;var g;Object.create;(function(t,n){if(!a(t))throw new TypeError("Expected a function");var e=function(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var o=n?n.apply(this,r):r[0],a=e.cache;if(a.has(o))return a.get(o);var u=t.apply(this,r);return a.set(o,u),u};e.cache=new Map})((function(t,n){void 0===n&&(n={});var e=n.fontSize,r=n.fontFamily,i=n.fontWeight,o=n.fontStyle,a=n.fontVariant;return g||(g=document.createElement("canvas").getContext("2d")),g.font=[o,a,i,e+"px",r].join(" "),g.measureText(p(t)?t:"").width}),(function(t,n){return void 0===n&&(n={}),function(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;var r=Array(t),i=0;for(n=0;n<e;n++)for(var o=arguments[n],a=0,u=o.length;a<u;a++,i++)r[i]=o[a];return r}([t],v(n)).join("")})),function(){function t(){this.map={}}t.prototype.has=function(t){return void 0!==this.map[t]},t.prototype.get=function(t,n){var e=this.map[t];return void 0===e?n:e},t.prototype.set=function(t,n){this.map[t]=n},t.prototype.clear=function(){this.map={}},t.prototype.delete=function(t){delete this.map[t]},t.prototype.size=function(){return Object.keys(this.map).length}}();function y(t,n,e,r){var i=t-e,o=n-r;return Math.sqrt(i*i+o*o)}function m(t,n){return Math.abs(t-n)<.001}function b(t,n){var e=l(t),r=l(n);return{x:e,y:r,width:f(t)-e,height:f(n)-r}}function x(t,n,e,r){return{minX:l([t,e]),maxX:f([t,e]),minY:l([n,r]),maxY:f([n,r])}}function M(t){return(t+2*Math.PI)%(2*Math.PI)}var w="undefined"!=typeof Float32Array?Float32Array:Array;Math.random;Math.PI;Math.hypot||(Math.hypot=function(){for(var t=0,n=arguments.length;n--;)t+=arguments[n]*arguments[n];return Math.sqrt(t)});_=new w(2),w!=Float32Array&&(_[0]=0,_[1]=0),A=_;var _,A,O={box:function(t,n,e,r){return b([t,e],[n,r])},length:function(t,n,e,r){return y(t,n,e,r)},pointAt:function(t,n,e,r,i){return{x:(1-i)*t+i*e,y:(1-i)*n+i*r}},pointDistance:function(t,n,e,r,i,o){var a=(e-t)*(i-t)+(r-n)*(o-n);return a<0?y(t,n,i,o):a>(e-t)*(e-t)+(r-n)*(r-n)?y(e,r,i,o):this.pointToLine(t,n,e,r,i,o)},pointToLine:function(t,n,e,r,i,o){var a=[e-t,r-n];if(function(t,n){return t[0]===n[0]&&t[1]===n[1]}(a,[0,0]))return Math.sqrt((i-t)*(i-t)+(o-n)*(o-n));var u=[-a[1],a[0]];!function(t,n){var e=n[0],r=n[1],i=e*e+r*r;i>0&&(i=1/Math.sqrt(i)),t[0]=n[0]*i,t[1]=n[1]*i}(u,u);var c=[i-t,o-n];return Math.abs(function(t,n){return t[0]*n[0]+t[1]*n[1]}(c,u))},tangentAngle:function(t,n,e,r){return Math.atan2(r-n,e-t)}};function S(t,n,e,r,i,o){var a,u=1/0,c=[e,r],s=20;o&&o>200&&(s=o/10);for(var f=1/s,l=f/10,h=0;h<=s;h++){var p=h*f,d=[i.apply(null,t.concat([p])),i.apply(null,n.concat([p]))];(b=y(c[0],c[1],d[0],d[1]))<u&&(a=p,u=b)}if(0===a)return{x:t[0],y:n[0]};if(1===a){var v=t.length;return{x:t[v-1],y:n[v-1]}}u=1/0;for(h=0;h<32&&!(l<1e-4);h++){var g=a-l,m=a+l,b=(d=[i.apply(null,t.concat([g])),i.apply(null,n.concat([g]))],y(c[0],c[1],d[0],d[1]));if(g>=0&&b<u)a=g,u=b;else{var x=[i.apply(null,t.concat([m])),i.apply(null,n.concat([m]))],M=y(c[0],c[1],x[0],x[1]);m<=1&&M<u?(a=m,u=M):l*=.5}}return{x:i.apply(null,t.concat([a])),y:i.apply(null,n.concat([a]))}}function P(t,n,e,r){var i=1-r;return i*i*t+2*r*i*n+r*r*e}function C(t,n,e){var r=t+e-2*n;if(m(r,0))return[.5];var i=(t-n)/r;return i<=1&&i>=0?[i]:[]}function j(t,n,e,r){return 2*(1-r)*(n-t)+2*r*(e-n)}function T(t,n,e,r,i,o,a){var u=P(t,e,i,a),c=P(n,r,o,a),s=O.pointAt(t,n,e,r,a),f=O.pointAt(e,r,i,o,a);return[[t,n,s.x,s.y,u,c],[u,c,f.x,f.y,i,o]]}var k={box:function(t,n,e,r,i,o){var a=C(t,e,i)[0],u=C(n,r,o)[0],c=[t,i],s=[n,o];return void 0!==a&&c.push(P(t,e,i,a)),void 0!==u&&s.push(P(n,r,o,u)),b(c,s)},length:function(t,n,e,r,i,o){return function t(n,e,r,i,o,a,u){if(0===u)return(y(n,e,r,i)+y(r,i,o,a)+y(n,e,o,a))/2;var c=T(n,e,r,i,o,a,.5),s=c[0],f=c[1];return s.push(u-1),f.push(u-1),t.apply(null,s)+t.apply(null,f)}(t,n,e,r,i,o,3)},nearestPoint:function(t,n,e,r,i,o,a,u){return S([t,e,i],[n,r,o],a,u,P)},pointDistance:function(t,n,e,r,i,o,a,u){var c=this.nearestPoint(t,n,e,r,i,o,a,u);return y(c.x,c.y,a,u)},interpolationAt:P,pointAt:function(t,n,e,r,i,o,a){return{x:P(t,e,i,a),y:P(n,r,o,a)}},divide:function(t,n,e,r,i,o,a){return T(t,n,e,r,i,o,a)},tangentAngle:function(t,n,e,r,i,o,a){var u=j(t,e,i,a),c=j(n,r,o,a);return M(Math.atan2(c,u))}};function E(t,n,e,r,i){var o=1-i;return o*o*o*t+3*n*i*o*o+3*e*i*i*o+r*i*i*i}function B(t,n,e,r,i){var o=1-i;return 3*(o*o*(n-t)+2*o*i*(e-n)+i*i*(r-e))}function I(t,n,e,r){var i,o,a,u=-3*t+9*n-9*e+3*r,c=6*t-12*n+6*e,s=3*n-3*t,f=[];if(m(u,0))m(c,0)||(i=-s/c)>=0&&i<=1&&f.push(i);else{var l=c*c-4*u*s;m(l,0)?f.push(-c/(2*u)):l>0&&(o=(-c-(a=Math.sqrt(l)))/(2*u),(i=(-c+a)/(2*u))>=0&&i<=1&&f.push(i),o>=0&&o<=1&&f.push(o))}return f}function D(t,n,e,r,i,o,a,u,c){var s=E(t,e,i,a,c),f=E(n,r,o,u,c),l=O.pointAt(t,n,e,r,c),h=O.pointAt(e,r,i,o,c),p=O.pointAt(i,o,a,u,c),d=O.pointAt(l.x,l.y,h.x,h.y,c),v=O.pointAt(h.x,h.y,p.x,p.y,c);return[[t,n,l.x,l.y,d.x,d.y,s,f],[s,f,v.x,v.y,p.x,p.y,a,u]]}function N(t,n,e,r,i,o,a,u,c){if(0===c)return function(t,n){for(var e=0,r=t.length,i=0;i<r;i++){e+=y(t[i],n[i],t[(i+1)%r],n[(i+1)%r])}return e/2}([t,e,i,a],[n,r,o,u]);var s=D(t,n,e,r,i,o,a,u,.5),f=s[0],l=s[1];return f.push(c-1),l.push(c-1),N.apply(null,f)+N.apply(null,l)}var F={extrema:I,box:function(t,n,e,r,i,o,a,u){for(var c=[t,a],s=[n,u],f=I(t,e,i,a),l=I(n,r,o,u),h=0;h<f.length;h++)c.push(E(t,e,i,a,f[h]));for(h=0;h<l.length;h++)s.push(E(n,r,o,u,l[h]));return b(c,s)},length:function(t,n,e,r,i,o,a,u){return N(t,n,e,r,i,o,a,u,3)},nearestPoint:function(t,n,e,r,i,o,a,u,c,s,f){return S([t,e,i,a],[n,r,o,u],c,s,E,f)},pointDistance:function(t,n,e,r,i,o,a,u,c,s,f){var l=this.nearestPoint(t,n,e,r,i,o,a,u,c,s,f);return y(l.x,l.y,c,s)},interpolationAt:E,pointAt:function(t,n,e,r,i,o,a,u,c){return{x:E(t,e,i,a,c),y:E(n,r,o,u,c)}},divide:function(t,n,e,r,i,o,a,u,c){return D(t,n,e,r,i,o,a,u,c)},tangentAngle:function(t,n,e,r,i,o,a,u,c){var s=B(t,e,i,a,c),f=B(n,r,o,u,c);return M(Math.atan2(f,s))}};function L(t,n){var e=Math.abs(t);return n>0?e:-1*e}var R=function(t,n,e,r,i,o){var a=e,u=r;if(0===a||0===u)return{x:t,y:n};for(var c,s,f=i-t,l=o-n,h=Math.abs(f),p=Math.abs(l),d=a*a,v=u*u,g=Math.PI/4,y=0;y<4;y++){c=a*Math.cos(g),s=u*Math.sin(g);var m=(d-v)*Math.pow(Math.cos(g),3)/a,b=(v-d)*Math.pow(Math.sin(g),3)/u,x=c-m,M=s-b,w=h-m,_=p-b,A=Math.hypot(M,x),O=Math.hypot(_,w);g+=A*Math.asin((x*_-M*w)/(A*O))/Math.sqrt(d+v-c*c-s*s),g=Math.min(Math.PI/2,Math.max(0,g))}return{x:t+L(c,f),y:n+L(s,l)}};function G(t,n,e,r,i,o){return e*Math.cos(i)*Math.cos(o)-r*Math.sin(i)*Math.sin(o)+t}function V(t,n,e,r,i,o){return e*Math.sin(i)*Math.cos(o)+r*Math.cos(i)*Math.sin(o)+n}function W(t,n,e){return{x:t*Math.cos(e),y:n*Math.sin(e)}}function q(t,n,e){var r=Math.cos(e),i=Math.sin(e);return[t*r-n*i,t*i+n*r]}var X={box:function(t,n,e,r,i,o,a){for(var u=function(t,n,e){return Math.atan(-n/t*Math.tan(e))}(e,r,i),c=1/0,s=-1/0,f=[o,a],l=2*-Math.PI;l<=2*Math.PI;l+=Math.PI){var h=u+l;o<a?o<h&&h<a&&f.push(h):a<h&&h<o&&f.push(h)}for(l=0;l<f.length;l++){var p=G(t,0,e,r,i,f[l]);p<c&&(c=p),p>s&&(s=p)}var d=function(t,n,e){return Math.atan(n/(t*Math.tan(e)))}(e,r,i),v=1/0,g=-1/0,y=[o,a];for(l=2*-Math.PI;l<=2*Math.PI;l+=Math.PI){var m=d+l;o<a?o<m&&m<a&&y.push(m):a<m&&m<o&&y.push(m)}for(l=0;l<y.length;l++){var b=V(0,n,e,r,i,y[l]);b<v&&(v=b),b>g&&(g=b)}return{x:c,y:v,width:s-c,height:g-v}},length:function(t,n,e,r,i,o,a){},nearestPoint:function(t,n,e,r,i,o,a,u,c){var s=q(u-t,c-n,-i),f=s[0],l=s[1],h=R(0,0,e,r,f,l),p=function(t,n,e,r){return(Math.atan2(r*t,e*n)+2*Math.PI)%(2*Math.PI)}(e,r,h.x,h.y);p<o?h=W(e,r,o):p>a&&(h=W(e,r,a));var d=q(h.x,h.y,i);return{x:d[0]+t,y:d[1]+n}},pointDistance:function(t,n,e,r,i,o,a,u,c){var s=this.nearestPoint(t,n,e,r,u,c);return y(s.x,s.y,u,c)},pointAt:function(t,n,e,r,i,o,a,u){var c=(a-o)*u+o;return{x:G(t,0,e,r,i,c),y:V(0,n,e,r,i,c)}},tangentAngle:function(t,n,e,r,i,o,a,u){var c=(a-o)*u+o,s=function(t,n,e,r,i,o,a,u){return-1*e*Math.cos(i)*Math.sin(u)-r*Math.sin(i)*Math.cos(u)}(0,0,e,r,i,0,0,c),f=function(t,n,e,r,i,o,a,u){return-1*e*Math.sin(i)*Math.sin(u)+r*Math.cos(i)*Math.cos(u)}(0,0,e,r,i,0,0,c);return M(Math.atan2(f,s))}};function $(t){for(var n=0,e=[],r=0;r<t.length-1;r++){var i=t[r],o=t[r+1],a=y(i[0],i[1],o[0],o[1]),u={from:i,to:o,length:a};e.push(u),n+=a}return{segments:e,totalLength:n}}function Y(t){if(t.length<2)return 0;for(var n=0,e=0;e<t.length-1;e++){var r=t[e],i=t[e+1];n+=y(r[0],r[1],i[0],i[1])}return n}function z(t,n){if(n>1||n<0||t.length<2)return null;var e=$(t),r=e.segments,i=e.totalLength;if(0===i)return{x:t[0][0],y:t[0][1]};for(var o=0,a=null,u=0;u<r.length;u++){var c=r[u],s=c.from,f=c.to,l=c.length/i;if(n>=o&&n<=o+l){var h=(n-o)/l;a=O.pointAt(s[0],s[1],f[0],f[1],h);break}o+=l}return a}function H(t,n){if(n>1||n<0||t.length<2)return 0;for(var e=$(t),r=e.segments,i=e.totalLength,o=0,a=0,u=0;u<r.length;u++){var c=r[u],s=c.from,f=c.to,l=c.length/i;if(n>=o&&n<=o+l){a=Math.atan2(f[1]-s[1],f[0]-s[0]);break}o+=l}return a}function Q(t,n,e){for(var r=1/0,i=0;i<t.length-1;i++){var o=t[i],a=t[i+1],u=O.pointDistance(o[0],o[1],a[0],a[1],n,e);u<r&&(r=u)}return r}var U={box:function(t){for(var n=[],e=[],r=0;r<t.length;r++){var i=t[r];n.push(i[0]),e.push(i[1])}return b(n,e)},length:function(t){return Y(t)},pointAt:function(t,n){return z(t,n)},pointDistance:function(t,n,e){return Q(t,n,e)},tangentAngle:function(t,n){return H(t,n)}};function Z(t){var n=t.slice(0);return t.length&&n.push(t[0]),n}var K={box:function(t){return U.box(t)},length:function(t){return Y(Z(t))},pointAt:function(t,n){return z(Z(t),n)},pointDistance:function(t,n,e){return Q(Z(t),n,e)},tangentAngle:function(t,n){return H(Z(t),n)}}},function(t,n,e){"use strict";e.r(n),e.d(n,"catmullRomToBezier",(function(){return c})),e.d(n,"fillPath",(function(){return T})),e.d(n,"fillPathByDiff",(function(){return B})),e.d(n,"formatPath",(function(){return N})),e.d(n,"intersection",(function(){return P})),e.d(n,"parsePathArray",(function(){return g})),e.d(n,"parsePathString",(function(){return u})),e.d(n,"pathToAbsolute",(function(){return f})),e.d(n,"pathToCurve",(function(){return d})),e.d(n,"rectPath",(function(){return w}));var r=e(0),i="\t\n\v\f\r   ᠎             　\u2028\u2029",o=new RegExp("([a-z])["+i+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+i+"]*,?["+i+"]*)+)","ig"),a=new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+i+"]*,?["+i+"]*","ig"),u=function(t){if(!t)return null;if(Object(r.b)(t))return t;var n={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},e=[];return String(t).replace(o,(function(r,i,o){var u=[],c=i.toLowerCase();if(o.replace(a,(function(t,n){n&&u.push(+n)})),"m"===c&&u.length>2&&(e.push([i].concat(u.splice(0,2))),c="l",i="m"===i?"l":"L"),"o"===c&&1===u.length&&e.push([i,u[0]]),"r"===c)e.push([i].concat(u));else for(;u.length>=n[c]&&(e.push([i].concat(u.splice(0,n[c]))),n[c]););return t})),e},c=function(t,n){for(var e=[],r=0,i=t.length;i-2*!n>r;r+=2){var o=[{x:+t[r-2],y:+t[r-1]},{x:+t[r],y:+t[r+1]},{x:+t[r+2],y:+t[r+3]},{x:+t[r+4],y:+t[r+5]}];n?r?i-4===r?o[3]={x:+t[0],y:+t[1]}:i-2===r&&(o[2]={x:+t[0],y:+t[1]},o[3]={x:+t[2],y:+t[3]}):o[0]={x:+t[i-2],y:+t[i-1]}:i-4===r?o[3]=o[2]:r||(o[0]={x:+t[r],y:+t[r+1]}),e.push(["C",(-o[0].x+6*o[1].x+o[2].x)/6,(-o[0].y+6*o[1].y+o[2].y)/6,(o[1].x+6*o[2].x-o[3].x)/6,(o[1].y+6*o[2].y-o[3].y)/6,o[2].x,o[2].y])}return e},s=function(t,n,e,r,i){var o=[];if(null===i&&null===r&&(r=e),t=+t,n=+n,e=+e,r=+r,null!==i){var a=Math.PI/180,u=t+e*Math.cos(-r*a),c=t+e*Math.cos(-i*a);o=[["M",u,n+e*Math.sin(-r*a)],["A",e,e,0,+(i-r>180),0,c,n+e*Math.sin(-i*a)]]}else o=[["M",t,n],["m",0,-r],["a",e,r,0,1,1,0,2*r],["a",e,r,0,1,1,0,-2*r],["z"]];return o},f=function(t){if(!(t=u(t))||!t.length)return[["M",0,0]];var n,e,r=[],i=0,o=0,a=0,f=0,l=0;"M"===t[0][0]&&(a=i=+t[0][1],f=o=+t[0][2],l++,r[0]=["M",i,o]);for(var h=3===t.length&&"M"===t[0][0]&&"R"===t[1][0].toUpperCase()&&"Z"===t[2][0].toUpperCase(),p=void 0,d=void 0,v=l,g=t.length;v<g;v++){if(r.push(p=[]),(n=(d=t[v])[0])!==n.toUpperCase())switch(p[0]=n.toUpperCase(),p[0]){case"A":p[1]=d[1],p[2]=d[2],p[3]=d[3],p[4]=d[4],p[5]=d[5],p[6]=+d[6]+i,p[7]=+d[7]+o;break;case"V":p[1]=+d[1]+o;break;case"H":p[1]=+d[1]+i;break;case"R":for(var y=2,m=(e=[i,o].concat(d.slice(1))).length;y<m;y++)e[y]=+e[y]+i,e[++y]=+e[y]+o;r.pop(),r=r.concat(c(e,h));break;case"O":r.pop(),(e=s(i,o,d[1],d[2])).push(e[0]),r=r.concat(e);break;case"U":r.pop(),r=r.concat(s(i,o,d[1],d[2],d[3])),p=["U"].concat(r[r.length-1].slice(-2));break;case"M":a=+d[1]+i,f=+d[2]+o;break;default:for(y=1,m=d.length;y<m;y++)p[y]=+d[y]+(y%2?i:o)}else if("R"===n)e=[i,o].concat(d.slice(1)),r.pop(),r=r.concat(c(e,h)),p=["R"].concat(d.slice(-2));else if("O"===n)r.pop(),(e=s(i,o,d[1],d[2])).push(e[0]),r=r.concat(e);else if("U"===n)r.pop(),r=r.concat(s(i,o,d[1],d[2],d[3])),p=["U"].concat(r[r.length-1].slice(-2));else for(var b=0,x=d.length;b<x;b++)p[b]=d[b];if("O"!==(n=n.toUpperCase()))switch(p[0]){case"Z":i=+a,o=+f;break;case"H":i=p[1];break;case"V":o=p[1];break;case"M":a=p[p.length-2],f=p[p.length-1];break;default:i=p[p.length-2],o=p[p.length-1]}}return r},l=function(t,n,e,r){return[t,n,e,r,e,r]},h=function(t,n,e,r,i,o){return[1/3*t+2/3*e,1/3*n+2/3*r,1/3*i+2/3*e,1/3*o+2/3*r,i,o]},p=function(t,n,e,r,i,o,a,u,c,s){e===r&&(e+=1);var f,l,h,d,v,g=120*Math.PI/180,y=Math.PI/180*(+i||0),m=[],b=function(t,n,e){return{x:t*Math.cos(e)-n*Math.sin(e),y:t*Math.sin(e)+n*Math.cos(e)}};if(s)l=s[0],h=s[1],d=s[2],v=s[3];else{t=(f=b(t,n,-y)).x,n=f.y,u=(f=b(u,c,-y)).x,c=f.y,t===u&&n===c&&(u+=1,c+=1);var x=(t-u)/2,M=(n-c)/2,w=x*x/(e*e)+M*M/(r*r);w>1&&(e*=w=Math.sqrt(w),r*=w);var _=e*e,A=r*r,O=(o===a?-1:1)*Math.sqrt(Math.abs((_*A-_*M*M-A*x*x)/(_*M*M+A*x*x)));d=O*e*M/r+(t+u)/2,v=O*-r*x/e+(n+c)/2,l=Math.asin(((n-v)/r).toFixed(9)),h=Math.asin(((c-v)/r).toFixed(9)),l=t<d?Math.PI-l:l,h=u<d?Math.PI-h:h,l<0&&(l=2*Math.PI+l),h<0&&(h=2*Math.PI+h),a&&l>h&&(l-=2*Math.PI),!a&&h>l&&(h-=2*Math.PI)}var S=h-l;if(Math.abs(S)>g){var P=h,C=u,j=c;h=l+g*(a&&h>l?1:-1),u=d+e*Math.cos(h),c=v+r*Math.sin(h),m=p(u,c,e,r,i,0,a,C,j,[h,P,d,v])}S=h-l;var T=Math.cos(l),k=Math.sin(l),E=Math.cos(h),B=Math.sin(h),I=Math.tan(S/4),D=4/3*e*I,N=4/3*r*I,F=[t,n],L=[t+D*k,n-N*T],R=[u+D*B,c-N*E],G=[u,c];if(L[0]=2*F[0]-L[0],L[1]=2*F[1]-L[1],s)return[L,R,G].concat(m);for(var V=[],W=0,q=(m=[L,R,G].concat(m).join().split(",")).length;W<q;W++)V[W]=W%2?b(m[W-1],m[W],y).y:b(m[W],m[W+1],y).x;return V},d=function(t,n){var e,r=f(t),i=n&&f(n),o={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},a={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},u=[],c=[],s="",d="",v=function(t,n,e){var r,i;if(!t)return["C",n.x,n.y,n.x,n.y,n.x,n.y];switch(!(t[0]in{T:1,Q:1})&&(n.qx=n.qy=null),t[0]){case"M":n.X=t[1],n.Y=t[2];break;case"A":t=["C"].concat(p.apply(0,[n.x,n.y].concat(t.slice(1))));break;case"S":"C"===e||"S"===e?(r=2*n.x-n.bx,i=2*n.y-n.by):(r=n.x,i=n.y),t=["C",r,i].concat(t.slice(1));break;case"T":"Q"===e||"T"===e?(n.qx=2*n.x-n.qx,n.qy=2*n.y-n.qy):(n.qx=n.x,n.qy=n.y),t=["C"].concat(h(n.x,n.y,n.qx,n.qy,t[1],t[2]));break;case"Q":n.qx=t[1],n.qy=t[2],t=["C"].concat(h(n.x,n.y,t[1],t[2],t[3],t[4]));break;case"L":t=["C"].concat(l(n.x,n.y,t[1],t[2]));break;case"H":t=["C"].concat(l(n.x,n.y,t[1],n.y));break;case"V":t=["C"].concat(l(n.x,n.y,n.x,t[1]));break;case"Z":t=["C"].concat(l(n.x,n.y,n.X,n.Y))}return t},g=function(t,n){if(t[n].length>7){t[n].shift();for(var o=t[n];o.length;)u[n]="A",i&&(c[n]="A"),t.splice(n++,0,["C"].concat(o.splice(0,6)));t.splice(n,1),e=Math.max(r.length,i&&i.length||0)}},y=function(t,n,o,a,u){t&&n&&"M"===t[u][0]&&"M"!==n[u][0]&&(n.splice(u,0,["M",a.x,a.y]),o.bx=0,o.by=0,o.x=t[u][1],o.y=t[u][2],e=Math.max(r.length,i&&i.length||0))};e=Math.max(r.length,i&&i.length||0);for(var m=0;m<e;m++){r[m]&&(s=r[m][0]),"C"!==s&&(u[m]=s,m&&(d=u[m-1])),r[m]=v(r[m],o,d),"A"!==u[m]&&"C"===s&&(u[m]="C"),g(r,m),i&&(i[m]&&(s=i[m][0]),"C"!==s&&(c[m]=s,m&&(d=c[m-1])),i[m]=v(i[m],a,d),"A"!==c[m]&&"C"===s&&(c[m]="C"),g(i,m)),y(r,i,o,a,m),y(i,r,a,o,m);var b=r[m],x=i&&i[m],M=b.length,w=i&&x.length;o.x=b[M-2],o.y=b[M-1],o.bx=parseFloat(b[M-4])||o.x,o.by=parseFloat(b[M-3])||o.y,a.bx=i&&(parseFloat(x[w-4])||a.x),a.by=i&&(parseFloat(x[w-3])||a.y),a.x=i&&x[w-2],a.y=i&&x[w-1]}return i?[r,i]:r},v=/,?([a-z]),?/gi,g=function(t){return t.join(",").replace(v,"$1")},y=function(t,n,e,r,i){return t*(t*(-3*n+9*e-9*r+3*i)+6*n-12*e+6*r)-3*n+3*e},m=function(t,n,e,r,i,o,a,u,c){null===c&&(c=1);for(var s=(c=c>1?1:c<0?0:c)/2,f=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],l=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],h=0,p=0;p<12;p++){var d=s*f[p]+s,v=y(d,t,e,i,a),g=y(d,n,r,o,u),m=v*v+g*g;h+=l[p]*Math.sqrt(m)}return s*h},b=function(t,n,e,r,i,o,a,u){for(var c,s,f,l,h=[],p=[[],[]],d=0;d<2;++d)if(0===d?(s=6*t-12*e+6*i,c=-3*t+9*e-9*i+3*a,f=3*e-3*t):(s=6*n-12*r+6*o,c=-3*n+9*r-9*o+3*u,f=3*r-3*n),Math.abs(c)<1e-12){if(Math.abs(s)<1e-12)continue;(l=-f/s)>0&&l<1&&h.push(l)}else{var v=s*s-4*f*c,g=Math.sqrt(v);if(!(v<0)){var y=(-s+g)/(2*c);y>0&&y<1&&h.push(y);var m=(-s-g)/(2*c);m>0&&m<1&&h.push(m)}}for(var b,x=h.length,M=x;x--;)b=1-(l=h[x]),p[0][x]=b*b*b*t+3*b*b*l*e+3*b*l*l*i+l*l*l*a,p[1][x]=b*b*b*n+3*b*b*l*r+3*b*l*l*o+l*l*l*u;return p[0][M]=t,p[1][M]=n,p[0][M+1]=a,p[1][M+1]=u,p[0].length=p[1].length=M+2,{min:{x:Math.min.apply(0,p[0]),y:Math.min.apply(0,p[1])},max:{x:Math.max.apply(0,p[0]),y:Math.max.apply(0,p[1])}}},x=function(t,n,e,r,i,o,a,u){if(!(Math.max(t,e)<Math.min(i,a)||Math.min(t,e)>Math.max(i,a)||Math.max(n,r)<Math.min(o,u)||Math.min(n,r)>Math.max(o,u))){var c=(t-e)*(o-u)-(n-r)*(i-a);if(c){var s=((t*r-n*e)*(i-a)-(t-e)*(i*u-o*a))/c,f=((t*r-n*e)*(o-u)-(n-r)*(i*u-o*a))/c,l=+s.toFixed(2),h=+f.toFixed(2);if(!(l<+Math.min(t,e).toFixed(2)||l>+Math.max(t,e).toFixed(2)||l<+Math.min(i,a).toFixed(2)||l>+Math.max(i,a).toFixed(2)||h<+Math.min(n,r).toFixed(2)||h>+Math.max(n,r).toFixed(2)||h<+Math.min(o,u).toFixed(2)||h>+Math.max(o,u).toFixed(2)))return{x:s,y:f}}}},M=function(t,n,e){return n>=t.x&&n<=t.x+t.width&&e>=t.y&&e<=t.y+t.height},w=function(t,n,e,r,i){if(i)return[["M",+t+ +i,n],["l",e-2*i,0],["a",i,i,0,0,1,i,i],["l",0,r-2*i],["a",i,i,0,0,1,-i,i],["l",2*i-e,0],["a",i,i,0,0,1,-i,-i],["l",0,2*i-r],["a",i,i,0,0,1,i,-i],["z"]];var o=[["M",t,n],["l",e,0],["l",0,r],["l",-e,0],["z"]];return o.parsePathArray=g,o},_=function(t,n,e,r){return null===t&&(t=n=e=r=0),null===n&&(n=t.y,e=t.width,r=t.height,t=t.x),{x:t,y:n,width:e,w:e,height:r,h:r,x2:t+e,y2:n+r,cx:t+e/2,cy:n+r/2,r1:Math.min(e,r)/2,r2:Math.max(e,r)/2,r0:Math.sqrt(e*e+r*r)/2,path:w(t,n,e,r),vb:[t,n,e,r].join(" ")}},A=function(t,n,e,i,o,a,u,c){Object(r.b)(t)||(t=[t,n,e,i,o,a,u,c]);var s=b.apply(null,t);return _(s.min.x,s.min.y,s.max.x-s.min.x,s.max.y-s.min.y)},O=function(t,n,e,r,i,o,a,u,c){var s=1-c,f=Math.pow(s,3),l=Math.pow(s,2),h=c*c,p=h*c,d=t+2*c*(e-t)+h*(i-2*e+t),v=n+2*c*(r-n)+h*(o-2*r+n),g=e+2*c*(i-e)+h*(a-2*i+e),y=r+2*c*(o-r)+h*(u-2*o+r);return{x:f*t+3*l*c*e+3*s*c*c*i+p*a,y:f*n+3*l*c*r+3*s*c*c*o+p*u,m:{x:d,y:v},n:{x:g,y:y},start:{x:s*t+c*e,y:s*n+c*r},end:{x:s*i+c*a,y:s*o+c*u},alpha:90-180*Math.atan2(d-g,v-y)/Math.PI}},S=function(t,n,e){if(!function(t,n){return t=_(t),n=_(n),M(n,t.x,t.y)||M(n,t.x2,t.y)||M(n,t.x,t.y2)||M(n,t.x2,t.y2)||M(t,n.x,n.y)||M(t,n.x2,n.y)||M(t,n.x,n.y2)||M(t,n.x2,n.y2)||(t.x<n.x2&&t.x>n.x||n.x<t.x2&&n.x>t.x)&&(t.y<n.y2&&t.y>n.y||n.y<t.y2&&n.y>t.y)}(A(t),A(n)))return e?0:[];for(var r=~~(m.apply(0,t)/8),i=~~(m.apply(0,n)/8),o=[],a=[],u={},c=e?0:[],s=0;s<r+1;s++){var f=O.apply(0,t.concat(s/r));o.push({x:f.x,y:f.y,t:s/r})}for(s=0;s<i+1;s++){f=O.apply(0,n.concat(s/i));a.push({x:f.x,y:f.y,t:s/i})}for(s=0;s<r;s++)for(var l=0;l<i;l++){var h=o[s],p=o[s+1],d=a[l],v=a[l+1],g=Math.abs(p.x-h.x)<.001?"y":"x",y=Math.abs(v.x-d.x)<.001?"y":"x",b=x(h.x,h.y,p.x,p.y,d.x,d.y,v.x,v.y);if(b){if(u[b.x.toFixed(4)]===b.y.toFixed(4))continue;u[b.x.toFixed(4)]=b.y.toFixed(4);var w=h.t+Math.abs((b[g]-h[g])/(p[g]-h[g]))*(p.t-h.t),S=d.t+Math.abs((b[y]-d[y])/(v[y]-d[y]))*(v.t-d.t);w>=0&&w<=1&&S>=0&&S<=1&&(e?c+=1:c.push({x:b.x,y:b.y,t1:w,t2:S}))}}return c},P=function(t,n){return function(t,n,e){var r,i,o,a,u,c,s,f,l,h;t=d(t),n=d(n);for(var p=e?0:[],v=0,g=t.length;v<g;v++){var y=t[v];if("M"===y[0])r=u=y[1],i=c=y[2];else{"C"===y[0]?(l=[r,i].concat(y.slice(1)),r=l[6],i=l[7]):(l=[r,i,r,i,u,c,u,c],r=u,i=c);for(var m=0,b=n.length;m<b;m++){var x=n[m];if("M"===x[0])o=s=x[1],a=f=x[2];else{"C"===x[0]?(h=[o,a].concat(x.slice(1)),o=h[6],a=h[7]):(h=[o,a,o,a,s,f,s,f],o=s,a=f);var M=S(l,h,e);if(e)p+=M;else{for(var w=0,_=M.length;w<_;w++)M[w].segment1=v,M[w].segment2=m,M[w].bez1=l,M[w].bez2=h;p=p.concat(M)}}}}}return p}(t,n)};function C(t,n){var e=[],r=[];return t.length&&function t(n,i){if(1===n.length)e.push(n[0]),r.push(n[0]);else{for(var o=[],a=0;a<n.length-1;a++)0===a&&e.push(n[0]),a===n.length-2&&r.push(n[a+1]),o[a]=[(1-i)*n[a][0]+i*n[a+1][0],(1-i)*n[a][1]+i*n[a+1][1]];t(o,i)}}(t,n),{left:e,right:r.reverse()}}var j=function(t,n,e){if(1===e)return[[].concat(t)];var r=[];if("L"===n[0]||"C"===n[0]||"Q"===n[0])r=r.concat(function(t,n,e){var r=[[t[1],t[2]]];e=e||2;var i=[];"A"===n[0]?(r.push(n[6]),r.push(n[7])):"C"===n[0]?(r.push([n[1],n[2]]),r.push([n[3],n[4]]),r.push([n[5],n[6]])):"S"===n[0]||"Q"===n[0]?(r.push([n[1],n[2]]),r.push([n[3],n[4]])):r.push([n[1],n[2]]);for(var o=r,a=1/e,u=0;u<e-1;u++){var c=C(o,a/(1-a*u));i.push(c.left),o=c.right}return i.push(o),i.map((function(t){var n=[];return 4===t.length&&(n.push("C"),n=n.concat(t[2])),t.length>=3&&(3===t.length&&n.push("Q"),n=n.concat(t[1])),2===t.length&&n.push("L"),n=n.concat(t[t.length-1])}))}(t,n,e));else{var i=[].concat(t);"M"===i[0]&&(i[0]="L");for(var o=0;o<=e-1;o++)r.push(i)}return r},T=function(t,n){if(1===t.length)return t;var e=t.length-1,r=n.length-1,i=e/r,o=[];if(1===t.length&&"M"===t[0][0]){for(var a=0;a<r-e;a++)t.push(t[0]);return t}for(a=0;a<r;a++){var u=Math.floor(i*a);o[u]=(o[u]||0)+1}var c=o.reduce((function(n,r,i){return i===e?n.concat(t[e]):n.concat(j(t[i],t[i+1],r))}),[]);return c.unshift(t[0]),"Z"!==n[r]&&"z"!==n[r]||c.push("Z"),c},k=function(t,n){if(t.length!==n.length)return!1;var e=!0;return Object(r.a)(t,(function(t,r){if(t!==n[r])return e=!1,!1})),e};function E(t,n,e){var r=null,i=e;return n<i&&(i=n,r="add"),t<i&&(i=t,r="del"),{type:r,min:i}}var B=function(t,n){var e=function(t,n){var e,r,i=t.length,o=n.length,a=0;if(0===i||0===o)return null;for(var u=[],c=0;c<=i;c++)u[c]=[],u[c][0]={min:c};for(var s=0;s<=o;s++)u[0][s]={min:s};for(c=1;c<=i;c++){e=t[c-1];for(s=1;s<=o;s++){r=n[s-1],a=k(e,r)?0:1;var f=u[c-1][s].min+1,l=u[c][s-1].min+1,h=u[c-1][s-1].min+a;u[c][s]=E(f,l,h)}}return u}(t,n),r=t.length,i=n.length,o=[],a=1,u=1;if(e[r][i].min!==r){for(var c=1;c<=r;c++){var s=e[c][c].min;u=c;for(var f=a;f<=i;f++)e[c][f].min<s&&(s=e[c][f].min,u=f);a=u,e[c][a].type&&o.push({index:c-1,type:e[c][a].type})}for(c=o.length-1;c>=0;c--)a=o[c].index,"add"===o[c].type?t.splice(a,0,[].concat(t[a])):t.splice(a,1)}var l=i-(r=t.length);if(r<i)for(c=0;c<l;c++)"z"===t[r-1][0]||"Z"===t[r-1][0]?t.splice(r-2,0,t[r-2]):t.push(t[r-1]),r+=1;return t};function I(t,n,e){for(var r,i=[].concat(t),o=1/(e+1),a=D(n)[0],u=1;u<=e;u++)o*=u,0===(r=Math.floor(t.length*o))?i.unshift([a[0]*o+t[r][0]*(1-o),a[1]*o+t[r][1]*(1-o)]):i.splice(r,0,[a[0]*o+t[r][0]*(1-o),a[1]*o+t[r][1]*(1-o)]);return i}function D(t){var n=[];switch(t[0]){case"M":case"L":n.push([t[1],t[2]]);break;case"A":n.push([t[6],t[7]]);break;case"Q":n.push([t[3],t[4]]),n.push([t[1],t[2]]);break;case"T":n.push([t[1],t[2]]);break;case"C":n.push([t[5],t[6]]),n.push([t[1],t[2]]),n.push([t[3],t[4]]);break;case"S":n.push([t[3],t[4]]),n.push([t[1],t[2]]);break;case"H":case"V":n.push([t[1],t[1]])}return n}var N=function(t,n){if(t.length<=1)return t;for(var e,r=0;r<n.length;r++)if(t[r][0]!==n[r][0])switch(e=D(t[r]),n[r][0]){case"M":t[r]=["M"].concat(e[0]);break;case"L":t[r]=["L"].concat(e[0]);break;case"A":t[r]=[].concat(n[r]),t[r][6]=e[0][0],t[r][7]=e[0][1];break;case"Q":if(e.length<2){if(!(r>0)){t[r]=n[r];break}e=I(e,t[r-1],1)}t[r]=["Q"].concat(e.reduce((function(t,n){return t.concat(n)}),[]));break;case"T":t[r]=["T"].concat(e[0]);break;case"C":if(e.length<3){if(!(r>0)){t[r]=n[r];break}e=I(e,t[r-1],2)}t[r]=["C"].concat(e.reduce((function(t,n){return t.concat(n)}),[]));break;case"S":if(e.length<2){if(!(r>0)){t[r]=n[r];break}e=I(e,t[r-1],1)}t[r]=["S"].concat(e.reduce((function(t,n){return t.concat(n)}),[]));break;default:t[r]=n[r]}return t}},function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){return u}));var r=e(1),i=e(17);function o(t,n,e){var i=1;return Object(r.h)(t)&&(i=t.split("\n").length),i>1?n*i+function(t,n){return n?n-t:.14*t}(n,e)*(i-1):n}function a(t,n){var e=Object(i.a)(),o=0;if(Object(r.e)(t)||""===t)return o;if(e.save(),e.font=n,Object(r.h)(t)&&t.includes("\n")){var a=t.split("\n");Object(r.a)(a,(function(t){var n=e.measureText(t).width;o<n&&(o=n)}))}else o=e.measureText(t).width;return e.restore(),o}function u(t){var n=t.fontSize,e=t.fontFamily,r=t.fontWeight;return[t.fontStyle,t.fontVariant,r,n+"px",e].join(" ").trim()}},function(t,n,e){"use strict";e.r(n),e.d(n,"version",(function(){return m}));var r=e(11);e.d(n,"PathUtil",(function(){return r}));var i=e(25);for(var o in i)["default","Event","Base","AbstractCanvas","AbstractGroup","AbstractShape","PathUtil","getBBoxMethod","registerBBox","getTextHeight","assembleFont","isAllowCapture","multiplyVec2","invert","getOffScreenContext","registerEasing","version"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);var a=e(26);for(var o in a)["default","Event","Base","AbstractCanvas","AbstractGroup","AbstractShape","PathUtil","getBBoxMethod","registerBBox","getTextHeight","assembleFont","isAllowCapture","multiplyVec2","invert","getOffScreenContext","registerEasing","version"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);var u=e(16);e.d(n,"Event",(function(){return u.a}));var c=e(19);e.d(n,"Base",(function(){return c.a}));var s=e(30);e.d(n,"AbstractCanvas",(function(){return s.a}));var f=e(28);e.d(n,"AbstractGroup",(function(){return f.a}));var l=e(29);e.d(n,"AbstractShape",(function(){return l.a}));var h=e(24);e.d(n,"getBBoxMethod",(function(){return h.a})),e.d(n,"registerBBox",(function(){return h.b}));var p=e(12);e.d(n,"getTextHeight",(function(){return p.b})),e.d(n,"assembleFont",(function(){return p.a}));var d=e(1);e.d(n,"isAllowCapture",(function(){return d.b}));var v=e(7);e.d(n,"multiplyVec2",(function(){return v.c})),e.d(n,"invert",(function(){return v.a}));var g=e(17);e.d(n,"getOffScreenContext",(function(){return g.a}));var y=e(18);e.d(n,"registerEasing",(function(){return y.b}));var m="0.5.11"},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.setClip=n.setTransform=n.setShadow=void 0;var r=e(9);n.setShadow=function(t,n){var e=t.cfg.el,r=t.attr(),i={dx:r.shadowOffsetX,dy:r.shadowOffsetY,blur:r.shadowBlur,color:r.shadowColor};if(i.dx||i.dy||i.blur||i.color){var o=n.find("filter",i);o||(o=n.addShadow(i)),e.setAttribute("filter","url(#".concat(o,")"))}else e.removeAttribute("filter")},n.setTransform=function(t){var n=t.attr().matrix;if(n){for(var e=t.cfg.el,r=[],i=0;i<9;i+=3)r.push("".concat(n[i],",").concat(n[i+1]));-1===(r=r.join(",")).indexOf("NaN")?e.setAttribute("transform","matrix(".concat(r,")")):console.warn("invalid matrix:",n)}},n.setClip=function(t,n){var e=t.getClip(),i=t.get("el");if(e){if(e&&!i.hasAttribute("clip-path")){(0,r.createDom)(e),e.createPath(n);var o=n.addClip(e);i.setAttribute("clip-path","url(#".concat(o,")"))}}else i.removeAttribute("clip-path")}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Text=n.Rect=n.Polyline=n.Polygon=n.Path=n.Marker=n.Line=n.Image=n.Ellipse=n.Dom=n.Circle=n.Base=void 0;var r=e(8);Object.defineProperty(n,"Base",{enumerable:!0,get:function(){return r.default}});var i=e(41);Object.defineProperty(n,"Circle",{enumerable:!0,get:function(){return i.default}});var o=e(42);Object.defineProperty(n,"Dom",{enumerable:!0,get:function(){return o.default}});var a=e(43);Object.defineProperty(n,"Ellipse",{enumerable:!0,get:function(){return a.default}});var u=e(44);Object.defineProperty(n,"Image",{enumerable:!0,get:function(){return u.default}});var c=e(45);Object.defineProperty(n,"Line",{enumerable:!0,get:function(){return c.default}});var s=e(46);Object.defineProperty(n,"Marker",{enumerable:!0,get:function(){return s.default}});var f=e(48);Object.defineProperty(n,"Path",{enumerable:!0,get:function(){return f.default}});var l=e(49);Object.defineProperty(n,"Polygon",{enumerable:!0,get:function(){return l.default}});var h=e(50);Object.defineProperty(n,"Polyline",{enumerable:!0,get:function(){return h.default}});var p=e(51);Object.defineProperty(n,"Rect",{enumerable:!0,get:function(){return p.default}});var d=e(53);Object.defineProperty(n,"Text",{enumerable:!0,get:function(){return d.default}})},function(t,n,e){"use strict";var r=function(){function t(t,n){this.bubbles=!0,this.target=null,this.currentTarget=null,this.delegateTarget=null,this.delegateObject=null,this.defaultPrevented=!1,this.propagationStopped=!1,this.shape=null,this.fromShape=null,this.toShape=null,this.propagationPath=[],this.type=t,this.name=t,this.originalEvent=n,this.timeStamp=n.timeStamp}return t.prototype.preventDefault=function(){this.defaultPrevented=!0,this.originalEvent.preventDefault&&this.originalEvent.preventDefault()},t.prototype.stopPropagation=function(){this.propagationStopped=!0},t.prototype.toString=function(){return"[Event (type="+this.type+")]"},t.prototype.save=function(){},t.prototype.restore=function(){},t}();n.a=r},function(t,n,e){"use strict";e.d(n,"a",(function(){return i}));var r=null;function i(){if(!r){var t=document.createElement("canvas");t.width=1,t.height=1,r=t.getContext("2d")}return r}},function(t,n,e){"use strict";e.d(n,"a",(function(){return L})),e.d(n,"b",(function(){return R}));var r={};function i(t){return+t}function o(t){return t*t}function a(t){return t*(2-t)}function u(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}function c(t){return t*t*t}function s(t){return--t*t*t+1}function f(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}e.r(r),e.d(r,"easeLinear",(function(){return i})),e.d(r,"easeQuad",(function(){return u})),e.d(r,"easeQuadIn",(function(){return o})),e.d(r,"easeQuadOut",(function(){return a})),e.d(r,"easeQuadInOut",(function(){return u})),e.d(r,"easeCubic",(function(){return f})),e.d(r,"easeCubicIn",(function(){return c})),e.d(r,"easeCubicOut",(function(){return s})),e.d(r,"easeCubicInOut",(function(){return f})),e.d(r,"easePoly",(function(){return p})),e.d(r,"easePolyIn",(function(){return l})),e.d(r,"easePolyOut",(function(){return h})),e.d(r,"easePolyInOut",(function(){return p})),e.d(r,"easeSin",(function(){return m})),e.d(r,"easeSinIn",(function(){return g})),e.d(r,"easeSinOut",(function(){return y})),e.d(r,"easeSinInOut",(function(){return m})),e.d(r,"easeExp",(function(){return w})),e.d(r,"easeExpIn",(function(){return x})),e.d(r,"easeExpOut",(function(){return M})),e.d(r,"easeExpInOut",(function(){return w})),e.d(r,"easeCircle",(function(){return O})),e.d(r,"easeCircleIn",(function(){return _})),e.d(r,"easeCircleOut",(function(){return A})),e.d(r,"easeCircleInOut",(function(){return O})),e.d(r,"easeBounce",(function(){return C})),e.d(r,"easeBounceIn",(function(){return P})),e.d(r,"easeBounceOut",(function(){return C})),e.d(r,"easeBounceInOut",(function(){return j})),e.d(r,"easeBack",(function(){return E})),e.d(r,"easeBackIn",(function(){return T})),e.d(r,"easeBackOut",(function(){return k})),e.d(r,"easeBackInOut",(function(){return E})),e.d(r,"easeElastic",(function(){return D})),e.d(r,"easeElasticIn",(function(){return I})),e.d(r,"easeElasticOut",(function(){return D})),e.d(r,"easeElasticInOut",(function(){return N}));var l=function t(n){function e(t){return Math.pow(t,n)}return n=+n,e.exponent=t,e}(3),h=function t(n){function e(t){return 1-Math.pow(1-t,n)}return n=+n,e.exponent=t,e}(3),p=function t(n){function e(t){return((t*=2)<=1?Math.pow(t,n):2-Math.pow(2-t,n))/2}return n=+n,e.exponent=t,e}(3),d=Math.PI,v=d/2;function g(t){return 1==+t?1:1-Math.cos(t*v)}function y(t){return Math.sin(t*v)}function m(t){return(1-Math.cos(d*t))/2}function b(t){return 1.0009775171065494*(Math.pow(2,-10*t)-.0009765625)}function x(t){return b(1-+t)}function M(t){return 1-b(t)}function w(t){return((t*=2)<=1?b(1-t):2-b(t-1))/2}function _(t){return 1-Math.sqrt(1-t*t)}function A(t){return Math.sqrt(1- --t*t)}function O(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var S=7.5625;function P(t){return 1-C(1-t)}function C(t){return(t=+t)<4/11?S*t*t:t<8/11?S*(t-=6/11)*t+3/4:t<10/11?S*(t-=9/11)*t+15/16:S*(t-=21/22)*t+63/64}function j(t){return((t*=2)<=1?1-C(1-t):C(t-1)+1)/2}var T=function t(n){function e(t){return(t=+t)*t*(n*(t-1)+t)}return n=+n,e.overshoot=t,e}(1.70158),k=function t(n){function e(t){return--t*t*((t+1)*n+t)+1}return n=+n,e.overshoot=t,e}(1.70158),E=function t(n){function e(t){return((t*=2)<1?t*t*((n+1)*t-n):(t-=2)*t*((n+1)*t+n)+2)/2}return n=+n,e.overshoot=t,e}(1.70158),B=2*Math.PI,I=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=B);function i(t){return n*b(- --t)*Math.sin((r-t)/e)}return i.amplitude=function(n){return t(n,e*B)},i.period=function(e){return t(n,e)},i}(1,.3),D=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=B);function i(t){return 1-n*b(t=+t)*Math.sin((t+r)/e)}return i.amplitude=function(n){return t(n,e*B)},i.period=function(e){return t(n,e)},i}(1,.3),N=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=B);function i(t){return((t=2*t-1)<0?n*b(-t)*Math.sin((r-t)/e):2-n*b(t)*Math.sin((r+t)/e))/2}return i.amplitude=function(n){return t(n,e*B)},i.period=function(e){return t(n,e)},i}(1,.3),F={};function L(t){return F[t.toLowerCase()]||r[t]}function R(t,n){F[t.toLowerCase()]=n}},function(t,n,e){"use strict";var r=e(6),i=function(){function t(){this._events={}}return t.prototype.on=function(t,n,e){return this._events[t]||(this._events[t]=[]),this._events[t].push({callback:n,once:!!e}),this},t.prototype.once=function(t,n){return this.on(t,n,!0)},t.prototype.emit=function(t){for(var n=this,e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var i=this._events[t]||[],o=this._events["*"]||[],a=function(r){for(var i=r.length,o=0;o<i;o++)if(r[o]){var a=r[o],u=a.callback;a.once&&(r.splice(o,1),0===r.length&&delete n._events[t],i--,o--),u.apply(n,e)}};a(i),a(o)},t.prototype.off=function(t,n){if(t)if(n){for(var e=this._events[t]||[],r=e.length,i=0;i<r;i++)e[i].callback===n&&(e.splice(i,1),r--,i--);0===e.length&&delete this._events[t]}else delete this._events[t];else this._events={};return this},t.prototype.getEvents=function(){return this._events},t}(),o=e(1),a=function(t){function n(n){var e=t.call(this)||this;e.destroyed=!1;var r=e.getDefaultCfg();return e.cfg=Object(o.i)(r,n),e}return Object(r.a)(n,t),n.prototype.getDefaultCfg=function(){return{}},n.prototype.get=function(t){return this.cfg[t]},n.prototype.set=function(t,n){this.cfg[t]=n},n.prototype.destroy=function(){this.cfg={destroyed:!0},this.off(),this.destroyed=!0},n}(i);n.a=a},function(t,n,e){"use strict";var r=e(6),i=e(0),o=e(21),a=e(1),u={};var c=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return Object(r.a)(n,t),n.prototype.isCanvas=function(){return!1},n.prototype.getBBox=function(){var t=1/0,n=-1/0,e=1/0,r=-1/0,o=[],u=[],c=this.getChildren().filter((function(t){return t.get("visible")&&(!t.isGroup()||t.isGroup()&&t.getChildren().length>0)}));return c.length>0?(Object(a.a)(c,(function(t){var n=t.getBBox();o.push(n.minX,n.maxX),u.push(n.minY,n.maxY)})),t=Object(i.k)(o),n=Object(i.j)(o),e=Object(i.k)(u),r=Object(i.j)(u)):(t=0,n=0,e=0,r=0),{x:t,y:e,minX:t,minY:e,maxX:n,maxY:r,width:n-t,height:r-e}},n.prototype.getCanvasBBox=function(){var t=1/0,n=-1/0,e=1/0,r=-1/0,o=[],u=[],c=this.getChildren().filter((function(t){return t.get("visible")&&(!t.isGroup()||t.isGroup()&&t.getChildren().length>0)}));return c.length>0?(Object(a.a)(c,(function(t){var n=t.getCanvasBBox();o.push(n.minX,n.maxX),u.push(n.minY,n.maxY)})),t=Object(i.k)(o),n=Object(i.j)(o),e=Object(i.k)(u),r=Object(i.j)(u)):(t=0,n=0,e=0,r=0),{x:t,y:e,minX:t,minY:e,maxX:n,maxY:r,width:n-t,height:r-e}},n.prototype.getDefaultCfg=function(){var n=t.prototype.getDefaultCfg.call(this);return n.children=[],n},n.prototype.onAttrChange=function(n,e,r){if(t.prototype.onAttrChange.call(this,n,e,r),"matrix"===n){var i=this.getTotalMatrix();this._applyChildrenMarix(i)}},n.prototype.applyMatrix=function(n){var e=this.getTotalMatrix();t.prototype.applyMatrix.call(this,n);var r=this.getTotalMatrix();r!==e&&this._applyChildrenMarix(r)},n.prototype._applyChildrenMarix=function(t){var n=this.getChildren();Object(a.a)(n,(function(n){n.applyMatrix(t)}))},n.prototype.addShape=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=t[0],r=t[1];Object(a.f)(e)?r=e:r.type=e;var i=u[r.type];i||(i=Object(a.k)(r.type),u[r.type]=i);var o=this.getShapeBase(),c=new o[i](r);return this.add(c),c},n.prototype.addGroup=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e,r=t[0],i=t[1];if(Object(a.d)(r))e=new r(i||{parent:this});else{var o=r||{},u=this.getGroupBase();e=new u(o)}return this.add(e),e},n.prototype.getCanvas=function(){return this.isCanvas()?this:this.get("canvas")},n.prototype.getShape=function(t,n,e){if(!Object(a.b)(this))return null;var r,i=this.getChildren();if(this.isCanvas())r=this._findShape(i,t,n,e);else{var o=[t,n,1];o=this.invertFromMatrix(o),this.isClipped(o[0],o[1])||(r=this._findShape(i,o[0],o[1],e))}return r},n.prototype._findShape=function(t,n,e,r){for(var i=null,o=t.length-1;o>=0;o--){var u=t[o];if(Object(a.b)(u)&&(u.isGroup()?i=u.getShape(n,e,r):u.isHit(n,e)&&(i=u)),i)break}return i},n.prototype.add=function(t){var n=this.getCanvas(),e=this.getChildren(),r=this.get("timeline"),i=t.getParent();i&&function(t,n,e){void 0===e&&(e=!0),e?n.destroy():(n.set("parent",null),n.set("canvas",null)),Object(a.j)(t.getChildren(),n)}(i,t,!1),t.set("parent",this),n&&function t(n,e){if(n.set("canvas",e),n.isGroup()){var r=n.get("children");r.length&&r.forEach((function(n){t(n,e)}))}}(t,n),r&&function t(n,e){if(n.set("timeline",e),n.isGroup()){var r=n.get("children");r.length&&r.forEach((function(n){t(n,e)}))}}(t,r),e.push(t),t.onCanvasChange("add"),this._applyElementMatrix(t)},n.prototype._applyElementMatrix=function(t){var n=this.getTotalMatrix();n&&t.applyMatrix(n)},n.prototype.getChildren=function(){return this.get("children")},n.prototype.sort=function(){var t,n=this.getChildren();Object(a.a)(n,(function(t,n){return t._INDEX=n,t})),n.sort((t=function(t,n){return t.get("zIndex")-n.get("zIndex")},function(n,e){var r=t(n,e);return 0===r?n._INDEX-e._INDEX:r})),this.onCanvasChange("sort")},n.prototype.clear=function(){if(this.set("clearing",!0),!this.destroyed){for(var t=this.getChildren(),n=t.length-1;n>=0;n--)t[n].destroy();this.set("children",[]),this.onCanvasChange("clear"),this.set("clearing",!1)}},n.prototype.destroy=function(){this.get("destroyed")||(this.clear(),t.prototype.destroy.call(this))},n.prototype.getFirst=function(){return this.getChildByIndex(0)},n.prototype.getLast=function(){var t=this.getChildren();return this.getChildByIndex(t.length-1)},n.prototype.getChildByIndex=function(t){return this.getChildren()[t]},n.prototype.getCount=function(){return this.getChildren().length},n.prototype.contain=function(t){return this.getChildren().indexOf(t)>-1},n.prototype.removeChild=function(t,n){void 0===n&&(n=!0),this.contain(t)&&t.remove(n)},n.prototype.findAll=function(t){var n=[],e=this.getChildren();return Object(a.a)(e,(function(e){t(e)&&n.push(e),e.isGroup()&&(n=n.concat(e.findAll(t)))})),n},n.prototype.find=function(t){var n=null,e=this.getChildren();return Object(a.a)(e,(function(e){if(t(e)?n=e:e.isGroup()&&(n=e.find(t)),n)return!1})),n},n.prototype.findById=function(t){return this.find((function(n){return n.get("id")===t}))},n.prototype.findByClassName=function(t){return this.find((function(n){return n.get("className")===t}))},n.prototype.findAllByName=function(t){return this.findAll((function(n){return n.get("name")===t}))},n}(o.a);n.a=c},function(t,n,e){"use strict";var r={};e.r(r),e.d(r,"leftTranslate",(function(){return u})),e.d(r,"leftRotate",(function(){return c})),e.d(r,"leftScale",(function(){return s})),e.d(r,"transform",(function(){return f})),e.d(r,"direction",(function(){return l})),e.d(r,"angleTo",(function(){return h})),e.d(r,"vertical",(function(){return p}));var i=e(6),o=e(0),a=e(2);function u(t,n,e){var r=[0,0,0,0,0,0,0,0,0];return a.a.fromTranslation(r,e),a.a.multiply(t,r,n)}function c(t,n,e){var r=[0,0,0,0,0,0,0,0,0];return a.a.fromRotation(r,e),a.a.multiply(t,r,n)}function s(t,n,e){var r=[0,0,0,0,0,0,0,0,0];return a.a.fromScaling(r,e),a.a.multiply(t,r,n)}function f(t,n){for(var e,r,i,o=t?[].concat(t):[1,0,0,0,1,0,0,0,1],f=0,l=n.length;f<l;f++){var h=n[f];switch(h[0]){case"t":u(o,o,[h[1],h[2]]);break;case"s":s(o,o,[h[1],h[2]]);break;case"r":c(o,o,h[1]);break;case"m":e=o,r=o,i=h[1],a.a.multiply(e,i,r)}}return o}function l(t,n){return t[0]*n[1]-n[0]*t[1]}function h(t,n,e){var r=a.b.angle(t,n),i=l(t,n)>=0;return e?i?2*Math.PI-r:r:i?r:2*Math.PI-r}function p(t,n,e){return e?(t[0]=n[1],t[1]=-1*n[0]):(t[0]=-1*n[1],t[1]=n[0]),t}var d=e(1),v=e(7),g=e(19),y=r.transform,m=["zIndex","capture","visible","type"],b=["repeat"];function x(t,n){var e={},r=n.attrs;for(var i in t)e[i]=r[i];return e}function M(t,n){var e={},r=n.attr();return Object(o.a)(t,(function(t,n){-1!==b.indexOf(n)||Object(o.c)(r[n],t)||(e[n]=t)})),e}function w(t,n){if(n.onFrame)return t;var e=n.startTime,r=n.delay,i=n.duration,a=Object.prototype.hasOwnProperty;return Object(o.a)(t,(function(t){e+r<t.startTime+t.delay+t.duration&&i>t.delay&&Object(o.a)(n.toAttrs,(function(n,e){a.call(t.toAttrs,e)&&(delete t.toAttrs[e],delete t.fromAttrs[e])}))})),t}var _=function(t){function n(n){var e=t.call(this,n)||this;e.attrs={};var r=e.getDefaultAttrs();return Object(o.l)(r,n.attrs),e.attrs=r,e.initAttrs(r),e.initAnimate(),e}return Object(i.a)(n,t),n.prototype.getDefaultCfg=function(){return{visible:!0,capture:!0,zIndex:0}},n.prototype.getDefaultAttrs=function(){return{matrix:this.getDefaultMatrix(),opacity:1}},n.prototype.onCanvasChange=function(t){},n.prototype.initAttrs=function(t){},n.prototype.initAnimate=function(){this.set("animable",!0),this.set("animating",!1)},n.prototype.isGroup=function(){return!1},n.prototype.getParent=function(){return this.get("parent")},n.prototype.getCanvas=function(){return this.get("canvas")},n.prototype.attr=function(){for(var t,n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var r=n[0],i=n[1];if(!r)return this.attrs;if(Object(o.h)(r)){for(var a in r)this.setAttr(a,r[a]);return this.afterAttrsChange(r),this}return 2===n.length?(this.setAttr(r,i),this.afterAttrsChange(((t={})[r]=i,t)),this):this.attrs[r]},n.prototype.isClipped=function(t,n){var e=this.getClip();return e&&!e.isHit(t,n)},n.prototype.setAttr=function(t,n){var e=this.attrs[t];e!==n&&(this.attrs[t]=n,this.onAttrChange(t,n,e))},n.prototype.onAttrChange=function(t,n,e){"matrix"===t&&this.set("totalMatrix",null)},n.prototype.afterAttrsChange=function(t){if(this.cfg.isClipShape){var n=this.cfg.applyTo;n&&n.onCanvasChange("clip")}else this.onCanvasChange("attr")},n.prototype.show=function(){return this.set("visible",!0),this.onCanvasChange("show"),this},n.prototype.hide=function(){return this.set("visible",!1),this.onCanvasChange("hide"),this},n.prototype.setZIndex=function(t){this.set("zIndex",t);var n=this.getParent();return n&&n.sort(),this},n.prototype.toFront=function(){var t=this.getParent();if(t){var n=t.getChildren(),e=(this.get("el"),n.indexOf(this));n.splice(e,1),n.push(this),this.onCanvasChange("zIndex")}},n.prototype.toBack=function(){var t=this.getParent();if(t){var n=t.getChildren(),e=(this.get("el"),n.indexOf(this));n.splice(e,1),n.unshift(this),this.onCanvasChange("zIndex")}},n.prototype.remove=function(t){void 0===t&&(t=!0);var n=this.getParent();n?(Object(d.j)(n.getChildren(),this),n.get("clearing")||this.onCanvasChange("remove")):this.onCanvasChange("remove"),t&&this.destroy()},n.prototype.resetMatrix=function(){this.attr("matrix",this.getDefaultMatrix()),this.onCanvasChange("matrix")},n.prototype.getMatrix=function(){return this.attr("matrix")},n.prototype.setMatrix=function(t){this.attr("matrix",t),this.onCanvasChange("matrix")},n.prototype.getTotalMatrix=function(){var t=this.cfg.totalMatrix;if(!t){var n=this.attr("matrix"),e=this.cfg.parentMatrix;t=e&&n?Object(v.b)(e,n):n||e,this.set("totalMatrix",t)}return t},n.prototype.applyMatrix=function(t){var n=this.attr("matrix"),e=null;e=t&&n?Object(v.b)(t,n):n||t,this.set("totalMatrix",e),this.set("parentMatrix",t)},n.prototype.getDefaultMatrix=function(){return null},n.prototype.applyToMatrix=function(t){var n=this.attr("matrix");return n?Object(v.c)(n,t):t},n.prototype.invertFromMatrix=function(t){var n=this.attr("matrix");if(n){var e=Object(v.a)(n);if(e)return Object(v.c)(e,t)}return t},n.prototype.setClip=function(t){var n=this.getCanvas(),e=null;if(t){var r=this.getShapeBase()[Object(o.q)(t.type)];r&&(e=new r({type:t.type,isClipShape:!0,applyTo:this,attrs:t.attrs,canvas:n}))}return this.set("clipShape",e),this.onCanvasChange("clip"),e},n.prototype.getClip=function(){var t=this.cfg.clipShape;return t||null},n.prototype.clone=function(){var t=this,n=this.attrs,e={};Object(o.a)(n,(function(t,r){Object(o.b)(n[r])?e[r]=function(t){for(var n=[],e=0;e<t.length;e++)Object(o.b)(t[e])?n.push([].concat(t[e])):n.push(t[e]);return n}(n[r]):e[r]=n[r]}));var r=new(0,this.constructor)({attrs:e});return Object(o.a)(m,(function(n){r.set(n,t.get(n))})),r},n.prototype.destroy=function(){this.destroyed||(this.attrs={},t.prototype.destroy.call(this))},n.prototype.isAnimatePaused=function(){return this.get("_pause").isPaused},n.prototype.animate=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(this.get("timeline")||this.get("canvas")){this.set("animating",!0);var e=this.get("timeline");e||(e=this.get("canvas").get("timeline"),this.set("timeline",e));var r=this.get("animations")||[];e.timer||e.initTimer();var i,a,u,c,s,f=t[0],l=t[1],h=t[2],p=void 0===h?"easeLinear":h,d=t[3],v=void 0===d?o.n:d,g=t[4],y=void 0===g?0:g;Object(o.d)(f)?(i=f,f={}):Object(o.h)(f)&&f.onFrame&&(i=f.onFrame,a=f.repeat),Object(o.h)(l)?(l=(s=l).duration,p=s.easing||"easeLinear",y=s.delay||0,a=s.repeat||a||!1,v=s.callback||o.n,u=s.pauseCallback||o.n,c=s.resumeCallback||o.n):(Object(o.f)(v)&&(y=v,v=null),Object(o.d)(p)?(v=p,p="easeLinear"):p=p||"easeLinear");var m=M(f,this),b={fromAttrs:x(m,this),toAttrs:m,duration:l,easing:p,repeat:a,callback:v,pauseCallback:u,resumeCallback:c,delay:y,startTime:e.getTime(),id:Object(o.p)(),onFrame:i,pathFormatted:!1};r.length>0?r=w(r,b):e.addAnimator(this),r.push(b),this.set("animations",r),this.set("_pause",{isPaused:!1})}},n.prototype.stopAnimate=function(t){var n=this;void 0===t&&(t=!0);var e=this.get("animations");Object(o.a)(e,(function(e){t&&(e.onFrame?n.attr(e.onFrame(1)):n.attr(e.toAttrs)),e.callback&&e.callback()})),this.set("animating",!1),this.set("animations",[])},n.prototype.pauseAnimate=function(){var t=this.get("timeline"),n=this.get("animations"),e=t.getTime();return Object(o.a)(n,(function(t){t._paused=!0,t._pauseTime=e,t.pauseCallback&&t.pauseCallback()})),this.set("_pause",{isPaused:!0,pauseTime:e}),this},n.prototype.resumeAnimate=function(){var t=this.get("timeline").getTime(),n=this.get("animations"),e=this.get("_pause").pauseTime;return Object(o.a)(n,(function(n){n.startTime=n.startTime+(t-e),n._paused=!1,n._pauseTime=null,n.resumeCallback&&n.resumeCallback()})),this.set("_pause",{isPaused:!1}),this.set("animations",n),this},n.prototype.emitDelegation=function(t,n){var e,r=this,i=n.propagationPath;this.getEvents();"mouseenter"===t?e=n.fromShape:"mouseleave"===t&&(e=n.toShape);for(var a=function(t){var a=i[t],c=a.get("name");if(c){if((a.isGroup()||a.isCanvas&&a.isCanvas())&&e&&Object(d.g)(a,e))return"break";Object(o.b)(c)?Object(o.a)(c,(function(t){r.emitDelegateEvent(a,t,n)})):u.emitDelegateEvent(a,c,n)}},u=this,c=0;c<i.length;c++){if("break"===a(c))break}},n.prototype.emitDelegateEvent=function(t,n,e){var r=this.getEvents(),i=n+":"+e.type;(r[i]||r["*"])&&(e.name=i,e.currentTarget=t,e.delegateTarget=this,e.delegateObject=t.get("delegateObject"),this.emit(i,e))},n.prototype.translate=function(t,n){void 0===t&&(t=0),void 0===n&&(n=0);var e=this.getMatrix(),r=y(e,[["t",t,n]]);return this.setMatrix(r),this},n.prototype.move=function(t,n){var e=this.attr("x")||0,r=this.attr("y")||0;return this.translate(t-e,n-r),this},n.prototype.moveTo=function(t,n){return this.move(t,n)},n.prototype.scale=function(t,n){var e=this.getMatrix(),r=y(e,[["s",t,n||t]]);return this.setMatrix(r),this},n.prototype.rotate=function(t){var n=this.getMatrix(),e=y(n,[["r",t]]);return this.setMatrix(e),this},n.prototype.rotateAtStart=function(t){var n=this.attr(),e=n.x,r=n.y,i=this.getMatrix(),o=y(i,[["t",-e,-r],["r",t],["t",e,r]]);return this.setMatrix(o),this},n.prototype.rotateAtPoint=function(t,n,e){var r=this.getMatrix(),i=y(r,[["t",-t,-n],["r",e],["t",t,n]]);return this.setMatrix(i),this},n}(g.a);n.a=_},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.refreshElement=n.drawChildren=void 0;var r=e(14),i=e(9);n.drawChildren=function(t,n){n.forEach((function(n){n.draw(t)}))},n.refreshElement=function(t,n){var e=t.get("canvas");if(e&&e.get("autoDraw")){var o=e.get("context"),a=t.getParent(),u=a?a.getChildren():[e],c=t.get("el");if("remove"===n)if(t.get("isClipShape")){var s=c&&c.parentNode,f=s&&s.parentNode;s&&f&&f.removeChild(s)}else c&&c.parentNode&&c.parentNode.removeChild(c);else if("show"===n)c.setAttribute("visibility","visible");else if("hide"===n)c.setAttribute("visibility","hidden");else if("zIndex"===n)(0,i.moveTo)(c,u.indexOf(t));else if("sort"===n){var l=t.get("children");l&&l.length&&(0,i.sortDom)(t,(function(t,n){return l.indexOf(t)-l.indexOf(n)?1:0}))}else"clear"===n?c&&(c.innerHTML=""):"matrix"===n?(0,r.setTransform)(t):"clip"===n?(0,r.setClip)(t,o):"attr"===n||"add"===n&&t.draw(o)}}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(13),o=e(3),a=e(15),u=e(22),c=e(14),s=e(5),f=e(9),l=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(n,t),n.prototype.isEntityGroup=function(){return!0},n.prototype.createDom=function(){var t=(0,f.createSVGElement)("g");this.set("el",t);var n=this.getParent();if(n){var e=n.get("el");e||(e=n.createDom(),n.set("el",e)),e.appendChild(t)}return t},n.prototype.afterAttrsChange=function(n){t.prototype.afterAttrsChange.call(this,n);var e=this.get("canvas");if(e&&e.get("autoDraw")){var r=e.get("context");this.createPath(r,n)}},n.prototype.onCanvasChange=function(t){(0,u.refreshElement)(this,t)},n.prototype.getShapeBase=function(){return a},n.prototype.getGroupBase=function(){return n},n.prototype.draw=function(t){var n=this.getChildren(),e=this.get("el");this.get("destroyed")?e&&e.parentNode.removeChild(e):(e||this.createDom(),(0,c.setClip)(this,t),this.createPath(t),n.length&&(0,u.drawChildren)(t,n))},n.prototype.createPath=function(t,n){var e=this.attr(),r=this.get("el");(0,o.each)(n||e,(function(t,n){s.SVG_ATTR_MAP[n]&&r.setAttribute(s.SVG_ATTR_MAP[n],t)})),(0,c.setTransform)(this)},n}(i.AbstractGroup);n.default=l},function(t,n,e){"use strict";e.d(n,"a",(function(){return o})),e.d(n,"b",(function(){return i}));var r=new Map;function i(t,n){r.set(t,n)}function o(t){return r.get(t)}var a=function(t){var n=t.attr();return{x:n.x,y:n.y,width:n.width,height:n.height}},u=function(t){var n=t.attr(),e=n.x,r=n.y,i=n.r;return{x:e-i,y:r-i,width:2*i,height:2*i}},c=e(10);function s(t,n){return t&&n?{minX:Math.min(t.minX,n.minX),minY:Math.min(t.minY,n.minY),maxX:Math.max(t.maxX,n.maxX),maxY:Math.max(t.maxY,n.maxY)}:t||n}function f(t,n){var e=t.get("startArrowShape"),r=t.get("endArrowShape");return e&&(n=s(n,e.getCanvasBBox())),r&&(n=s(n,r.getCanvasBBox())),n}var l=e(12),h=e(0),p=/[MLHVQTCSAZ]([^MLHVQTCSAZ]*)/gi,d=/[^\s\,]+/gi;var v=function(t){var n=t||[];return Object(h.b)(n)?n:Object(h.i)(n)?(n=n.match(p),Object(h.a)(n,(function(t,e){if((t=t.match(d))[0].length>1){var r=t[0].charAt(0);t.splice(1,0,t[0].substr(1)),t[0]=r}Object(h.a)(t,(function(n,e){isNaN(n)||(t[e]=+n)})),n[e]=t})),n):void 0};e(2);var g="\t\n\v\f\r   ᠎             　\u2028\u2029";new RegExp("([a-z])["+g+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+g+"]*,?["+g+"]*)+)","ig"),new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+g+"]*,?["+g+"]*","ig");Math.PI;function y(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function m(t,n){return y(t)*y(n)?(t[0]*n[0]+t[1]*n[1])/(y(t)*y(n)):1}function b(t,n){return(t[0]*n[1]<t[1]*n[0]?-1:1)*Math.acos(m(t,n))}function x(t,n){return t[0]===n[0]&&t[1]===n[1]}function M(t,n){var e=n[1],r=n[2],i=Object(h.m)(Object(h.o)(n[3]),2*Math.PI),o=n[4],a=n[5],u=t[0],c=t[1],s=n[6],f=n[7],l=Math.cos(i)*(u-s)/2+Math.sin(i)*(c-f)/2,p=-1*Math.sin(i)*(u-s)/2+Math.cos(i)*(c-f)/2,d=l*l/(e*e)+p*p/(r*r);d>1&&(e*=Math.sqrt(d),r*=Math.sqrt(d));var v=e*e*(p*p)+r*r*(l*l),g=v?Math.sqrt((e*e*(r*r)-v)/v):1;o===a&&(g*=-1),isNaN(g)&&(g=0);var y=r?g*e*p/r:0,M=e?g*-r*l/e:0,w=(u+s)/2+Math.cos(i)*y-Math.sin(i)*M,_=(c+f)/2+Math.sin(i)*y+Math.cos(i)*M,A=[(l-y)/e,(p-M)/r],O=[(-1*l-y)/e,(-1*p-M)/r],S=b([1,0],A),P=b(A,O);return m(A,O)<=-1&&(P=Math.PI),m(A,O)>=1&&(P=0),0===a&&P>0&&(P-=2*Math.PI),1===a&&P<0&&(P+=2*Math.PI),{cx:w,cy:_,rx:x(t,[s,f])?0:e,ry:x(t,[s,f])?0:r,startAngle:S,endAngle:S+P,xRotation:i,arcFlag:o,sweepFlag:a}}function w(t,n){return[n[0]+(n[0]-t[0]),n[1]+(n[1]-t[1])]}function _(t,n){var e=t.prePoint,r=t.currentPoint,i=t.nextPoint,o=Math.pow(r[0]-e[0],2)+Math.pow(r[1]-e[1],2),a=Math.pow(r[0]-i[0],2)+Math.pow(r[1]-i[1],2),u=Math.pow(e[0]-i[0],2)+Math.pow(e[1]-i[1],2),c=Math.acos((o+a-u)/(2*Math.sqrt(o)*Math.sqrt(a)));if(!c||0===Math.sin(c)||Object(h.g)(c,0))return{xExtra:0,yExtra:0};var s=Math.abs(Math.atan2(i[1]-r[1],i[0]-r[0])),f=Math.abs(Math.atan2(i[0]-r[0],i[1]-r[1]));return s=s>Math.PI/2?Math.PI-s:s,f=f>Math.PI/2?Math.PI-f:f,{xExtra:Math.cos(c/2-s)*(n/2*(1/Math.sin(c/2)))-n/2||0,yExtra:Math.cos(f-c/2)*(n/2*(1/Math.sin(c/2)))-n/2||0}}i("rect",a),i("image",a),i("circle",u),i("marker",u),i("polyline",(function(t){for(var n=t.attr().points,e=[],r=[],i=0;i<n.length;i++){var o=n[i];e.push(o[0]),r.push(o[1])}var a=c.Util.getBBoxByArray(e,r),u=a.x,s=a.y,l={minX:u,minY:s,maxX:u+a.width,maxY:s+a.height};return{x:(l=f(t,l)).minX,y:l.minY,width:l.maxX-l.minX,height:l.maxY-l.minY}})),i("polygon",(function(t){for(var n=t.attr().points,e=[],r=[],i=0;i<n.length;i++){var o=n[i];e.push(o[0]),r.push(o[1])}return c.Util.getBBoxByArray(e,r)})),i("text",(function(t){var n=t.attr(),e=n.x,r=n.y,i=n.text,o=n.fontSize,a=n.lineHeight,u=n.font;u||(u=Object(l.a)(n));var c,s=Object(l.c)(i,u);if(s){var f=n.textAlign,h=n.textBaseline,p=Object(l.b)(i,o,a),d={x:e,y:r-p};f&&("end"===f||"right"===f?d.x-=s:"center"===f&&(d.x-=s/2)),h&&("top"===h?d.y+=p:"middle"===h&&(d.y+=p/2)),c={x:d.x,y:d.y,width:s,height:p}}else c={x:e,y:r,width:0,height:0};return c})),i("path",(function(t){var n=t.attr(),e=n.path,r=n.stroke?n.lineWidth:0,i=function(t,n){for(var e=[],r=[],i=[],o=0;o<t.length;o++){var a=(y=t[o]).currentPoint,u=y.params,s=y.prePoint,f=void 0;switch(y.command){case"Q":f=c.Quad.box(s[0],s[1],u[1],u[2],u[3],u[4]);break;case"C":f=c.Cubic.box(s[0],s[1],u[1],u[2],u[3],u[4],u[5],u[6]);break;case"A":var l=y.arcParams;f=c.Arc.box(l.cx,l.cy,l.rx,l.ry,l.xRotation,l.startAngle,l.endAngle);break;default:e.push(a[0]),r.push(a[1])}f&&(y.box=f,e.push(f.x,f.x+f.width),r.push(f.y,f.y+f.height)),n&&("L"===y.command||"M"===y.command)&&y.prePoint&&y.nextPoint&&i.push(y)}e=e.filter((function(t){return!Number.isNaN(t)&&t!==1/0&&t!==-1/0})),r=r.filter((function(t){return!Number.isNaN(t)&&t!==1/0&&t!==-1/0}));var p=Object(h.k)(e),d=Object(h.k)(r),v=Object(h.j)(e),g=Object(h.j)(r);if(0===i.length)return{x:p,y:d,width:v-p,height:g-d};for(o=0;o<i.length;o++){var y;(a=(y=i[o]).currentPoint)[0]===p?p-=_(y,n).xExtra:a[0]===v&&(v+=_(y,n).xExtra),a[1]===d?d-=_(y,n).yExtra:a[1]===g&&(g+=_(y,n).yExtra)}return{x:p,y:d,width:v-p,height:g-d}}(t.get("segments")||function(t){for(var n=[],e=null,r=null,i=null,o=0,a=(t=v(t)).length,u=0;u<a;u++){var c=t[u];r=t[u+1];var s=c[0],f={command:s,prePoint:e,params:c,startTangent:null,endTangent:null};switch(s){case"M":i=[c[1],c[2]],o=u;break;case"A":var l=M(e,c);f.arcParams=l}if("Z"===s)e=i,r=t[o+1];else{var h=c.length;e=[c[h-2],c[h-1]]}r&&"Z"===r[0]&&(r=t[o],n[o]&&(n[o].prePoint=e)),f.currentPoint=e,n[o]&&x(e,n[o].currentPoint)&&(n[o].prePoint=f.prePoint);var p=r?[r[r.length-2],r[r.length-1]]:null;f.nextPoint=p;var d=f.prePoint;if(["L","H","V"].includes(s))f.startTangent=[d[0]-e[0],d[1]-e[1]],f.endTangent=[e[0]-d[0],e[1]-d[1]];else if("Q"===s){var g=[c[1],c[2]];f.startTangent=[d[0]-g[0],d[1]-g[1]],f.endTangent=[e[0]-g[0],e[1]-g[1]]}else if("T"===s){g=w((b=n[u-1]).currentPoint,d);"Q"===b.command?(f.command="Q",f.startTangent=[d[0]-g[0],d[1]-g[1]],f.endTangent=[e[0]-g[0],e[1]-g[1]]):(f.command="TL",f.startTangent=[d[0]-e[0],d[1]-e[1]],f.endTangent=[e[0]-d[0],e[1]-d[1]])}else if("C"===s){var y=[c[1],c[2]],m=[c[3],c[4]];f.startTangent=[d[0]-y[0],d[1]-y[1]],f.endTangent=[e[0]-m[0],e[1]-m[1]],0===f.startTangent[0]&&0===f.startTangent[1]&&(f.startTangent=[y[0]-m[0],y[1]-m[1]]),0===f.endTangent[0]&&0===f.endTangent[1]&&(f.endTangent=[m[0]-y[0],m[1]-y[1]])}else if("S"===s){var b;y=w((b=n[u-1]).currentPoint,d),m=[c[1],c[2]];"C"===b.command?(f.command="C",f.startTangent=[d[0]-y[0],d[1]-y[1]],f.endTangent=[e[0]-m[0],e[1]-m[1]]):(f.command="SQ",f.startTangent=[d[0]-m[0],d[1]-m[1]],f.endTangent=[e[0]-m[0],e[1]-m[1]])}else if("A"===s){var _=.001,A=f.arcParams||{},O=A.cx,S=void 0===O?0:O,P=A.cy,C=void 0===P?0:P,j=A.rx,T=void 0===j?0:j,k=A.ry,E=void 0===k?0:k,B=A.sweepFlag,I=void 0===B?0:B,D=A.startAngle,N=void 0===D?0:D,F=A.endAngle,L=void 0===F?0:F;0===I&&(_*=-1);var R=T*Math.cos(N-_)+S,G=E*Math.sin(N-_)+C;f.startTangent=[R-i[0],G-i[1]];var V=T*Math.cos(N+L+_)+S,W=E*Math.sin(N+L-_)+C;f.endTangent=[d[0]-V,d[1]-W]}n.push(f)}return n}(e),r),o=i.x,a=i.y,u={minX:o,minY:a,maxX:o+i.width,maxY:a+i.height};return{x:(u=f(t,u)).minX,y:u.minY,width:u.maxX-u.minX,height:u.maxY-u.minY}})),i("line",(function(t){var n=t.attr(),e=n.x1,r=n.y1,i=n.x2,o=n.y2,a={minX:Math.min(e,i),maxX:Math.max(e,i),minY:Math.min(r,o),maxY:Math.max(r,o)};return{x:(a=f(t,a)).minX,y:a.minY,width:a.maxX-a.minX,height:a.maxY-a.minY}})),i("ellipse",(function(t){var n=t.attr(),e=n.x,r=n.y,i=n.rx,o=n.ry;return{x:e-i,y:r-o,width:2*i,height:2*o}}))},function(t,n){},function(t,n){},function(t,n){var e,r,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function u(t){if(e===setTimeout)return setTimeout(t,0);if((e===o||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(n){try{return e.call(null,t,0)}catch(n){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:o}catch(t){e=o}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(t){r=a}}();var c,s=[],f=!1,l=-1;function h(){f&&c&&(f=!1,c.length?s=c.concat(s):l=-1,s.length&&p())}function p(){if(!f){var t=u(h);f=!0;for(var n=s.length;n;){for(c=s,s=[];++l<n;)c&&c[l].run();l=-1,n=s.length}c=null,f=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(n){try{return r.call(null,t)}catch(n){return r.call(this,t)}}}(t)}}function d(t,n){this.fun=t,this.array=n}function v(){}i.nextTick=function(t){var n=new Array(arguments.length-1);if(arguments.length>1)for(var e=1;e<arguments.length;e++)n[e-1]=arguments[e];s.push(new d(t,n)),1!==s.length||f||u(p)},d.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=v,i.addListener=v,i.once=v,i.off=v,i.removeListener=v,i.removeAllListeners=v,i.emit=v,i.prependListener=v,i.prependOnceListener=v,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(t,n,e){"use strict";var r=e(6),i=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return Object(r.a)(n,t),n.prototype.isGroup=function(){return!0},n.prototype.isEntityGroup=function(){return!1},n.prototype.clone=function(){for(var n=t.prototype.clone.call(this),e=this.getChildren(),r=0;r<e.length;r++){var i=e[r];n.add(i.clone())}return n},n}(e(20).a);n.a=i},function(t,n,e){"use strict";var r=e(6),i=e(21),o=e(7),a=function(t){function n(n){return t.call(this,n)||this}return Object(r.a)(n,t),n.prototype._isInBBox=function(t,n){var e=this.getBBox();return e.minX<=t&&e.maxX>=t&&e.minY<=n&&e.maxY>=n},n.prototype.afterAttrsChange=function(n){t.prototype.afterAttrsChange.call(this,n),this.clearCacheBBox()},n.prototype.getBBox=function(){var t=this.cfg.bbox;return t||(t=this.calculateBBox(),this.set("bbox",t)),t},n.prototype.getCanvasBBox=function(){var t=this.cfg.canvasBBox;return t||(t=this.calculateCanvasBBox(),this.set("canvasBBox",t)),t},n.prototype.applyMatrix=function(n){t.prototype.applyMatrix.call(this,n),this.set("canvasBBox",null)},n.prototype.calculateCanvasBBox=function(){var t=this.getBBox(),n=this.getTotalMatrix(),e=t.minX,r=t.minY,i=t.maxX,a=t.maxY;if(n){var u=Object(o.c)(n,[t.minX,t.minY]),c=Object(o.c)(n,[t.maxX,t.minY]),s=Object(o.c)(n,[t.minX,t.maxY]),f=Object(o.c)(n,[t.maxX,t.maxY]);e=Math.min(u[0],c[0],s[0],f[0]),i=Math.max(u[0],c[0],s[0],f[0]),r=Math.min(u[1],c[1],s[1],f[1]),a=Math.max(u[1],c[1],s[1],f[1])}var l=this.attrs;if(l.shadowColor){var h=l.shadowBlur,p=void 0===h?0:h,d=l.shadowOffsetX,v=void 0===d?0:d,g=l.shadowOffsetY,y=void 0===g?0:g,m=e-p+v,b=i+p+v,x=r-p+y,M=a+p+y;e=Math.min(e,m),i=Math.max(i,b),r=Math.min(r,x),a=Math.max(a,M)}return{x:e,y:r,minX:e,minY:r,maxX:i,maxY:a,width:i-e,height:a-r}},n.prototype.clearCacheBBox=function(){this.set("bbox",null),this.set("canvasBBox",null)},n.prototype.isClipShape=function(){return this.get("isClipShape")},n.prototype.isInShape=function(t,n){return!1},n.prototype.isOnlyHitBox=function(){return!1},n.prototype.isHit=function(t,n){var e=this.get("startArrowShape"),r=this.get("endArrowShape"),i=[t,n,1],o=(i=this.invertFromMatrix(i))[0],a=i[1],u=this._isInBBox(o,a);if(this.isOnlyHitBox())return u;if(u&&!this.isClipped(o,a)){if(this.isInShape(o,a))return!0;if(e&&e.isHit(o,a))return!0;if(r&&r.isHit(o,a))return!0}return!1},n}(i.a);n.a=a},function(t,n,e){"use strict";var r,i,o=e(6),a=e(31),u=e(20),c=e(1),s=e(0),f=0,l=0,h=0,p=0,d=0,v=0,g="object"==typeof performance&&performance.now?performance:Date,y="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function m(){return d||(y(b),d=g.now()+v)}function b(){d=0}function x(){this._call=this._time=this._next=null}function M(t,n,e){var r=new x;return r.restart(t,n,e),r}function w(){d=(p=g.now())+v,f=l=0;try{!function(){m(),++f;for(var t,n=r;n;)(t=d-n._time)>=0&&n._call.call(null,t),n=n._next;--f}()}finally{f=0,function(){var t,n,e=r,o=1/0;for(;e;)e._call?(o>e._time&&(o=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:r=n);i=t,A(o)}(),d=0}}function _(){var t=g.now(),n=t-p;n>1e3&&(v-=n,p=t)}function A(t){f||(l&&(l=clearTimeout(l)),t-d>24?(t<1/0&&(l=setTimeout(w,t-g.now()-v)),h&&(h=clearInterval(h))):(h||(p=g.now(),h=setInterval(_,1e3)),f=1,y(w)))}x.prototype=M.prototype={constructor:x,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?m():+e)+(null==n?0:+n),this._next||i===this||(i?i._next=this:r=this,i=this),this._call=t,this._time=e,A()},stop:function(){this._call&&(this._call=null,this._time=1/0,A())}};var O=function(t,n,e){t.prototype=n.prototype=e,e.constructor=t};function S(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function P(){}var C="\\s*([+-]?\\d+)\\s*",j="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",T="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",k=/^#([0-9a-f]{3,8})$/,E=new RegExp(`^rgb\\(${C},${C},${C}\\)$`),B=new RegExp(`^rgb\\(${T},${T},${T}\\)$`),I=new RegExp(`^rgba\\(${C},${C},${C},${j}\\)$`),D=new RegExp(`^rgba\\(${T},${T},${T},${j}\\)$`),N=new RegExp(`^hsl\\(${j},${T},${T}\\)$`),F=new RegExp(`^hsla\\(${j},${T},${T},${j}\\)$`),L={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function R(){return this.rgb().formatHex()}function G(){return this.rgb().formatRgb()}function V(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=k.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?W(n):3===e?new Y(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?q(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?q(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=E.exec(t))?new Y(n[1],n[2],n[3],1):(n=B.exec(t))?new Y(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=I.exec(t))?q(n[1],n[2],n[3],n[4]):(n=D.exec(t))?q(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=N.exec(t))?K(n[1],n[2]/100,n[3]/100,1):(n=F.exec(t))?K(n[1],n[2]/100,n[3]/100,n[4]):L.hasOwnProperty(t)?W(L[t]):"transparent"===t?new Y(NaN,NaN,NaN,0):null}function W(t){return new Y(t>>16&255,t>>8&255,255&t,1)}function q(t,n,e,r){return r<=0&&(t=n=e=NaN),new Y(t,n,e,r)}function X(t){return t instanceof P||(t=V(t)),t?new Y((t=t.rgb()).r,t.g,t.b,t.opacity):new Y}function $(t,n,e,r){return 1===arguments.length?X(t):new Y(t,n,e,null==r?1:r)}function Y(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function z(){return`#${Z(this.r)}${Z(this.g)}${Z(this.b)}`}function H(){const t=Q(this.opacity);return`${1===t?"rgb(":"rgba("}${U(this.r)}, ${U(this.g)}, ${U(this.b)}${1===t?")":`, ${t})`}`}function Q(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function U(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Z(t){return((t=U(t))<16?"0":"")+t.toString(16)}function K(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new tt(t,n,e,r)}function J(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);if(t instanceof P||(t=V(t)),!t)return new tt;if(t instanceof tt)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),a=NaN,u=o-i,c=(o+i)/2;return u?(a=n===o?(e-r)/u+6*(e<r):e===o?(r-n)/u+2:(n-e)/u+4,u/=c<.5?o+i:2-o-i,a*=60):u=c>0&&c<1?0:a,new tt(a,u,c,t.opacity)}function tt(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function nt(t){return(t=(t||0)%360)<0?t+360:t}function et(t){return Math.max(0,Math.min(1,t||0))}function rt(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}function it(t,n,e,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*n+(4-6*o+3*a)*e+(1+3*t+3*o-3*a)*r+a*i)/6}O(P,V,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:R,formatHex:R,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return J(this).formatHsl()},formatRgb:G,toString:G}),O(Y,$,S(P,{brighter(t){return t=null==t?1/.7:Math.pow(1/.7,t),new Y(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new Y(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Y(U(this.r),U(this.g),U(this.b),Q(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:z,formatHex:z,formatHex8:function(){return`#${Z(this.r)}${Z(this.g)}${Z(this.b)}${Z(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:H,toString:H})),O(tt,(function(t,n,e,r){return 1===arguments.length?J(t):new tt(t,n,e,null==r?1:r)}),S(P,{brighter(t){return t=null==t?1/.7:Math.pow(1/.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new Y(rt(t>=240?t-240:t+120,i,r),rt(t,i,r),rt(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new tt(nt(this.h),et(this.s),et(this.l),Q(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=Q(this.opacity);return`${1===t?"hsl(":"hsla("}${nt(this.h)}, ${100*et(this.s)}%, ${100*et(this.l)}%${1===t?")":`, ${t})`}`}}));var ot=t=>()=>t;function at(t,n){return function(e){return t+e*n}}function ut(t){return 1==(t=+t)?ct:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):ot(isNaN(n)?e:n)}}function ct(t,n){var e=n-t;return e?at(t,e):ot(isNaN(t)?n:t)}var st=function t(n){var e=ut(n);function r(t,n){var r=e((t=$(t)).r,(n=$(n)).r),i=e(t.g,n.g),o=e(t.b,n.b),a=ct(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=i(n),t.b=o(n),t.opacity=a(n),t+""}}return r.gamma=t,r}(1);function ft(t){return function(n){var e,r,i=n.length,o=new Array(i),a=new Array(i),u=new Array(i);for(e=0;e<i;++e)r=$(n[e]),o[e]=r.r||0,a[e]=r.g||0,u[e]=r.b||0;return o=t(o),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=u(t),r+""}}}ft((function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],o=t[r+1],a=r>0?t[r-1]:2*i-o,u=r<n-1?t[r+2]:2*o-i;return it((e-r/n)*n,a,i,o,u)}})),ft((function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],o=t[r%n],a=t[(r+1)%n],u=t[(r+2)%n];return it((e-r/n)*n,i,o,a,u)}}));var lt=function(t,n){n||(n=[]);var e,r=t?Math.min(n.length,t.length):0,i=n.slice();return function(o){for(e=0;e<r;++e)i[e]=t[e]*(1-o)+n[e]*o;return i}};function ht(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function pt(t,n){var e,r=n?n.length:0,i=t?Math.min(r,t.length):0,o=new Array(i),a=new Array(r);for(e=0;e<i;++e)o[e]=xt(t[e],n[e]);for(;e<r;++e)a[e]=n[e];return function(t){for(e=0;e<i;++e)a[e]=o[e](t);return a}}var dt=function(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}},vt=function(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}},gt=function(t,n){var e,r={},i={};for(e in null!==t&&"object"==typeof t||(t={}),null!==n&&"object"==typeof n||(n={}),n)e in t?r[e]=xt(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}},yt=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,mt=new RegExp(yt.source,"g");var bt=function(t,n){var e,r,i,o=yt.lastIndex=mt.lastIndex=0,a=-1,u=[],c=[];for(t+="",n+="";(e=yt.exec(t))&&(r=mt.exec(n));)(i=r.index)>o&&(i=n.slice(o,i),u[a]?u[a]+=i:u[++a]=i),(e=e[0])===(r=r[0])?u[a]?u[a]+=r:u[++a]=r:(u[++a]=null,c.push({i:a,x:vt(e,r)})),o=mt.lastIndex;return o<n.length&&(i=n.slice(o),u[a]?u[a]+=i:u[++a]=i),u.length<2?c[0]?function(t){return function(n){return t(n)+""}}(c[0].x):function(t){return function(){return t}}(n):(n=c.length,function(t){for(var e,r=0;r<n;++r)u[(e=c[r]).i]=e.x(t);return u.join("")})},xt=function(t,n){var e,r=typeof n;return null==n||"boolean"===r?ot(n):("number"===r?vt:"string"===r?(e=V(n))?(n=e,st):bt:n instanceof V?st:n instanceof Date?dt:ht(n)?lt:Array.isArray(n)?pt:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?gt:vt)(t,n)},Mt=e(18),wt=e(11),_t=[1,0,0,0,1,0,0,0,1];function At(t,n,e){var r,i=n.startTime;if(e<i+n.delay||n._paused)return!1;var o=n.duration,a=n.easing,u=Object(Mt.a)(a);if(e=e-i-n.delay,n.repeat)r=u(r=e%o/o);else{if(!((r=e/o)<1))return n.onFrame?t.attr(n.onFrame(1)):t.attr(n.toAttrs),!0;r=u(r)}if(n.onFrame){var c=n.onFrame(r);t.attr(c)}else!function(t,n,e){var r={},i=n.fromAttrs,o=n.toAttrs;if(!t.destroyed){var a,u,c,f;for(var l in o)if(!Object(s.c)(i[l],o[l]))if("path"===l){var h=o[l],p=i[l];h.length>p.length?(h=wt.parsePathString(o[l]),p=wt.parsePathString(i[l]),p=wt.fillPathByDiff(p,h),p=wt.formatPath(p,h),n.fromAttrs.path=p,n.toAttrs.path=h):n.pathFormatted||(h=wt.parsePathString(o[l]),p=wt.parsePathString(i[l]),p=wt.formatPath(p,h),n.fromAttrs.path=p,n.toAttrs.path=h,n.pathFormatted=!0),r[l]=[];for(var d=0;d<h.length;d++){for(var v=h[d],g=p[d],y=[],m=0;m<v.length;m++)Object(s.f)(v[m])&&g&&Object(s.f)(g[m])?(a=xt(g[m],v[m]),y.push(a(e))):y.push(v[m]);r[l].push(y)}}else if("matrix"===l){var b=(c=i[l]||_t,(ht(f=o[l]||_t)?lt:pt)(c,f))(e);r[l]=b}else["fill","stroke","fillStyle","strokeStyle"].includes(l)&&(u=o[l],/^[r,R,L,l]{1}[\s]*\(/.test(u))?r[l]=o[l]:Object(s.d)(o[l])||(a=xt(i[l],o[l]),r[l]=a(e));t.attr(r)}}(t,n,r);return!1}var Ot=function(){function t(t){this.animators=[],this.current=0,this.timer=null,this.canvas=t}return t.prototype.initTimer=function(){var t,n,e,r=this;this.timer=M((function(i){if(r.current=i,r.animators.length>0){for(var o=r.animators.length-1;o>=0;o--)if((t=r.animators[o]).destroyed)r.removeAnimator(o);else{if(!t.isAnimatePaused())for(var a=(n=t.get("animations")).length-1;a>=0;a--)e=n[a],At(t,e,i)&&(n.splice(a,1),!1,e.callback&&e.callback());0===n.length&&r.removeAnimator(o)}r.canvas.get("autoDraw")||r.canvas.draw()}}))},t.prototype.addAnimator=function(t){this.animators.push(t)},t.prototype.removeAnimator=function(t){this.animators.splice(t,1)},t.prototype.isAnimating=function(){return!!this.animators.length},t.prototype.stop=function(){this.timer&&this.timer.stop()},t.prototype.stopAllAnimations=function(t){void 0===t&&(t=!0),this.animators.forEach((function(n){n.stopAnimate(t)})),this.animators=[],this.canvas.draw()},t.prototype.getTime=function(){return this.current},t}(),St=e(16),Pt=["mousedown","mouseup","dblclick","mouseout","mouseover","mousemove","mouseleave","mouseenter","touchstart","touchmove","touchend","dragenter","dragover","dragleave","drop","contextmenu","mousewheel"];function Ct(t,n,e){e.name=n,e.target=t,e.currentTarget=t,e.delegateTarget=t,t.emit(n,e)}function jt(t,n,e){if(e.bubbles){var r=void 0,i=!1;if("mouseenter"===n?(r=e.fromShape,i=!0):"mouseleave"===n&&(i=!0,r=e.toShape),t.isCanvas()&&i)return;if(r&&Object(c.g)(t,r))return void(e.bubbles=!1);e.name=n,e.currentTarget=t,e.delegateTarget=t,t.emit(n,e)}}var Tt=function(){function t(t){var n=this;this.draggingShape=null,this.dragging=!1,this.currentShape=null,this.mousedownShape=null,this.mousedownPoint=null,this._eventCallback=function(t){var e=t.type;n._triggerEvent(e,t)},this._onDocumentMove=function(t){if(n.canvas.get("el")!==t.target&&(n.dragging||n.currentShape)){var e=n._getPointInfo(t);n.dragging&&n._emitEvent("drag",t,e,n.draggingShape)}},this._onDocumentMouseUp=function(t){if(n.canvas.get("el")!==t.target&&n.dragging){var e=n._getPointInfo(t);n.draggingShape&&n._emitEvent("drop",t,e,null),n._emitEvent("dragend",t,e,n.draggingShape),n._afterDrag(n.draggingShape,e,t)}},this.canvas=t.canvas}return t.prototype.init=function(){this._bindEvents()},t.prototype._bindEvents=function(){var t=this,n=this.canvas.get("el");Object(c.a)(Pt,(function(e){n.addEventListener(e,t._eventCallback)})),document&&(document.addEventListener("mousemove",this._onDocumentMove),document.addEventListener("mouseup",this._onDocumentMouseUp))},t.prototype._clearEvents=function(){var t=this,n=this.canvas.get("el");Object(c.a)(Pt,(function(e){n.removeEventListener(e,t._eventCallback)})),document&&(document.removeEventListener("mousemove",this._onDocumentMove),document.removeEventListener("mouseup",this._onDocumentMouseUp))},t.prototype._getEventObj=function(t,n,e,r,i,o){var a=new St.a(t,n);return a.fromShape=i,a.toShape=o,a.x=e.x,a.y=e.y,a.clientX=e.clientX,a.clientY=e.clientY,a.propagationPath.push(r),a},t.prototype._getShape=function(t,n){return this.canvas.getShape(t.x,t.y,n)},t.prototype._getPointInfo=function(t){var n=this.canvas,e=n.getClientByEvent(t),r=n.getPointByEvent(t);return{x:r.x,y:r.y,clientX:e.x,clientY:e.y}},t.prototype._triggerEvent=function(t,n){var e=this._getPointInfo(n),r=this._getShape(e,n),i=this["_on"+t],o=!1;if(i)i.call(this,e,r,n);else{var a=this.currentShape;"mouseenter"===t||"dragenter"===t||"mouseover"===t?(this._emitEvent(t,n,e,null,null,r),r&&this._emitEvent(t,n,e,r,null,r),"mouseenter"===t&&this.draggingShape&&this._emitEvent("dragenter",n,e,null)):"mouseleave"===t||"dragleave"===t||"mouseout"===t?(o=!0,a&&this._emitEvent(t,n,e,a,a,null),this._emitEvent(t,n,e,null,a,null),"mouseleave"===t&&this.draggingShape&&this._emitEvent("dragleave",n,e,null)):this._emitEvent(t,n,e,r,null,null)}if(o||(this.currentShape=r),r&&!r.get("destroyed")){var u=this.canvas;u.get("el").style.cursor=r.attr("cursor")||u.get("cursor")}},t.prototype._onmousedown=function(t,n,e){0===e.button&&(this.mousedownShape=n,this.mousedownPoint=t,this.mousedownTimeStamp=e.timeStamp),this._emitEvent("mousedown",e,t,n,null,null)},t.prototype._emitMouseoverEvents=function(t,n,e,r){var i=this.canvas.get("el");e!==r&&(e&&(this._emitEvent("mouseout",t,n,e,e,r),this._emitEvent("mouseleave",t,n,e,e,r),r&&!r.get("destroyed")||(i.style.cursor=this.canvas.get("cursor"))),r&&(this._emitEvent("mouseover",t,n,r,e,r),this._emitEvent("mouseenter",t,n,r,e,r)))},t.prototype._emitDragoverEvents=function(t,n,e,r,i){r?(r!==e&&(e&&this._emitEvent("dragleave",t,n,e,e,r),this._emitEvent("dragenter",t,n,r,e,r)),i||this._emitEvent("dragover",t,n,r)):e&&this._emitEvent("dragleave",t,n,e,e,r),i&&this._emitEvent("dragover",t,n,r)},t.prototype._afterDrag=function(t,n,e){t&&(t.set("capture",!0),this.draggingShape=null),this.dragging=!1;var r=this._getShape(n,e);r!==t&&this._emitMouseoverEvents(e,n,t,r),this.currentShape=r},t.prototype._onmouseup=function(t,n,e){if(0===e.button){var r=this.draggingShape;this.dragging?(r&&this._emitEvent("drop",e,t,n),this._emitEvent("dragend",e,t,r),this._afterDrag(r,t,e)):(this._emitEvent("mouseup",e,t,n),n===this.mousedownShape&&this._emitEvent("click",e,t,n),this.mousedownShape=null,this.mousedownPoint=null)}},t.prototype._ondragover=function(t,n,e){e.preventDefault();var r=this.currentShape;this._emitDragoverEvents(e,t,r,n,!0)},t.prototype._onmousemove=function(t,n,e){var r=this.canvas,i=this.currentShape,o=this.draggingShape;if(this.dragging)o&&this._emitDragoverEvents(e,t,i,n,!1),this._emitEvent("drag",e,t,o);else{var a=this.mousedownPoint;if(a){var u=this.mousedownShape,c=e.timeStamp-this.mousedownTimeStamp,s=a.clientX-t.clientX,f=a.clientY-t.clientY;c>120||s*s+f*f>40?u&&u.get("draggable")?((o=this.mousedownShape).set("capture",!1),this.draggingShape=o,this.dragging=!0,this._emitEvent("dragstart",e,t,o),this.mousedownShape=null,this.mousedownPoint=null):!u&&r.get("draggable")?(this.dragging=!0,this._emitEvent("dragstart",e,t,null),this.mousedownShape=null,this.mousedownPoint=null):(this._emitMouseoverEvents(e,t,i,n),this._emitEvent("mousemove",e,t,n)):(this._emitMouseoverEvents(e,t,i,n),this._emitEvent("mousemove",e,t,n))}else this._emitMouseoverEvents(e,t,i,n),this._emitEvent("mousemove",e,t,n)}},t.prototype._emitEvent=function(t,n,e,r,i,o){var a=this._getEventObj(t,n,e,r,i,o);if(r){a.shape=r,Ct(r,t,a);for(var u=r.getParent();u;)u.emitDelegation(t,a),a.propagationStopped||jt(u,t,a),a.propagationPath.push(u),u=u.getParent()}else{Ct(this.canvas,t,a)}},t.prototype.destroy=function(){this._clearEvents(),this.canvas=null,this.currentShape=null,this.draggingShape=null,this.mousedownPoint=null,this.mousedownShape=null,this.mousedownTimeStamp=null},t}(),kt=Object(a.a)(),Et=kt&&"firefox"===kt.name,Bt=function(t){function n(n){var e=t.call(this,n)||this;return e.initContainer(),e.initDom(),e.initEvents(),e.initTimeline(),e}return Object(o.a)(n,t),n.prototype.getDefaultCfg=function(){var n=t.prototype.getDefaultCfg.call(this);return n.cursor="default",n.supportCSSTransform=!1,n},n.prototype.initContainer=function(){var t=this.get("container");Object(c.h)(t)&&(t=document.getElementById(t),this.set("container",t))},n.prototype.initDom=function(){var t=this.createDom();this.set("el",t),this.get("container").appendChild(t),this.setDOMSize(this.get("width"),this.get("height"))},n.prototype.initEvents=function(){var t=new Tt({canvas:this});t.init(),this.set("eventController",t)},n.prototype.initTimeline=function(){var t=new Ot(this);this.set("timeline",t)},n.prototype.setDOMSize=function(t,n){var e=this.get("el");c.c&&(e.style.width=t+"px",e.style.height=n+"px")},n.prototype.changeSize=function(t,n){this.setDOMSize(t,n),this.set("width",t),this.set("height",n),this.onCanvasChange("changeSize")},n.prototype.getRenderer=function(){return this.get("renderer")},n.prototype.getCursor=function(){return this.get("cursor")},n.prototype.setCursor=function(t){this.set("cursor",t);var n=this.get("el");c.c&&n&&(n.style.cursor=t)},n.prototype.getPointByEvent=function(t){if(this.get("supportCSSTransform")){if(Et&&!Object(c.e)(t.layerX)&&t.layerX!==t.offsetX)return{x:t.layerX,y:t.layerY};if(!Object(c.e)(t.offsetX))return{x:t.offsetX,y:t.offsetY}}var n=this.getClientByEvent(t),e=n.x,r=n.y;return this.getPointByClient(e,r)},n.prototype.getClientByEvent=function(t){var n=t;return t.touches&&(n="touchend"===t.type?t.changedTouches[0]:t.touches[0]),{x:n.clientX,y:n.clientY}},n.prototype.getPointByClient=function(t,n){var e=this.get("el").getBoundingClientRect();return{x:t-e.left,y:n-e.top}},n.prototype.getClientByPoint=function(t,n){var e=this.get("el").getBoundingClientRect();return{x:t+e.left,y:n+e.top}},n.prototype.draw=function(){},n.prototype.removeDom=function(){var t=this.get("el");t.parentNode.removeChild(t)},n.prototype.clearEvents=function(){this.get("eventController").destroy()},n.prototype.isCanvas=function(){return!0},n.prototype.getParent=function(){return null},n.prototype.destroy=function(){var n=this.get("timeline");this.get("destroyed")||(this.clear(),n&&n.stop(),this.clearEvents(),this.removeDom(),t.prototype.destroy.call(this))},n}(u.a);n.a=Bt},function(t,n,e){"use strict";(function(t){e.d(n,"a",(function(){return h}));var r=function(t,n,e){if(e||2===arguments.length)for(var r,i=0,o=n.length;i<o;i++)!r&&i in n||(r||(r=Array.prototype.slice.call(n,0,i)),r[i]=n[i]);return t.concat(r||Array.prototype.slice.call(n))},i=function(t,n,e){this.name=t,this.version=n,this.os=e,this.type="browser"},o=function(n){this.version=n,this.type="node",this.name="node",this.os=t.platform},a=function(t,n,e,r){this.name=t,this.version=n,this.os=e,this.bot=r,this.type="bot-device"},u=function(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null},c=function(){this.type="react-native",this.name="react-native",this.version=null,this.os=null},s=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,f=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],l=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function h(n){return n?d(n):"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product?new c:"undefined"!=typeof navigator?d(navigator.userAgent):void 0!==t&&t.version?new o(t.version.slice(1)):null}function p(t){return""!==t&&f.reduce((function(n,e){var r=e[0],i=e[1];if(n)return n;var o=i.exec(t);return!!o&&[r,o]}),!1)}function d(t){var n=p(t);if(!n)return null;var e=n[0],o=n[1];if("searchbot"===e)return new u;var c=o[1]&&o[1].split(".").join("_").split("_").slice(0,3);c?c.length<3&&(c=r(r([],c,!0),function(t){for(var n=[],e=0;e<t;e++)n.push("0");return n}(3-c.length),!0)):c=[];var f=c.join("."),h=function(t){for(var n=0,e=l.length;n<e;n++){var r=l[n],i=r[0];if(r[1].exec(t))return i}return null}(t),d=s.exec(t);return d&&d[1]?new a(e,f,h,d[1]):new i(e,f,h)}}).call(this,e(27))},,,,,,,,,function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.version=n.Shape=n.Group=n.Canvas=n.IShape=n.IGroup=n.IElement=void 0;var r=e(4),i=e(15);n.Shape=i,r.__exportStar(e(13),n);var o=e(55);Object.defineProperty(n,"IElement",{enumerable:!0,get:function(){return o.IElement}}),Object.defineProperty(n,"IGroup",{enumerable:!0,get:function(){return o.IGroup}}),Object.defineProperty(n,"IShape",{enumerable:!0,get:function(){return o.IShape}});var a=e(56);Object.defineProperty(n,"Canvas",{enumerable:!0,get:function(){return a.default}});var u=e(23);Object.defineProperty(n,"Group",{enumerable:!0,get:function(){return u.default}}),n.version="0.5.6"},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(3),o=e(5),a=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="circle",n.canFill=!0,n.canStroke=!0,n}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x:0,y:0,r:0})},n.prototype.createPath=function(t,n){var e=this.attr(),r=this.get("el");(0,i.each)(n||e,(function(t,n){"x"===n||"y"===n?r.setAttribute("c".concat(n),t):o.SVG_ATTR_MAP[n]&&r.setAttribute(o.SVG_ATTR_MAP[n],t)}))},n}(e(8).default);n.default=a},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(3),o=e(5),a=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="dom",n.canFill=!1,n.canStroke=!1,n}return r.__extends(n,t),n.prototype.createPath=function(t,n){var e=this.attr(),r=this.get("el");if((0,i.each)(n||e,(function(t,n){o.SVG_ATTR_MAP[n]&&r.setAttribute(o.SVG_ATTR_MAP[n],t)})),"function"==typeof e.html){var a=e.html.call(this,e);if(a instanceof Element||a instanceof HTMLDocument){for(var u=r.childNodes,c=u.length-1;c>=0;c--)r.removeChild(u[c]);r.appendChild(a)}else r.innerHTML=a}else r.innerHTML=e.html},n}(e(8).default);n.default=a},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(3),o=e(5),a=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="ellipse",n.canFill=!0,n.canStroke=!0,n}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x:0,y:0,rx:0,ry:0})},n.prototype.createPath=function(t,n){var e=this.attr(),r=this.get("el");(0,i.each)(n||e,(function(t,n){"x"===n||"y"===n?r.setAttribute("c".concat(n),t):o.SVG_ATTR_MAP[n]&&r.setAttribute(o.SVG_ATTR_MAP[n],t)}))},n}(e(8).default);n.default=a},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(3),o=e(5),a=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="image",n.canFill=!1,n.canStroke=!1,n}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x:0,y:0,width:0,height:0})},n.prototype.createPath=function(t,n){var e=this,r=this.attr(),a=this.get("el");(0,i.each)(n||r,(function(t,n){"img"===n?e._setImage(r.img):o.SVG_ATTR_MAP[n]&&a.setAttribute(o.SVG_ATTR_MAP[n],t)}))},n.prototype.setAttr=function(t,n){this.attrs[t]=n,"img"===t&&this._setImage(n)},n.prototype._setImage=function(t){var n=this.attr(),e=this.get("el");if((0,i.isString)(t))e.setAttribute("href",t);else if(t instanceof window.Image)n.width||(e.setAttribute("width",t.width),this.attr("width",t.width)),n.height||(e.setAttribute("height",t.height),this.attr("height",t.height)),e.setAttribute("href",t.src);else if(t instanceof HTMLElement&&(0,i.isString)(t.nodeName)&&"CANVAS"===t.nodeName.toUpperCase())e.setAttribute("href",t.toDataURL());else if(t instanceof ImageData){var r=document.createElement("canvas");r.setAttribute("width","".concat(t.width)),r.setAttribute("height","".concat(t.height)),r.getContext("2d").putImageData(t,0,0),n.width||(e.setAttribute("width","".concat(t.width)),this.attr("width",t.width)),n.height||(e.setAttribute("height","".concat(t.height)),this.attr("height",t.height)),e.setAttribute("href",r.toDataURL())}},n}(e(8).default);n.default=a},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(10),o=e(3),a=e(5),u=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="line",n.canFill=!1,n.canStroke=!0,n}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x1:0,y1:0,x2:0,y2:0,startArrow:!1,endArrow:!1})},n.prototype.createPath=function(t,n){var e=this.attr(),r=this.get("el");(0,o.each)(n||e,(function(n,i){if("startArrow"===i||"endArrow"===i)if(n){var u=(0,o.isObject)(n)?t.addArrow(e,a.SVG_ATTR_MAP[i]):t.getDefaultArrow(e,a.SVG_ATTR_MAP[i]);r.setAttribute(a.SVG_ATTR_MAP[i],"url(#".concat(u,")"))}else r.removeAttribute(a.SVG_ATTR_MAP[i]);else a.SVG_ATTR_MAP[i]&&r.setAttribute(a.SVG_ATTR_MAP[i],n)}))},n.prototype.getTotalLength=function(){var t=this.attr(),n=t.x1,e=t.y1,r=t.x2,o=t.y2;return i.Line.length(n,e,r,o)},n.prototype.getPoint=function(t){var n=this.attr(),e=n.x1,r=n.y1,o=n.x2,a=n.y2;return i.Line.pointAt(e,r,o,a,t)},n}(e(8).default);n.default=u},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(3),o=e(8),a=e(47),u=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="marker",n.canFill=!0,n.canStroke=!0,n}return r.__extends(n,t),n.prototype.createPath=function(t){this.get("el").setAttribute("d",this._assembleMarker())},n.prototype._assembleMarker=function(){var t=this._getPath();return(0,i.isArray)(t)?t.map((function(t){return t.join(" ")})).join(""):t},n.prototype._getPath=function(){var t,n=this.attr(),e=n.x,r=n.y,o=n.r||n.radius,u=n.symbol||"circle";return(t=(0,i.isFunction)(u)?u:a.default.get(u))?t(e,r,o):(console.warn("".concat(t," symbol is not exist.")),null)},n.symbolsFactory=a.default,n}(o.default);n.default=u},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r={circle:function(t,n,e){return[["M",t,n],["m",-e,0],["a",e,e,0,1,0,2*e,0],["a",e,e,0,1,0,2*-e,0]]},square:function(t,n,e){return[["M",t-e,n-e],["L",t+e,n-e],["L",t+e,n+e],["L",t-e,n+e],["Z"]]},diamond:function(t,n,e){return[["M",t-e,n],["L",t,n-e],["L",t+e,n],["L",t,n+e],["Z"]]},triangle:function(t,n,e){var r=e*Math.sin(1/3*Math.PI);return[["M",t-e,n+r],["L",t,n-r],["L",t+e,n+r],["z"]]},triangleDown:function(t,n,e){var r=e*Math.sin(1/3*Math.PI);return[["M",t-e,n-r],["L",t+e,n-r],["L",t,n+r],["Z"]]}};n.default={get:function(t){return r[t]},register:function(t,n){r[t]=n},remove:function(t){delete r[t]},getAll:function(){return r}}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(3),o=e(5),a=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="path",n.canFill=!0,n.canStroke=!0,n}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{startArrow:!1,endArrow:!1})},n.prototype.createPath=function(t,n){var e=this,r=this.attr(),a=this.get("el");(0,i.each)(n||r,(function(n,u){if("path"===u&&(0,i.isArray)(n))a.setAttribute("d",e._formatPath(n));else if("startArrow"===u||"endArrow"===u)if(n){var c=(0,i.isObject)(n)?t.addArrow(r,o.SVG_ATTR_MAP[u]):t.getDefaultArrow(r,o.SVG_ATTR_MAP[u]);a.setAttribute(o.SVG_ATTR_MAP[u],"url(#".concat(c,")"))}else a.removeAttribute(o.SVG_ATTR_MAP[u]);else o.SVG_ATTR_MAP[u]&&a.setAttribute(o.SVG_ATTR_MAP[u],n)}))},n.prototype._formatPath=function(t){var n=t.map((function(t){return t.join(" ")})).join("");return~n.indexOf("NaN")?"":n},n.prototype.getTotalLength=function(){var t=this.get("el");return t?t.getTotalLength():null},n.prototype.getPoint=function(t){var n=this.get("el"),e=this.getTotalLength();if(0===e)return null;var r=n?n.getPointAtLength(t*e):null;return r?{x:r.x,y:r.y}:null},n}(e(8).default);n.default=a},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(3),o=e(5),a=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="polygon",n.canFill=!0,n.canStroke=!0,n}return r.__extends(n,t),n.prototype.createPath=function(t,n){var e=this.attr(),r=this.get("el");(0,i.each)(n||e,(function(t,n){"points"===n&&(0,i.isArray)(t)&&t.length>=2?r.setAttribute("points",t.map((function(t){return"".concat(t[0],",").concat(t[1])})).join(" ")):o.SVG_ATTR_MAP[n]&&r.setAttribute(o.SVG_ATTR_MAP[n],t)}))},n}(e(8).default);n.default=a},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(10),o=e(10),a=e(3),u=e(5),c=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="polyline",n.canFill=!0,n.canStroke=!0,n}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{startArrow:!1,endArrow:!1})},n.prototype.onAttrChange=function(n,e,r){t.prototype.onAttrChange.call(this,n,e,r),-1!==["points"].indexOf(n)&&this._resetCache()},n.prototype._resetCache=function(){this.set("totalLength",null),this.set("tCache",null)},n.prototype.createPath=function(t,n){var e=this.attr(),r=this.get("el");(0,a.each)(n||e,(function(t,n){"points"===n&&(0,a.isArray)(t)&&t.length>=2?r.setAttribute("points",t.map((function(t){return"".concat(t[0],",").concat(t[1])})).join(" ")):u.SVG_ATTR_MAP[n]&&r.setAttribute(u.SVG_ATTR_MAP[n],t)}))},n.prototype.getTotalLength=function(){var t=this.attr().points,n=this.get("totalLength");return(0,a.isNil)(n)?(this.set("totalLength",i.Polyline.length(t)),this.get("totalLength")):n},n.prototype.getPoint=function(t){var n,e,r=this.attr().points,i=this.get("tCache");return i||(this._setTcache(),i=this.get("tCache")),(0,a.each)(i,(function(r,i){t>=r[0]&&t<=r[1]&&(n=(t-r[0])/(r[1]-r[0]),e=i)})),o.Line.pointAt(r[e][0],r[e][1],r[e+1][0],r[e+1][1],n)},n.prototype._setTcache=function(){var t=this.attr().points;if(t&&0!==t.length){var n=this.getTotalLength();if(!(n<=0)){var e,r,i=0,u=[];(0,a.each)(t,(function(a,c){t[c+1]&&((e=[])[0]=i/n,r=o.Line.length(a[0],a[1],t[c+1][0],t[c+1][1]),i+=r,e[1]=i/n,u.push(e))})),this.set("tCache",u)}}},n.prototype.getStartTangent=function(){var t=this.attr().points,n=[];return n.push([t[1][0],t[1][1]]),n.push([t[0][0],t[0][1]]),n},n.prototype.getEndTangent=function(){var t=this.attr().points,n=t.length-1,e=[];return e.push([t[n-1][0],t[n-1][1]]),e.push([t[n][0],t[n][1]]),e},n}(e(8).default);n.default=c},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(3),o=e(8),a=e(5),u=e(52),c=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="rect",n.canFill=!0,n.canStroke=!0,n}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x:0,y:0,width:0,height:0,radius:0})},n.prototype.createPath=function(t,n){var e=this,r=this.attr(),o=this.get("el"),u=!1,c=["x","y","width","height","radius"];(0,i.each)(n||r,(function(t,n){-1===c.indexOf(n)||u?-1===c.indexOf(n)&&a.SVG_ATTR_MAP[n]&&o.setAttribute(a.SVG_ATTR_MAP[n],t):(o.setAttribute("d",e._assembleRect(r)),u=!0)}))},n.prototype._assembleRect=function(t){var n=t.x,e=t.y,r=t.width,o=t.height,a=t.radius;if(!a)return"M ".concat(n,",").concat(e," l ").concat(r,",0 l 0,").concat(o," l").concat(-r," 0 z");var c=(0,u.parseRadius)(a);return(0,i.isArray)(a)?1===a.length?c.r1=c.r2=c.r3=c.r4=a[0]:2===a.length?(c.r1=c.r3=a[0],c.r2=c.r4=a[1]):3===a.length?(c.r1=a[0],c.r2=c.r4=a[1],c.r3=a[2]):(c.r1=a[0],c.r2=a[1],c.r3=a[2],c.r4=a[3]):c.r1=c.r2=c.r3=c.r4=a,[["M ".concat(n+c.r1,",").concat(e)],["l ".concat(r-c.r1-c.r2,",0")],["a ".concat(c.r2,",").concat(c.r2,",0,0,1,").concat(c.r2,",").concat(c.r2)],["l 0,".concat(o-c.r2-c.r3)],["a ".concat(c.r3,",").concat(c.r3,",0,0,1,").concat(-c.r3,",").concat(c.r3)],["l ".concat(c.r3+c.r4-r,",0")],["a ".concat(c.r4,",").concat(c.r4,",0,0,1,").concat(-c.r4,",").concat(-c.r4)],["l 0,".concat(c.r4+c.r1-o)],["a ".concat(c.r1,",").concat(c.r1,",0,0,1,").concat(c.r1,",").concat(-c.r1)],["z"]].join(" ")},n}(o.default);n.default=c},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.parsePath=n.parseRadius=void 0;var r=e(3),i=/[MLHVQTCSAZ]([^MLHVQTCSAZ]*)/gi,o=/[^\s,]+/gi;n.parseRadius=function(t){var n=0,e=0,i=0,o=0;return(0,r.isArray)(t)?1===t.length?n=e=i=o=t[0]:2===t.length?(n=i=t[0],e=o=t[1]):3===t.length?(n=t[0],e=o=t[1],i=t[2]):(n=t[0],e=t[1],i=t[2],o=t[3]):n=e=i=o=t,{r1:n,r2:e,r3:i,r4:o}},n.parsePath=function(t){return t=t||[],(0,r.isArray)(t)?t:(0,r.isString)(t)?(t=t.match(i),(0,r.each)(t,(function(n,e){if((n=n.match(o))[0].length>1){var i=n[0].charAt(0);n.splice(1,0,n[0].substr(1)),n[0]=i}(0,r.each)(n,(function(t,e){isNaN(t)||(n[e]=+t)})),t[e]=n})),t):void 0}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(3),o=e(54),a=e(14),u=e(5),c=e(8),s={top:"before-edge",middle:"central",bottom:"after-edge",alphabetic:"baseline",hanging:"hanging"},f={top:"text-before-edge",middle:"central",bottom:"text-after-edge",alphabetic:"alphabetic",hanging:"hanging"},l={left:"left",start:"left",center:"middle",right:"end",end:"end"},h=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.type="text",n.canFill=!0,n.canStroke=!0,n}return r.__extends(n,t),n.prototype.getDefaultAttrs=function(){var n=t.prototype.getDefaultAttrs.call(this);return r.__assign(r.__assign({},n),{x:0,y:0,text:null,fontSize:12,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",textAlign:"start",textBaseline:"bottom"})},n.prototype.createPath=function(t,n){var e=this,r=this.attr(),o=this.get("el");this._setFont(),(0,i.each)(n||r,(function(t,n){"text"===n?e._setText("".concat(t)):"matrix"===n&&t?(0,a.setTransform)(e):u.SVG_ATTR_MAP[n]&&o.setAttribute(u.SVG_ATTR_MAP[n],t)})),o.setAttribute("paint-order","stroke"),o.setAttribute("style","stroke-linecap:butt; stroke-linejoin:miter;")},n.prototype._setFont=function(){var t=this.get("el"),n=this.attr(),e=n.textBaseline,r=n.textAlign,i=(0,o.detect)();i&&"firefox"===i.name?t.setAttribute("dominant-baseline",f[e]||"alphabetic"):t.setAttribute("alignment-baseline",s[e]||"baseline"),t.setAttribute("text-anchor",l[r]||"left")},n.prototype._setText=function(t){var n=this.get("el"),e=this.attr(),r=e.x,o=e.textBaseline,a=void 0===o?"bottom":o;if(t)if(~t.indexOf("\n")){var u=t.split("\n"),c=u.length-1,s="";(0,i.each)(u,(function(t,n){0===n?"alphabetic"===a?s+='<tspan x="'.concat(r,'" dy="').concat(-c,'em">').concat(t,"</tspan>"):"top"===a?s+='<tspan x="'.concat(r,'" dy="0.9em">').concat(t,"</tspan>"):"middle"===a?s+='<tspan x="'.concat(r,'" dy="').concat(-(c-1)/2,'em">').concat(t,"</tspan>"):"bottom"===a?s+='<tspan x="'.concat(r,'" dy="-').concat(c+.3,'em">').concat(t,"</tspan>"):"hanging"===a&&(s+='<tspan x="'.concat(r,'" dy="').concat(-(c-1)-.3,'em">').concat(t,"</tspan>")):s+='<tspan x="'.concat(r,'" dy="1em">').concat(t,"</tspan>")})),n.innerHTML=s}else n.innerHTML=t;else n.innerHTML=""},n}(c.default);n.default=h},function(t,n,e){"use strict";e.r(n),function(t){e.d(n,"BrowserInfo",(function(){return i})),e.d(n,"NodeInfo",(function(){return o})),e.d(n,"SearchBotDeviceInfo",(function(){return a})),e.d(n,"BotInfo",(function(){return u})),e.d(n,"ReactNativeInfo",(function(){return c})),e.d(n,"detect",(function(){return h})),e.d(n,"browserName",(function(){return d})),e.d(n,"parseUserAgent",(function(){return v})),e.d(n,"detectOS",(function(){return g})),e.d(n,"getNodeVersion",(function(){return y}));var r=function(t,n,e){if(e||2===arguments.length)for(var r,i=0,o=n.length;i<o;i++)!r&&i in n||(r||(r=Array.prototype.slice.call(n,0,i)),r[i]=n[i]);return t.concat(r||Array.prototype.slice.call(n))},i=function(t,n,e){this.name=t,this.version=n,this.os=e,this.type="browser"},o=function(n){this.version=n,this.type="node",this.name="node",this.os=t.platform},a=function(t,n,e,r){this.name=t,this.version=n,this.os=e,this.bot=r,this.type="bot-device"},u=function(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null},c=function(){this.type="react-native",this.name="react-native",this.version=null,this.os=null},s=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,f=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],l=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function h(t){return t?v(t):"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product?new c:"undefined"!=typeof navigator?v(navigator.userAgent):y()}function p(t){return""!==t&&f.reduce((function(n,e){var r=e[0],i=e[1];if(n)return n;var o=i.exec(t);return!!o&&[r,o]}),!1)}function d(t){var n=p(t);return n?n[0]:null}function v(t){var n=p(t);if(!n)return null;var e=n[0],o=n[1];if("searchbot"===e)return new u;var c=o[1]&&o[1].split(".").join("_").split("_").slice(0,3);c?c.length<3&&(c=r(r([],c,!0),function(t){for(var n=[],e=0;e<t;e++)n.push("0");return n}(3-c.length),!0)):c=[];var f=c.join("."),l=g(t),h=s.exec(t);return h&&h[1]?new a(e,f,l,h[1]):new i(e,f,l)}function g(t){for(var n=0,e=l.length;n<e;n++){var r=l[n],i=r[0];if(r[1].exec(t))return i}return null}function y(){return void 0!==t&&t.version?new o(t.version.slice(1)):null}}.call(this,e(27))},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),e(4).__exportStar(e(13),n)},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(4),i=e(13),o=e(5),a=e(22),u=e(14),c=e(9),s=e(15),f=e(23),l=e(57),h=function(t){function n(n){return t.call(this,r.__assign(r.__assign({},n),{autoDraw:!0,renderer:"svg"}))||this}return r.__extends(n,t),n.prototype.getShapeBase=function(){return s},n.prototype.getGroupBase=function(){return f.default},n.prototype.getShape=function(t,n,e){var r=e.target||e.srcElement;if(!o.SHAPE_TO_TAGS[r.tagName]){for(var i=r.parentNode;i&&!o.SHAPE_TO_TAGS[i.tagName];)i=i.parentNode;r=i}return this.find((function(t){return t.get("el")===r}))},n.prototype.createDom=function(){var t=(0,c.createSVGElement)("svg"),n=new l.default(t);return t.setAttribute("width","".concat(this.get("width"))),t.setAttribute("height","".concat(this.get("height"))),this.set("context",n),t},n.prototype.onCanvasChange=function(t){var n=this.get("context"),e=this.get("el");if("sort"===t){var r=this.get("children");r&&r.length&&(0,c.sortDom)(this,(function(t,n){return r.indexOf(t)-r.indexOf(n)?1:0}))}else if("clear"===t){if(e){e.innerHTML="";var i=n.el;i.innerHTML="",e.appendChild(i)}}else"matrix"===t?(0,u.setTransform)(this):"clip"===t?(0,u.setClip)(this,n):"changeSize"===t&&(e.setAttribute("width","".concat(this.get("width"))),e.setAttribute("height","".concat(this.get("height"))))},n.prototype.draw=function(){var t=this.get("context"),n=this.getChildren();(0,u.setClip)(this,t),n.length&&(0,a.drawChildren)(t,n)},n}(i.AbstractCanvas);n.default=h},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(3),i=e(58),o=e(59),a=e(60),u=e(61),c=e(62),s=e(9),f=function(){function t(t){var n=(0,s.createSVGElement)("defs"),e=(0,r.uniqueId)("defs_");n.id=e,t.appendChild(n),this.children=[],this.defaultArrow={},this.el=n,this.canvas=t}return t.prototype.find=function(t,n){for(var e=this.children,r=null,i=0;i<e.length;i++)if(e[i].match(t,n)){r=e[i].id;break}return r},t.prototype.findById=function(t){for(var n=this.children,e=null,r=0;r<n.length;r++)if(n[r].id===t){e=n[r];break}return e},t.prototype.add=function(t){this.children.push(t),t.canvas=this.canvas,t.parent=this},t.prototype.getDefaultArrow=function(t,n){var e=t.stroke||t.strokeStyle;if(this.defaultArrow[e])return this.defaultArrow[e].id;var r=new a.default(t,n);return this.defaultArrow[e]=r,this.el.appendChild(r.el),this.add(r),r.id},t.prototype.addGradient=function(t){var n=new i.default(t);return this.el.appendChild(n.el),this.add(n),n.id},t.prototype.addArrow=function(t,n){var e=new a.default(t,n);return this.el.appendChild(e.el),this.add(e),e.id},t.prototype.addShadow=function(t){var n=new o.default(t);return this.el.appendChild(n.el),this.add(n),n.id},t.prototype.addPattern=function(t){var n=new c.default(t);return this.el.appendChild(n.el),this.add(n),n.id},t.prototype.addClip=function(t){var n=new u.default(t);return this.el.appendChild(n.el),this.add(n),n.id},t}();n.default=f},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(3),i=e(9),o=/^l\s*\(\s*([\d.]+)\s*\)\s*(.*)/i,a=/^r\s*\(\s*([\d.]+)\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*\)\s*(.*)/i,u=/[\d.]+:(#[^\s]+|[^)]+\))/gi;function c(t){var n=t.match(u);if(!n)return"";var e="";return n.sort((function(t,n){return t=t.split(":"),n=n.split(":"),Number(t[0])-Number(n[0])})),(0,r.each)(n,(function(t){t=t.split(":"),e+='<stop offset="'.concat(t[0],'" stop-color="').concat(t[1],'"></stop>')})),e}var s=function(){function t(t){this.cfg={};var n,e,u,s,f,l,h,p=null,d=(0,r.uniqueId)("gradient_");return"l"===t.toLowerCase()[0]?function(t,n){var e,i,a=o.exec(t),u=(0,r.mod)((0,r.toRadian)(parseFloat(a[1])),2*Math.PI),s=a[2];u>=0&&u<.5*Math.PI?(e={x:0,y:0},i={x:1,y:1}):.5*Math.PI<=u&&u<Math.PI?(e={x:1,y:0},i={x:0,y:1}):Math.PI<=u&&u<1.5*Math.PI?(e={x:1,y:1},i={x:0,y:0}):(e={x:0,y:1},i={x:1,y:0});var f=Math.tan(u),l=f*f,h=(i.x-e.x+f*(i.y-e.y))/(l+1)+e.x,p=f*(i.x-e.x+f*(i.y-e.y))/(l+1)+e.y;n.setAttribute("x1",e.x),n.setAttribute("y1",e.y),n.setAttribute("x2",h),n.setAttribute("y2",p),n.innerHTML=c(s)}(t,p=(0,i.createSVGElement)("linearGradient")):(p=(0,i.createSVGElement)("radialGradient"),n=t,e=p,u=a.exec(n),s=parseFloat(u[1]),f=parseFloat(u[2]),l=parseFloat(u[3]),h=u[4],e.setAttribute("cx",s),e.setAttribute("cy",f),e.setAttribute("r",l),e.innerHTML=c(h)),p.setAttribute("id",d),this.el=p,this.id=d,this.cfg=t,this}return t.prototype.match=function(t,n){return this.cfg===n},t}();n.default=s},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(3),i=e(9),o={shadowColor:"color",shadowOpacity:"opacity",shadowBlur:"blur",shadowOffsetX:"dx",shadowOffsetY:"dy"},a={x:"-40%",y:"-40%",width:"200%",height:"200%"},u=function(){function t(t){this.type="filter",this.cfg={},this.type="filter";var n=(0,i.createSVGElement)("filter");return(0,r.each)(a,(function(t,e){n.setAttribute(e,t)})),this.el=n,this.id=(0,r.uniqueId)("filter_"),this.el.id=this.id,this.cfg=t,this._parseShadow(t,n),this}return t.prototype.match=function(t,n){if(this.type!==t)return!1;var e=!0,i=this.cfg;return(0,r.each)(Object.keys(i),(function(t){if(i[t]!==n[t])return e=!1,!1})),e},t.prototype.update=function(t,n){var e=this.cfg;return e[o[t]]=n,this._parseShadow(e,this.el),this},t.prototype._parseShadow=function(t,n){var e='<feDropShadow\n      dx="'.concat(t.dx||0,'"\n      dy="').concat(t.dy||0,'"\n      stdDeviation="').concat(t.blur?t.blur/10:0,'"\n      flood-color="').concat(t.color?t.color:"#000",'"\n      flood-opacity="').concat(t.opacity?t.opacity:1,'"\n      />');n.innerHTML=e},t}();n.default=u},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(3),i=e(9),o=function(){function t(t,n){this.cfg={};var e=(0,i.createSVGElement)("marker"),o=(0,r.uniqueId)("marker_");e.setAttribute("id",o);var a=(0,i.createSVGElement)("path");a.setAttribute("stroke",t.stroke||"none"),a.setAttribute("fill",t.fill||"none"),e.appendChild(a),e.setAttribute("overflow","visible"),e.setAttribute("orient","auto-start-reverse"),this.el=e,this.child=a,this.id=o;var u=t["marker-start"===n?"startArrow":"endArrow"];return this.stroke=t.stroke||"#000",!0===u?this._setDefaultPath(n,a):(this.cfg=u,this._setMarker(t.lineWidth,a)),this}return t.prototype.match=function(){return!1},t.prototype._setDefaultPath=function(t,n){var e=this.el;n.setAttribute("d","M0,0 L".concat(10*Math.cos(Math.PI/6),",5 L0,10")),e.setAttribute("refX","".concat(10*Math.cos(Math.PI/6))),e.setAttribute("refY","".concat(5))},t.prototype._setMarker=function(t,n){var e=this.el,i=this.cfg.path,o=this.cfg.d;(0,r.isArray)(i)&&(i=i.map((function(t){return t.join(" ")})).join("")),n.setAttribute("d",i),e.appendChild(n),o&&e.setAttribute("refX","".concat(o/t))},t.prototype.update=function(t){var n=this.child;n.attr?n.attr("fill",t):n.setAttribute("fill",t)},t}();n.default=o},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(3),i=e(9),o=function(){function t(t){this.type="clip",this.cfg={};var n=(0,i.createSVGElement)("clipPath");this.el=n,this.id=(0,r.uniqueId)("clip_"),n.id=this.id;var e=t.cfg.el;return n.appendChild(e),this.cfg=t,this}return t.prototype.match=function(){return!1},t.prototype.remove=function(){var t=this.el;t.parentNode.removeChild(t)},t}();n.default=o},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(3),i=e(9),o=/^p\s*\(\s*([axyn])\s*\)\s*(.*)/i,a=function(){function t(t){this.cfg={};var n=(0,i.createSVGElement)("pattern");n.setAttribute("patternUnits","userSpaceOnUse");var e=(0,i.createSVGElement)("image");n.appendChild(e);var a=(0,r.uniqueId)("pattern_");n.id=a,this.el=n,this.id=a,this.cfg=t;var u=o.exec(t)[2];e.setAttribute("href",u);var c=new Image;function s(){n.setAttribute("width","".concat(c.width)),n.setAttribute("height","".concat(c.height))}return u.match(/^data:/i)||(c.crossOrigin="Anonymous"),c.src=u,c.complete?s():(c.onload=s,c.src=c.src),this}return t.prototype.match=function(t,n){return this.cfg===n},t}();n.default=a}])}));
//# sourceMappingURL=g.min.js.map