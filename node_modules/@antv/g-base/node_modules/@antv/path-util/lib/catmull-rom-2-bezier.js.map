{"version": 3, "file": "catmull-rom-2-bezier.js", "sourceRoot": "", "sources": ["../src/catmull-rom-2-bezier.ts"], "names": [], "mappings": ";;AAAA,iDAAyC;AAKzC,SAAS,YAAY,CAAC,MAAa,EAAE,MAAc,EAAE,MAAe,EAAE,UAAiB;IACrF,IAAM,GAAG,GAAW,EAAE,CAAC;IACvB,IAAM,aAAa,GAAG,CAAC,CAAC,UAAU,CAAC;IAEnC,IAAI,SAAc,CAAC;IACnB,IAAI,SAAc,CAAC;IACnB,IAAI,GAAS,CAAC;IACd,IAAI,GAAS,CAAC;IACd,IAAI,OAAa,CAAC;IAClB,IAAI,GAAS,CAAC;IACd,IAAI,GAAS,CAAC;IAEd,IAAI,aAAa,EAAE;QACf,GAAG,GAAU,UAAU,GAApB,EAAE,GAAG,GAAK,UAAU,GAAf,CAAgB;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YAChD,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,GAAG,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACrC,GAAG,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;SACtC;KACF;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QACpD,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;YACtB,GAAG,GAAG,KAAK,CAAC;SACb;aAAM,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE;YACnC,GAAG,GAAG,KAAK,CAAC;YACZ,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACd,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACf;aAAM;YACL,IAAM,OAAO,GAAG,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;YAC5B,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAEnD,IAAI,CAAC,GAAS,CAAE,CAAC,EAAE,CAAC,CAAE,CAAC;YACvB,CAAC,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YACtC,CAAC,GAAG,kBAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YAE7B,IAAI,EAAE,GAAG,kBAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YACzC,IAAI,EAAE,GAAG,kBAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAEzC,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;YACpB,IAAI,GAAG,KAAK,CAAC,EAAE;gBACb,EAAE,IAAI,GAAG,CAAC;gBACV,EAAE,IAAI,GAAG,CAAC;aACX;YAED,IAAI,EAAE,GAAG,kBAAI,CAAC,KAAK,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACtC,IAAI,EAAE,GAAG,kBAAI,CAAC,KAAK,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAErC,GAAG,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACpC,OAAO,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAExC,sBAAsB;YACtB,OAAO,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,OAAO,EAAE,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;YAC5E,OAAO,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,OAAO,EAAE,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;YAE5E,cAAc;YACd,EAAE,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACxC,EAAE,GAAG,kBAAI,CAAC,KAAK,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;YACxC,GAAG,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAEpC,wBAAwB;YACxB,GAAG,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,GAAG,EAAE,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;YACpE,GAAG,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,GAAG,EAAE,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;YAEpE,kBAAkB;YAClB,EAAE,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;YACpC,EAAE,GAAG,kBAAI,CAAC,KAAK,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YACvC,OAAO,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAExC,IAAI,aAAa,EAAE;gBACjB,GAAG,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACnC,GAAG,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACnC,OAAO,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;gBAC3C,OAAO,GAAG,kBAAI,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;aAC5C;YAED,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACd,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACd,GAAG,GAAG,OAAO,CAAC;SACf;KACF;IAED,IAAI,MAAM,EAAE;QACV,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;KACvB;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;;GAKG;AACH,SAAS,iBAAiB,CACxB,GAAa,EACb,CAAkB,EAClB,UAGC;IAJD,kBAAA,EAAA,SAAkB;IAClB,2BAAA,EAAA;QACE,CAAE,CAAC,EAAE,CAAC,CAAE;QACR,CAAE,CAAC,EAAE,CAAC,CAAE;KACT;IAED,IAAM,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IACnB,IAAM,SAAS,GAAU,EAAE,CAAC;IAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QAC7C,SAAS,CAAC,IAAI,CAAC,CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC;KACxC;IAED,IAAM,gBAAgB,GAAG,YAAY,CAAC,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC1E,IAAM,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC;IAC7B,IAAM,EAAE,GAAkB,EAAE,CAAC;IAE7B,IAAI,GAAS,CAAC;IACd,IAAI,GAAS,CAAC;IACd,IAAI,CAAM,CAAC;IAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QACnC,GAAG,GAAG,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9B,GAAG,GAAG,gBAAgB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAErB,EAAE,CAAC,IAAI,CAAC,CAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;KAC9D;IAED,IAAI,MAAM,EAAE;QACV,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAC5B,GAAG,GAAG,gBAAgB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAC9B,CAAC,GAAK,SAAS,GAAd,CAAe;QAElB,EAAE,CAAC,IAAI,CAAC,CAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;KAC9D;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,kBAAe,iBAAiB,CAAC"}