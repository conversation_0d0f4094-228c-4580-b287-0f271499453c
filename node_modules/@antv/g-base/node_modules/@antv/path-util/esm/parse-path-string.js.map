{"version": 3, "file": "parse-path-string.js", "sourceRoot": "", "sources": ["../src/parse-path-string.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAErC,IAAM,MAAM,GAAG,0IAA0I,CAAC;AAC1J,IAAM,YAAY,GAAG,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,GAAG,uCAAuC,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,EAAE,IAAI,CAAC,CAAC;AAC3I,IAAM,WAAW,GAAG,IAAI,MAAM,CAAC,oCAAoC,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC;AAE9G,oEAAoE;AACpE,MAAM,CAAC,OAAO,UAAU,eAAe,CAAC,UAAkB;IACxD,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,IAAI,CAAC;KACb;IAED,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;QACvB,OAAO,UAAU,CAAC;KACnB;IACD,IAAM,WAAW,GAAG;QAClB,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;KACL,CAAC;IACF,IAAM,IAAI,GAAG,EAAE,CAAC;IAEhB,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,CAAC;YACnC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QACH,IAAI,IAAI,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,GAAG,GAAG,CAAC;YACX,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;SAC3B;QACD,IAAI,IAAI,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;SAC7B;QACD,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;SACjC;aAAM;YACL,OAAO,MAAM,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;gBACzC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;oBACtB,MAAM;iBACP;aACF;SACF;QACD,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC;IAEH,OAAO,IAAI,CAAC;AACd,CAAC"}