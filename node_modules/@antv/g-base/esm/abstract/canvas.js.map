{"version": 3, "file": "canvas.js", "sourceRoot": "", "sources": ["../../src/abstract/canvas.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,SAAS,MAAM,aAAa,CAAC;AAGpC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,QAAQ,MAAM,qBAAqB,CAAC;AAC3C,OAAO,eAAe,MAAM,0BAA0B,CAAC;AAEvD,IAAM,SAAS,GAAG,IAAI,CAAC;AAEvB,IAAM,OAAO,GAAG,MAAM,EAAE,CAAC;AACzB,IAAM,SAAS,GAAG,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC;AAExD;IAA8B,0BAAS;IACrC,gBAAY,GAAc;QAA1B,YACE,kBAAM,GAAG,CAAC,SAKX;QAJC,KAAI,CAAC,aAAa,EAAE,CAAC;QACrB,KAAI,CAAC,OAAO,EAAE,CAAC;QACf,KAAI,CAAC,UAAU,EAAE,CAAC;QAClB,KAAI,CAAC,YAAY,EAAE,CAAC;;IACtB,CAAC;IAED,8BAAa,GAAb;QACE,IAAM,GAAG,GAAG,iBAAM,aAAa,WAAE,CAAC;QAClC,sCAAsC;QACtC,GAAG,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;QAC1B,mDAAmD;QACnD,GAAG,CAAC,qBAAqB,CAAC,GAAG,KAAK,CAAC;QACnC,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;OAGG;IACH,8BAAa,GAAb;QACE,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;YACvB,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC/C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;SAClC;IACH,CAAC;IAED;;;OAGG;IACH,wBAAO,GAAP;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACnB,QAAQ;QACR,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACxC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAC1B,SAAS;QACT,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;IACzD,CAAC;IAQD;;;OAGG;IACH,2BAAU,GAAV;QACE,IAAM,eAAe,GAAG,IAAI,eAAe,CAAC;YAC1C,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;QACH,eAAe,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACH,6BAAY,GAAZ;QACE,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACH,2BAAU,GAAV,UAAW,KAAa,EAAE,MAAc;QACtC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,SAAS,EAAE;YACb,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,SAAS,CAAC;YACnC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;SACtC;IACH,CAAC;IAED,OAAO;IACP,2BAAU,GAAV,UAAW,KAAa,EAAE,MAAc;QACtC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC3B,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,4BAAW,GAAX;QACE,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACH,0BAAS,GAAT;QACE,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,0BAAS,GAAT,UAAU,MAAc;QACtB,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC3B,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,SAAS,IAAI,EAAE,EAAE;YACnB,qBAAqB;YACrB,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;SAC1B;IACH,CAAC;IAED,OAAO;IACP,gCAAe,GAAf,UAAgB,EAAS;QACvB,IAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAC5D,IAAI,mBAAmB,EAAE;YACvB,oBAAoB;YACpB,IAAI,SAAS,IAAI,CAAC,KAAK,CAAE,EAAU,CAAC,MAAM,CAAC,IAAK,EAAU,CAAC,MAAM,KAAM,EAAiB,CAAC,OAAO,EAAE;gBAChG,OAAO;oBACL,CAAC,EAAG,EAAU,CAAC,MAAM;oBACrB,CAAC,EAAG,EAAU,CAAC,MAAM;iBACtB,CAAC;aACH;YACD,IAAI,CAAC,KAAK,CAAE,EAAiB,CAAC,OAAO,CAAC,EAAE;gBACtC,iDAAiD;gBACjD,OAAO;oBACL,CAAC,EAAG,EAAiB,CAAC,OAAO;oBAC7B,CAAC,EAAG,EAAiB,CAAC,OAAO;iBAC9B,CAAC;aACH;SACF;QACD,+DAA+D;QAC/D,8BAA8B;QACxB,IAAA,KAA6B,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAjD,OAAO,OAAA,EAAK,OAAO,OAA8B,CAAC;QAC7D,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAED,wCAAwC;IACxC,iCAAgB,GAAhB,UAAiB,EAAS;QACxB,IAAI,UAAU,GAAuB,EAAgB,CAAC;QACtD,IAAK,EAAiB,CAAC,OAAO,EAAE;YAC9B,IAAI,EAAE,CAAC,IAAI,KAAK,UAAU,EAAE;gBAC1B,UAAU,GAAI,EAAiB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;aACnD;iBAAM;gBACL,UAAU,GAAI,EAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aAC5C;SACF;QACD,OAAO;YACL,CAAC,EAAE,UAAU,CAAC,OAAO;YACrB,CAAC,EAAE,UAAU,CAAC,OAAO;SACtB,CAAC;IACJ,CAAC;IAED,OAAO;IACP,iCAAgB,GAAhB,UAAiB,OAAe,EAAE,OAAe;QAC/C,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAM,IAAI,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC;QACxC,OAAO;YACL,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,IAAI;YACtB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,GAAG;SACtB,CAAC;IACJ,CAAC;IAED,OAAO;IACP,iCAAgB,GAAhB,UAAiB,CAAS,EAAE,CAAS;QACnC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAM,IAAI,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC;QACxC,OAAO;YACL,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI;YAChB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG;SAChB,CAAC;IACJ,CAAC;IAED,OAAO;IACP,qBAAI,GAAJ,cAAQ,CAAC;IAET;;;OAGG;IACH,0BAAS,GAAT;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,4BAAW,GAAX;QACE,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACpD,eAAe,CAAC,OAAO,EAAE,CAAC;IAC5B,CAAC;IAED,yBAAQ,GAAR;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAAS,GAAT;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,wBAAO,GAAP;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtC,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;YACzB,OAAO;SACR;QACD,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,cAAc;QACd,IAAI,QAAQ,EAAE;YACZ,cAAc;YACd,QAAQ,CAAC,IAAI,EAAE,CAAC;SACjB;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,iBAAM,OAAO,WAAE,CAAC;IAClB,CAAC;IACH,aAAC;AAAD,CAAC,AAnOD,CAA8B,SAAS,GAmOtC;AAED,eAAe,MAAM,CAAC"}