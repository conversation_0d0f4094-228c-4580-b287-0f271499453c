{"version": 3, "file": "line.js", "sourceRoot": "", "sources": ["../../src/bbox/line.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,cAAc,EAAE,MAAM,QAAQ,CAAC;AAExC,MAAM,CAAC,OAAO,WAAW,KAAa;IACpC,IAAM,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IACnB,IAAA,EAAE,GAAiB,KAAK,GAAtB,EAAE,EAAE,GAAa,KAAK,GAAlB,EAAE,EAAE,GAAS,KAAK,GAAd,EAAE,EAAE,GAAK,KAAK,GAAV,CAAW;IACjC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9B,IAAI,IAAI,GAAG;QACT,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,IAAI,MAAA;KACL,CAAC;IACF,IAAI,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACnC,OAAO;QACL,CAAC,EAAE,IAAI,CAAC,IAAI;QACZ,CAAC,EAAE,IAAI,CAAC,IAAI;QACZ,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;QAC5B,MAAM,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;KAC9B,CAAC;AACJ,CAAC"}