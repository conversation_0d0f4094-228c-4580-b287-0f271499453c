{"version": 3, "file": "stack.js", "sourceRoot": "", "sources": ["../../src/adjusts/stack.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,CAAC,MAAM,YAAY,CAAC;AAEhC,OAAO,MAAM,MAAM,UAAU,CAAC;AAE9B,IAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AAEtB;IAAmC,yBAAM;IACvC,eAAY,GAAa;QAAzB,YACE,kBAAM,GAAG,CAAC,SAOX;QALS,IAAA,KAAuE,GAAG,YAAvD,EAAnB,WAAW,mBAAG,CAAC,GAAG,CAAC,KAAA,EAAE,KAAkD,GAAG,OAAzC,EAAZ,MAAM,mBAAG,GAAG,KAAA,EAAE,KAAoC,GAAG,KAA9B,EAAT,IAAI,mBAAG,EAAE,KAAA,EAAE,KAAyB,GAAG,aAAR,EAApB,YAAY,mBAAG,KAAK,KAAA,CAAS;QACnF,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,KAAI,CAAC,YAAY,GAAG,YAAY,CAAC;;IACnC,CAAC;IAED;;;OAGG;IACI,uBAAO,GAAd,UAAe,cAAwB;QAC/B,IAAA,KAA2B,IAAI,EAA7B,MAAM,YAAA,EAAE,YAAY,kBAAS,CAAC;QAEtC,8BAA8B;QAC9B,YAAY;QACZ,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QAE/F,OAAO,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IAEO,uBAAO,GAAf,UAAgB,gBAA0B;QACxC,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IAC7C,CAAC;IAEO,4BAAY,GAApB,UAAqB,cAAwB;QACrC,IAAA,KAAmC,IAAI,EAArC,MAAM,YAAA,EAAE,MAAM,YAAA,EAAE,YAAY,kBAAS,CAAC;QAE9C,SAAS;QACT,IAAM,gBAAgB,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QAEtF,kBAAkB;QAClB,IAAM,QAAQ,GAAG,IAAI,KAAK,EAAU,CAAC;QACrC,IAAM,QAAQ,GAAG,IAAI,KAAK,EAAU,CAAC;QAErC,OAAO,gBAAgB,CAAC,GAAG,CAAC,UAAC,SAAS;YACpC,OAAO,SAAS,CAAC,GAAG,CAAC,UAAC,IAAI;;gBACxB,IAAM,CAAC,GAAW,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,GAAW,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;gBAEtC,IAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAE1B,2DAA2D;gBAC3D,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE5B,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;oBACf,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;oBAE3C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBACpB,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;qBACpB;oBACD,IAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAW,CAAC;oBACzC,IAAM,SAAS,GAAG,CAAC,GAAG,MAAM,CAAC;oBAE7B,MAAM;oBACN,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;oBAE3B,6BACK,IAAI,gBAEN,MAAM,IAAG,CAAC,MAAM,EAAE,SAAS,CAAC,OAC7B;iBACH;gBAED,aAAa;gBACb,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kCAAkB,GAA1B,UAA2B,cAAwB;QAAnD,iBAkCC;QAjCO,IAAA,KAAmC,IAAI,EAArC,MAAM,YAAA,EAAE,MAAM,YAAA,EAAE,YAAY,kBAAS,CAAC;QAC9C,IAAM,MAAM,GAAG,GAAG,CAAC;QAEnB,YAAY;QACZ,IAAM,gBAAgB,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QAEtF,SAAS;QACT,IAAM,KAAK,GAAG,IAAI,KAAK,EAAU,CAAC;QAElC,OAAO,gBAAgB,CAAC,GAAG,CAAC,UAAC,SAAS;YACpC,OAAO,SAAS,CAAC,GAAG,CAClB,UAAC,IAAI;;gBACK,IAAA,IAAI,GAAK,KAAI,KAAT,CAAU;gBACtB,IAAM,MAAM,GAAW,IAAI,CAAC,MAAM,CAAC,CAAC;gBAEpC,yBAAyB;gBACzB,IAAM,WAAW,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;gBAExC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;oBACtB,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ;iBAC7C;gBAED,IAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAW,CAAC;gBAC/C,mBAAmB;gBACnB,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,GAAG,WAAW,CAAC,CAAC;gBAE5C,6BACK,IAAI,gBACN,MAAM,IAAG,UAAU,OACpB;YACJ,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IACH,YAAC;AAAD,CAAC,AA7GD,CAAmC,MAAM,GA6GxC"}