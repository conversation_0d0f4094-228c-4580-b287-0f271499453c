import { Data, DodgeCfg } from '../interface';
import Adjust from './adjust';
export default class Dodge extends Adjust {
    private cacheMap;
    private adjustDataArray;
    private mergeData;
    constructor(cfg: DodgeCfg);
    process(groupDataArray: Data[][]): Data[][];
    protected adjustDim(dim: string, values: number[], data: Data[], frameIndex: number): any[];
    private getDodgeOffset;
    private getIntervalOnlyOffset;
    private getDodgeOnlyOffset;
    private getIntervalAndDodgeOffset;
    private getDistribution;
}
