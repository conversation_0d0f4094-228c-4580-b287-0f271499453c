{"version": 3, "file": "dodge.js", "sourceRoot": "", "sources": ["../../src/adjusts/dodge.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,CAAC,MAAM,YAAY,CAAC;AAChC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAExD,OAAO,MAAM,MAAM,UAAU,CAAC;AAE9B;IAAmC,yBAAM;IAKvC,eAAY,GAAa;QAAzB,YACE,kBAAM,GAAG,CAAC,SA2BX;QAhCO,cAAQ,GAA2B,EAAE,CAAC;QACtC,qBAAe,GAAa,EAAE,CAAC;QAC/B,eAAS,GAAW,EAAE,CAAC;QAK3B,IAAA,KAYE,GAAG,YAZqB,EAA1B,WAAW,mBAAG,YAAY,KAAA,EAC1B,KAWE,GAAG,WAXmB,EAAxB,UAAU,mBAAG,WAAW,KAAA,EACxB,OAAO,GAUL,GAAG,QAVE,EACP,eAAe,GASb,GAAG,gBATU,EACf,YAAY,GAQV,GAAG,aARO,EACZ,gBAAgB,GAOd,GAAG,iBAPW,EAChB,QAAQ,GAMN,GAAG,SANG,EACR,WAAW,GAKT,GAAG,YALM,EACX,cAAc,GAIZ,GAAG,eAJS,EACd,cAAc,GAGZ,GAAG,eAHS,EACd,gBAAgB,GAEd,GAAG,iBAFW,EAChB,YAAY,GACV,GAAG,aADO,CACN;QACR,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,KAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,KAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,KAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,KAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,KAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,KAAI,CAAC,YAAY,GAAG,YAAY,CAAC;;IACnC,CAAC;IAEM,uBAAO,GAAd,UAAe,cAAwB;QACrC,IAAM,gBAAgB,GAAG,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACjD,YAAY;QACZ,IAAM,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAEtC,IAAA,OAAO,GAAK,IAAI,QAAT,CAAU;QAEzB,kBAAkB;QAClB,IAAM,eAAe,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;QAEjF,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;QAE5C,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAES,yBAAS,GAAnB,UAAoB,GAAW,EAAE,MAAgB,EAAE,IAAY,EAAE,UAAkB;QAAnF,iBA+BC;QA9BS,IAAA,YAAY,GAAK,IAAI,aAAT,CAAU;QAC9B,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QACtC,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ;QAErD,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,UAAC,KAAK,EAAE,GAAG;YAC3B,IAAI,KAAY,CAAC;YAEjB,2BAA2B;YAC3B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;gBACvB,KAAK,GAAG;oBACN,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;oBAClB,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;iBACpB,CAAC;aACH;iBAAM;gBACL,mBAAmB;gBACnB,KAAK,GAAG,KAAI,CAAC,cAAc,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;aAC3D;YACD,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,CAAC;gBACd,IAAM,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACrB,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC9C,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;oBAClB,IAAA,GAAG,GAAW,KAAK,IAAhB,EAAE,IAAI,GAAK,KAAK,KAAV,CAAW;oBAC5B,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;iBAChG;qBAAM;oBACL,CAAC,CAAC,GAAG,CAAC,GAAG,KAAI,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;iBAChE;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,8BAAc,GAAtB,UAAuB,KAAY,EAAE,GAAW,EAAE,GAAW;QACrD,IAAA,KAKF,IAAI,EAJN,UAAU,gBAAA,EACV,WAAW,iBAAA,EACX,eAAe,qBAAA,EACf,YAAY,kBACN,CAAC;QACD,IAAA,GAAG,GAAW,KAAK,IAAhB,EAAE,IAAI,GAAK,KAAK,KAAV,CAAW;QAE5B,IAAM,UAAU,GAAG,IAAI,GAAG,GAAG,CAAC;QAC9B,IAAI,QAAQ,CAAC;QACb,UAAU;QACV,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,eAAe,IAAI,CAAC,EAAE;YAC9E,qBAAqB;YACrB,IAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACpD,QAAQ,GAAG,GAAG,GAAG,MAAM,CAAC;SACzB;aAAM,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,YAAY,IAAI,CAAC,EAAE;YAClF,kBAAkB;YAClB,IAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACjD,QAAQ,GAAG,GAAG,GAAG,MAAM,CAAC;SACzB;aAAM,IACL,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC;YACzB,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC;YACtB,eAAe,IAAI,CAAC;YACpB,YAAY,IAAI,CAAC,EACjB;YACA,mCAAmC;YACnC,IAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACxD,QAAQ,GAAG,GAAG,GAAG,MAAM,CAAC;SACzB;aAAM;YACL,OAAO;YACP,IAAM,KAAK,GAAG,CAAC,UAAU,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC;YAC9C,IAAM,MAAM,GAAG,WAAW,GAAG,KAAK,CAAC;YACnC,IAAM,MAAM,GACZ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;gBACzD,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC;gBAClC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;gBACf,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC;YACrB,QAAQ,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;SACtC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,qCAAqB,GAA7B,UAA8B,GAAW,EAAE,GAAW;QAC9C,IAAA,KASF,IAAI,EARN,WAAW,iBAAA,EACX,eAAe,qBAAA,EACf,iBAAiB,uBAAA,EACjB,QAAQ,cAAA,EACR,UAAU,gBAAA,EACV,cAAc,oBAAA,EACd,cAAc,oBAAA,EACd,gBAAgB,sBACV,CAAC;QACT,IAAM,yBAAyB,GAAG,eAAe,GAAG,iBAAiB,CAAC;QACtE,IAAI,sBAAsB,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,yBAAyB,CAAC,GAAG,QAAQ,GAAG,UAAU,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAClH,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,yBAAyB,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,sBAAsB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QACzH,mEAAmE;QACnE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,GAAG,GAAG,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC;QAC7F,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;YAC5B,IAAM,kBAAkB,GAAG,cAAc,GAAG,iBAAiB,CAAC;YAC9D,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;YAC5B,IAAM,kBAAkB,GAAG,cAAc,GAAG,iBAAiB,CAAC;YAC9D,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;SACrD;QACD,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACxE,SAAS;QACT,sBAAsB,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,yBAAyB,CAAC,GAAG,QAAQ,GAAG,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACrH,IAAM,MAAM,GACV,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,GAAG,GAAG,sBAAsB;YACzD,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,yBAAyB,CAAC,GAAG,QAAQ;YAC/C,yBAAyB,GAAG,CAAC,CAAC;QAChC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,kCAAkB,GAA1B,UAA2B,GAAW,EAAE,GAAW;QAC3C,IAAA,KASF,IAAI,EARN,WAAW,iBAAA,EACX,YAAY,kBAAA,EACZ,iBAAiB,uBAAA,EACjB,QAAQ,cAAA,EACR,WAAW,iBAAA,EACX,cAAc,oBAAA,EACd,cAAc,oBAAA,EACd,gBAAgB,sBACV,CAAC;QACT,IAAM,sBAAsB,GAAG,YAAY,GAAG,iBAAiB,CAAC;QAChE,IAAI,yBAAyB,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QACjE,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,yBAAyB,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,sBAAsB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QACzH,mEAAmE;QACnE,SAAS,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,GAAG,GAAG,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC;QACjF,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;YAC5B,IAAM,kBAAkB,GAAG,cAAc,GAAG,iBAAiB,CAAC;YAC9D,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;YAC5B,IAAM,kBAAkB,GAAG,cAAc,GAAG,iBAAiB,CAAC;YAC9D,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;SACrD;QACD,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACxE,QAAQ;QACR,yBAAyB,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,GAAG,GAAG,sBAAsB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QACrH,IAAM,MAAM,GACV,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,GAAG,GAAG,sBAAsB;YACzD,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,yBAAyB,CAAC,GAAG,QAAQ;YAC/C,yBAAyB,GAAG,CAAC,CAAC;QAChC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,yCAAyB,GAAjC,UAAkC,GAAW,EAAE,GAAW;QAClD,IAAA,KAKF,IAAI,EAJN,eAAe,qBAAA,EACf,YAAY,kBAAA,EACZ,iBAAiB,uBAAA,EACjB,QAAQ,cACF,CAAC;QACT,IAAM,yBAAyB,GAAG,eAAe,GAAG,iBAAiB,CAAC;QACtE,IAAM,sBAAsB,GAAG,YAAY,GAAG,iBAAiB,CAAC;QAChE,IAAM,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,yBAAyB,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,sBAAsB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QAC3H,IAAM,MAAM,GACV,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,GAAG,GAAG,sBAAsB;YACzD,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,yBAAyB,CAAC,GAAG,QAAQ;YAC/C,yBAAyB,GAAG,CAAC,CAAC;QAChC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,+BAAe,GAAvB,UAAwB,GAAW;QACjC,IAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QAC9C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QAExB,IAAI,CAAC,GAAG,EAAE;YACR,GAAG,GAAG,EAAE,CAAC;YACT,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,UAAC,IAAI,EAAE,KAAK;gBACnC,IAAM,MAAM,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAa,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBAClB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAChB;gBACD,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,UAAC,GAAW;oBACzB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;wBACb,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;qBACf;oBACD,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;SACrB;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IACH,YAAC;AAAD,CAAC,AAlPD,CAAmC,MAAM,GAkPxC"}