{"version": 3, "file": "symmetric.js", "sourceRoot": "", "sources": ["../../src/adjusts/symmetric.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,CAAC,MAAM,YAAY,CAAC;AAEhC,OAAO,MAAM,MAAM,UAAU,CAAC;AAE9B;IAAuC,6BAAM;IAA7C;;IAyDA,CAAC;IAxDQ,2BAAO,GAAd,UAAe,cAAwB;QACrC,IAAM,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAEtC,IAAA,KAAqB,IAAI,EAAvB,MAAM,YAAA,EAAE,MAAM,YAAS,CAAC;QAEhC,gBAAgB;QAChB,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAE/C,YAAY;QACZ,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,UAAC,GAAG,IAAK,OAAA,KAAK,CAAC,GAAG,CAAC,EAAV,CAAU,CAAC,CAAC,CAAC;QAErE,OAAO,CAAC,CAAC,GAAG,CAAC,cAAc,EAAE,UAAC,SAAS;YACrC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE,UAAC,IAAI;;gBAC3B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC5B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;gBAE5B,SAAS;gBACT,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACrB,IAAM,KAAG,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;oBAEtC,6BACK,IAAI,gBACN,MAAM,IAAG,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,UAAC,CAAS,IAAK,OAAA,KAAG,GAAG,CAAC,EAAP,CAAO,CAAC,OAC/C;iBACH;gBAED,UAAU;gBACV,IAAM,MAAM,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClC,6BACK,IAAI,gBACN,MAAM,IAAG,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,OACnC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,iBAAiB;IACT,oCAAgB,GAAxB,UAAyB,SAAiB;QAA1C,iBAQC;QAPO,IAAA,KAAqB,IAAI,EAAvB,MAAM,YAAA,EAAE,MAAM,YAAS,CAAC;QAEhC,mBAAmB;QACnB,IAAM,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,CAAW,EAAtB,CAAsB,CAAC,CAAC;QAE9E,qBAAqB;QACrB,OAAO,CAAC,CAAC,SAAS,CAAC,cAAc,EAAE,UAAC,SAAS,IAAK,OAAA,KAAI,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,EAAtC,CAAsC,CAAC,CAAC;IAC5F,CAAC;IAEO,kCAAc,GAAtB,UAAuB,SAAiB,EAAE,GAAW;QACnD,cAAc;QACd,IAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE,UAAC,IAAI,IAAK,OAAA,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,EAApB,CAAoB,CAAC,CAAC;QACnE,uCAAuC;QACvC,IAAM,aAAa,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE3C,WAAW;QACX,OAAO,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,aAAa,EAAE;IACpC,CAAC;IACH,gBAAC;AAAD,CAAC,AAzDD,CAAuC,MAAM,GAyD5C"}