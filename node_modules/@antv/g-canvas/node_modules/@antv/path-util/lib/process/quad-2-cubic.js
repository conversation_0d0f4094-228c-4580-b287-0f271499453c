"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.quadToCubic = void 0;
function quadToCubic(x1, y1, qx, qy, x2, y2) {
    var r13 = 1 / 3;
    var r23 = 2 / 3;
    return [
        r13 * x1 + r23 * qx,
        r13 * y1 + r23 * qy,
        r13 * x2 + r23 * qx,
        r13 * y2 + r23 * qy,
        x2, y2, // x,y
    ];
}
exports.quadToCubic = quadToCubic;
//# sourceMappingURL=quad-2-cubic.js.map