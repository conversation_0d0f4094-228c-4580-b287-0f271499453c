{"name": "@antv/path-util", "version": "2.0.15", "description": "A common util collection for antv projects", "main": "lib/index.js", "types": "lib/index.d.ts", "module": "esm/index.js", "files": ["src", "package.json", "esm", "lib", "README.md"], "scripts": {"build": "npm run clean && run-p build:*", "build:esm": "tsc -p tsconfig.json --target ES5 --module ESNext --outDir esm", "build:cjs": "tsc -p tsconfig.json --target ES5 --module commonjs --outDir lib", "clean": "rm -rf lib && rm -rf esm", "coverage": "npm run coverage-generator && npm run coverage-viewer", "coverage-generator": "torch --coverage --compile --source-pattern src/*.js,src/**/*.js --opts __tests__/mocha.opts", "coverage-viewer": "torch-coverage", "test": "torch --renderer --compile --opts __tests__/mocha.opts", "test-live": "torch --compile --interactive --opts __tests__/mocha.opts", "tsc": "tsc --noEmit", "typecheck": "tsc --noEmit"}, "repository": {"type": "git", "url": "git+https://github.com/antvis/util.git"}, "keywords": ["util", "antv", "g"], "author": "https://github.com/orgs/antvis/people", "license": "ISC", "bugs": {"url": "https://github.com/antvis/util/issues"}, "devDependencies": {"@antv/torch": "^1.0.0", "chai": "^4.2.0", "less": "^3.9.0", "npm-run-all": "^4.1.5"}, "homepage": "https://github.com/antvis/util#readme", "dependencies": {"@antv/matrix-util": "^3.0.4", "@antv/util": "^2.0.9", "tslib": "^2.0.3"}}