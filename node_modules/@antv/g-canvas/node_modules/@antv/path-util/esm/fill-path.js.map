{"version": 3, "file": "fill-path.js", "sourceRoot": "", "sources": ["../src/fill-path.ts"], "names": [], "mappings": "AAAA,SAAS,WAAW,CAAC,MAAM,EAAE,CAAC;IAC5B,IAAM,IAAI,GAAG,EAAE,CAAC;IAChB,IAAM,KAAK,GAAG,EAAE,CAAC;IAEjB,SAAS,OAAO,CAAC,MAAM,EAAE,CAAC;QACxB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACvB;aAAM;YACL,IAAM,YAAY,GAAG,EAAE,CAAC;YACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1C,IAAI,CAAC,KAAK,CAAC,EAAE;oBACX,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;iBACtB;gBACD,IAAI,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC3B,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBAC3B;gBACD,YAAY,CAAC,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC;aACpH;YACD,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;SAC1B;IACH,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,EAAE;QACjB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KACpB;IACD,OAAO,EAAE,IAAI,MAAA,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;AAC1C,CAAC;AAED,SAAS,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK;IACnC,IAAM,MAAM,GAAG,CAAE,CAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAE,CAAE,CAAC;IAC1C,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;IACnB,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAClB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACrB;SAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACzB,MAAM,CAAC,IAAI,CAAC,CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;QAChC,MAAM,CAAC,IAAI,CAAC,CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;QAChC,MAAM,CAAC,IAAI,CAAC,CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;KACjC;SAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC3C,MAAM,CAAC,IAAI,CAAC,CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;QAChC,MAAM,CAAC,IAAI,CAAC,CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;KACjC;SAAM;QACL,MAAM,CAAC,IAAI,CAAC,CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;KACjC;IAED,IAAI,YAAY,GAAG,MAAM,CAAC;IAC1B,IAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAClC,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3B,IAAM,KAAK,GAAG,WAAW,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QAC5C,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1B,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC;KAC5B;IACD,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC5B,IAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAC,OAAO;QAClC,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACd,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9B;QACD,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;YACvB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACf;YACD,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9B;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACf;QACD,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9C,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK;IACrC,IAAI,KAAK,KAAK,CAAC,EAAE;QACf,OAAO,CAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,CAAC;KAC7B;IACD,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACtD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;KAC3D;SAAM;QACL,IAAM,IAAI,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACnB,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;SACf;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACnC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrB;KACF;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,MAAM,CAAC,OAAO,UAAU,QAAQ,CAAC,MAAM,EAAE,MAAM;IAC7C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,MAAM,CAAC;KACf;IACD,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACpC,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACpC,IAAM,KAAK,GAAG,SAAS,GAAG,SAAS,CAAC;IACpC,IAAM,cAAc,GAAG,EAAE,CAAC;IAC1B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;YAC9C,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACxB;QACD,OAAO,MAAM,CAAC;KACf;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;QAClC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACpC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;KAC1D;IACD,IAAM,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,KAAK,EAAE,CAAC;QACpD,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;SACzC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACtE,CAAC,EAAoC,EAAE,CAAC,CAAC;IACzC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;QAC1D,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAClB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}