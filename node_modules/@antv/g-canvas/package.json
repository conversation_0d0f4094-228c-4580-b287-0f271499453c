{"name": "@antv/g-canvas", "version": "0.5.17", "description": "A canvas library which providing 2d", "main": "lib/index.js", "module": "esm/index.js", "unpkg": "dist/g.min.js", "types": "lib/index.d.ts", "files": ["src", "package.json", "esm", "lib", "dist", "LICENSE", "README.md"], "scripts": {"build": "npm run clean && run-p build:*", "build:esm": "tsc -p tsconfig.json --target ES5 --module ESNext --outDir esm", "build:cjs": "tsc -p tsconfig.json --target ES5 --module commonjs --outDir lib", "build:umd": "webpack --config webpack.config.js --mode production", "clean": "rm -rf esm lib dist", "coverage": "npm run coverage-generator && npm run coverage-viewer", "coverage-generator": "torch --coverage --compile --source-pattern src/*.js,src/**/*.js --opts tests/mocha.opts", "coverage-viewer": "torch-coverage", "test": "torch --renderer --compile --opts tests/mocha.opts", "test-live": "torch --compile --interactive tests/unit", "tsc": "tsc --noEmit", "typecheck": "tsc --noEmit", "dist": "webpack --config webpack.config.js --mode production"}, "repository": {"type": "git", "url": "git+https://github.com/antvis/g.git"}, "keywords": ["util", "antv", "g"], "publishConfig": {"access": "public"}, "author": "https://github.com/orgs/antvis/people", "license": "ISC", "bugs": {"url": "https://github.com/antvis/g/issues"}, "devDependencies": {"@antv/torch": "^1.0.0", "less": "^4.1.1", "npm-run-all": "^4.1.5", "webpack": "^4.26.1", "webpack-cli": "^4.5.0"}, "homepage": "https://github.com/antvis/g#readme", "dependencies": {"@antv/g-base": "^0.5.12", "@antv/g-math": "^0.1.9", "@antv/matrix-util": "^3.1.0-beta.1", "@antv/path-util": "~2.0.5", "@antv/util": "~2.0.0", "gl-matrix": "^3.0.0", "tslib": "^2.0.3"}}