{"version": 3, "file": "locale.js", "sourceRoot": "", "sources": ["../../src/core/locale.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAEjC,OAAO,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAC;AACpC,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC,IAAM,SAAS,GAAG,EAAE,CAAC;AAErB;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAAC,MAAc,EAAE,SAAiB;IAC9D,SAAS,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;AAChC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,SAAS,CAAC,MAAc;IACtC,OAAO;QACL,GAAG,EAAE,UAAC,GAAsB,EAAE,GAAyB;YACrD,OAAO,QAAQ,CACb,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,EACxG,GAAG,CACJ,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import { get } from '@antv/util';\nimport { Locale } from '../types/locale';\nimport { template } from '../utils';\nimport { GLOBAL } from './global';\n\nconst LocaleMap = {};\n\n/**\n * register a locale\n * @param locale\n * @param localeObj\n */\nexport function registerLocale(locale: string, localeObj: Locale) {\n  LocaleMap[locale] = localeObj;\n}\n\n/**\n * get locale of specific language\n * @param lang\n * @returns\n */\nexport function getLocale(locale: string) {\n  return {\n    get: (key: string | string[], obj?: Record<string, any>) => {\n      return template(\n        get(LocaleMap[locale], key) || get(LocaleMap[GLOBAL.locale], key) || get(LocaleMap['en-US'], key) || key,\n        obj\n      );\n    },\n  };\n}\n"]}