{"version": 3, "file": "global.js", "sourceRoot": "", "sources": ["../../src/core/global.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC;;GAEG;AACH,MAAM,CAAC,IAAM,MAAM,GAAG;IACpB,WAAW;IACX,MAAM,EAAE,OAAO;CAChB,CAAC;AAEF;;;;GAIG;AACH,MAAM,UAAU,SAAS,CAAC,KAA0B;IAClD,IAAI,CAAC,KAAK,EAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAf,CAAe,CAAC,CAAC;AACzC,CAAC", "sourcesContent": ["import { each } from '@antv/util';\n/**\n * @file 全局的一些变量定义：含国际化、主题...\n */\nexport const GLOBAL = {\n  /** 全局语言 */\n  locale: 'en-US',\n};\n\n/**\n * 全局变量设置\n * @param key\n * @param value\n */\nexport function setGlobal(datum: Record<string, any>): void {\n  each(datum, (v, k) => (GLOBAL[k] = v));\n}\n"]}