{"version": 3, "file": "plot.js", "sourceRoot": "", "sources": ["../../src/core/plot.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,qBAAqB,CAAC;AACrC,OAAO,EAAE,KAAK,EAAwB,MAAM,UAAU,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAEnC,OAAO,EAAE,UAAU,EAAE,yBAAyB,EAAE,gBAAgB,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAoBzF,IAAM,qBAAqB,GAAG,wBAAwB,CAAC;AAEvD,mBAAmB;AACnB,MAAM,CAAC,IAAM,sBAAsB,GAAG;IACpC,SAAS;IACT,eAAe;IACf,UAAU;IACV,YAAY;IACZ,iBAAiB;IACjB,qBAAqB;IACrB,aAAa;CACd,CAAC;AAEF;;GAEG;AACH;IAA0D,wBAAE;IAqC1D,cAAY,SAA+B,EAAE,OAAU;QAAvD,YACE,iBAAO,SAQR;QAPC,KAAI,CAAC,SAAS,GAAG,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAEhG,KAAI,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,EAAE,KAAI,CAAC,iBAAiB,EAAE,EAAE,OAAO,CAAC,CAAC;QAEjE,KAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,KAAI,CAAC,UAAU,EAAE,CAAC;;IACpB,CAAC;IA7CD;;;OAGG;IACI,sBAAiB,GAAxB;QACE,OAAO;YACL,QAAQ,EAAE,QAAQ;YAClB,KAAK,EAAE;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE;oBACL,UAAU,EAAE,KAAK;oBACjB,QAAQ,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE;iBACvD;aACF;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE;oBACL,QAAQ,EAAE,IAAI;oBACd,UAAU,EAAE,KAAK;iBAClB;aACF;YACD,SAAS,EAAE,IAAI;SAChB,CAAC;IACJ,CAAC;IAwBD;;OAEG;IACK,uBAAQ,GAAhB;QACQ,IAAA,KAAyC,IAAI,CAAC,OAAO,EAAnD,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,mBAAmB,yBAAiB,CAAC;QAE5D,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,uCACpB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,OAAO,EAAE,KAAK,IACX,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,KACnC,YAAY,EAAE,KAAK,KAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,sBAAsB,CAAC,KAC7C,mBAAmB,qBAAA,IACnB,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACK,2BAAY,GAApB,UAAqB,KAAa,EAAE,MAAc;QAChD,IAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnD,OAAO,EAAE,KAAK,EAAE,KAAK,IAAI,SAAS,CAAC,KAAK,IAAI,GAAG,EAAE,MAAM,EAAE,MAAM,IAAI,SAAS,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;IAC/F,CAAC;IAED;;OAEG;IACK,yBAAU,GAAlB;QAAA,iBAQC;QAPC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,UAAC,CAAQ;gBAC1B,IAAI,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,EAAE;oBACX,KAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;iBACtB;YACH,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IAED;;;OAGG;IACO,gCAAiB,GAA3B;QACE,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClC,CAAC;IAOD;;OAEG;IACI,qBAAM,GAAb;QACE,8BAA8B;QAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,+DAA+D;QAC/D,+BAA+B;QAC/B,iEAAiE;QACjE,2DAA2D;QAC3D,aAAa;QACb,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;YACnB,IAAI,EAAE,EAAE;YACR,OAAO,EAAE,IAAI;SACd,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,cAAc;QACrC,aAAa;QACb,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,KAAK;QACL,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QACpB,KAAK;QACL,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,qBAAM,GAAb,UAAc,OAAmB;QAC/B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAED;;;OAGG;IACO,2BAAY,GAAtB,UAAuB,OAAmB;QACxC,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED;;;;;OAKG;IACI,uBAAQ,GAAf,UAAgB,IAAe,EAAE,SAAyB,EAAE,MAAsB;QAAtB,uBAAA,EAAA,aAAsB;QAChF,IAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvD,IAAI,CAAC,QAAQ,EAAE,UAAC,GAAY;YAC1B,IAAI,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,EAAE;gBAC5B,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aAC5B;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,wBAAS,GAAhB;QACE,IAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvD,IAAM,YAAY,GAAkB,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE,UAAC,OAAgB;YAC9B,IAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;YAC/B,IAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,UAAC,KAAK;gBACjB,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACI,yBAAU,GAAjB,UAAkB,IAAS;QACzB,aAAa;QACb,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QACtB,4BAA4B;QAC5B,+BAA+B;IACjC,CAAC;IAED;;;;OAIG;IACI,yBAAU,GAAjB,UAAkB,KAAa,EAAE,MAAc;QAC7C,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,6BAAc,GAArB,UAAsB,WAAyB,EAAE,IAAW;QAC1D,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;QAChC,IAAM,QAAQ,qBAAO,WAAW,OAAC,CAAC;QAClC,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACpD,IAAM,OAAO,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,UAAC,EAAE,IAAK,OAAA,EAAE,CAAC,KAAK,EAAR,CAAQ,CAAC,CAAC;QAEjE,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gCACd,CAAC;YACR,IAAI,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAE5B,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAApC,CAAoC,CAAC,CAAC;YACrF,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;gBACpB,UAAU,GAAG,UAAU,CAAC,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC7D,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;aAC/B;YACD,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;;QARpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;oBAA9B,CAAC;SAST;QAED,QAAQ,CAAC,OAAO,CAAC,UAAC,UAAU,IAAK,OAAA,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,EAAjC,CAAiC,CAAC,CAAC;QACpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,gCAAiB,GAAxB,UAAyB,WAAwD;QAC/E,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QAC1D,IAAM,OAAO,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,UAAC,EAAE,IAAK,OAAA,EAAE,CAAC,KAAK,EAAR,CAAQ,CAAC,CAAC;QAEjE,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gCACd,CAAC;YACR,IAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAE9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAApC,CAAoC,CAAC,EAAE;gBACrE,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;aACnC;;QALH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;oBAA9B,CAAC;SAMT;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IACD;;OAEG;IACI,sBAAO,GAAd;QACE,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,SAAS;QACT,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACrB,YAAY;QACZ,IAAI,CAAC,GAAG,EAAE,CAAC;QAEX,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACO,0BAAW,GAArB;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAElC,IAAA,KAA6B,IAAI,CAAC,OAAO,EAAvC,OAAO,aAAA,EAAE,aAAa,mBAAiB,CAAC;QAChD,aAAa;QACb,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,mBAAmB;QACnB,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;QAEzC,aAAa;QACb,OAAO,CAAC;YACN,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,4BAAa,GAAvB;QACE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,6BAAc,GAAtB;QAAA,iBAiBC;QAhBC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO;SACR;QAEO,IAAA,KAAmB,IAAI,CAAC,OAAO,QAAjB,EAAd,OAAO,mBAAG,IAAI,KAAA,CAAkB;QACxC,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACjC,YAAY;gBACN,IAAA,KAAoB,gBAAgB,CAAC,KAAI,CAAC,SAAS,CAAC,EAAlD,KAAK,WAAA,EAAE,MAAM,YAAqC,CAAC;gBAE3D,yBAAyB;gBACzB,IAAI,KAAK,KAAK,KAAI,CAAC,KAAK,CAAC,KAAK,IAAI,MAAM,KAAK,KAAI,CAAC,KAAK,CAAC,MAAM,EAAE;oBAC9D,KAAI,CAAC,aAAa,EAAE,CAAC;iBACtB;YACH,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IAED;;OAEG;IACK,+BAAgB,GAAxB;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;IACH,CAAC;IACH,WAAC;AAAD,CAAC,AAzTD,CAA0D,EAAE,GAyT3D", "sourcesContent": ["import EE from '@antv/event-emitter';\nimport { Chart, Element, Event, View } from '@antv/g2';\nimport { each } from '@antv/util';\nimport { bind } from 'size-sensor';\nimport { Annotation, Options, Size, StateCondition, StateName, StateObject } from '../types';\nimport { deepAssign, getAllElementsRecursively, getContainerSize, pick } from '../utils';\nimport { Adaptor } from './adaptor';\n\n/** 单独 pick 出来的用于基类的类型定义 */\nexport type PickOptions = Pick<\n  Options,\n  | 'width'\n  | 'height'\n  | 'padding'\n  | 'appendPadding'\n  | 'renderer'\n  | 'pixelRatio'\n  | 'autoFit'\n  | 'syncViewPadding'\n  | 'supportCSSTransform'\n  | 'limitInPlot'\n  | 'locale'\n  | 'defaultInteractions'\n>;\n\nconst SOURCE_ATTRIBUTE_NAME = 'data-chart-source-type';\n\n/** plot 图表容器的配置 */\nexport const PLOT_CONTAINER_OPTIONS = [\n  'padding',\n  'appendPadding',\n  'renderer',\n  'pixelRatio',\n  'syncViewPadding',\n  'supportCSSTransform',\n  'limitInPlot',\n];\n\n/**\n * 所有 plot 的基类\n */\nexport abstract class Plot<O extends PickOptions> extends EE {\n  /**\n   * 获取默认的 options 配置项\n   * 每个组件都可以复写\n   */\n  static getDefaultOptions(): any {\n    return {\n      renderer: 'canvas',\n      xAxis: {\n        nice: true,\n        label: {\n          autoRotate: false,\n          autoHide: { type: 'equidistance', cfg: { minGap: 6 } },\n        },\n      },\n      yAxis: {\n        nice: true,\n        label: {\n          autoHide: true,\n          autoRotate: false,\n        },\n      },\n      animation: true,\n    };\n  }\n\n  /** plot 类型名称 */\n  public abstract readonly type: string;\n  /** plot 的 schema 配置 */\n  public options: O;\n  /** plot 绘制的 dom */\n  public readonly container: HTMLElement;\n  /** G2 chart 实例 */\n  public chart: Chart;\n  /** resizer unbind  */\n  private unbind: () => void;\n\n  constructor(container: string | HTMLElement, options: O) {\n    super();\n    this.container = typeof container === 'string' ? document.getElementById(container) : container;\n\n    this.options = deepAssign({}, this.getDefaultOptions(), options);\n\n    this.createG2();\n\n    this.bindEvents();\n  }\n\n  /**\n   * 创建 G2 实例\n   */\n  private createG2() {\n    const { width, height, defaultInteractions } = this.options;\n\n    this.chart = new Chart({\n      container: this.container,\n      autoFit: false, // G2Plot 使用 size-sensor 进行 autoFit\n      ...this.getChartSize(width, height),\n      localRefresh: false, // 默认关闭，目前 G 还有一些位置问题，难以排查！\n      ...pick(this.options, PLOT_CONTAINER_OPTIONS),\n      defaultInteractions,\n    });\n\n    // 给容器增加标识，知道图表的来源区别于 G2\n    this.container.setAttribute(SOURCE_ATTRIBUTE_NAME, 'G2Plot');\n  }\n\n  /**\n   * 计算默认的 chart 大小。逻辑简化：如果存在 width 或 height，则直接使用，否则使用容器大小\n   * @param width\n   * @param height\n   */\n  private getChartSize(width: number, height: number): Size {\n    const chartSize = getContainerSize(this.container);\n    return { width: width || chartSize.width || 400, height: height || chartSize.height || 400 };\n  }\n\n  /**\n   * 绑定代理所有 G2 的事件\n   */\n  private bindEvents() {\n    if (this.chart) {\n      this.chart.on('*', (e: Event) => {\n        if (e?.type) {\n          this.emit(e.type, e);\n        }\n      });\n    }\n  }\n\n  /**\n   * 获取默认的 options 配置项\n   * 每个组件都可以复写\n   */\n  protected getDefaultOptions(): any {\n    return Plot.getDefaultOptions();\n  }\n\n  /**\n   * 每个组件有自己的 schema adaptor\n   */\n  protected abstract getSchemaAdaptor(): Adaptor<O>;\n\n  /**\n   * 绘制\n   */\n  public render() {\n    // 暴力处理，先清空再渲染，需要 G2 层自行做好更新渲染\n    this.chart.clear();\n    // 因为子 view 会继承父 view 的 options 配置（包括 legend，所以会导致 legend 重复创建）\n    // 所以这里给 chart 实例的 options 配置清空\n    // 最好的解法是在 G2 view.clear 方法的时候，重置 options 配置。或者提供方法去 resetOptions\n    // #1684 理论上在多 view 图形上，只要存在 custom legend，都存在类似问题（子弹图、双轴图）\n    // @ts-ignore\n    this.chart.options = {\n      data: [],\n      animate: true,\n    };\n    this.chart.views = []; // 删除已有的 views\n    // 执行 adaptor\n    this.execAdaptor();\n    // 渲染\n    this.chart.render();\n    // 绑定\n    this.bindSizeSensor();\n  }\n\n  /**\n   * 更新: 更新配置且重新渲染\n   * @param options\n   */\n  public update(options: Partial<O>) {\n    this.updateOption(options);\n    this.render();\n  }\n\n  /**\n   * 更新配置\n   * @param options\n   */\n  protected updateOption(options: Partial<O>) {\n    this.options = deepAssign({}, this.options, options);\n  }\n\n  /**\n   * 设置状态\n   * @param type 状态类型，支持 'active' | 'inactive' | 'selected' 三种\n   * @param conditions 条件，支持数组\n   * @param status 是否激活，默认 true\n   */\n  public setState(type: StateName, condition: StateCondition, status: boolean = true) {\n    const elements = getAllElementsRecursively(this.chart);\n\n    each(elements, (ele: Element) => {\n      if (condition(ele.getData())) {\n        ele.setState(type, status);\n      }\n    });\n  }\n\n  /**\n   * 获取状态\n   */\n  public getStates(): StateObject[] {\n    const elements = getAllElementsRecursively(this.chart);\n\n    const stateObjects: StateObject[] = [];\n    each(elements, (element: Element) => {\n      const data = element.getData();\n      const states = element.getStates();\n      each(states, (state) => {\n        stateObjects.push({ data, state, geometry: element.geometry, element });\n      });\n    });\n\n    return stateObjects;\n  }\n\n  /**\n   * 更新数据\n   * @override\n   * @param options\n   */\n  public changeData(data: any) {\n    // @ts-ignore\n    this.update({ data });\n    // TODO: 临时方案，最好使用下面的方式去更新数据\n    // this.chart.changeData(data);\n  }\n\n  /**\n   * 修改画布大小\n   * @param width\n   * @param height\n   */\n  public changeSize(width: number, height: number) {\n    this.chart.changeSize(width, height);\n  }\n\n  /**\n   * 增加图表标注。通过 id 标识，如果匹配到，就做更新\n   */\n  public addAnnotations(annotations: Annotation[], view?: View): void {\n    view = view ? view : this.chart;\n    const incoming = [...annotations];\n    const controller = view.getController('annotation');\n    const current = controller.getComponents().map((co) => co.extra);\n\n    controller.clear(true);\n    for (let i = 0; i < current.length; i++) {\n      let annotation = current[i];\n\n      const findIndex = incoming.findIndex((item) => item.id && item.id === annotation.id);\n      if (findIndex !== -1) {\n        annotation = deepAssign({}, annotation, incoming[findIndex]);\n        incoming.splice(findIndex, 1);\n      }\n      controller.annotation(annotation);\n    }\n\n    incoming.forEach((annotation) => controller.annotation(annotation));\n    view.render(true);\n  }\n\n  /**\n   * 删除图表标注。通过 id 标识，如果匹配到，就做删除\n   */\n  public removeAnnotations(annotations: Array<{ id: string } & Partial<Annotation>>): void {\n    const controller = this.chart.getController('annotation');\n    const current = controller.getComponents().map((co) => co.extra);\n\n    controller.clear(true);\n    for (let i = 0; i < current.length; i++) {\n      const annotation = current[i];\n\n      if (!annotations.find((item) => item.id && item.id === annotation.id)) {\n        controller.annotation(annotation);\n      }\n    }\n\n    this.chart.render(true);\n  }\n  /**\n   * 销毁\n   */\n  public destroy() {\n    // 取消 size-sensor 的绑定\n    this.unbindSizeSensor();\n    // G2 的销毁\n    this.chart.destroy();\n    // 清空已经绑定的事件\n    this.off();\n\n    this.container.removeAttribute(SOURCE_ATTRIBUTE_NAME);\n  }\n\n  /**\n   * 执行 adaptor 操作\n   */\n  protected execAdaptor() {\n    const adaptor = this.getSchemaAdaptor();\n\n    const { padding, appendPadding } = this.options;\n    // 更新 padding\n    this.chart.padding = padding;\n    // 更新 appendPadding\n    this.chart.appendPadding = appendPadding;\n\n    // 转化成 G2 API\n    adaptor({\n      chart: this.chart,\n      options: this.options,\n    });\n  }\n\n  /**\n   * 当图表容器大小变化的时候，执行的函数\n   */\n  protected triggerResize() {\n    this.chart.forceFit();\n  }\n\n  /**\n   * 绑定 dom 容器大小变化的事件\n   */\n  private bindSizeSensor() {\n    if (this.unbind) {\n      return;\n    }\n\n    const { autoFit = true } = this.options;\n    if (autoFit) {\n      this.unbind = bind(this.container, () => {\n        // 获取最新的宽高信息\n        const { width, height } = getContainerSize(this.container);\n\n        // 主要是防止绑定的时候触发 resize 回调\n        if (width !== this.chart.width || height !== this.chart.height) {\n          this.triggerResize();\n        }\n      });\n    }\n  }\n\n  /**\n   * 取消绑定\n   */\n  private unbindSizeSensor() {\n    if (this.unbind) {\n      this.unbind();\n      this.unbind = undefined;\n    }\n  }\n}\n"]}