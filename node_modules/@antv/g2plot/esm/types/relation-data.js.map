{"version": 3, "file": "relation-data.js", "sourceRoot": "", "sources": ["../../src/types/relation-data.ts"], "names": [], "mappings": "", "sourcesContent": ["/** 节点 */\nexport type Node = {\n  readonly id: number;\n  readonly name: string;\n};\n\n/** 边 */\nexport type Link = {\n  readonly source: number;\n  readonly target: number;\n  readonly value: number;\n};\n\n/** 带有节点与边的数据类型，目前用于桑基图、和弦图 */\nexport type NodeLinkData = {\n  readonly nodes: Node[];\n  readonly links: Link[];\n};\n"]}