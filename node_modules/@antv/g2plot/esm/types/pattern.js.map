{"version": 3, "file": "pattern.js", "sourceRoot": "", "sources": ["../../src/types/pattern.ts"], "names": [], "mappings": "", "sourcesContent": ["export type PatternCfg = {\n  /**\n   * @title 背景色\n   * @description pattern background color. Default: inherit (默认: 继承图形元素颜色)\n   * @default \"inherit\"\n   */\n  backgroundColor?: string;\n  /**\n   * @title 贴图图案填充色\n   */\n  fill?: string;\n  /**\n   * @title 填充透明度\n   */\n  fillOpacity?: number;\n  /**\n   * @title 描边色\n   * @description 贴图图案描边色\n   */\n  stroke?: string;\n  /**\n   * @title 描边透明度\n   */\n  strokeOpacity?: number;\n  /**\n   * @title 描边粗细\n   */\n  lineWidth?: number;\n  /**\n   * @title 透明度\n   * @description 整个pattern 透明度\n   */\n  opacity?: number;\n  /**\n   * @title 旋转角度\n   * @description 整个pattern 的旋转角度\n   */\n  rotation?: number;\n};\n\n/**\n * @title dot pattern\n */\nexport type DotPatternCfg = PatternCfg & {\n  /**\n   * @title 点的大小\n   * @default 4\n   */\n  size?: number;\n  /**\n   * @title 点间距\n   * @default 4\n   */\n  padding?: number;\n  /**\n   * @title 是否交错\n   * @default true\n   */\n  isStagger?: boolean;\n};\n\n/**\n * @title line pattern\n */\nexport type LinePatternCfg = PatternCfg & {\n  /**\n   * @title 线之间的距离\n   */\n  spacing?: number;\n};\n\n/**\n * @title square pattern\n */\nexport type SquarePatternCfg = PatternCfg & {\n  /**\n   * @title 矩形的大小\n   */\n  size?: number;\n  /**\n   * @title 矩形之间的间隔\n   */\n  padding?: number;\n  /**\n   * @title 是否交错\n   * @description 即 staggered squares.\n   * @default true\n   */\n  isStagger?: boolean;\n};\n"]}