{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/types/index.ts"], "names": [], "mappings": "AAAA,cAAc,cAAc,CAAC;AAC7B,cAAc,QAAQ,CAAC;AACvB,cAAc,QAAQ,CAAC;AACvB,cAAc,UAAU,CAAC;AACzB,cAAc,UAAU,CAAC;AACzB,cAAc,eAAe,CAAC;AAC9B,cAAc,UAAU,CAAC;AACzB,cAAc,QAAQ,CAAC;AACvB,cAAc,SAAS,CAAC;AACxB,cAAc,aAAa,CAAC;AAC5B,cAAc,WAAW,CAAC", "sourcesContent": ["export * from './annotation';\nexport * from './attr';\nexport * from './axis';\nexport * from './button';\nexport * from './common';\nexport * from './interaction';\nexport * from './locale';\nexport * from './meta';\nexport * from './state';\nexport * from './statistic';\nexport * from './tooltip';\n\n/** 去除 readonly 修饰 */\nexport type Writable<T> = { -readonly [P in keyof T]: T[P] };\n"]}