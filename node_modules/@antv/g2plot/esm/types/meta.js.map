{"version": 3, "file": "meta.js", "sourceRoot": "", "sources": ["../../src/types/meta.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ScaleConfig } from '@antv/g2';\n\n/** scale 元信息，取名为 meta */\nexport type Meta = ScaleConfig & {\n  /**\n   * @title mescale 的 type 类型ta\n   * @description 对于连续的，一般是 linear，对于分类一般为 cat。当然也有 log, pow, time 等类型，或者通过 tickMethod 自定义自己的 scale\n   */\n  readonly type?: string;\n  /**\n   * @title 是否进行 scale 的同步\n   * @description 设置为 false 则不同步； 设置为 true 则以 field 为 key 进行同步；设置为 string，则以这个 string 为 key 进行同步\n   */\n  readonly sync?: boolean | string;\n};\n"]}