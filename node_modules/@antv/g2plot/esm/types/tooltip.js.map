{"version": 3, "file": "tooltip.js", "sourceRoot": "", "sources": ["../../src/types/tooltip.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Types } from '@antv/g2';\nimport { TooltipAttr } from '../types/attr';\n\nexport type TooltipMapping = {\n  /**\n   * @title 映射字段\n   * @description 指定需要显示 tooltip 中的字段，默认是包含 x seriesFields y\n   */\n  readonly fields?: string[] | false;\n  /**\n   * @title value 格式化\n   */\n  readonly formatter?: TooltipAttr;\n};\n\nexport type TooltipOptions = Types.TooltipCfg & TooltipMapping;\n\nexport type Tooltip = false | TooltipOptions;\n"]}