{"version": 3, "file": "label.js", "sourceRoot": "", "sources": ["../../src/types/label.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Types } from '@antv/g2';\n\nexport type Label =\n  | false\n  | ({\n      /**\n       * @title 映射的字段\n       */\n      readonly fields?: string[];\n      /**\n       * @title 回调函数\n       * @description 回调函数，返回相关点 value\n       */\n      readonly callback?: Types.LabelCallback;\n      /**\n       * @title 格式化\n       * @description 功能同 content ，兼容 v1，一般用于自定义。\n       */\n      readonly formatter?: Types.GeometryLabelCfg['content'];\n    } & Types.GeometryLabelCfg);\n"]}