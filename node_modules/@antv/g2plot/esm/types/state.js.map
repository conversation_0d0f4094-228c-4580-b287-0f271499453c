{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../src/types/state.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Element, Geometry, Types } from '@antv/g2';\nimport { Data, Datum } from './common';\n\nexport type State = Types.StateOption;\n\n/**\n * @title 状态名称\n * @description G2 Element 开放 'active' | 'inactive' | 'selected' | 'default' 四种状态\n */\nexport type StateName = 'active' | 'inactive' | 'selected' | 'default';\n\n/**\n * @title 状态条件\n */\nexport type StateCondition = (data: Datum | Data) => boolean;\n\n/**\n * @title 状态对象\n * @description 可通过 `plot.getStates()` 获取\n */\nexport type StateObject = { data: Datum | Data; state: string; geometry: Geometry; element: Element };\n"]}