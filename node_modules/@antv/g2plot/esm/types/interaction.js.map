{"version": 3, "file": "interaction.js", "sourceRoot": "", "sources": ["../../src/types/interaction.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ShapeAttrs } from '@antv/g2';\nimport { ButtonCfg } from './button';\n\nexport type Interaction = {\n  readonly type: string;\n  readonly cfg?: Record<string, any>;\n  /** 是否开启交互, 默认开启 */\n  readonly enable?: boolean;\n};\n\n/** brush 交互 */\nexport type BrushCfg = {\n  /**\n   * @title 是否启用\n   * @description 是否启用 brush 交互\n   */\n  readonly enabled?: boolean;\n  /**\n   * @title brush 类型\n   * @description '矩形', 'x 方向' 和 'y 方向', 'circle', 'path'(不规则矩形). 默认: 'rect'.\n   * @default \"rect\"\n   */\n  readonly type?: 'rect' | 'x-rect' | 'y-rect' | 'circle' | 'path';\n  /**\n   * @title brush 操作\n   * @description '筛选过滤' 和 '高亮强调'. 默认: 'filter'. 目前只在 type 为 'rect' 的情况下生效\n   * @default \"filter\"\n   */\n  readonly action?: 'filter' | 'highlight';\n  /**\n   * @title mask\n   * @description brush mask 的配置\n   */\n  readonly mask?: {\n    /**\n     * @title mask 样式\n     * @description mask 蒙层样式\n     */\n    style?: ShapeAttrs;\n  };\n  /**\n   * @title button 配置\n   * @description brush button 的配置, 只在 action: 'filter' 的情况下适用\n   */\n  readonly button?: ButtonCfg;\n\n  /**\n   * @title 是否允许 brush 交互开始的回调\n   * @description 是否允许 brush 交互开始的回调\n   */\n  readonly isStartEnable?: (context: any) => boolean;\n};\n"]}