{"version": 3, "file": "locale.js", "sourceRoot": "", "sources": ["../../src/types/locale.ts"], "names": [], "mappings": "", "sourcesContent": ["export type Locale = {\n  locale: string;\n\n  // 1. 通用\n  general: {\n    increase: string;\n    decrease: string;\n    root: string;\n  };\n\n  // 2. 按照图表组件\n  /** 中心文本 */\n  statistic: {\n    total: string;\n  };\n  /** 转化率组件 */\n  conversionTag: {\n    label: string;\n  };\n  legend?: Record<string, string>;\n  tooltip?: Record<string, string>;\n  slider?: Record<string, string>;\n  scrollbar?: Record<string, string>;\n\n  // 3. 按照图表类型\n  waterfall: {\n    /** 总计或累计值 */\n    total: string;\n  };\n};\n"]}