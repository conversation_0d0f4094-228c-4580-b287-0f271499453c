{"version": 3, "file": "statistic.js", "sourceRoot": "", "sources": ["../../src/types/statistic.ts"], "names": [], "mappings": "", "sourcesContent": ["import { View } from '@antv/g2';\nimport { Data, Datum } from './common';\n\ntype CSSStyle = Omit<Partial<CSSStyleDeclaration>, 'opacity' | 'fontWeight' | 'lineHeight'> & {\n  /**\n   * @title 透明度\n   */\n  opacity?: number;\n  /**\n   * @title 字体粗细程度\n   */\n  fontWeight?: string | number;\n  /**\n   * @title 行高\n   */\n  lineHeight?: string | number;\n};\n\n/**\n * @title 统计文本\n * @description 支持三种设置模式(优先级)：customHtml > formatter > content\n */\nexport type StatisticText = {\n  /**\n   * @title 统计文本的样式\n   */\n  readonly style?: CSSStyle | ((datum: Datum) => CSSStyle);\n  /**\n   * @title 文本内容\n   */\n  readonly content?: string;\n  /**\n   * @title 文本的格式化\n   */\n  readonly formatter?: (datum?: Datum, data?: Data /** filterData */) => string;\n  /**\n   * @title 自定义中心文本的 html\n   */\n  readonly customHtml?: (container: HTMLElement, view: View, datum?: Datum, data?: Data /** filterData */) => string;\n  /**\n   * @title 旋转弧度\n   */\n  readonly rotate?: number;\n  /**\n   * @title 横轴偏移值\n   */\n  readonly offsetX?: number;\n  /**\n   * @title 纵轴偏移值\n   */\n  readonly offsetY?: number;\n};\n\n/**\n * 中心文本的统计信息，统一一个数据结构\n */\nexport type Statistic = {\n  /**\n   * @title 标题\n   */\n  readonly title?: false | StatisticText;\n  /**\n   * @title 内容\n   */\n  readonly content?: false | StatisticText;\n};\n"]}