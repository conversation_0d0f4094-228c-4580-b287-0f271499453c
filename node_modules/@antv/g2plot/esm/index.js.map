{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,IAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC,aAAa;AACb,OAAO,KAAK,EAAE,MAAM,UAAU,CAAC;AAC/B,uCAAuC;AACvC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AACrG,QAAQ;AACR,OAAO,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,0CAA0C;AAC1C,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAWpF,WAAW;AACX,OAAO,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC1C,uBAAuB;AACvB,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AACnC,oDAAoD;AACpD,OAAO,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAC5B,2DAA2D;AAC3D,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAEpC,yDAAyD;AACzD,OAAO,EAAE,GAAG,EAAE,MAAM,aAAa,CAAC;AAElC,+DAA+D;AAC/D,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAE7D,8FAA8F;AAC9F,OAAO,EAAE,GAAG,EAAE,MAAM,aAAa,CAAC;AAElC,6DAA6D;AAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,uGAAuG;AACvG,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,6GAA6G;AAC7G,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAEvD,uDAAuD;AACvD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,iEAAiE;AACjE,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,2DAA2D;AAC3D,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,WAAW;AACX,OAAO,EAAE,MAAM,EAAE,yBAAyB,EAAE,MAAM,gBAAgB,CAAC;AAEnE,2DAA2D;AAC3D,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,mEAAmE;AACnE,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAE1C,kGAAkG;AAClG,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,2DAA2D;AAC3D,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAEpC,yIAAyI;AACzI,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAEtD,eAAe;AACf,OAAO,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,SAAS,EAAE,MAAM,aAAa,CAAC;AAE3D,0DAA0D;AAC1D,OAAO,EAAE,GAAG,EAAE,MAAM,aAAa,CAAC;AAElC,6DAA6D;AAC7D,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAE5C,2DAA2D;AAC3D,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,mGAAmG;AACnG,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAE/C,+DAA+D;AAC/D,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AAErD,uEAAuE;AACvE,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAEpC,2DAA2D;AAC3D,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,yDAAyD;AACzD,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAE1C,oGAAoG;AACpG,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,8FAA8F;AAC9F,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAE5C,+DAA+D;AAC/D,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,+DAA+D;AAC/D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAEjD,+DAA+D;AAC/D,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,OAAO;AACP,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAE1C,2DAA2D;AAC3D,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAEpC,qGAAqG;AACrG,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,sDAAsD;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,uEAAuE;AACvE,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAE/C,2BAA2B;AAC3B,mEAAmE;AACnE,OAAO,EAAE,CAAC,EAAE,MAAM,UAAU,CAAC;AAC7B,SAAS;AACT,cAAc,SAAS,CAAC;AACxB,uCAAuC;AACvC,OAAO,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,SAAS,CAAC;AACjD,8BAA8B;AAC9B,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,EAAE,EAAE,CAAC;AACd,uBAAuB;AACvB,OAAO,EAAE,cAAc,EAAE,CAAC;AAE1B,8BAA8B;AAC9B,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AACtC,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AAEtC,MAAM,CAAC,IAAM,QAAQ,GAAG,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,OAAO,SAAA,EAAE,UAAU,YAAA,EAAE,WAAW,aAAA,EAAE,KAAK,OAAA,EAAE,SAAS,WAAA,EAAE,CAAC", "sourcesContent": ["export const version = '2.4.33';\n\n// G2 自定义能力透出\nimport * as G2 from '@antv/g2';\n/** 开放一些通用的 adaptor 通道方法，实验阶段：不保证稳定性 */\nimport { animation, annotation, interaction, legend, scale, theme, tooltip } from './adaptor/common';\n// 国际化处理\nimport { registerLocale } from './core/locale';\nimport { EN_US_LOCALE } from './locales/en_US';\nimport { ZH_CN_LOCALE } from './locales/zh_CN';\n/** 各个 geometry 的 adaptor，可以让开发者更快的构造图形 */\nexport { area, interval, line, point, polygon, schema } from './adaptor/geometries';\nexport type {\n  AreaGeometryOptions,\n  IntervalGeometryOptions,\n  LineGeometryOptions,\n  PointGeometryOptions,\n  PolygonGeometryOptions,\n  SchemaGeometryOptions,\n} from './adaptor/geometries';\n/** Adaptor 及其参数的类型定义 */\nexport type { Adaptor, Params } from './core/adaptor';\n/** 全局变量 */\nexport { setGlobal } from './core/global';\n/** G2Plot 的 Plot 基类 */\nexport { Plot } from './core/plot';\n/** 对于没有开发完成的图表，可以暂时先放到 Lab 下面，先做体验，稳定后放到根 export */\nexport { Lab } from './lab';\n// 面积图及类型定义 | author by [hustcc](https://github.com/hustcc)\nexport { Area } from './plots/area';\nexport type { AreaOptions } from './plots/area';\n// 条形图及类型定义 | author by [BBSQQ](https://github.com/BBSQQ)\nexport { Bar } from './plots/bar';\nexport type { BarOptions } from './plots/bar';\n// 对称条形图及类型定义 | author by [arcsin1](https://github.com/arcsin1)\nexport { BidirectionalBar } from './plots/bidirectional-bar';\nexport type { BidirectionalBarOptions } from './plots/bidirectional-bar';\n// 箱线图及类型定义 | author by [BBSQQ](https://github.com/BBSQQ), [visiky](https://github.com/visiky)\nexport { Box } from './plots/box';\nexport type { BoxOptions } from './plots/box';\n// 子弹图及类型定义 | author by [arcsin1](https://github.com/arcsin1)\nexport { Bullet } from './plots/bullet';\nexport type { BulletOptions } from './plots/bullet';\n// 弦图及类型定义 | author by [MrSmallLiu](https://github.com/MrSmallLiu), [visiky](https://github.com/visiky)\nexport { Chord } from './plots/chord';\nexport type { ChordOptions } from './plots/chord';\n// circle-packing 及类型定义 | author by [visiky](https://github.com/visiky), [Angeli](https://github.com/Angelii)\nexport { CirclePacking } from './plots/circle-packing';\nexport type { CirclePackingOptions } from './plots/circle-packing';\n// 柱形图及类型定义 | author by [zqlu](https://github.com/zqlu)\nexport { Column } from './plots/column';\nexport type { ColumnOptions } from './plots/column';\n// 混合图形 | author by [liuzhenying](https://github.com/liuzhenying)\nexport { DualAxes } from './plots/dual-axes';\nexport type { DualAxesOptions } from './plots/dual-axes';\n// 分面图及类型定义 | author by [visiky](https://github.com/visiky)\nexport { Facet } from './plots/facet';\nexport type { FacetOptions } from './plots/facet';\n// 漏斗图及类型定义\nexport { Funnel, FUNNEL_CONVERSATION_FIELD } from './plots/funnel';\nexport type { FunnelOptions } from './plots/funnel';\n// 仪表盘及类型定义 | author by [hustcc](https://github.com/hustcc)\nexport { Gauge } from './plots/gauge';\nexport type { GaugeOptions } from './plots/gauge';\n// 热力图及类型定义 | author by [jiazhewang](https://github.com/jiazhewang)\nexport { Heatmap } from './plots/heatmap';\nexport type { HeatmapOptions } from './plots/heatmap';\n// 直方图及类型定义 | author by [arcsin1](https://github.com/arcsin1), [visiky](https://github.com/visiky)\nexport { Histogram } from './plots/histogram';\nexport type { HistogramOptions } from './plots/histogram';\n// 折线图及类型定义 | author by [hustcc](https://github.com/hustcc)\nexport { Line } from './plots/line';\nexport type { LineOptions } from './plots/line';\n// 水波图及类型定义 | author by [CarisL](https://github.com/CarisL), [hustcc](https://github.com/hustcc), [pearmini](https://github.com/pearmini)\nexport { addWaterWave, Liquid } from './plots/liquid';\nexport type { LiquidOptions } from './plots/liquid';\n// 已经废弃，更名为 Mix\nexport { Mix as Mix, Mix as MultiView } from './plots/mix';\nexport type { MixOptions, MixOptions as MultiViewOptions } from './plots/mix';\n// 饼图及类型定义 | author by [visiky](https://github.com/visiky)\nexport { Pie } from './plots/pie';\nexport type { PieOptions } from './plots/pie';\n// 进度图及类型定义 | author by [connono](https://github.com/connono)\nexport { Progress } from './plots/progress';\nexport type { ProgressOptions } from './plots/progress';\n// 雷达图及类型定义 | author by [visiky](https://github.com/visiky)\nexport { Radar } from './plots/radar';\nexport type { RadarOptions } from './plots/radar';\n// 玉珏图 | author by [yujs](https://github.com/yujs) | updated by [visiky](https://github.com/visiky)\nexport { RadialBar } from './plots/radial-bar';\nexport type { RadialBarOptions } from './plots/radial-bar';\n// 环形进度图及类型定义 | author by [connono](https://github.com/connono)\nexport { RingProgress } from './plots/ring-progress';\nexport type { RingProgressOptions } from './plots/ring-progress';\n// 玫瑰图及类型定义 | author by [zhangzhonghe](https://github.com/zhangzhonghe)\nexport { Rose } from './plots/rose';\nexport type { RoseOptions } from './plots/rose';\n// 桑基图及类型定义 | author by [hustcc](https://github.com/hustcc)\nexport { Sankey } from './plots/sankey';\nexport type { SankeyOptions } from './plots/sankey';\n// 散点图及类型定义 | author by [lxfu1](https://github.com/lxfu1)\nexport { Scatter } from './plots/scatter';\nexport type { ScatterOptions } from './plots/scatter';\n// K线图及类型定义 | author by [jhwong](https://github.com/jinhuiWong), [visiky](https://github.com/visiky)\nexport { Stock } from './plots/stock';\nexport type { StockOptions } from './plots/stock';\n// 旭日图及类型定义 | author by [lxfu1](https://github.com/lxfu1), [visiky](https://github.com/visiky)\nexport { Sunburst } from './plots/sunburst';\nexport type { SunburstOptions } from './plots/sunburst';\n// 迷你面积图及类型定义 | author by [connono](https://github.com/connono)\nexport { TinyArea } from './plots/tiny-area';\nexport type { TinyAreaOptions } from './plots/tiny-area';\n// 迷你柱形图及类型定义 | author by [connono](https://github.com/connono)\nexport { TinyColumn } from './plots/tiny-column';\nexport type { TinyColumnOptions } from './plots/tiny-column';\n// 迷你折线图及类型定义 | author by [connono](https://github.com/connono)\nexport { TinyLine } from './plots/tiny-line';\nexport type { TinyLineOptions } from './plots/tiny-line';\n// 矩形树图\nexport { Treemap } from './plots/treemap';\nexport type { TreemapOptions } from './plots/treemap';\n// 韦恩图及类型定义 | author by [visiky](https://github.com/visiky)\nexport { Venn } from './plots/venn';\nexport type { VennOptions } from './plots/venn';\n// 小提琴图及类型定义 | author by [YiSiWang](https://github.com/YiSiWang), [visiky](https://github.com/visiky)\nexport { Violin } from './plots/violin';\nexport type { ViolinOptions } from './plots/violin';\n// 瀑布图 | author by [visiky](https://github.com/visiky)\nexport { Waterfall } from './plots/waterfall';\nexport type { WaterfallOptions } from './plots/waterfall';\n// 词云图及类型定义 | author by [zhangzhonghe](https://github.com/zhangzhonghe)\nexport { WordCloud } from './plots/word-cloud';\nexport type { WordCloudOptions } from './plots/word-cloud';\n// 以下开放自定义图表开发的能力（目前仅仅是孵化中）\n/** 所有开放图表都使用 G2Plot.P 作为入口开发，理论上官方的所有图表都可以走 G2Plot.P 的入口（暂时不处理） */\nexport { P } from './plugin';\n// 类型定义导出\nexport * from './types';\n/** 开发 adaptor 可能会用到的方法或一些工具方法，不强制使用 */\nexport { flow, measureTextWidth } from './utils';\n/** 开放 getCanvasPatterng 方法 */\nexport { getCanvasPattern } from './utils/pattern';\nexport { G2 };\n/** 透出 国际化 工具函数，便于使用 */\nexport { registerLocale };\n\n/** default locale register */\nregisterLocale('en-US', EN_US_LOCALE);\nregisterLocale('zh-CN', ZH_CN_LOCALE);\n\nexport const adaptors = { scale, legend, tooltip, annotation, interaction, theme, animation };\n"]}