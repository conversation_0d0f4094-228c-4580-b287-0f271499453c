{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/gauge/utils.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAEpD,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAG/D;;;;;GAKG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAe,EAAE,OAAgC;IAChF,OAAO,CACL,KAAK;QACH,gBAAgB;SACf,GAAG,CAAC,UAAC,CAAS,EAAE,GAAW;;QAC1B,gBAAS,GAAC,WAAW,IAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,GAAC,UAAU,IAAG,UAAG,GAAG,CAAE,EAAE,GAAC,OAAO,IAAG,OAAO,KAAG;IAClG,CAAC,CAAC,CACL,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,OAAgC;;IAC/D,OAAO,WAAG,GAAC,OAAO,IAAG,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,MAAG,CAAC;AAC/C,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,OAAgC,EAAE,KAA6B;IAC1F,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;IAExC,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5E,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;QAClB,UAAU,CAAC,KAAK,EAAE,CAAC;KACpB;IACD,OAAO,gBAAgB,CAAC,UAAsB,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC", "sourcesContent": ["import { clamp, get, size, uniq } from '@antv/util';\nimport { Data } from '../../types';\nimport { PERCENT, RANGE_TYPE, RANGE_VALUE } from './constants';\nimport { GaugeOptions, GaugeRangeData } from './types';\n\n/**\n * 将 range 生成为 data 数据\n * @param range\n * @param key\n * @returns {GaugeRangeData}\n */\nexport function processRangeData(range: number[], percent: GaugeOptions['percent']): GaugeRangeData {\n  return (\n    range\n      // 映射为 stack 的数据\n      .map((r: number, idx: number) => {\n        return { [RANGE_VALUE]: r - (range[idx - 1] || 0), [RANGE_TYPE]: `${idx}`, [PERCENT]: percent };\n      })\n  );\n}\n\n/**\n * 获取 仪表盘 指针数据\n * @param percent\n */\nexport function getIndicatorData(percent: GaugeOptions['percent']): Data {\n  return [{ [PERCENT]: clamp(percent, 0, 1) }];\n}\n\n/**\n * 获取仪表盘 表盘弧形数据\n * @param percent\n * @param range\n */\nexport function getRangeData(percent: GaugeOptions['percent'], range?: GaugeOptions['range']): GaugeRangeData {\n  const ticks = get(range, ['ticks'], []);\n\n  const clampTicks = size(ticks) ? uniq(ticks) : [0, clamp(percent, 0, 1), 1];\n  if (!clampTicks[0]) {\n    clampTicks.shift();\n  }\n  return processRangeData(clampTicks as number[], percent);\n}\n"]}