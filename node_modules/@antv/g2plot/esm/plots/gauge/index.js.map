{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/gauge/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,UAAU,CAAC;AAEnD,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,kBAAkB,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AACjF,WAAW;AACX,OAAO,oBAAoB,CAAC;AAC5B,OAAO,sBAAsB,CAAC;AAE9B,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAIzD;;GAEG;AACH;IAA2B,yBAAkB;IAA7C;QAAA,qEAuDC;QA9CC,WAAW;QACJ,UAAI,GAAW,OAAO,CAAC;;IA6ChC,CAAC;IAtDC;;;OAGG;IACI,uBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAKD;;;OAGG;IACI,0BAAU,GAAjB,UAAkB,OAAe;QAC/B,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,gBAAgB,CAAC,kBAAkB,EACnC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,CACtE,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;QAC/B,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,KAAK,kBAAkB,EAA3B,CAA2B,CAAC,CAAC;QAChF,IAAI,aAAa,EAAE;YACjB,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;SAC/C;QAED,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,KAAK,aAAa,EAAtB,CAAsB,CAAC,CAAC;QACvE,IAAI,SAAS,EAAE;YACb,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;SAC3D;QACD,gDAAgD;QAChD,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,gBAAgB,CAAC,iBAAiB,EAClC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CACrE,CAAC;IACJ,CAAC;IAED;;;OAGG;IACO,iCAAiB,GAA3B;QACE,OAAO,KAAK,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACO,gCAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,YAAC;AAAD,CAAC,AAvDD,CAA2B,IAAI,GAuD9B", "sourcesContent": ["import { Event, VIEW_LIFE_CIRCLE } from '@antv/g2';\nimport { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor, statistic } from './adaptor';\nimport { DEFAULT_OPTIONS, INDICATEOR_VIEW_ID, RANGE_VIEW_ID } from './constants';\n// 注册 shape\nimport './shapes/indicator';\nimport './shapes/meter-gauge';\nimport { GaugeOptions } from './types';\nimport { getIndicatorData, getRangeData } from './utils';\n\nexport type { GaugeOptions };\n\n/**\n * 仪表盘\n */\nexport class Gauge extends Plot<GaugeOptions> {\n  /**\n   * 获取 仪表盘 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<GaugeOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'gauge';\n\n  /**\n   * 更新数据\n   * @param percent\n   */\n  public changeData(percent: number) {\n    this.chart.emit(\n      VIEW_LIFE_CIRCLE.BEFORE_CHANGE_DATA,\n      Event.fromData(this.chart, VIEW_LIFE_CIRCLE.BEFORE_CHANGE_DATA, null)\n    );\n\n    this.updateOption({ percent });\n    const indicatorView = this.chart.views.find((v) => v.id === INDICATEOR_VIEW_ID);\n    if (indicatorView) {\n      indicatorView.data(getIndicatorData(percent));\n    }\n\n    const rangeView = this.chart.views.find((v) => v.id === RANGE_VIEW_ID);\n    if (rangeView) {\n      rangeView.data(getRangeData(percent, this.options.range));\n    }\n    // todo 后续让 G2 层在 afterrender 之后，来重绘 annotations\n    statistic({ chart: this.chart, options: this.options }, true);\n\n    this.chart.emit(\n      VIEW_LIFE_CIRCLE.AFTER_CHANGE_DATA,\n      Event.fromData(this.chart, VIEW_LIFE_CIRCLE.AFTER_CHANGE_DATA, null)\n    );\n  }\n\n  /**\n   * 获取默认配置\n   * 供 base 使用\n   */\n  protected getDefaultOptions() {\n    return Gauge.getDefaultOptions();\n  }\n\n  /**\n   * 获取适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<GaugeOptions> {\n    return adaptor;\n  }\n}\n"]}