import { Data } from '../../types';
import { GaugeOptions, GaugeRangeData } from './types';
/**
 * 将 range 生成为 data 数据
 * @param range
 * @param key
 * @returns {GaugeRangeData}
 */
export declare function processRangeData(range: number[], percent: GaugeOptions['percent']): GaugeRangeData;
/**
 * 获取 仪表盘 指针数据
 * @param percent
 */
export declare function getIndicatorData(percent: GaugeOptions['percent']): Data;
/**
 * 获取仪表盘 表盘弧形数据
 * @param percent
 * @param range
 */
export declare function getRangeData(percent: GaugeOptions['percent'], range?: GaugeOptions['range']): GaugeRangeData;
