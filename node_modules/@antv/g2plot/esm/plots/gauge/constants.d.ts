export declare const RANGE_VALUE = "range";
export declare const RANGE_TYPE = "type";
export declare const PERCENT = "percent";
export declare const DEFAULT_COLOR = "#f0f0f0";
/** 仪表盘由 指针和表盘 组成 */
export declare const INDICATEOR_VIEW_ID = "indicator-view";
export declare const RANGE_VIEW_ID = "range-view";
/**
 * 仪表盘默认配置项
 */
export declare const DEFAULT_OPTIONS: {
    percent: number;
    range: {
        ticks: any[];
    };
    innerRadius: number;
    radius: number;
    startAngle: number;
    endAngle: number;
    syncViewPadding: boolean;
    axis: {
        line: any;
        label: {
            offset: number;
            style: {
                textAlign: "center";
                textBaseline: "middle";
            };
        };
        subTickLine: {
            length: number;
        };
        tickLine: {
            length: number;
        };
        grid: any;
    };
    indicator: {
        pointer: {
            style: {
                lineWidth: number;
                lineCap: "round";
            };
        };
        pin: {
            style: {
                r: number;
                lineWidth: number;
                fill: string;
            };
        };
    };
    statistic: {
        title: false;
    };
    meta: {
        range: {
            sync: string;
        };
        percent: {
            sync: string;
            tickCount: number;
            tickInterval: number;
        };
    };
    animation: false;
};
