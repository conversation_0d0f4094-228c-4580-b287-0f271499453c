{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/gauge/types.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC", "sourcesContent": ["import { Options, ShapeStyle, Statistic, StyleAttr } from '../../types';\nimport { Axis } from '../../types/axis';\nimport { PERCENT, RANGE_TYPE, RANGE_VALUE } from './constants';\n\n/** 指标指标的配置 */\nexport type Indicator = {\n  /**\n   * @title 指针\n   * @description 只允许静态的 object\n   */\n  readonly pointer?: {\n    readonly style?: ShapeStyle;\n  };\n  /**\n   * @title 圆环\n   * @description 只允许静态的 object\n   */\n  readonly pin?: {\n    readonly style?: ShapeStyle;\n  };\n  /**\n   * @title 自定义指针 shape\n   * @default 'gauge-indicator'\n   */\n  readonly shape?: string;\n};\n\nexport type Range = {\n  /**\n   * @title 辅助的刻度值\n   * @description 0 ~ 1 的数字\n   */\n  readonly ticks?: number[];\n  /**\n   * @title 辅助刻度的颜色配置\n   */\n  readonly color?: string | string[];\n  /**\n   * @title 仪表盘辅助背景的宽度\n   */\n  readonly width?: number;\n};\n\n/**\n * @title 仪表盘辅助生成的 rangeData\n */\nexport type GaugeRangeData = {\n  readonly [RANGE_VALUE]?: number;\n  readonly [RANGE_TYPE]: string;\n  readonly [PERCENT]: number;\n}[];\n\n/**\n * @title 仪表盘配置类型定义\n */\nexport interface GaugeOptions\n  extends Omit<Options, 'data' | 'legend' | 'xAxis' | 'yAxis' | 'xField' | 'yField' | 'color'> {\n  /**\n   * @title 指标的比例\n   * @description 范围0 ~ 1\n   */\n  readonly percent: number;\n  /**\n   * @title 外弧度\n   * @description 范围0 ~ 1\n   */\n  readonly radius?: number;\n  /**\n   * @title 内弧度\n   * @description 范围0 ~ 1\n   */\n  readonly innerRadius?: number;\n  /**\n   * @title 弧度起始\n   */\n  readonly startAngle?: number;\n  /**\n   * @title 弧度结束\n   */\n  readonly endAngle?: number;\n  /**\n   * @title 辅助的 range 组件\n   */\n  readonly range?: Range;\n  /**\n   * @title 坐标轴配置\n   */\n  readonly axis?: Axis;\n  /**\n   * @title 指针的配置\n   */\n  readonly indicator?: false | Indicator;\n  /**\n   * @title 统计文本\n   */\n  readonly statistic?: Statistic;\n  /**\n   * @title 仪表盘样式\n   */\n  readonly gaugeStyle?: StyleAttr;\n\n  /**\n   * @title meter gauge 相关配置\n   */\n\n  /**\n   * @title 仪表盘类型\n   * @description 可选项: 'meter', default 为空\n   */\n  readonly type?: string;\n  /**\n   * @title 仪表盘配置\n   * @description 当仪表盘类型 = 'meter' 生效\n   */\n  readonly meter?: {\n    /**\n     * @title 仪表盘总步数\n     * @default \"50\"\n     */\n    readonly steps?: number;\n    /**\n     * @title step 与 gap 的宽度占比\n     * @default \"0.5\"\n     */\n    readonly stepRatio?: number;\n  };\n}\n\n/**\n * @title 仪表盘\n * @description 自定义 shape 使用的 customInfo\n */\nexport type GaugeCustomInfo = {\n  /**\n   * @title 仪表盘 meter 类型的相关配置\n   */\n  readonly meter?: GaugeOptions['meter'];\n};\n"]}