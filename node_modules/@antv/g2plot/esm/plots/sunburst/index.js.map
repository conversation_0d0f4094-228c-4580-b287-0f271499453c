{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/sunburst/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAC;AAClE,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AAC3F,OAAO,gBAAgB,CAAC;AAKxB;IAA8B,4BAAqB;IAAnD;QAAA,qEAgCC;QAhBC,WAAW;QACJ,UAAI,GAAW,UAAU,CAAC;;IAenC,CAAC;IA/BC;;;OAGG;IACI,0BAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAYD;;OAEG;IACO,oCAAiB,GAA3B;QACE,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACO,mCAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IAtBD,kBAAkB;IACX,gCAAuB,GAAG,uBAAuB,CAAC;IACzD,gBAAgB;IACT,4BAAmB,GAAG,mBAAmB,CAAC;IACjD,cAAc;IACP,6BAAoB,GAAG,oBAAoB,CAAC;IAkBrD,eAAC;CAAA,AAhCD,CAA8B,IAAI,GAgCjC;SAhCY,QAAQ", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { NODE_ANCESTORS_FIELD } from '../../utils/hierarchy/util';\nimport { adaptor } from './adaptor';\nimport { DEFAULT_OPTIONS, SUNBURST_ANCESTOR_FIELD, SUNBURST_PATH_FIELD } from './constant';\nimport './interactions';\nimport { SunburstOptions } from './types';\n\nexport type { SunburstOptions };\n\nexport class Sunburst extends Plot<SunburstOptions> {\n  /**\n   * 获取 旭日图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<SunburstOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 旭日图 节点的祖先节点 */\n  static SUNBURST_ANCESTOR_FIELD = SUNBURST_ANCESTOR_FIELD;\n  /** 旭日图 节点的路径 */\n  static SUNBURST_PATH_FIELD = SUNBURST_PATH_FIELD;\n  /** 节点的祖先节点 */\n  static NODE_ANCESTORS_FIELD = NODE_ANCESTORS_FIELD;\n\n  /** 图表类型 */\n  public type: string = 'sunburst';\n\n  /**\n   * 获取 旭日图 默认配置\n   */\n  protected getDefaultOptions() {\n    return Sunburst.getDefaultOptions();\n  }\n\n  /**\n   * 获取旭日图的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<SunburstOptions> {\n    return adaptor;\n  }\n}\n"]}