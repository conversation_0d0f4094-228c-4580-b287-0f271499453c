{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/sunburst/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { GeometryOptions } from '../../adaptor/geometries';\nimport { ColorAttr, Options, StyleAttr } from '../../types';\nimport { DrillDownCfg } from '../../types/drill-down';\nimport { HierarchyOption } from '../../utils/hierarchy/types';\n\nexport interface SunburstOptions\n  extends Omit<Options, 'data' | 'slider' | 'scrollbar' | 'xAxis' | 'yAxis'>,\n    Pick<GeometryOptions, 'customInfo'> {\n  /**\n   * @title 旭日图数据\n   */\n  readonly data: any;\n  /**\n   * @title 径向类型\n   * @description  径向类型'x' | 'y'\n   */\n  readonly reflect?: 'x' | 'y';\n\n  // 样式\n\n  /**\n   * @title 内径\n   */\n  readonly innerRadius?: number;\n  /**\n   * @title 半经\n   */\n  readonly radius?: number;\n  /**\n   * @title 颜色映射\n   */\n  readonly colorField?: string;\n  /**\n   * @title 颜色\n   */\n  readonly color?: ColorAttr;\n  /**\n   * @title 旭日图形样式\n   */\n  readonly sunburstStyle?: StyleAttr;\n\n  /**\n   * @title 层级布局\n   */\n  readonly hierarchyConfig?: Omit<HierarchyOption, 'as' | 'type'> & {\n    /** default: 'value', required data to be like: { name: 'xx', [field]: 12, children: [] } */\n    readonly field?: string;\n    /**\n     * @title 是否忽略父节点的权重\n     * @description  其父节点的权重不由子节点权重总和决定\n     * @default false\n     */\n    readonly ignoreParentValue?: boolean;\n    /**\n     * @title 展示的层级深度\n     * @description  取值 > 0 默认空, 代表全部展示\n     */\n    readonly activeDepth?: number;\n  };\n\n  // 其他\n\n  /**\n   * @title 额外的原始字段\n   */\n  readonly rawFields?: string[];\n  /**\n   * @title 下钻交互\n   */\n  readonly drilldown?: DrillDownCfg;\n  /**\n   * @title 自定义旭日图形状\n   * @description polygon 图形元素展示形状\n   */\n  readonly shape?: string;\n}\n"]}