{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/sunburst/utils.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,+BAA+B,EAAE,MAAM,uCAAuC,CAAC;AACxF,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AACnC,OAAO,EAAE,SAAS,EAAE,MAAM,iCAAiC,CAAC;AAC5D,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AAG1E;;;GAGG;AACH,MAAM,UAAU,aAAa,CAAC,OAAuF;IAC3G,IAAA,IAAI,GAAkD,OAAO,KAAzD,EAAE,UAAU,GAAsC,OAAO,WAA7C,EAAE,SAAS,GAA2B,OAAO,UAAlC,EAAE,KAAyB,OAAO,gBAAZ,EAApB,eAAe,mBAAG,EAAE,KAAA,CAAa;IAC9D,IAAA,WAAW,GAAK,eAAe,YAApB,CAAqB;IACxC,IAAM,SAAS,GAAG;QAChB,SAAS,EAAE,SAAS;QACpB,OAAO,EAAE,OAAO;KACjB,CAAC;IACF,4DAA4D;IAC5D,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;IACxC,gCAAgC;IAChC,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,WAAW,CAAC;IAEzC,IAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,sBAChC,KAAK,EAAE,WAAW,IAAI,OAAO,IAC1B,IAAI,CAAC,eAAe,EAAE,CAAC,aAAa,CAAC,CAAC;QACzC,aAAa;QACb,IAAI,EAAE,oBAAa,IAAI,CAAE,EACzB,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,IACd,CAAC;IAEH,IAAM,MAAM,GAAG,EAAE,CAAC;IAElB,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI;;;QACjB,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;YACpB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE;YAC/C,OAAO,IAAI,CAAC;SACb;QAED,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,YAAY,gBAAQ,IAAI,CAAE,CAAC;QAC/B,OAAO,YAAY,CAAC,KAAK,GAAG,CAAC,EAAE;YAC7B,IAAI,GAAG,UAAG,MAAA,YAAY,CAAC,MAAM,CAAC,IAAI,0CAAE,IAAI,gBAAM,IAAI,CAAE,CAAC;YACrD,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC;SACpC;QAED,IAAM,QAAQ,kCACT,IAAI,CAAC,IAAI,CAAC,IAAI,kCAAM,CAAC,SAAS,IAAI,EAAE,CAAC,UAAE,eAAe,CAAC,KAAK,UAAE,gBAChE,mBAAmB,IAAG,IAAI,KAC1B,uBAAuB,IAAG,YAAY,CAAC,IAAI,CAAC,IAAI,QAC9C,IAAI,CACR,CAAC;QACF,cAAc;QACd,IAAI,WAAW,EAAE;YACf,QAAQ,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAI,MAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,0CAAG,WAAW,CAAC,CAAA,CAAC;SACpF;QACD,IAAI,UAAU,EAAE;YACd,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAI,MAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,0CAAG,UAAU,CAAC,CAAA,CAAC;SACjF;QACD,QAAQ,CAAC,GAAG,GAAG,eAAe,CAAC;QAC/B,QAAQ,CAAC,+BAA+B,CAAC,GAAG,EAAE,eAAe,iBAAA,EAAE,UAAU,YAAA,EAAE,SAAS,WAAA,EAAE,CAAC;QACvF,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import { omit } from '@antv/util';\nimport { HIERARCHY_DATA_TRANSFORM_PARAMS } from '../../interactions/actions/drill-down';\nimport { pick } from '../../utils';\nimport { partition } from '../../utils/hierarchy/partition';\nimport { treemap } from '../../utils/hierarchy/treemap';\nimport { SUNBURST_ANCESTOR_FIELD, SUNBURST_PATH_FIELD } from './constant';\nimport { SunburstOptions } from './types';\n\n/**\n * sunburst 处理数据\n * @param options\n */\nexport function transformData(options: Pick<SunburstOptions, 'data' | 'colorField' | 'rawFields' | 'hierarchyConfig'>) {\n  const { data, colorField, rawFields, hierarchyConfig = {} } = options;\n  const { activeDepth } = hierarchyConfig;\n  const transform = {\n    partition: partition,\n    treemap: treemap,\n  };\n  // @ts-ignore 兼容旧版本，支持 seriesField 来作为 hierarchyConfig.field\n  const seriesField = options.seriesField;\n  // @ts-ignore 兼容旧版本，支持矩阵树图形状的旭日图\n  const type = options.type || 'partition';\n\n  const nodes = transform[type](data, {\n    field: seriesField || 'value',\n    ...omit(hierarchyConfig, ['activeDepth']),\n    // @ts-ignore\n    type: `hierarchy.${type}`,\n    as: ['x', 'y'],\n  });\n\n  const result = [];\n\n  nodes.forEach((node) => {\n    if (node.depth === 0) {\n      return null;\n    }\n    if (activeDepth > 0 && node.depth > activeDepth) {\n      return null;\n    }\n\n    let path = node.data.name;\n    let ancestorNode = { ...node };\n    while (ancestorNode.depth > 1) {\n      path = `${ancestorNode.parent.data?.name} / ${path}`;\n      ancestorNode = ancestorNode.parent;\n    }\n\n    const nodeInfo = {\n      ...pick(node.data, [...(rawFields || []), hierarchyConfig.field]),\n      [SUNBURST_PATH_FIELD]: path,\n      [SUNBURST_ANCESTOR_FIELD]: ancestorNode.data.name,\n      ...node,\n    };\n    // note: 兼容旧版本\n    if (seriesField) {\n      nodeInfo[seriesField] = node.data[seriesField] || node.parent?.data?.[seriesField];\n    }\n    if (colorField) {\n      nodeInfo[colorField] = node.data[colorField] || node.parent?.data?.[colorField];\n    }\n    nodeInfo.ext = hierarchyConfig;\n    nodeInfo[HIERARCHY_DATA_TRANSFORM_PARAMS] = { hierarchyConfig, colorField, rawFields };\n    result.push(nodeInfo);\n  });\n\n  return result;\n}\n"]}