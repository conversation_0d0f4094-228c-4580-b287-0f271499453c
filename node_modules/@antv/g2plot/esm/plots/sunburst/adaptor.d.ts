import { Params } from '../../core/adaptor';
import { SunburstOptions } from './types';
/**
 * axis 配置
 * @param params
 */
export declare function axis(params: Params<SunburstOptions>): Params<SunburstOptions>;
/**
 * meta 配置
 * @param params
 */
export declare function meta(params: Params<SunburstOptions>): Params<SunburstOptions>;
/**
 * tooltip 配置
 * @param params
 */
export declare function tooltip(params: Params<SunburstOptions>): Params<SunburstOptions>;
/**
 * 旭日图适配器
 * @param chart
 * @param options
 */
export declare function adaptor(params: Params<SunburstOptions>): Params<SunburstOptions>;
