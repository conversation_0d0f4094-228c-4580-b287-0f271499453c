{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/bar/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ColumnOptions } from '../column/types';\n\n// rename column to bar\nexport interface BarOptions\n  extends Omit<ColumnOptions, 'columnStyle' | 'columnWidthRatio' | 'minColumnWidth' | 'maxColumnWidth'> {\n  /**\n   * @title 柱状图柱子样式配置\n   */\n  readonly barStyle?: ColumnOptions['columnStyle'];\n  /**\n   * @title 柱状图宽度占比\n   * @description 范围[0-1]\n   */\n  readonly barWidthRatio?: ColumnOptions['columnWidthRatio'];\n  /**\n   * @title 条形图最小宽度（像素）\n   */\n  readonly minBarWidth?: ColumnOptions['minColumnWidth'];\n  /**\n   * @title 条形图最大宽度（像素）\n   */\n  readonly maxBarWidth?: ColumnOptions['maxColumnWidth'];\n  /**\n   * @title 条形图柱子的背景配置\n   */\n  readonly barBackground?: ColumnOptions['columnBackground'];\n}\n"]}