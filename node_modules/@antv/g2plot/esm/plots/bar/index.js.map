{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/bar/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,+BAA+B,CAAC;AACzE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAK9C;;GAEG;AACH;IAAyB,uBAAgB;IAAzC;QAAA,qEAwCC;QA/BC,WAAW;QACK,UAAI,GAAW,KAAK,CAAC;;IA8BvC,CAAC;IAvCC;;;OAGG;IACI,qBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAKD;;OAEG;IACI,wBAAU,GAAjB,UAAkB,IAAwB;;QACxC,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QACtB,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAC;QACxB,IAAA,SAAS,GAAK,OAAO,UAAZ,CAAa;QACxB,IAAA,MAAM,GAA2B,OAAO,OAAlC,EAAE,MAAM,GAAmB,OAAO,OAA1B,EAAE,KAAK,GAAY,OAAO,MAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;QAC/C,KAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,EAAlC,MAAM,QAAA,EAAE,MAAM,QAAA,CAAqB;QACpC,KAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,EAA9B,KAAK,QAAA,EAAE,KAAK,QAAA,CAAmB;QAChC,IAAM,oBAAoB,yBAAQ,OAAO,KAAE,MAAM,QAAA,EAAE,MAAM,QAAA,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,GAAE,CAAC;QAC1E,IAAI,CAAC,EAAE,KAAK,OAAA,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC,CAAC;QAC/C,KAAK,CAAC,UAAU,CAAC,wBAAwB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACO,+BAAiB,GAA3B;QACE,OAAO,GAAG,CAAC,iBAAiB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACO,8BAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,UAAC;AAAD,CAAC,AAxCD,CAAyB,IAAI,GAwC5B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { getDataWhetherPercentage } from '../../utils/transform/percent';\nimport { adaptor, meta } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constants';\nimport { BarOptions } from './types';\n\nexport type { BarOptions };\n\n/**\n * 条形图\n */\nexport class Bar extends Plot<BarOptions> {\n  /**\n   * 获取 条形图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<BarOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public readonly type: string = 'bar';\n\n  /**\n   * @override\n   */\n  public changeData(data: BarOptions['data']) {\n    this.updateOption({ data });\n    const { chart, options } = this;\n    const { isPercent } = options;\n    let { xField, yField, xAxis, yAxis } = options;\n    [xField, yField] = [yField, xField];\n    [xAxis, yAxis] = [yAxis, xAxis];\n    const switchedFieldOptions = { ...options, xField, yField, yAxis, xAxis };\n    meta({ chart, options: switchedFieldOptions });\n    chart.changeData(getDataWhetherPercentage(data, xField, yField, xField, isPercent));\n  }\n\n  /**\n   * 获取 条形图 默认配置\n   */\n  protected getDefaultOptions() {\n    return Bar.getDefaultOptions();\n  }\n\n  /**\n   * 获取 条形图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<BarOptions> {\n    return adaptor;\n  }\n}\n"]}