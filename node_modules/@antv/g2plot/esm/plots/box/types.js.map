{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/box/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Options, StyleAttr } from '../../types';\n\nexport interface BoxOptions extends Options {\n  /**\n   * @title x轴字段\n   */\n  readonly xField: string;\n  /**\n   * @title y轴映射\n   * @descriptionbox range [low, q1, median, q3, high] 五个字段 or 一个数组字段\n   */\n  readonly yField: string | [string?, string?, string?, string?, string?];\n  /**\n   * @title 箱型样式\n   */\n  readonly boxStyle?: StyleAttr;\n  /**\n   * @title 分组拆分字段\n   * @default 分组情况，颜色作为视觉通道\n   */\n  readonly groupField?: string;\n  /**\n   * @title 异常值字段\n   */\n  readonly outliersField?: string;\n  /**\n   * @title 异常值样式\n   */\n  readonly outliersStyle?: StyleAttr;\n}\n"]}