{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/box/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAE/D,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAGxC;IAAyB,uBAAgB;IAAzC;QAAA,qEAyCC;QAhCC,WAAW;QACJ,UAAI,GAAW,KAAK,CAAC;;IA+B9B,CAAC;IAxCC;;;OAGG;IACI,qBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAKD;;;OAGG;IACI,wBAAU,GAAjB,UAAkB,IAAI;QACpB,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QACpB,IAAA,MAAM,GAAK,IAAI,CAAC,OAAO,OAAjB,CAAkB;QAEhC,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,KAAK,gBAAgB,EAAzB,CAAyB,CAAC,CAAC;QAC7E,IAAI,YAAY,EAAE;YAChB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzB;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACO,+BAAiB,GAA3B;QACE,OAAO,GAAG,CAAC,iBAAiB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACO,8BAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,UAAC;AAAD,CAAC,AAzCD,CAAyB,IAAI,GAyC5B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor } from './adaptor';\nimport { DEFAULT_OPTIONS, OUTLIERS_VIEW_ID } from './constant';\nimport { BoxOptions } from './types';\nimport { transformData } from './utils';\nexport type { BoxOptions };\n\nexport class Box extends Plot<BoxOptions> {\n  /**\n   * 获取 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<BoxOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'box';\n\n  /**\n   * @override\n   * @param data\n   */\n  public changeData(data) {\n    this.updateOption({ data });\n    const { yField } = this.options;\n\n    const outliersView = this.chart.views.find((v) => v.id === OUTLIERS_VIEW_ID);\n    if (outliersView) {\n      outliersView.data(data);\n    }\n\n    this.chart.changeData(transformData(data, yField));\n  }\n\n  /**\n   * 获取 箱型图 默认配置项\n   */\n  protected getDefaultOptions(): Partial<BoxOptions> {\n    return Box.getDefaultOptions();\n  }\n\n  /**\n   * 获取 箱型图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<BoxOptions> {\n    return adaptor;\n  }\n}\n"]}