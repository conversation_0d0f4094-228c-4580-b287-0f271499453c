{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/box/utils.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AACjC,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAGvC;;;;GAIG;AACH,MAAM,CAAC,IAAM,aAAa,GAAG,UAAC,IAAwB,EAAE,MAA4B;IAClF,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,sCAAsC;IACtC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAClB,IAAA,KAAG,GAA0B,MAAM,GAAhC,EAAE,IAAE,GAAsB,MAAM,GAA5B,EAAE,QAAM,GAAc,MAAM,GAApB,EAAE,IAAE,GAAU,MAAM,GAAhB,EAAE,MAAI,GAAI,MAAM,GAAV,CAAW;QAC3C,OAAO,GAAG,GAAG,CAAC,IAAI,EAAE,UAAC,GAAG;YACtB,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAG,CAAC,EAAE,GAAG,CAAC,IAAE,CAAC,EAAE,GAAG,CAAC,QAAM,CAAC,EAAE,GAAG,CAAC,IAAE,CAAC,EAAE,GAAG,CAAC,MAAI,CAAC,CAAC,CAAC;YACtE,OAAO,GAAG,CAAC;QACb,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC", "sourcesContent": ["import { map } from '@antv/util';\nimport { BOX_RANGE } from './constant';\nimport { BoxOptions } from './types';\n\n/**\n * @desc 将数据转换为 box 需要的的图表数据,如果yField为数组,从data中解构出对应数组值并写入data,否则直接返回data\n * @param data\n * @param yField\n */\nexport const transformData = (data: BoxOptions['data'], yField: BoxOptions['yField']) => {\n  let newData = data;\n  // formate data when `yField` is Array\n  if (Array.isArray(yField)) {\n    const [low, q1, median, q3, high] = yField;\n    newData = map(data, (obj) => {\n      obj[BOX_RANGE] = [obj[low], obj[q1], obj[median], obj[q3], obj[high]];\n      return obj;\n    });\n  }\n\n  return newData;\n};\n"]}