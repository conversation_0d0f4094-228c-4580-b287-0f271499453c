{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/bidirectional-bar/utils.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAQrC;;;;;GAKG;AACH,MAAM,UAAU,aAAa,CAC3B,MAAc,EACd,MAAgB,EAChB,WAAmB,EACnB,IAAW,EACX,OAAiB;IAEjB,IAAM,QAAQ,GAAkB,EAAE,CAAC;IACnC,MAAM,CAAC,OAAO,CAAC,UAAC,CAAS;QACvB,IAAI,CAAC,OAAO,CAAC,UAAC,CAAM;;YAClB,IAAM,GAAG;gBACP,GAAC,MAAM,IAAG,CAAC,CAAC,MAAM,CAAC;gBACnB,GAAC,WAAW,IAAG,CAAC;gBAChB,GAAC,CAAC,IAAG,CAAC,CAAC,CAAC,CAAC;mBACV,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;IACzD,IAAA,KAA0B,SAAS,GAAzB,EAAV,KAAK,mBAAG,EAAE,KAAA,EAAE,KAAc,SAAS,GAAb,EAAV,KAAK,mBAAG,EAAE,KAAA,CAAc;IAC3C,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACvE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,MAAyC;IACpE,OAAO,MAAM,KAAK,UAAU,CAAC;AAC/B,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,eAAe,CAAC,KAAU,EAAE,KAAU,EAAE,CAAM;IACrD,IAAA,EAAE,GAAQ,KAAK,GAAb,EAAE,EAAE,GAAI,KAAK,GAAT,CAAU;IACvB,IAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC;IAC1B,IAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC;IACpB,IAAA,KAAuB,KAAK,CAAC,cAAc,EAAzC,MAAM,YAAA,EAAE,QAAQ,cAAyB,CAAC;IAClD,wBAAwB;IACxB,IAAI,YAAY,CAAC,MAAM,CAAC,IAAI,QAAQ,KAAK,KAAK,EAAE;QAC9C;;;WAGG;QACH,EAAE,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QAC3D,EAAE,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KAC5D;IACD,IAAI,YAAY,CAAC,MAAM,CAAC,IAAI,QAAQ,KAAK,QAAQ,EAAE;QACjD;;;;WAIG;QACH,EAAE,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QAC1E,EAAE,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;KAC5E;IACD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,QAAQ,KAAK,QAAQ,EAAE;QAClD;;;;WAIG;QACH,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;QACpD,EAAE,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;QACvE,EAAE,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;KAC3E;IACD,qCAAqC;IACrC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,QAAQ,KAAK,KAAK,EAAE;QAC/C,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;QACpD,EAAE,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QACvD,EAAE,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;KACxD;AACH,CAAC", "sourcesContent": ["import { Types } from '@antv/g2';\nimport { groupBy } from '@antv/util';\nimport { Datum } from '../../types';\nimport { BidirectionalBarOptions } from './types';\n\ntype TransformData = {\n  [key: string]: string | number;\n}[];\n\n/**\n * bidirectional-bar 处理数据, 通过 SERIES_FIELD_KEY 字段分成左右数据\n * @param xField\n * @param yField\n * @param data\n */\nexport function transformData(\n  xField: string,\n  yField: string[],\n  seriesField: string,\n  data: Datum,\n  reverse?: boolean\n): Types.Data[] {\n  const hopeData: TransformData = [];\n  yField.forEach((d: string) => {\n    data.forEach((k: any) => {\n      const obj = {\n        [xField]: k[xField],\n        [seriesField]: d,\n        [d]: k[d],\n      };\n      hopeData.push(obj);\n    });\n  });\n  const groupData = Object.values(groupBy(hopeData, seriesField));\n  const [data1 = [], data2 = []] = groupData;\n  return reverse ? [data1.reverse(), data2.reverse()] : [data1, data2];\n}\n\n/**\n * 是否横向，默认空为横向\n * @param layout\n */\nexport function isHorizontal(layout: BidirectionalBarOptions['layout']) {\n  return layout !== 'vertical';\n}\n\n/**\n * 多 view 进行同步 padding 的自定义逻辑\n * @param chart\n * @param views\n * @param p\n */\nexport function syncViewPadding(chart: any, views: any, p: any) {\n  const [v1, v2] = views;\n  const p1 = v1.autoPadding;\n  const p2 = v2.autoPadding;\n  const { layout, position } = chart.__axisPosition;\n  // 目前只能根据布局的比例来判断 layout\n  if (isHorizontal(layout) && position === 'top') {\n    /**\n     * 保证 v1 的 left 和 v2 right 的间隔相等，因为 v1 有轴\n     * position top 即为 v1 左边，中间间距设置就为 0\n     */\n    v1.autoPadding = p.instance(p1.top, 0, p1.bottom, p1.left);\n    v2.autoPadding = p.instance(p2.top, p1.left, p2.bottom, 0);\n  }\n  if (isHorizontal(layout) && position === 'bottom') {\n    /**\n     * 保证 v1 的 left 和 v2 right 的间隔相等，因为 v1 有轴\n     * position bottom 即为 v1 的右边，v1 right = right / 2  v2 left = right / 2\n     * + 5 是为了 让那个轴不要太贴近了，更好看\n     */\n    v1.autoPadding = p.instance(p1.top, p1.right / 2 + 5, p1.bottom, p1.left);\n    v2.autoPadding = p.instance(p2.top, p2.right, p2.bottom, p1.right / 2 + 5);\n  }\n  if (!isHorizontal(layout) && position === 'bottom') {\n    /**\n     * 保证 v1 的 left 和 v2 left 的间隔相等 left 取最大值\n     * position bottom 即为 v1 下边，v1 bottom = bottom / 2  v2 top = bottom / 2\n     * + 5 是为了 让那个轴不要太贴近了，更好看\n     */\n    const left = p1.left >= p2.left ? p1.left : p2.left;\n    v1.autoPadding = p.instance(p1.top, p1.right, p1.bottom / 2 + 5, left);\n    v2.autoPadding = p.instance(p1.bottom / 2 + 5, p2.right, p2.bottom, left);\n  }\n  // 垂直状态，不建议设置position 为 top， 还是做个兼容处理\n  if (!isHorizontal(layout) && position === 'top') {\n    const left = p1.left >= p2.left ? p1.left : p2.left;\n    v1.autoPadding = p.instance(p1.top, p1.right, 0, left);\n    v2.autoPadding = p.instance(0, p2.right, p1.top, left);\n  }\n}\n"]}