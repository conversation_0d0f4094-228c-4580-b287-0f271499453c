{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/bidirectional-bar/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Options, StyleAttr } from '../../types';\nimport { Axis } from '../../types/axis';\n\nexport interface BidirectionalBarOptions extends Omit<Options, 'yAxis' | 'yField'> {\n  /**\n   * @title x轴字段\n   */\n  readonly xField: string;\n  /**\n   * @title y轴字段\n   */\n  readonly yField: [string, string];\n  /**\n   * @title yAxis\n   * @description yAxis 为多个 key 为 yField 里面的 2 个字段\n   */\n  readonly yAxis?:\n    | false\n    | {\n        [key: string]: Axis;\n      };\n  /**\n   * @title 柱状图宽度占比\n   * @description 范围[0-1]\n   */\n  readonly widthRatio?: number;\n  /**\n   * @title 柱状图柱子样式\n   */\n  readonly barStyle?: StyleAttr;\n  /**\n   * @title 布局方向选择\n   * @default \"horizontal\"\n   */\n  readonly layout?: 'horizontal' | 'vertical';\n}\n"]}