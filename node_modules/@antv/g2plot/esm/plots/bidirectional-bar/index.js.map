{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/bidirectional-bar/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,UAAU,CAAC;AAEnD,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AACvD,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAEjF,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAIvE;IAAsC,oCAA6B;IAAnE;QAAA,qEAwDC;QA1CC,WAAW;QACJ,UAAI,GAAW,mBAAmB,CAAC;;IAyC5C,CAAC;IAvDC;;;OAGG;IACI,kCAAiB,GAAxB;QACE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAM,iBAAiB,WAAE,EAAE;YAC/C,eAAe,iBAAA;SAChB,CAAC,CAAC;IACL,CAAC;IAQD;;OAEG;IACI,qCAAU,GAAjB,UAAkB,IAAS;QAAT,qBAAA,EAAA,SAAS;QACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,gBAAgB,CAAC,kBAAkB,EACnC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,CACtE,CAAC;QAEF,YAAY;QACZ,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QACtB,IAAA,KAA6B,IAAI,CAAC,OAAO,EAAvC,MAAM,YAAA,EAAE,MAAM,YAAA,EAAE,MAAM,YAAiB,CAAC;QAChD,OAAO;QACP,IAAM,SAAS,GAAU,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9F,IAAA,aAAa,GAAoB,SAAS,GAA7B,EAAE,cAAc,GAAI,SAAS,GAAb,CAAc;QAClD,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QAC5D,IAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAC9D,gBAAgB;QAChB,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9B,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAChC,OAAO;QACP,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAExB,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,gBAAgB,CAAC,iBAAiB,EAClC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CACrE,CAAC;IACJ,CAAC;IAES,4CAAiB,GAA3B;QACE,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACO,2CAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IA5CD,gBAAgB;IACT,iCAAgB,GAAG,gBAAgB,CAAC;IA4C7C,uBAAC;CAAA,AAxDD,CAAsC,IAAI,GAwDzC;SAxDY,gBAAgB", "sourcesContent": ["import { Event, VIEW_LIFE_CIRCLE } from '@antv/g2';\nimport { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { deepAssign, findViewById } from '../../utils';\nimport { adaptor } from './adaptor';\nimport { FIRST_AXES_VIEW, SECOND_AXES_VIEW, SERIES_FIELD_KEY } from './constant';\nimport { BidirectionalBarOptions } from './types';\nimport { isHorizontal, syncViewPadding, transformData } from './utils';\n\nexport type { BidirectionalBarOptions };\n\nexport class BidirectionalBar extends Plot<BidirectionalBarOptions> {\n  /**\n   * 获取 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<BidirectionalBarOptions> {\n    return deepAssign({}, super.getDefaultOptions(), {\n      syncViewPadding,\n    });\n  }\n\n  /** 对称条形图分类字段 */\n  static SERIES_FIELD_KEY = SERIES_FIELD_KEY;\n\n  /** 图表类型 */\n  public type: string = 'bidirectional-bar';\n\n  /**\n   * @override\n   */\n  public changeData(data = []) {\n    this.chart.emit(\n      VIEW_LIFE_CIRCLE.BEFORE_CHANGE_DATA,\n      Event.fromData(this.chart, VIEW_LIFE_CIRCLE.BEFORE_CHANGE_DATA, null)\n    );\n\n    // 更新options\n    this.updateOption({ data });\n    const { xField, yField, layout } = this.options;\n    // 处理数据\n    const groupData: any[] = transformData(xField, yField, SERIES_FIELD_KEY, data, isHorizontal(layout));\n    const [firstViewData, secondViewData] = groupData;\n    const firstView = findViewById(this.chart, FIRST_AXES_VIEW);\n    const secondView = findViewById(this.chart, SECOND_AXES_VIEW);\n    // 更新对应view的data\n    firstView.data(firstViewData);\n    secondView.data(secondViewData);\n    // 重新渲染\n    this.chart.render(true);\n\n    this.chart.emit(\n      VIEW_LIFE_CIRCLE.AFTER_CHANGE_DATA,\n      Event.fromData(this.chart, VIEW_LIFE_CIRCLE.AFTER_CHANGE_DATA, null)\n    );\n  }\n\n  protected getDefaultOptions() {\n    return BidirectionalBar.getDefaultOptions();\n  }\n\n  /**\n   * 获取对称条形图的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<BidirectionalBarOptions> {\n    return adaptor;\n  }\n}\n"]}