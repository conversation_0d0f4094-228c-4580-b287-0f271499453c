import { Params } from '../../core/adaptor';
import { BidirectionalBarOptions } from './types';
/**
 * interaction 配置
 * @param params
 */
export declare function interaction(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions>;
/**
 * limitInPlot
 * @param params
 */
export declare function limitInPlot(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions>;
/**
 * theme
 * @param params
 */
export declare function theme(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions>;
/**
 * animation
 * @param params
 */
export declare function animation(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions>;
/**
 * 对称条形图适配器
 * @param chart
 * @param options
 */
export declare function adaptor(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions>;
