import { Adaptor } from '../../core/adaptor';
import { Plot } from '../../core/plot';
import { ChordOptions } from './types';
export type { ChordOptions };
/**
 *  弦图 Chord
 */
export declare class Chord extends Plot<ChordOptions> {
    /**
     * 获取 面积图 默认配置项
     * 供外部使用
     */
    static getDefaultOptions(): Partial<ChordOptions>;
    /** 图表类型 */
    type: string;
    protected getDefaultOptions(): Partial<ChordOptions>;
    /**
     * 获取适配器
     */
    protected getSchemaAdaptor(): Adaptor<ChordOptions>;
}
