{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/chord/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAK7C;;GAEG;AACH;IAA2B,yBAAkB;IAA7C;QAAA,qEAqBC;QAbC,WAAW;QACJ,UAAI,GAAW,OAAO,CAAC;;IAYhC,CAAC;IApBC;;;OAGG;IACI,uBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAIS,iCAAiB,GAA3B;QACE,OAAO,KAAK,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACO,gCAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,YAAC;AAAD,CAAC,AArBD,CAA2B,IAAI,GAqB9B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constant';\nimport { ChordOptions } from './types';\n\nexport type { ChordOptions };\n\n/**\n *  弦图 Chord\n */\nexport class Chord extends Plot<ChordOptions> {\n  /**\n   * 获取 面积图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<ChordOptions> {\n    return DEFAULT_OPTIONS;\n  }\n  /** 图表类型 */\n  public type: string = 'chord';\n\n  protected getDefaultOptions() {\n    return Chord.getDefaultOptions();\n  }\n\n  /**\n   * 获取适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<ChordOptions> {\n    return adaptor;\n  }\n}\n"]}