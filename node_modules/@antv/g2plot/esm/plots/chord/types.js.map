{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/chord/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Data, Datum, Options, StyleAttr } from '../../types';\n\n/** 配置类型定义 */\nexport interface ChordOptions extends Omit<Options, 'xField' | 'yField' | 'xAxis' | 'yAxis'> {\n  /**\n   * @title 来源字段\n   */\n  readonly sourceField: string;\n  /**\n   * @title 去向字段\n   */\n  readonly targetField: string;\n  /**\n   * @title 权重字段\n   */\n  readonly weightField: string;\n  /**\n   * @title 源字段\n   */\n  readonly rawFields?: string[];\n  /**\n   * @title 数据\n   */\n  readonly data: Data;\n  /**\n   * @title 节点间距比例\n   * @description 参考画布的宽度,取值[0-1]\n   * @default 0.1\n   */\n  readonly nodePaddingRatio?: number;\n  /**\n   * @title 节点的厚度\n   * @description 取值[0-1]\n   * @default 0.05\n   */\n  readonly nodeWidthRatio?: number;\n  /**\n   * @title 节点排序方式\n   */\n  readonly nodeSort?: (a: Datum, b: Datum) => number;\n  /**\n   * @title 节点样式\n   */\n  readonly nodeStyle?: StyleAttr;\n  /**\n   * @title 边样式\n   */\n  readonly edgeStyle?: StyleAttr;\n}\n"]}