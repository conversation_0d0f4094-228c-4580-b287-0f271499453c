{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/facet/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Types, View } from '@antv/g2';\nimport { Geometry } from '../../adaptor/geometries/base';\nimport { Data, Meta, Options, Tooltip } from '../../types';\nimport { Annotation } from '../../types/annotation';\nimport { Axis } from '../../types/axis';\nimport { Interaction } from '../../types/interaction';\nimport { Legend } from '../../types/legend';\nimport { IPlotTypes } from '../mix/utils';\n\n/**\n * @title geometry 映射信息\n */\nexport type IGeometry = Geometry & {\n  adjust?: Types.AdjustOption;\n};\n\n/**\n * @title 子 view 的配置\n * @description 1. 暂时不开嵌套 view 的情况 2. 暂不开放 分面子 view 的 meta 独立设置\n */\nexport type IView = {\n  /**\n   * @title 图形\n   * @description geometry 及映射配置\n   */\n  readonly geometries: IGeometry[];\n  /**\n   * @title 数据\n   * @description optional，view 中的数据\n   */\n  readonly data?: Data;\n  /**\n   * @title meta\n   */\n  readonly meta?: Record<string, Axis>;\n  /**\n   * @title 坐标系的配置\n   * @description 每一个 view 具有相同的坐标系\n   */\n  readonly coordinate?: Types.CoordinateOption;\n  /**\n   * @title 坐标轴配置\n   */\n  readonly axes?: false | Record<string, Axis>;\n  /**\n   * @title interactions 配置\n   */\n  readonly interactions?: Interaction[];\n  /**\n   * @title annotation 配置\n   */\n  readonly annotations?: Annotation[];\n  /**\n   * @title animation 配置\n   */\n  readonly animation?: Options['animation'];\n  /**\n   * @title tooltip 配置\n   */\n  readonly tooltip?: Tooltip;\n};\n\n/**\n * @title 子 plot 的配置\n */\nexport type IPlot = IPlotTypes;\n/**\n * @title facetData map\n */\ntype FacetDataMap = {\n  /**\n   * @title rect 类型分面配置\n   */\n  readonly rect: Types.RectData;\n  /**\n   * @title mirror 类型分面配置\n   */\n  readonly mirror: Types.MirrorData;\n  /**\n   * @title list 类型分面配置\n   */\n  readonly list: Types.ListData;\n  /**\n   * @title matrix 类型分面配置\n   */\n  readonly matrix: Types.MatrixData;\n  /**\n   * @title circle 类型分面配置\n   */\n  readonly circle: Types.CircleData;\n  /**\n   * @title tree 类型分面配置\n   */\n  readonly tree: Types.TreeData;\n};\n\ntype FacetCfg = Types.MirrorCfg & Types.RectCfg & Types.TreeCfg & Types.ListCfg & Types.CircleCfg & Types.MatrixCfg;\n\n/**\n * @title 分面图的配置类型定义\n */\nexport interface FacetOptions<T extends keyof Types.FacetCfgMap = keyof Types.FacetCfgMap> extends Options, FacetCfg {\n  /**\n   * @title 分面类型\n   * @description G2 内置了六种分面: rect、list、circle、tree、mirror 和 matrix\n   */\n  readonly type: T;\n  /**\n   * @title 数据划分维度\n   */\n  readonly fields: string[];\n  /**\n   * @title 分面数据\n   */\n  readonly data: Data;\n  /**\n   * @title 绘图\n   * @description 每个分面 view 中的具体绘图表现 回调的方式\n   */\n  readonly eachView: (innerView: View, facet?: FacetDataMap[T]) => IView | IPlot;\n  /**\n   * @title 是否展示分面标题\n   * @default false\n   */\n  readonly showTitle?: boolean;\n  /**\n   * @title meta 字段\n   * @description facet 中对应的 meta 字段配置\n   */\n  readonly meta?: Record<string, Meta>;\n  /**\n   * @title 坐标系的配置\n   * @description 每一个 view 具有相同的坐标系\n   */\n  readonly coordinate?: Types.CoordinateOption;\n  /**\n   * @title 轴配置\n   */\n  readonly axes?: false | Record<string, Axis>;\n  /**\n   * @title tooltip 配置\n   */\n  readonly tooltip?: Tooltip;\n  /**\n   * @title 图例配置\n   */\n  readonly legend?: Legend;\n  /**\n   * @title 图例配置\n   */\n  readonly interactions?: Interaction[];\n}\n"]}