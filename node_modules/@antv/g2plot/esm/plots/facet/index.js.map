{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/facet/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAK7C;IAA2B,yBAAkB;IAA7C;QAAA,qEAyBC;QAhBC,WAAW;QACJ,UAAI,GAAW,MAAM,CAAC;;IAe/B,CAAC;IAxBC;;;OAGG;IACI,uBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAKD;;OAEG;IACO,iCAAiB,GAA3B;QACE,OAAO,KAAK,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACO,gCAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,YAAC;AAAD,CAAC,AAzBD,CAA2B,IAAI,GAyB9B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constant';\nimport { FacetOptions } from './types';\n\nexport type { FacetOptions };\n\nexport class Facet extends Plot<FacetOptions> {\n  /**\n   * 获取 分面图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<FacetOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'area';\n\n  /**\n   * 获取 分面图 默认配置\n   */\n  protected getDefaultOptions() {\n    return Facet.getDefaultOptions();\n  }\n\n  /**\n   * 获取 分面图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<FacetOptions> {\n    return adaptor;\n  }\n}\n"]}