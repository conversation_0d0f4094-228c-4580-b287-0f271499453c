{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/facet/utils.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,QAAQ,IAAI,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAC5E,OAAO,EAAE,qBAAqB,EAAE,MAAM,gBAAgB,CAAC;AAEvD,OAAO,EAAE,gBAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAGjE;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,QAAc,EAAE,OAAc;IACpD,IAAA,IAAI,GAAwF,OAAO,KAA/F,EAAE,UAAU,GAA4E,OAAO,WAAnF,EAAE,YAAY,GAA8D,OAAO,aAArE,EAAE,WAAW,GAAiD,OAAO,YAAxD,EAAE,SAAS,GAAsC,OAAO,UAA7C,EAAE,OAAO,GAA6B,OAAO,QAApC,EAAE,IAAI,GAAuB,OAAO,KAA9B,EAAE,IAAI,GAAiB,OAAO,KAAxB,EAAE,UAAU,GAAK,OAAO,WAAZ,CAAa;IAE5G,oBAAoB;IACpB,IAAI,IAAI,EAAE;QACR,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACrB;IAED,aAAa;IACb,IAAI,MAAM,GAAwB,EAAE,CAAC;IACrC,IAAI,IAAI,EAAE;QACR,IAAI,CAAC,IAAI,EAAE,UAAC,IAAU,EAAE,KAAa;YACnC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;KACJ;IAED,MAAM,GAAG,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACtC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAEvB,6BAA6B;IAC7B,IAAI,UAAU,EAAE;QACd,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;KACjC;IAED,yCAAyC;IACzC,IAAI,IAAI,KAAK,KAAK,EAAE;QAClB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACtB;SAAM;QACL,IAAI,CAAC,IAAI,EAAE,UAAC,IAAU,EAAE,KAAa;YACnC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;KACJ;IAED,IAAI,CAAC,UAAU,EAAE,UAAC,QAAQ;QACxB,WAAW;QACH,IAAA,GAAG,GAAK,eAAe,CAAC;YAC9B,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,QAAQ;SAClB,CAAC,IAHS,CAGR;QAEH,kBAAkB;QACV,IAAA,MAAM,GAAK,QAAQ,OAAb,CAAc;QAC5B,IAAI,MAAM,EAAE;YACV,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SAC7B;IACH,CAAC,CAAC,CAAC;IAEH,kBAAkB;IAClB,IAAI,CAAC,YAAY,EAAE,UAAC,WAAwB;QAC1C,IAAI,WAAW,CAAC,MAAM,KAAK,KAAK,EAAE;YAChC,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAC9C;aAAM;YACL,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;SACzD;IACH,CAAC,CAAC,CAAC;IACH,iBAAiB;IACjB,IAAI,CAAC,WAAW,EAAE,UAAC,UAAU;QAC3B,QAAQ,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,cACjC,UAAU,EACb,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,sBAAsB;IACtB,gBAAgB,CAAC,QAAQ,EAAE,SAAiC,CAAC,CAAC;IAE9D,IAAI,OAAO,EAAE;QACX,aAAa;QACb,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAChC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;KAC3B;SAAM,IAAI,OAAO,KAAK,KAAK,EAAE;QAC5B,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;KACvC;AACH,CAAC", "sourcesContent": ["import { View } from '@antv/g2';\nimport { each } from '@antv/util';\nimport { geometry as geometryAdaptor } from '../../adaptor/geometries/base';\nimport { AXIS_META_CONFIG_KEYS } from '../../constant';\nimport { Axis, Interaction, Options } from '../../types';\nimport { addViewAnimation, deepAssign, pick } from '../../utils';\nimport { IView } from './types';\n\n/**\n *\n * @param params 分面图 参数\n * @returns facet eachView 的回调设置每个 view 的展示\n */\nexport function execViewAdaptor(viewOfG2: View, options: IView): void {\n  const { data, coordinate, interactions, annotations, animation, tooltip, axes, meta, geometries } = options;\n\n  // 1. data, optional\n  if (data) {\n    viewOfG2.data(data);\n  }\n\n  // 2. meta 配置\n  let scales: Record<string, any> = {};\n  if (axes) {\n    each(axes, (axis: Axis, field: string) => {\n      scales[field] = pick(axis, AXIS_META_CONFIG_KEYS);\n    });\n  }\n\n  scales = deepAssign({}, meta, scales);\n  viewOfG2.scale(scales);\n\n  // 3. coordinate 配置 (默认由顶层决定)\n  if (coordinate) {\n    viewOfG2.coordinate(coordinate);\n  }\n\n  // 4. axis 轴配置 (默认由顶层决定，但可以通过 false 强制关闭)\n  if (axes === false) {\n    viewOfG2.axis(false);\n  } else {\n    each(axes, (axis: Axis, field: string) => {\n      viewOfG2.axis(field, axis);\n    });\n  }\n\n  each(geometries, (geometry) => {\n    // Geometry\n    const { ext } = geometryAdaptor({\n      chart: viewOfG2,\n      options: geometry,\n    });\n\n    // Geometry adjust\n    const { adjust } = geometry;\n    if (adjust) {\n      ext.geometry.adjust(adjust);\n    }\n  });\n\n  // 5. interactions\n  each(interactions, (interaction: Interaction) => {\n    if (interaction.enable === false) {\n      viewOfG2.removeInteraction(interaction.type);\n    } else {\n      viewOfG2.interaction(interaction.type, interaction.cfg);\n    }\n  });\n  // 6. annotations\n  each(annotations, (annotation) => {\n    viewOfG2.annotation()[annotation.type]({\n      ...annotation,\n    });\n  });\n\n  // 7. animation (先做动画)\n  addViewAnimation(viewOfG2, animation as Options['animation']);\n\n  if (tooltip) {\n    // 8. tooltip\n    viewOfG2.interaction('tooltip');\n    viewOfG2.tooltip(tooltip);\n  } else if (tooltip === false) {\n    viewOfG2.removeInteraction('tooltip');\n  }\n}\n"]}