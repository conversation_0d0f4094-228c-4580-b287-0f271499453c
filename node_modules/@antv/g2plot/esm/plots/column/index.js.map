{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/column/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,+BAA+B,CAAC;AACzE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAK9C;;GAEG;AACH;IAA4B,0BAAmB;IAA/C;QAAA,qEAoCC;QA3BC,WAAW;QACK,UAAI,GAAW,QAAQ,CAAC;;IA0B1C,CAAC;IAnCC;;;OAGG;IACI,wBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAKD;;OAEG;IACI,2BAAU,GAAjB,UAAkB,IAA2B;QAC3C,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QACtB,IAAA,KAAgC,IAAI,CAAC,OAAO,EAA1C,MAAM,YAAA,EAAE,MAAM,YAAA,EAAE,SAAS,eAAiB,CAAC;QAC7C,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAC;QAChC,IAAI,CAAC,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,wBAAwB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;IAC3F,CAAC;IAED;;OAEG;IACO,kCAAiB,GAA3B;QACE,OAAO,MAAM,CAAC,iBAAiB,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACO,iCAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,aAAC;AAAD,CAAC,AApCD,CAA4B,IAAI,GAoC/B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { getDataWhetherPercentage } from '../../utils/transform/percent';\nimport { adaptor, meta } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constants';\nimport { ColumnOptions } from './types';\n\nexport type { ColumnOptions };\n\n/**\n * 柱形图\n */\nexport class Column extends Plot<ColumnOptions> {\n  /**\n   * 获取 柱形图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<ColumnOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public readonly type: string = 'column';\n\n  /**\n   * @override\n   */\n  public changeData(data: ColumnOptions['data']) {\n    this.updateOption({ data });\n    const { yField, xField, isPercent } = this.options;\n    const { chart, options } = this;\n    meta({ chart, options });\n    this.chart.changeData(getDataWhetherPercentage(data, yField, xField, yField, isPercent));\n  }\n\n  /**\n   * 获取 柱形图 默认配置\n   */\n  protected getDefaultOptions() {\n    return Column.getDefaultOptions();\n  }\n\n  /**\n   * 获取 柱形图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<ColumnOptions> {\n    return adaptor;\n  }\n}\n"]}