{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/column/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ShapeAttrs } from '@antv/g2';\nimport { OptionWithConnectedArea } from '../../adaptor/connected-area';\nimport { OptionWithConversionTag } from '../../adaptor/conversion-tag';\nimport { IntervalGeometryOptions } from '../../adaptor/geometries/interval';\nimport { BrushCfg, Options, StyleAttr } from '../../types';\nimport { Transformations } from '../../types/coordinate';\n\ntype PartialIntervalGeometryOptions = Pick<IntervalGeometryOptions, 'dodgePadding' | 'intervalPadding'>;\n\nexport interface ColumnOptions\n  extends Options,\n    OptionWithConversionTag,\n    OptionWithConnectedArea,\n    PartialIntervalGeometryOptions {\n  /**\n   * @title x轴字段\n   */\n  readonly xField: string;\n  /**\n   * @title y轴字段\n   */\n  readonly yField: string;\n  /**\n   * @title 拆分字段\n   */\n  readonly seriesField?: string;\n  /**\n   * @title 是否分组柱形图\n   * @default false\n   */\n  readonly isGroup?: boolean;\n  /**\n   * @title 是否是区间柱状图\n   * @default false\n   */\n  readonly isRange?: boolean;\n  /**\n   * @title 是否是百分比柱状图\n   * @default false\n   */\n  readonly isPercent?: boolean;\n  /**\n   * @title 是否堆积柱状图\n   * @default false\n   */\n  readonly isStack?: boolean;\n  /**\n   * @title 柱状图宽度占比\n   * @description 范围[0-1]\n   */\n  readonly columnWidthRatio?: number;\n  /**\n   * @title 分组中柱子之间的间距\n   * @description 范围[0-1]，仅对分组柱状图适用\n   */\n  readonly marginRatio?: number;\n  /**\n   * @title 柱状图最小宽度（像素）\n   */\n  readonly minColumnWidth?: number;\n  /**\n   * @title 柱状图最大宽度（像素）\n   */\n  readonly maxColumnWidth?: number;\n  /**\n   * @title 柱状图柱子的背景\n   */\n  readonly columnBackground?: { style?: ShapeAttrs };\n  /**\n   * @title 柱子样式\n   */\n  readonly columnStyle?: StyleAttr;\n  /**\n   * @title 分组字段\n   * @description 优先级高于 seriesField , isGroup: true 时会根据 groupField 进行分组\n   */\n  readonly groupField?: string;\n  /**\n   * @title 自定义柱状图\n   * @description interval 图形元素展示形状\n   */\n  readonly shape?: string;\n  /**\n   * @title 图表交互\n   * @description 开启下钻交互，以及进行下钻交互的配置\n   */\n  readonly brush?: BrushCfg;\n\n  /**\n   * @title 坐标转换\n   * @description 可以对坐标系进行转换，如: reflectX, reflectY, transpose 等\n   */\n  readonly coordinate?: Transformations;\n}\n"]}