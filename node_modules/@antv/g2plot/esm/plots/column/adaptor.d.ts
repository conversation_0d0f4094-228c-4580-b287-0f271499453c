import { Params } from '../../core/adaptor';
import { ColumnOptions } from './types';
/**
 * meta 配置
 * @param params
 */
export declare function meta(params: Params<ColumnOptions>): Params<ColumnOptions>;
/**
 * legend 配置
 * @param params
 */
export declare function legend(params: Params<ColumnOptions>): Params<ColumnOptions>;
/**
 * 柱形图适配器
 * @param params
 */
export declare function adaptor(params: Params<ColumnOptions>, isBar?: boolean): Params<import("../../types").Options>;
