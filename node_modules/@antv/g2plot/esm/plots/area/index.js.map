{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/area/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,+BAA+B,CAAC;AACzE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAK9C;IAA0B,wBAAiB;IAA3C;QAAA,qEAqCC;QA5BC,WAAW;QACJ,UAAI,GAAW,MAAM,CAAC;;IA2B/B,CAAC;IApCC;;;OAGG;IACI,sBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAKD;;OAEG;IACO,gCAAiB,GAA3B;QACE,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,yBAAU,GAAjB,UAAkB,IAAyB;QACzC,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QACtB,IAAA,KAAgC,IAAI,CAAC,OAAO,EAA1C,SAAS,eAAA,EAAE,MAAM,YAAA,EAAE,MAAM,YAAiB,CAAC;QAC7C,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAC;QAChC,IAAI,CAAC,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,wBAAwB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;IAC3F,CAAC;IAED;;OAEG;IACO,+BAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,WAAC;AAAD,CAAC,AArCD,CAA0B,IAAI,GAqC7B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { getDataWhetherPercentage } from '../../utils/transform/percent';\nimport { adaptor, meta } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constants';\nimport { AreaOptions } from './types';\n\nexport type { AreaOptions };\n\nexport class Area extends Plot<AreaOptions> {\n  /**\n   * 获取 面积图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<AreaOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'area';\n\n  /**\n   * 获取 面积图 默认配置\n   */\n  protected getDefaultOptions() {\n    return Area.getDefaultOptions();\n  }\n\n  /**\n   * @override\n   * @param data\n   */\n  public changeData(data: AreaOptions['data']) {\n    this.updateOption({ data });\n    const { isPercent, xField, yField } = this.options;\n    const { chart, options } = this;\n    meta({ chart, options });\n    this.chart.changeData(getDataWhetherPercentage(data, yField, xField, yField, isPercent));\n  }\n\n  /**\n   * 获取 面积图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<AreaOptions> {\n    return adaptor;\n  }\n}\n"]}