{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/area/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import {\n  GeometryOptions,\n  AreaGeometryOptions,\n  LineGeometryOptions,\n  PointGeometryOptions,\n} from '../../adaptor/geometries';\nimport { Options, StyleAttr } from '../../types';\nimport { Transformations } from '../../types/coordinate';\n\n/** 面积图的配置类型定义 */\nexport interface AreaOptions extends Options, Pick<GeometryOptions, 'customInfo'> {\n  /**\n   * @title x轴字段\n   */\n  readonly xField?: string;\n  /**\n   * @title y轴字段\n   */\n  readonly yField?: string;\n  /**\n   * @title 分组字段\n   */\n  readonly seriesField?: string;\n  /**\n   * @title 是否配置堆积\n   * @default false\n   */\n  readonly isStack?: boolean;\n  /**\n   * @title 是否配置百分比\n   * @default false\n   */\n  readonly isPercent?: boolean;\n  /**\n   * @title 是否配置平滑\n   * @default false\n   */\n  readonly smooth?: boolean;\n  /**\n   * @title 面积图形样式\n   */\n  readonly areaStyle?: StyleAttr;\n  /**\n   * @title 面积 shape 配置\n   */\n  readonly areaShape?: Required<AreaGeometryOptions>['area']['shape'];\n  /**\n   * @title 面积中折线的样式\n   */\n  readonly line?: LineGeometryOptions['line'] & Pick<PointGeometryOptions, 'state'>;\n  /**\n   * @title 面积图数据点图形样式\n   */\n  readonly point?: PointGeometryOptions['point'] & Pick<PointGeometryOptions, 'state'>;\n  /**\n   * @title 面积图填充\n   * @description 面积图是否从 0 基准线开始填充\n   * @default false\n   */\n  readonly startOnZero?: boolean;\n\n  /**\n   * @title 坐标转换\n   * @description 可以对坐标系进行转换，如: reflectX, reflectY, transpose 等\n   */\n  readonly coordinate?: Transformations;\n}\n"]}