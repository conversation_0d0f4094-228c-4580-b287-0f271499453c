{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/_template/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAKpC;;GAEG;AACH;IAA8B,4BAAqB;IAAnD;QAAA,qEAUC;QATC,WAAW;QACJ,UAAI,GAAW,UAAU,CAAC;;IAQnC,CAAC;IANC;;OAEG;IACO,mCAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,eAAC;AAAD,CAAC,AAVD,CAA8B,IAAI,GAUjC", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor } from './adaptor';\nimport { TemplateOptions } from './types';\n\nexport type { TemplateOptions };\n\n/**\n * 这个是一个图表开发的 模板代码！\n */\nexport class Template extends Plot<TemplateOptions> {\n  /** 图表类型 */\n  public type: string = 'template';\n\n  /**\n   * 获取适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<TemplateOptions> {\n    return adaptor;\n  }\n}\n"]}