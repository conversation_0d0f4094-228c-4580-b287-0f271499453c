{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/ring-progress/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,UAAU,CAAC;AAEnD,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAK9C;IAAkC,gCAAyB;IAA3D;QAAA,qEA2CC;QAlCC,WAAW;QACJ,UAAI,GAAW,cAAc,CAAC;;IAiCvC,CAAC;IA1CC;;;OAGG;IACI,8BAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAKD;;;OAGG;IACI,iCAAU,GAAjB,UAAkB,OAAe;QAC/B,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,gBAAgB,CAAC,kBAAkB,EACnC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,CACtE,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;QAE/B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1C,gDAAgD;QAChD,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,gBAAgB,CAAC,iBAAiB,EAClC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CACrE,CAAC;IACJ,CAAC;IAES,wCAAiB,GAA3B;QACE,OAAO,YAAY,CAAC,iBAAiB,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACO,uCAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,mBAAC;AAAD,CAAC,AA3CD,CAAkC,IAAI,GA2CrC", "sourcesContent": ["import { Event, VIEW_LIFE_CIRCLE } from '@antv/g2';\nimport { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { getProgressData } from '../progress/utils';\nimport { adaptor, statistic } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constants';\nimport { RingProgressOptions } from './types';\n\nexport type { RingProgressOptions };\n\nexport class RingProgress extends Plot<RingProgressOptions> {\n  /**\n   * 获取默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<RingProgressOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'ring-process';\n\n  /**\n   * 更新数据\n   * @param percent\n   */\n  public changeData(percent: number) {\n    this.chart.emit(\n      VIEW_LIFE_CIRCLE.BEFORE_CHANGE_DATA,\n      Event.fromData(this.chart, VIEW_LIFE_CIRCLE.BEFORE_CHANGE_DATA, null)\n    );\n    this.updateOption({ percent });\n\n    this.chart.data(getProgressData(percent));\n    // todo 后续让 G2 层在 afterrender 之后，来重绘 annotations\n    statistic({ chart: this.chart, options: this.options }, true);\n\n    this.chart.emit(\n      VIEW_LIFE_CIRCLE.AFTER_CHANGE_DATA,\n      Event.fromData(this.chart, VIEW_LIFE_CIRCLE.AFTER_CHANGE_DATA, null)\n    );\n  }\n\n  protected getDefaultOptions() {\n    return RingProgress.getDefaultOptions();\n  }\n\n  /**\n   * 获取 环形进度图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<RingProgressOptions> {\n    return adaptor;\n  }\n}\n"]}