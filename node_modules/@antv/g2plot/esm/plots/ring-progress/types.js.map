{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/ring-progress/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ColorAttr, Options, Statistic, StyleAttr } from '../../types';\n\n/** mini 图类型定义需要 omit 很多的 G2 Options 配置 */\nexport interface RingProgressOptions extends Omit<Options, 'data' | 'tooltip' | 'legend' | 'label' | 'color'> {\n  /**\n   * @title 进度百分比\n   */\n  readonly percent: number;\n  /**\n   * @title 外环的半径\n   */\n  readonly radius?: number;\n  /**\n   * @title 内环的半径\n   */\n  readonly innerRadius?: number;\n  /**\n   * @title 进度条颜色\n   */\n  readonly color?: ColorAttr;\n  /**\n   * @title 进度条样式\n   */\n  readonly progressStyle?: StyleAttr;\n  /**\n   * @title 统计内容组件\n   */\n  readonly statistic?: Statistic;\n}\n"]}