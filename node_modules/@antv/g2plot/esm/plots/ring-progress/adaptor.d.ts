import { Params } from '../../core/adaptor';
import { RingProgressOptions } from './types';
/**
 * statistic 配置
 * @param params
 */
export declare function statistic(params: Params<RingProgressOptions>, updated?: boolean): Params<RingProgressOptions>;
/**
 * 环形进度图适配器
 * @param chart
 * @param options
 */
export declare function adaptor(params: Params<RingProgressOptions>): Params<import("../progress").ProgressOptions>;
