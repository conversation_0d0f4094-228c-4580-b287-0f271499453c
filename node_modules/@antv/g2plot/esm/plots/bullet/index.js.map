{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/bullet/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAE7C,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAIxC;IAA4B,0BAAmB;IAA/C;QAAA,qEAiCC;QAxBC,WAAW;QACJ,UAAI,GAAW,QAAQ,CAAC;;IAuBjC,CAAC;IAhCC;;;OAGG;IACI,wBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAKM,2BAAU,GAAjB,UAAkB,IAAI;QACpB,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QACtB,IAAA,KAAmB,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAA5C,GAAG,SAAA,EAAE,GAAG,SAAA,EAAE,EAAE,QAAgC,CAAC;QACrD,UAAU;QACV,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,GAAG,KAAA,EAAE,GAAG,KAAA,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAChF,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACO,iCAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACO,kCAAiB,GAA3B;QACE,OAAO,MAAM,CAAC,iBAAiB,EAAE,CAAC;IACpC,CAAC;IACH,aAAC;AAAD,CAAC,AAjCD,CAA4B,IAAI,GAiC/B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor, meta } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constant';\nimport { BulletOptions } from './types';\nimport { transformData } from './utils';\n\nexport type { BulletOptions };\n\nexport class Bullet extends Plot<BulletOptions> {\n  /**\n   * 获取 子弹图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<BulletOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'bullet';\n\n  public changeData(data) {\n    this.updateOption({ data });\n    const { min, max, ds } = transformData(this.options);\n    // 处理scale\n    meta({ options: this.options, ext: { data: { min, max } }, chart: this.chart });\n    this.chart.changeData(ds);\n  }\n\n  /**\n   * 获取子弹图的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<BulletOptions> {\n    return adaptor;\n  }\n\n  /**\n   * 获取 子弹图 默认配置\n   */\n  protected getDefaultOptions() {\n    return Bullet.getDefaultOptions();\n  }\n}\n"]}