{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/bullet/utils.ts"], "names": [], "mappings": "AAQA;;;;;;GAMG;AACH,SAAS,iBAAiB,CAAC,MAAgB,EAAE,KAAa,EAAE,KAAa;IACvE,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAG,KAAK,cAAI,KAAK,CAAE,CAAC,CAAC,CAAC,UAAG,KAAK,CAAE,CAAC;AAC9D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,aAAa,CAAC,OAAsB;IAC1C,IAAA,IAAI,GAA4D,OAAO,KAAnE,EAAE,MAAM,GAAoD,OAAO,OAA3D,EAAE,YAAY,GAAsC,OAAO,aAA7C,EAAE,UAAU,GAA0B,OAAO,WAAjC,EAAE,WAAW,GAAa,OAAO,YAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAChF,IAAM,EAAE,GAAU,EAAE,CAAC;IACrB,IAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAI,CAAC,OAAO,CAAC,UAAC,IAAS,EAAE,KAAa;QACpC,mBAAmB;QACnB,IAAM,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAC9C,WAAW,CAAC,IAAI,CAAC,UAAC,CAAS,EAAE,CAAS,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,CAAC,CAAC;QAClD,WAAW,CAAC,OAAO,CAAC,UAAC,CAAS,EAAE,CAAS;;YACvC,IAAM,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChE,EAAE,CAAC,IAAI;oBACL,IAAI,EAAE,UAAG,UAAU,cAAI,CAAC,CAAE;;gBAC1B,GAAC,MAAM,IAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC/C,GAAC,UAAU,IAAG,KAAK;oBACnB,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAM,aAAa,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAClD,aAAa,CAAC,OAAO,CAAC,UAAC,CAAS,EAAE,CAAS;;YACzC,EAAE,CAAC,IAAI;oBACL,IAAI,EAAE,iBAAiB,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC,CAAC;;gBACvD,GAAC,MAAM,IAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC/C,GAAC,YAAY,IAAG,CAAC;oBACjB,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAM,YAAY,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAChD,YAAY,CAAC,OAAO,CAAC,UAAC,CAAS,EAAE,CAAS;;YACxC,EAAE,CAAC,IAAI;oBACL,IAAI,EAAE,iBAAiB,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC,CAAC;;gBACrD,GAAC,MAAM,IAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC/C,GAAC,WAAW,IAAG,CAAC;oBAChB,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,iBAAiB;QACjB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IACH,kBAAkB;IAClB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC7C,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/C,kBAAkB;IAClB,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAExB,cAAc;IACd,IAAI,MAAM,KAAK,UAAU,EAAE;QACzB,EAAE,CAAC,OAAO,EAAE,CAAC;KACd;IACD,OAAO,EAAE,GAAG,KAAA,EAAE,GAAG,KAAA,EAAE,EAAE,IAAA,EAAE,CAAC;AAC1B,CAAC", "sourcesContent": ["import { BulletOptions } from './types';\n\ntype TransformData = {\n  min: number;\n  max: number;\n  ds: any[];\n};\n\n/**\n * 获取分类字段 key 值 一个分类值的时候， 返回非索引 key 值，在 tooltip 不做索引区分\n * @param values 数据量\n * @param field 指标字段\n * @param index 索引\n * @returns string\n */\nfunction getSeriesFieldKey(values: number[], field: string, index: number): string {\n  return values.length > 1 ? `${field}_${index}` : `${field}`;\n}\n\n/**\n * bullet 处理数据\n * @param options\n */\nexport function transformData(options: BulletOptions): TransformData {\n  const { data, xField, measureField, rangeField, targetField, layout } = options;\n  const ds: any[] = [];\n  const scales: number[] = [];\n  data.forEach((item: any, index: number) => {\n    // 构建 title * range\n    const rangeValues = [item[rangeField]].flat();\n    rangeValues.sort((a: number, b: number) => a - b);\n    rangeValues.forEach((d: number, i: number) => {\n      const range = i === 0 ? d : rangeValues[i] - rangeValues[i - 1];\n      ds.push({\n        rKey: `${rangeField}_${i}`,\n        [xField]: xField ? item[xField] : String(index), // 没有xField就用索引\n        [rangeField]: range,\n      });\n    });\n\n    // 构建 title * measure\n    const measureValues = [item[measureField]].flat();\n    measureValues.forEach((d: number, i: number) => {\n      ds.push({\n        mKey: getSeriesFieldKey(measureValues, measureField, i),\n        [xField]: xField ? item[xField] : String(index),\n        [measureField]: d,\n      });\n    });\n\n    // 构建 title * target\n    const targetValues = [item[targetField]].flat();\n    targetValues.forEach((d: number, i: number) => {\n      ds.push({\n        tKey: getSeriesFieldKey(targetValues, targetField, i),\n        [xField]: xField ? item[xField] : String(index),\n        [targetField]: d,\n      });\n    });\n\n    // 为了取最大值和最小值，先存储\n    scales.push(item[rangeField], item[measureField], item[targetField]);\n  });\n  // scales 是嵌套的需要拍平\n  let min = Math.min(...scales.flat(Infinity));\n  const max = Math.max(...scales.flat(Infinity));\n  // min 大于 0 从 0 开始\n  min = min > 0 ? 0 : min;\n\n  // 垂直情况，需要反转数据\n  if (layout === 'vertical') {\n    ds.reverse();\n  }\n  return { min, max, ds };\n}\n"]}