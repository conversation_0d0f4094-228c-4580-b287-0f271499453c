{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/radar/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { AreaGeometryOptions, PointGeometryOptions } from '../../adaptor/geometries';\nimport { Options, ShapeStyle } from '../../types';\n\nexport interface RadarOptions extends Options {\n  /**\n   * @title x轴字段\n   */\n  readonly xField: string;\n  /**\n   * @title y轴字段\n   * @description 映射雷达图的射线长度\n   */\n  readonly yField: string;\n  /**\n   * @title 分组字段\n   */\n  readonly seriesField?: string;\n  /**\n   * @title 是否平滑\n   * @default false\n   */\n  readonly smooth?: boolean;\n  /**\n   * @title 折线图形样式\n   */\n  readonly lineStyle?: ShapeStyle | ((x: any, y: any, series?: any) => ShapeStyle);\n  /**\n   * @title 数据点图形样式\n   */\n  readonly point?: PointGeometryOptions['point'] & Pick<PointGeometryOptions, 'state'>;\n  /**\n   * @title area 图形样式\n   * @description 均提供回调的方式, 不开放 field 映射配置\n   */\n  readonly area?: AreaGeometryOptions['area'];\n  /**\n   * @title 角度轴配置\n   */\n  readonly xAxis?: any;\n  /**\n   * @title 径向轴配置\n   */\n  readonly yAxis?: any;\n  /**\n   * @title 雷达图半径\n   */\n  readonly radius?: number;\n  /**\n   * @title 雷达图开始角度\n   */\n  readonly startAngle?: number;\n  /**\n   * @title 雷达图结束角度\n   */\n  readonly endAngle?: number;\n}\n"]}