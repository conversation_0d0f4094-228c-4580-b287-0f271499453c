{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/radar/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AACzC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,gBAAgB,CAAC;AAKxB;IAA2B,yBAAkB;IAA7C;QAAA,qEA8DC;QA7DC,WAAW;QACJ,UAAI,GAAW,OAAO,CAAC;;IA4DhC,CAAC;IA1DC;;;OAGG;IACI,0BAAU,GAAjB,UAAkB,IAAI;QACpB,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACO,iCAAiB,GAA3B;QACE,OAAO,UAAU,CAAC,EAAE,EAAE,iBAAM,iBAAiB,WAAE,EAAE;YAC/C,KAAK,EAAE;gBACL,KAAK,EAAE;oBACL,MAAM,EAAE,EAAE;iBACX;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE;wBACJ,IAAI,EAAE,MAAM;qBACb;iBACF;aACF;YACD,KAAK,EAAE;gBACL,IAAI,EAAE;oBACJ,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;qBACf;iBACF;aACF;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE,KAAK;aAChB;YACD,OAAO,EAAE;gBACP,MAAM,EAAE,IAAI;gBACZ,cAAc,EAAE,IAAI;gBACpB,WAAW,EAAE,IAAI;gBACjB,UAAU,EAAE;oBACV,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE;wBACJ,KAAK,EAAE;4BACL,MAAM,EAAE,SAAS;4BACjB,QAAQ,EAAE,CAAC,CAAC,CAAC;yBACd;qBACF;oBACD,MAAM,EAAE,IAAI;iBACb;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,gCAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,YAAC;AAAD,CAAC,AA9DD,CAA2B,IAAI,GA8D9B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { deepAssign } from '../../utils';\nimport { adaptor } from './adaptor';\nimport './interactions';\nimport { RadarOptions } from './types';\n\nexport type { RadarOptions };\n\nexport class Radar extends Plot<RadarOptions> {\n  /** 图表类型 */\n  public type: string = 'radar';\n\n  /**\n   * @override\n   * @param data\n   */\n  public changeData(data) {\n    this.updateOption({ data });\n    this.chart.changeData(data);\n  }\n\n  /**\n   * 获取 雷达图 默认配置\n   */\n  protected getDefaultOptions(): Partial<RadarOptions> {\n    return deepAssign({}, super.getDefaultOptions(), {\n      xAxis: {\n        label: {\n          offset: 15,\n        },\n        grid: {\n          line: {\n            type: 'line',\n          },\n        },\n      },\n      yAxis: {\n        grid: {\n          line: {\n            type: 'circle',\n          },\n        },\n      },\n      legend: {\n        position: 'top',\n      },\n      tooltip: {\n        shared: true,\n        showCrosshairs: true,\n        showMarkers: true,\n        crosshairs: {\n          type: 'xy',\n          line: {\n            style: {\n              stroke: '#565656',\n              lineDash: [4],\n            },\n          },\n          follow: true,\n        },\n      },\n    });\n  }\n\n  /**\n   * 获取 雷达图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<RadarOptions> {\n    return adaptor;\n  }\n}\n"]}