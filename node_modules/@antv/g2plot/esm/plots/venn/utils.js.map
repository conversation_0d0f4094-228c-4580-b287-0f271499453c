{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/venn/utils.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,aAAa,CAAC;AACzC,OAAO,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAC5E,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AAUtD;;;;;GAKG;AACH,MAAM,CAAC,IAAM,WAAW,GAAG,OAAO,CAChC,CAAC,UAAC,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS;IACxC,IAAM,QAAQ,GAAG,IAAI,GAAG,EAAyC,CAAC;IAClE,IAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC;IAC5C,IAAI,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,YAAY,CAAC,CAAC,GAAG,GAAG,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;SACpF;aAAM;YACL,mCAAmC;YACnC,IAAM,QAAQ,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAC,EAAE,IAAK,OAAA,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAhB,CAAgB,CAAC,CAAC;YAC5D,QAAQ,CAAC,GAAG,CACV,CAAC,CAAC,QAAQ,CAAC,EACX,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAAtB,CAAsB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CACxE,CAAC;SACH;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAqB,EACtB;IAAC,gBAAS;SAAT,UAAS,EAAT,qBAAS,EAAT,IAAS;QAAT,2BAAS;;IAAK,OAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AAAtB,CAAsB,CAClB,CAAC;AAEtB;;;;;;;;GAQG;AACH,MAAM,UAAU,cAAc,CAAC,OAAoB,EAAE,KAAa,EAAE,MAAc,EAAE,OAAmB;IAAnB,wBAAA,EAAA,WAAmB;IAC7F,IAAA,IAAI,GAA2B,OAAO,KAAlC,EAAE,SAAS,GAAgB,OAAO,UAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IAE/C,WAAW;IACX,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC7C,OAAO,EAAE,CAAC;KACX;IAED,IAAM,QAAQ,GAAa,IAAI,CAAC,GAAG,CAAC,UAAC,CAAC;;QAAK,OAAA,uBACtC,CAAC,WACJ,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,EACxB,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,OACjB,UAAU,IAAG,EAAE,KACf,QAAQ,IAAG,EAAE,OACd;IANyC,CAMzC,CAAC,CAAC;IACJ,mBAAmB;IACnB,QAAQ,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,EAA7B,CAA6B,CAAC,CAAC;IACvD,oCAAoC;IAEpC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChC,IAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAChE,IAAM,WAAW,GAAG,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC1D,QAAQ,CAAC,OAAO,CAAC,UAAC,GAAG;QACnB,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QACtB,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QACnB,mBAAmB;QACnB,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAC,GAAG,IAAK,OAAA,OAAO,CAAC,GAAG,CAAC,EAAZ,CAAY,CAAC,CAAC;QACnD,IAAI,IAAI,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,IAAI,IAAI,CAAC;SACd;QACD,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;QACvB,IAAM,MAAM,GAAG,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACjD,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC;IACH,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CAAC,QAAe,EAAE,OAAc;IACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;YAClC,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { assign, memoize } from '@antv/util';\nimport { LEVEL, log } from '../../utils';\nimport { blend } from '../../utils/color/blend';\nimport { ID_FIELD, PATH_FIELD } from './constant';\nimport { computeTextCentres, intersectionAreaPath } from './layout/diagram';\nimport { scaleSolution, venn } from './layout/layout';\nimport { VennData, VennOptions } from './types';\n\ntype ColorMapFunction = (\n  colorPalette: string[],\n  data: VennData,\n  blendMode: VennOptions['blendMode'],\n  setsField: VennOptions['setsField']\n) => Map<string, string>;\n\n/**\n * 获取 颜色映射\n * @usage colorMap.get(id) => color\n *\n * @returns Map<string, string>\n */\nexport const getColorMap = memoize(\n  ((colorPalette, data, blendMode, setsField) => {\n    const colorMap = new Map<string /** id */, string /** color */>();\n    const colorPaletteLen = colorPalette.length;\n    data.forEach((d, idx) => {\n      if (d[setsField].length === 1) {\n        colorMap.set(d[ID_FIELD], colorPalette[(idx + colorPaletteLen) % colorPaletteLen]);\n      } else {\n        /** 一般都是可以获取到颜色的，如果不正确 就是输入了非法数据 */\n        const colorArr = d[setsField].map((id) => colorMap.get(id));\n        colorMap.set(\n          d[ID_FIELD],\n          colorArr.slice(1).reduce((a, b) => blend(a, b, blendMode), colorArr[0])\n        );\n      }\n    });\n\n    return colorMap;\n  }) as ColorMapFunction,\n  (...params) => JSON.stringify(params)\n) as ColorMapFunction;\n\n/**\n * 给韦恩图数据进行布局\n *\n * @param data\n * @param width\n * @param height\n * @param padding\n * @returns 韦恩图数据\n */\nexport function layoutVennData(options: VennOptions, width: number, height: number, padding: number = 0): VennData {\n  const { data, setsField, sizeField } = options;\n\n  // 处理空数据的情况\n  if (data.length === 0) {\n    log(LEVEL.WARN, false, 'warn: %s', '数据不能为空');\n    return [];\n  }\n\n  const vennData: VennData = data.map((d) => ({\n    ...d,\n    sets: d[setsField] || [],\n    size: d[sizeField],\n    [PATH_FIELD]: '',\n    [ID_FIELD]: '',\n  }));\n  // 1. 进行排序，避免图形元素遮挡\n  vennData.sort((a, b) => a.sets.length - b.sets.length);\n  // todo 2. 可以在这里处理下非法数据输入，避免直接 crash\n\n  const solution = venn(vennData);\n  const circles = scaleSolution(solution, width, height, padding);\n  const textCenters = computeTextCentres(circles, vennData);\n  vennData.forEach((row) => {\n    const sets = row.sets;\n    const id = sets.join(',');\n    row[ID_FIELD] = id;\n    // 保留 vennText 布局方法\n    const setCircles = sets.map((set) => circles[set]);\n    let path = intersectionAreaPath(setCircles);\n    if (!/[zZ]$/.test(path)) {\n      path += ' Z';\n    }\n    row[PATH_FIELD] = path;\n    const center = textCenters[id] || { x: 0, y: 0 };\n    assign(row, center);\n  });\n  return vennData;\n}\n\n/**\n * 检查是否存在 非法元素\n * @param legalArr 合法集合：['A', 'B']\n * @param testArr 检查集合：['A', 'B', 'C'] or ['A', 'C']（存在非法 'C'）\n * @return boolean\n */\nexport function islegalSets(legalArr: any[], testArr: any[]): boolean {\n  for (let i = 0; i < testArr.length; i++) {\n    if (!legalArr.includes(testArr[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n"]}