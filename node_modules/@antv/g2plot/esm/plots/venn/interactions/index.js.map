{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/plots/venn/interactions/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,mBAAmB,EAAE,MAAM,UAAU,CAAC;AAC/D,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,MAAM,oBAAoB,CAAC;AAEpF,0DAA0D;AAE1D,cAAc,CAAC,qBAAqB,EAAE,iBAAwB,CAAC,CAAC;AAChE,cAAc,CAAC,wBAAwB,EAAE,oBAA2B,CAAC,CAAC;AACtE,cAAc,CAAC,uBAAuB,EAAE,mBAA0B,CAAC,CAAC;AACpE,cAAc,CAAC,8BAA8B,EAAE,yBAAgC,CAAC,CAAC;AAEjF,iDAAiD;AAEjD,gCAAgC;AAChC,mBAAmB,CAAC,qBAAqB,EAAE;IACzC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,4BAA4B,EAAE,CAAC;IAChF,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,2BAA2B,EAAE,CAAC;CAC9E,CAAC,CAAC;AAEH,4BAA4B;AAC5B,mBAAmB,CAAC,wBAAwB,EAAE;IAC5C,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,kCAAkC,EAAE,CAAC;IACtF,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,8BAA8B,EAAE,CAAC;CACjF,CAAC,CAAC;AAEH,kCAAkC;AAClC,wBAAwB;AACxB,mBAAmB,CAAC,uBAAuB,EAAE;IAC3C,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,8BAA8B,EAAE,CAAC;IAC7E,QAAQ,EAAE,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,6BAA6B,CAAC,EAAE,CAAC;CAC7E,CAAC,CAAC;AACH,uBAAuB;AACvB,mBAAmB,CAAC,8BAA8B,EAAE;IAClD,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,qCAAqC,EAAE,CAAC;IACpF,QAAQ,EAAE,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,oCAAoC,CAAC,EAAE,CAAC;CACpF,CAAC,CAAC;AAEH,oCAAoC;AACpC,8BAA8B;AAC9B,mBAAmB,CAAC,oBAAoB,EAAE;IACxC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,MAAM,EAAE,CAAC,oBAAoB,EAAE,4BAA4B,CAAC,EAAE,CAAC;IAC5G,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,MAAM,EAAE,CAAC,mBAAmB,EAAE,2BAA2B,CAAC,EAAE,CAAC;CACzG,CAAC,CAAC;AAEH,8BAA8B;AAC9B,mBAAmB,CAAC,uBAAuB,EAAE;IAC3C,KAAK,EAAE;QACL;YACE,OAAO,EAAE,wBAAwB;YACjC,MAAM,EAAE,CAAC,iCAAiC,EAAE,kCAAkC,CAAC;SAChF;KACF;IACD,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,MAAM,EAAE,CAAC,6BAA6B,EAAE,8BAA8B,CAAC,EAAE,CAAC;CACtH,CAAC,CAAC", "sourcesContent": ["import { registerAction, registerInteraction } from '@antv/g2';\nimport { VennElementActive } from './actions/active';\nimport { VennElementHighlight } from './actions/highlight';\nimport { VennElementSelected, VennElementSingleSelected } from './actions/selected';\n\n/** ================== 注册交互反馈 aciton ================== */\n\nregisterAction('venn-element-active', VennElementActive as any);\nregisterAction('venn-element-highlight', VennElementHighlight as any);\nregisterAction('venn-element-selected', VennElementSelected as any);\nregisterAction('venn-element-single-selected', VennElementSingleSelected as any);\n\n/** ================== 注册交互 ================== */\n\n// ========= Active 交互 =========\nregisterInteraction('venn-element-active', {\n  start: [{ trigger: 'element:mouseenter', action: 'venn-element-active:active' }],\n  end: [{ trigger: 'element:mouseleave', action: 'venn-element-active:reset' }],\n});\n\n// ========= 高亮 交互 =========\nregisterInteraction('venn-element-highlight', {\n  start: [{ trigger: 'element:mouseenter', action: 'venn-element-highlight:highlight' }],\n  end: [{ trigger: 'element:mouseleave', action: 'venn-element-highlight:reset' }],\n});\n\n// ========= Selected 交互 =========\n// 点击 venn element （可多选）\nregisterInteraction('venn-element-selected', {\n  start: [{ trigger: 'element:click', action: 'venn-element-selected:toggle' }],\n  rollback: [{ trigger: 'dblclick', action: ['venn-element-selected:reset'] }],\n});\n// 点击 venn element （单选）\nregisterInteraction('venn-element-single-selected', {\n  start: [{ trigger: 'element:click', action: 'venn-element-single-selected:toggle' }],\n  rollback: [{ trigger: 'dblclick', action: ['venn-element-single-selected:reset'] }],\n});\n\n// ========= 韦恩图的图例事件，单独注册 =========\n// legend hover，element active\nregisterInteraction('venn-legend-active', {\n  start: [{ trigger: 'legend-item:mouseenter', action: ['list-active:active', 'venn-element-active:active'] }],\n  end: [{ trigger: 'legend-item:mouseleave', action: ['list-active:reset', 'venn-element-active:reset'] }],\n});\n\n// legend hover，element active\nregisterInteraction('venn-legend-highlight', {\n  start: [\n    {\n      trigger: 'legend-item:mouseenter',\n      action: ['legend-item-highlight:highlight', 'venn-element-highlight:highlight'],\n    },\n  ],\n  end: [{ trigger: 'legend-item:mouseleave', action: ['legend-item-highlight:reset', 'venn-element-highlight:reset'] }],\n});\n"]}