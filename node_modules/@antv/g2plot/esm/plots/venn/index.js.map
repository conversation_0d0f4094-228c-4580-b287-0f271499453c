{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/venn/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAK7C;;GAEG;AACH;IAA0B,wBAAiB;IAA3C;QAAA,qEAmCC;QAlCC,WAAW;QACJ,UAAI,GAAW,MAAM,CAAC;;IAiC/B,CAAC;IA/BQ,sBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACO,gCAAiB,GAA3B;QACE,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACO,+BAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACO,4BAAa,GAAvB;QACE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACzB,aAAa;YACb,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,iDAAiD;YACxE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,gBAAgB;YACpC,KAAK;YACL,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACzB;IACH,CAAC;IACH,WAAC;AAAD,CAAC,AAnCD,CAA0B,IAAI,GAmC7B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constant';\nimport { VennOptions } from './types';\n\nexport type { VennOptions };\n\n/**\n * 这个是一个图表开发的 模板代码！\n */\nexport class Venn extends Plot<VennOptions> {\n  /** 图表类型 */\n  public type: string = 'venn';\n\n  static getDefaultOptions() {\n    return DEFAULT_OPTIONS;\n  }\n\n  /**\n   * 获取 韦恩图 默认配置\n   */\n  protected getDefaultOptions() {\n    return Venn.getDefaultOptions();\n  }\n\n  /**\n   * 获取适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<VennOptions> {\n    return adaptor;\n  }\n\n  /**\n   * 覆写父类的方法\n   */\n  protected triggerResize() {\n    if (!this.chart.destroyed) {\n      // 首先自适应容器的宽高\n      this.chart.forceFit(); // g2 内部执行 changeSize，changeSize 中执行 render(true)\n      this.chart.clear();\n      this.execAdaptor(); // 核心：宽高更新之后计算布局\n      // 渲染\n      this.chart.render(true);\n    }\n  }\n}\n"]}