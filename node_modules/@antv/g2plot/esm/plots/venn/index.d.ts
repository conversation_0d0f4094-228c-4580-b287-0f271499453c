import { Adaptor } from '../../core/adaptor';
import { Plot } from '../../core/plot';
import { VennOptions } from './types';
export type { VennOptions };
/**
 * 这个是一个图表开发的 模板代码！
 */
export declare class Venn extends Plot<VennOptions> {
    /** 图表类型 */
    type: string;
    static getDefaultOptions(): Partial<VennOptions>;
    /**
     * 获取 韦恩图 默认配置
     */
    protected getDefaultOptions(): Partial<VennOptions>;
    /**
     * 获取适配器
     */
    protected getSchemaAdaptor(): Adaptor<VennOptions>;
    /**
     * 覆写父类的方法
     */
    protected triggerResize(): void;
}
