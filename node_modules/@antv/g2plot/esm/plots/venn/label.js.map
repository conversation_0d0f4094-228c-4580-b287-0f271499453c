{"version": 3, "file": "label.js", "sourceRoot": "", "sources": ["../../../src/plots/venn/label.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,aAAa,EAAE,qBAAqB,EAAE,MAAM,UAAU,CAAC;AAEhE,SAAS;AACT,cAAc;AACd,wBAAwB;AACxB;IAAwB,6BAAa;IAArC;;IAiBA,CAAC;IAhBC;;;;;;OAMG;IACO,iCAAa,GAAvB,UAAwB,QAAQ,EAAE,WAAW,EAAE,KAAa;QACpD,IAAA,KAAW,QAAQ,CAAC,IAAI,EAAtB,CAAC,OAAA,EAAE,CAAC,OAAkB,CAAC;QACzB,IAAA,KAAuB,QAAQ,CAAC,eAAe,EAA7C,OAAO,aAAA,EAAE,OAAO,aAA6B,CAAC;QACtD,OAAO;YACL,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC;YAChC,CAAC,EAAE,CAAC,GAAG,OAAO;YACd,CAAC,EAAE,CAAC,GAAG,OAAO;SACf,CAAC;IACJ,CAAC;IACH,gBAAC;AAAD,CAAC,AAjBD,CAAwB,aAAa,GAiBpC;AAED,yBAAyB;AACzB,qBAAqB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC", "sourcesContent": ["import { GeometryLabel, registerGeometryLabel } from '@antv/g2';\n\n// Step 1\n// 自定义 Label 类\n// 需要继承 GeometryLabel 基类\nclass VennLabel extends GeometryLabel {\n  /**\n   * 获取每个 label 的位置\n   * @param labelCfg\n   * @param mappingData\n   * @param index\n   * @returns label point\n   */\n  protected getLabelPoint(labelCfg, mappingData, index: number) {\n    const { x, y } = labelCfg.data;\n    const { offsetX, offsetY } = labelCfg.customLabelInfo;\n    return {\n      content: labelCfg.content[index],\n      x: x + offsetX,\n      y: y + offsetY,\n    };\n  }\n}\n\n// Step 2: 注册 CustomLabel\nregisterGeometryLabel('venn', VennLabel);\n"]}