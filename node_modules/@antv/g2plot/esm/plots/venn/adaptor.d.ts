import { Params } from '../../core/adaptor';
import './interactions';
import './label';
import './shape';
import { VennOptions } from './types';
/** 图例默认预留空间 */
export declare const LEGEND_SPACE = 40;
/**
 * legend 配置
 * @param params
 */
export declare function legend(params: Params<VennOptions>): Params<VennOptions>;
/**
 * 默认关闭坐标轴
 * @param params
 */
export declare function axis(params: Params<VennOptions>): Params<VennOptions>;
/**
 * 图适配器
 * @param chart
 * @param options
 */
export declare function adaptor(params: Params<VennOptions>): Params<VennOptions>;
