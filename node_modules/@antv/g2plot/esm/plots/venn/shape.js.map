{"version": 3, "file": "shape.js", "sourceRoot": "", "sources": ["../../../src/plots/venn/shape.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,aAAa,EAAS,IAAI,EAAE,MAAM,UAAU,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAElD,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAGxC;;;GAGG;AACH,SAAS,YAAY,CAAC,GAAoB;IACxC,mBAAmB;IACnB,OAAO,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1E,CAAC;AAED,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE;IAC9B,IAAI,YAAC,GAA+D,EAAE,SAAiB;QACrF,IAAM,IAAI,GAAG,GAAG,CAAC,IAAa,CAAC;QAC/B,IAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QACnD,IAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;QAEpC,IAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;QAEzD,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE;YACrB,KAAK,wBACA,SAAS,KACZ,IAAI,EAAE,QAAQ,GACf;YACD,IAAI,EAAE,WAAW;SAClB,CAAC,CAAC;QAEG,IAAA,KAAuB,GAAG,CAAC,UAAwB,EAAjD,OAAO,aAAA,EAAE,OAAO,aAAiC,CAAC;QAE1D,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/D,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAExB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,SAAS,YAAC,SAA+B;QAC/B,IAAA,KAAK,GAAK,SAAS,MAAd,CAAe;QAC5B,OAAO;YACL,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE;gBACL,SAAS,EAAE,CAAC;gBACZ,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,KAAK;gBACX,CAAC,EAAE,CAAC;aACL;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["import { IGroup } from '@antv/g-base';\nimport { registerShape, Types, Util } from '@antv/g2';\nimport { parsePathString } from '@antv/path-util';\nimport { Datum, Point } from '../../types';\nimport { deepAssign } from '../../utils';\nimport { PATH_FIELD } from './constant';\nimport { CustomInfo } from './types';\n\n/**\n * 获取填充属性\n * @param cfg 图形绘制数据\n */\nfunction getFillAttrs(cfg: Types.ShapeInfo) {\n  // style.fill 优先级更高\n  return deepAssign({}, cfg.defaultStyle, { fill: cfg.color }, cfg.style);\n}\n\nregisterShape('schema', 'venn', {\n  draw(cfg: Types.ShapeInfo & { points: Point[]; nextPoints: Point[] }, container: IGroup) {\n    const data = cfg.data as Datum;\n    const segments = parsePathString(data[PATH_FIELD]);\n    const fillAttrs = getFillAttrs(cfg);\n\n    const group = container.addGroup({ name: 'venn-shape' });\n\n    group.addShape('path', {\n      attrs: {\n        ...fillAttrs,\n        path: segments,\n      },\n      name: 'venn-path',\n    });\n\n    const { offsetX, offsetY } = cfg.customInfo as CustomInfo;\n\n    const matrix = Util.transform(null, [['t', offsetX, offsetY]]);\n    group.setMatrix(matrix);\n\n    return group;\n  },\n  getMarker(markerCfg: Types.ShapeMarkerCfg) {\n    const { color } = markerCfg;\n    return {\n      symbol: 'circle',\n      style: {\n        lineWidth: 0,\n        stroke: color,\n        fill: color,\n        r: 4,\n      },\n    };\n  },\n});\n"]}