{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/rose/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAK7C;IAA0B,wBAAiB;IAA3C;QAAA,qEAkCC;QAzBC,UAAU;QACH,UAAI,GAAW,MAAM,CAAC;;IAwB/B,CAAC;IAjCC;;;OAGG;IACI,sBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAKD;;;OAGG;IACI,yBAAU,GAAjB,UAAkB,IAAI;QACpB,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACO,gCAAiB,GAA3B;QACE,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACO,+BAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,WAAC;AAAD,CAAC,AAlCD,CAA0B,IAAI,GAkC7B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constant';\nimport { RoseOptions } from './types';\n\nexport type { RoseOptions };\n\nexport class Rose extends Plot<RoseOptions> {\n  /**\n   * 获取 玫瑰图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<RoseOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 玫瑰图 */\n  public type: string = 'rose';\n\n  /**\n   * @override\n   * @param data\n   */\n  public changeData(data) {\n    this.updateOption({ data });\n    this.chart.changeData(data);\n  }\n\n  /**\n   * 获取默认的 options 配置项\n   */\n  protected getDefaultOptions(): Partial<RoseOptions> {\n    return Rose.getDefaultOptions();\n  }\n\n  /**\n   * 获取 玫瑰图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<RoseOptions> {\n    return adaptor;\n  }\n}\n"]}