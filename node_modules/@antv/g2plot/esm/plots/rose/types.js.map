{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/rose/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { GeometryOptions, IntervalGeometryOptions } from '../../adaptor/geometries';\nimport { Options, StyleAttr } from '../../types';\n\nexport interface RoseOptions extends Options, Pick<GeometryOptions, 'customInfo'> {\n  /**\n   * @title 扇形切片分类所对应的数据字段名\n   * @description 每个扇形的弧度相等\n   */\n  readonly xField: string;\n  /**\n   * @title 扇形切片半径长度所对应的数据字段名\n   */\n  readonly yField: string;\n  /**\n   * @title 拆分字段\n   */\n  readonly seriesField?: string;\n  /**\n   * @title 是否分组玫瑰图\n   * @default false\n   */\n  readonly isGroup?: boolean;\n  /**\n   * @title 是否堆积玫瑰图\n   * @default false\n   */\n  readonly isStack?: boolean;\n  /**\n   * @title 玫瑰图的半径\n   * @description 原点为画布中心。配置值域为 (0,1] 1 代表玫瑰图大小为 1，即撑满绘图区域\n   */\n  readonly radius?: number;\n  /**\n   * @title 内部空心圆的半径\n   * @description 规则与 radius 一致\n   */\n  readonly innerRadius?: number;\n  /**\n   * @title 玫瑰图开始角度\n   */\n  readonly startAngle?: number;\n  /**\n   * @title 玫瑰图结束角度\n   */\n  readonly endAngle?: number;\n  /**\n   * @title 设置扇形样式\n   * @description sectorStyle 中的fill会覆盖 color 的配置,sectorStyle 可以直接指定，也可以通过 callback 的方式，根据数据为每个扇形切片指定单独的样式\n   */\n  readonly sectorStyle?: StyleAttr;\n  /**\n   * @title 扇形自定义形状\n   * @description interval 图形元素展示形状\n   */\n  readonly shape?: Required<IntervalGeometryOptions>['interval']['shape'];\n}\n"]}