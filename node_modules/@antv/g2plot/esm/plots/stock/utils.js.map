{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/stock/utils.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAC1C,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAExE;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,IAA2B,EAAE,MAAwC;IAChG,OAAO,GAAG,CAAC,IAAI,EAAE,UAAC,IAAI;QACpB,IAAM,GAAG,GAAG,IAAI,iBAAS,IAAI,CAAE,CAAC;QAChC,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,EAAE;YACnB,IAAA,MAAI,GAAsB,MAAM,GAA5B,EAAE,OAAK,GAAe,MAAM,GAArB,EAAE,IAAI,GAAS,MAAM,GAAf,EAAE,GAAG,GAAI,MAAM,GAAV,CAAW;YACxC,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,MAAI,CAAC,IAAI,GAAG,CAAC,OAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC;YACnE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAI,CAAC,EAAE,GAAG,CAAC,OAAK,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7D;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { isArray, map } from '@antv/util';\nimport { TREND_DOWN, TREND_FIELD, TREND_UP, Y_FIELD } from './constant';\n\n/**\n * @desc 股票图数据处理\n * @param data\n * @param yField\n */\nexport function getStockData(data: Record<string, any>[], yField: [string, string, string, string]) {\n  return map(data, (item) => {\n    const obj = item && { ...item };\n    if (isArray(yField) && obj) {\n      const [open, close, high, low] = yField;\n      obj[TREND_FIELD] = obj[open] <= obj[close] ? TREND_UP : TREND_DOWN;\n      obj[Y_FIELD] = [obj[open], obj[close], obj[high], obj[low]];\n    }\n    return obj;\n  });\n}\n"]}