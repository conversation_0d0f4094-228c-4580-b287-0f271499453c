{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/stock/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAE7C,OAAO,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAIvC;IAA2B,yBAAkB;IAA7C;QAAA,qEAoCC;QA3BC,WAAW;QACJ,UAAI,GAAW,OAAO,CAAC;;IA0BhC,CAAC;IAnCC;;;OAGG;IACI,uBAAiB,GAAxB;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAKD;;;OAGG;IACO,iCAAiB,GAA3B;QACE,OAAO,KAAK,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACO,gCAAgB,GAA1B;QACE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,0BAAU,GAAjB,UAAkB,IAA0B;QAC1C,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QACpB,IAAA,MAAM,GAAK,IAAI,CAAC,OAAO,OAAjB,CAAkB;QAChC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IACpD,CAAC;IACH,YAAC;AAAD,CAAC,AApCD,CAA2B,IAAI,GAoC9B", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constant';\nimport { StockOptions } from './types';\nimport { getStockData } from './utils';\n\nexport type { StockOptions };\n\nexport class Stock extends Plot<StockOptions> {\n  /**\n   * 获取 散点图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<StockOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'stock';\n\n  /**\n   * 默认配置\n   *  g2/g2plot默 认 配 置 -->  图 表 默 认 配 置  --> 开 发 者 自 定 义 配 置  --> 最 终 绘 图 配 置\n   */\n  protected getDefaultOptions(): Partial<StockOptions> {\n    return Stock.getDefaultOptions();\n  }\n\n  /**\n   * 获取 蜡烛图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<StockOptions> {\n    return adaptor;\n  }\n\n  /**\n   * @override\n   * @param data\n   */\n  public changeData(data: StockOptions['data']) {\n    this.updateOption({ data });\n    const { yField } = this.options;\n    this.chart.changeData(getStockData(data, yField));\n  }\n}\n"]}