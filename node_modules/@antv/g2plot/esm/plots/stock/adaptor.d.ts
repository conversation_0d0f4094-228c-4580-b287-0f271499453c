import { Params } from '../../core/adaptor';
import { StockOptions } from './types';
/**
 * meta 配置
 * @param params
 */
export declare function meta(params: Params<StockOptions>): Params<StockOptions>;
/**
 * axis 配置
 * @param params
 */
export declare function axis(params: Params<StockOptions>): Params<StockOptions>;
/**
 * tooltip 配置
 * @param params
 */
export declare function tooltip(params: Params<StockOptions>): Params<StockOptions>;
/**
 * legend 配置
 * @param params
 */
export declare function legend(params: Params<StockOptions>): Params<StockOptions>;
/**
 * K线图适配器
 * @param chart
 * @param options
 */
export declare function adaptor(params: Params<StockOptions>): void;
