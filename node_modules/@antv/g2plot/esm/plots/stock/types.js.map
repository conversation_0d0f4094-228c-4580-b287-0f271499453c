{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/stock/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Options, StyleAttr } from '../../types';\n\nexport interface StockOptions extends Options {\n  /**\n   * @title x 轴字段 日期\n   */\n  readonly xField: string;\n  /**\n   * @title y 轴映射\n   * @description   range  【开盘价/收盘价/最高价/最低价】，设置一个指定 [open, close, high, low]【开盘价/收盘价/最高价/最低价】字段的数组\n   */\n  readonly yField: [string, string, string, string];\n\n  /**\n   * @title 颜色配置\n   * @description  不支持 color 配置\n   */\n\n  /**\n   * @title 上涨色\n   */\n  readonly risingFill?: string;\n  /**\n   * @title 下跌色\n   */\n  readonly fallingFill?: string;\n  /**\n   * @title 样式配置\n   */\n  readonly stockStyle?: StyleAttr;\n}\n"]}