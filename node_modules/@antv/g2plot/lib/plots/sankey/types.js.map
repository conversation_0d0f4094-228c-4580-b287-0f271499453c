{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/sankey/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Data, Options, State, StyleAttr } from '../../types';\nimport { NodeDepth, NodeSort } from './layout';\n\n/**\n * @title node-link 数据类型的结构\n */\nexport type NodeLinkData = {\n  /**\n   * @title 节点数据\n   */\n  readonly nodes: {\n    /**\n     * @title id\n     * @description 唯一即可，一般可以直接等于 name\n     */\n    readonly id: string;\n    /**\n     * @title 节点的名称\n     * @description 用于 UI 上的现实\n     */\n    readonly name: string;\n    /**\n     * @title 节点的值\n     * @description 不传则节点大小有来源求和决定\n     */\n    readonly fixedValue?: number;\n  }[];\n\n  readonly links: {\n    /**\n     * @title 来源节点\n     * @description 在 nodes 中的 index\n     */\n    readonly source: number;\n    /**\n     * @title 目标节点\n     * @description  在 nodes 中的 index\n     */\n    readonly target: number;\n    /**\n     * @title 边的值\n     */\n    readonly value: number;\n  }[];\n};\n\n/**\n * @title 配置类型定义\n */\nexport interface SankeyOptions extends Omit<Options, 'data' | 'xField' | 'yField' | 'xAxis' | 'yAxis'> {\n  /**\n   * @title 数据集的类型\n   * @description 数据集的类型 'node-link' | 'detail'\n   * @default \"detail\"\n   */\n  readonly dataType?: 'node-link' | 'detail';\n  /**\n   * @title 来源字段\n   * @description dataType = 'node-link' 的时候，不用传\n   */\n  readonly sourceField?: string;\n  /**\n   * @title 去向字段\n   * @description dataType = 'node-link' 的时候，不用传\n   */\n  readonly targetField?: string;\n  /**\n   * @title 权重字段\n   * @description dataType = 'node-link' 的时候，不用传\n   */\n  readonly weightField?: string;\n  /**\n   * @title 附加的元字段\n   */\n  readonly rawFields?: string[];\n  /**\n   * @title 数据\n   */\n  readonly data: Data | NodeLinkData;\n  /**\n   * @title 节点宽度\n   * @description 参考画布的宽度\n   * @default 0.008\n   */\n  readonly nodeWidthRatio?: number;\n  /**\n   * @title 节点宽度的像素设置\n   * @description 优先级高于 nodeWidthRatio\n   */\n  readonly nodeWidth?: number;\n  /**\n   * @title 节点之间的间距比例\n   * @description 参考画布高度\n   * @default 0.03\n   */\n  readonly nodePaddingRatio?: number;\n  /**\n   * @title 节点间距的像素设置\n   * @description 优先级高于 nodePaddingRatio\n   */\n  readonly nodePadding?: number;\n  /**\n   * @title 节点对齐的方式\n   * @description 节点对齐的方式 'left' | 'right' | 'center' | 'justify'\n   * @default \"justify\"\n   */\n  readonly nodeAlign?: 'left' | 'right' | 'center' | 'justify';\n  /**\n   * @title 节点排序方式\n   */\n  readonly nodeSort?: NodeSort;\n  /**\n   * @title 节点排放分层的顺序\n   * @description 从 0 开始，并且返回值需要保证所有的层级都有节点\n   */\n  readonly nodeDepth?: NodeDepth;\n  /**\n   * @title 节点样式\n   */\n  readonly nodeStyle?: StyleAttr;\n  /**\n   * @title 节点状态样式\n   */\n  readonly nodeState?: State;\n  /**\n   * @title 边样式\n   */\n  readonly edgeStyle?: StyleAttr;\n  /**\n   * @title 边状态样式\n   */\n  readonly edgeState?: State;\n  /**\n   * @title 节点位置是否可以拖拽\n   * @default false\n   */\n  readonly nodeDraggable?: boolean;\n  /**\n   * @title 边交互\n   */\n  readonly edgeInteractions?: Options['interactions'];\n  /**\n   * @title 节点交互\n   */\n  readonly nodeInteractions?: Options['interactions'];\n}\n"]}