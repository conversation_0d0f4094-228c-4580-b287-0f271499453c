{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/plots/sankey/sankey/index.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;GAOG;AACH,mCAA4C;AAEnC,uFAFU,eAAM,OAEV;AADf,iCAAuD;AAA9C,+FAAA,MAAM,OAAA;AAAE,gGAAA,OAAO,OAAA;AAAE,6FAAA,IAAI,OAAA;AAAE,8FAAA,KAAK,OAAA", "sourcesContent": ["/**\n * 桑基图布局代码，Fork from https://github.com/d3/d3-sankey/tree/master/src\n * 主要修改：\n * 1. 删除 d3-array 依赖\n * 2. 修改一些 set map 的遍历\n * 3. 数组创建出 [empty] 导致出错\n * 4. 通过 align 方法实现 depth 自定义\n */\nimport { Sankey as sankey } from './sankey';\nexport { center, justify, left, right } from './align';\nexport { sankey };\n"]}