{"version": 3, "file": "sankey.js", "sourceRoot": "", "sources": ["../../../../src/plots/sankey/sankey/sankey.ts"], "names": [], "mappings": ";;;AAAA,iCAAkC;AAClC,mCAAmE;AAEnE,SAAS,sBAAsB,CAAC,CAAC,EAAE,CAAC;IAClC,OAAO,gBAAgB,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AACnE,CAAC;AAED,SAAS,sBAAsB,CAAC,CAAC,EAAE,CAAC;IAClC,OAAO,gBAAgB,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AACnE,CAAC;AAED,SAAS,gBAAgB,CAAC,CAAC,EAAE,CAAC;IAC5B,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;AACrB,CAAC;AAED,SAAS,KAAK,CAAC,CAAC;IACd,OAAO,CAAC,CAAC,KAAK,CAAC;AACjB,CAAC;AAED,SAAS,SAAS,CAAC,CAAC;IAClB,OAAO,CAAC,CAAC,KAAK,CAAC;AACjB,CAAC;AAED,SAAS,YAAY,CAAC,KAAK;IACzB,OAAO,KAAK,CAAC,KAAK,CAAC;AACrB,CAAC;AAED,SAAS,YAAY,CAAC,KAAK;IACzB,OAAO,KAAK,CAAC,KAAK,CAAC;AACrB,CAAC;AAED,SAAS,IAAI,CAAC,QAAQ,EAAE,EAAE;IACxB,IAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAC,IAAI;QAAE,MAAM,IAAI,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC;IAC7C,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,mBAAmB,CAAC,EAAS;QAAP,KAAK,WAAA;IAClC,KAAmB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;QAArB,IAAM,IAAI,cAAA;QACb,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACjB,IAAI,EAAE,GAAG,EAAE,CAAC;QACZ,KAAmB,UAAgB,EAAhB,KAAA,IAAI,CAAC,WAAW,EAAhB,cAAgB,EAAhB,IAAgB,EAAE;YAAhC,IAAM,IAAI,SAAA;YACb,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YAC9B,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC;SAClB;QACD,KAAmB,UAAgB,EAAhB,KAAA,IAAI,CAAC,WAAW,EAAhB,cAAgB,EAAhB,IAAgB,EAAE;YAAhC,IAAM,IAAI,SAAA;YACb,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YAC9B,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC;SAClB;KACF;AACH,CAAC;AAED,SAAgB,MAAM;IACpB,IAAI,EAAE,GAAG,CAAC,EACR,EAAE,GAAG,CAAC,EACN,EAAE,GAAG,CAAC,EACN,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS;IACnB,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,YAAY;IACzB,IAAI,EAAE,GAAG,CAAC,EACR,EAAE,CAAC,CAAC,cAAc;IACpB,IAAI,EAAE,GAAG,SAAS,CAAC;IACnB,IAAI,KAAK,GAAG,eAAO,CAAC;IACpB,IAAI,KAAK,CAAC;IACV,IAAI,IAAI,CAAC;IACT,IAAI,QAAQ,CAAC;IACb,IAAI,KAAK,GAAG,YAAY,CAAC;IACzB,IAAI,KAAK,GAAG,YAAY,CAAC;IACzB,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,SAAS,MAAM,CAAC,GAAG;QACjB,IAAM,KAAK,GAAG;YACZ,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC;YACjB,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC;SAClB,CAAC;QACF,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACxB,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACzB,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACzB,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC1B,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAC3B,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAC3B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,CAAC,MAAM,GAAG,UAAU,KAAK;QAC7B,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAC3B,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAQ,EAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5F,CAAC,CAAC;IAEF,MAAM,CAAC,SAAS,GAAG,UAAU,CAAC;QAC5B,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAQ,EAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAClG,CAAC,CAAC;IAEF,MAAM,CAAC,SAAS,GAAG,UAAU,CAAC;QAC5B,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACxF,CAAC,CAAC;IAEF,MAAM,CAAC,QAAQ,GAAG,UAAU,CAAC;QAC3B,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACxD,CAAC,CAAC;IAEF,MAAM,CAAC,SAAS,GAAG,UAAU,CAAC;QAC5B,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrD,CAAC,CAAC;IAEF,MAAM,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1D,CAAC,CAAC;IAEF,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC;QACxB,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAQ,EAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAClG,CAAC,CAAC;IAEF,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC;QACxB,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAQ,EAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAClG,CAAC,CAAC;IAEF,MAAM,CAAC,QAAQ,GAAG,UAAU,CAAC;QAC3B,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAChE,CAAC,CAAC;IAEF,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC;QACvB,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IACrG,CAAC,CAAC;IAEF,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,OAAO,SAAS,CAAC,MAAM;YACrB,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;YAC9E,CAAC,CAAC;gBACE,CAAC,EAAE,EAAE,EAAE,CAAC;gBACR,CAAC,EAAE,EAAE,EAAE,CAAC;aACT,CAAC;IACR,CAAC,CAAC;IAEF,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;IACrE,CAAC,CAAC;IAEF,SAAS,gBAAgB,CAAC,EAAgB;YAAd,KAAK,WAAA,EAAE,KAAK,WAAA;QACtC,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,GAAG;YACtB,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;YACjB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,IAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAV,CAAU,CAAC,CAAC,CAAC;QAEvD,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,GAAG;YACtB,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;YACX,IAAA,MAAM,GAAa,IAAI,OAAjB,EAAE,MAAM,GAAK,IAAI,OAAT,CAAU;YAC9B,IAAI,OAAO,MAAM,KAAK,QAAQ;gBAAE,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC9E,IAAI,OAAO,MAAM,KAAK,QAAQ;gBAAE,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC9E,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,KAA2C,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;gBAAvC,IAAA,gBAA4B,EAA1B,WAAW,iBAAA,EAAE,WAAW,iBAAA;gBACnC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC3B,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC5B;SACF;IACH,CAAC;IAED,SAAS,iBAAiB,CAAC,EAAS;YAAP,KAAK,WAAA;QAChC,KAAmB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;YAArB,IAAM,IAAI,cAAA;YACb,IAAI,CAAC,KAAK;gBACR,IAAI,CAAC,UAAU,KAAK,SAAS;oBAC3B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAA,cAAK,EAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE,IAAA,cAAK,EAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;oBAC1E,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;SACvB;IACH,CAAC;IAED,SAAS,iBAAiB,CAAC,EAAS;YAAP,KAAK,WAAA;QAChC,IAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QACvB,IAAI,OAAO,GAAG,IAAI,GAAG,CAAM,KAAK,CAAC,CAAC;QAClC,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,OAAO,CAAC,IAAI,EAAE;YACnB,OAAO,CAAC,OAAO,CAAC,UAAC,IAAI;gBACnB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;gBACf,KAAyB,UAAgB,EAAhB,KAAA,IAAI,CAAC,WAAW,EAAhB,cAAgB,EAAhB,IAAgB,EAAE;oBAA9B,IAAA,MAAM,gBAAA;oBACjB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBAClB;YACH,CAAC,CAAC,CAAC;YACH,IAAI,EAAE,CAAC,GAAG,CAAC;gBAAE,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YAC9C,OAAO,GAAG,IAAI,CAAC;YACf,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;SAClB;QAED,2BAA2B;QAC3B,IAAI,KAAK,EAAE;YACT,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAA,mBAAU,EAAC,KAAK,EAAE,UAAC,CAAM,IAAK,OAAA,CAAC,CAAC,KAAK,EAAP,CAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAEzE,IAAI,IAAI,SAAA,CAAC;YACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAChB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;aAC/C;SACF;IACH,CAAC;IAED,SAAS,kBAAkB,CAAC,EAAS;YAAP,KAAK,WAAA;QACjC,IAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QACvB,IAAI,OAAO,GAAG,IAAI,GAAG,CAAM,KAAK,CAAC,CAAC;QAClC,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,OAAO,CAAC,IAAI,EAAE;YACnB,OAAO,CAAC,OAAO,CAAC,UAAC,IAAI;gBACnB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBAChB,KAAyB,UAAgB,EAAhB,KAAA,IAAI,CAAC,WAAW,EAAhB,cAAgB,EAAhB,IAAgB,EAAE;oBAA9B,IAAA,MAAM,gBAAA;oBACjB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBAClB;YACH,CAAC,CAAC,CAAC;YACH,IAAI,EAAE,CAAC,GAAG,CAAC;gBAAE,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YAC9C,OAAO,GAAG,IAAI,CAAC;YACf,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;SAClB;IACH,CAAC;IAED,SAAS,iBAAiB,CAAC,EAAS;YAAP,KAAK,WAAA;QAChC,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAA,mBAAU,EAAC,KAAK,EAAE,UAAC,CAAM,IAAK,OAAA,CAAC,CAAC,KAAK,EAAP,CAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAClE,IAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACpC,IAAM,OAAO,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,cAAM,OAAA,EAAE,EAAF,CAAE,CAAC,CAAC;QACnD,KAAmB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;YAArB,IAAM,IAAI,cAAA;YACb,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9E,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YACf,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;YACvB,IAAI,OAAO,CAAC,CAAC,CAAC;gBAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;gBACjC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC1B;QACD,IAAI,IAAI;YACN,KAAqB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;gBAAzB,IAAM,MAAM,gBAAA;gBACf,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACnB;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,SAAS,sBAAsB,CAAC,OAAO;QACrC,IAAM,EAAE,GAAG,IAAA,mBAAU,EAAC,OAAO,EAAE,UAAC,CAAQ,IAAK,OAAA,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,IAAA,cAAK,EAAC,CAAC,EAAE,KAAK,CAAC,EAAjD,CAAiD,CAAkB,CAAC;QACjH,KAAoB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;YAAxB,IAAM,OAAK,gBAAA;YACd,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,KAAmB,UAAK,EAAL,UAAA,OAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;gBAArB,IAAM,IAAI,cAAA;gBACb,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;gBACZ,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC9B,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;gBACjB,KAAmB,UAAgB,EAAhB,KAAA,IAAI,CAAC,WAAW,EAAhB,cAAgB,EAAhB,IAAgB,EAAE;oBAAhC,IAAM,IAAI,SAAA;oBACb,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;iBAC9B;aACF;YACD,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACrC,IAAM,IAAI,GAAG,OAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACvB,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aACxB;YACD,YAAY,CAAC,OAAK,CAAC,CAAC;SACrB;IACH,CAAC;IAED,SAAS,mBAAmB,CAAC,KAAK;QAChC,IAAM,OAAO,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACzC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAE,IAAA,mBAAU,EAAC,OAAO,EAAE,UAAC,CAAQ,IAAK,OAAA,CAAC,CAAC,MAAM,EAAR,CAAQ,CAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;QACpG,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,EAAE,CAAC,EAAE;YACnC,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAChC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;YACvD,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACvC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACxC;IACH,CAAC;IAED,6DAA6D;IAC7D,SAAS,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI;QAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YAC9C,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,KAAqB,UAAM,EAAN,iBAAM,EAAN,oBAAM,EAAN,IAAM,EAAE;gBAAxB,IAAM,MAAM,eAAA;gBACf,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,KAAgC,UAAkB,EAAlB,KAAA,MAAM,CAAC,WAAW,EAAlB,cAAkB,EAAlB,IAAkB,EAAE;oBAAzC,IAAA,WAAiB,EAAf,MAAM,YAAA,EAAE,OAAK,WAAA;oBACxB,IAAM,CAAC,GAAG,OAAK,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChD,CAAC,IAAI,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;oBACnC,CAAC,IAAI,CAAC,CAAC;iBACR;gBACD,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAAE,SAAS;gBACvB,IAAM,IAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;gBACvC,MAAM,CAAC,EAAE,IAAI,IAAE,CAAC;gBAChB,MAAM,CAAC,EAAE,IAAI,IAAE,CAAC;gBAChB,gBAAgB,CAAC,MAAM,CAAC,CAAC;aAC1B;YACD,IAAI,IAAI,KAAK,SAAS;gBAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACtD,IAAI,MAAM,CAAC,MAAM;gBAAE,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACpD;IACH,CAAC;IAED,6DAA6D;IAC7D,SAAS,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI;QAC5C,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;YACnD,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,KAAqB,UAAM,EAAN,iBAAM,EAAN,oBAAM,EAAN,IAAM,EAAE;gBAAxB,IAAM,MAAM,eAAA;gBACf,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,KAAgC,UAAkB,EAAlB,KAAA,MAAM,CAAC,WAAW,EAAlB,cAAkB,EAAlB,IAAkB,EAAE;oBAAzC,IAAA,WAAiB,EAAf,MAAM,YAAA,EAAE,OAAK,WAAA;oBACxB,IAAM,CAAC,GAAG,OAAK,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChD,CAAC,IAAI,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;oBACnC,CAAC,IAAI,CAAC,CAAC;iBACR;gBACD,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAAE,SAAS;gBACvB,IAAM,IAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;gBACvC,MAAM,CAAC,EAAE,IAAI,IAAE,CAAC;gBAChB,MAAM,CAAC,EAAE,IAAI,IAAE,CAAC;gBAChB,gBAAgB,CAAC,MAAM,CAAC,CAAC;aAC1B;YACD,IAAI,IAAI,KAAK,SAAS;gBAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACtD,IAAI,MAAM,CAAC,MAAM;gBAAE,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACpD;IACH,CAAC;IAED,SAAS,iBAAiB,CAAC,KAAK,EAAE,KAAK;QACrC,IAAM,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC;QAC5B,IAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACzB,4BAA4B,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QACnE,4BAA4B,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QACnE,4BAA4B,CAAC,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QACjE,4BAA4B,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,mCAAmC;IACnC,SAAS,4BAA4B,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;QACtD,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC5B,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAM,IAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;YACjC,IAAI,IAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,CAAC,EAAE,IAAI,IAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,IAAE,CAAC,CAAC;YAChD,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;SAClB;IACH,CAAC;IAED,iCAAiC;IACjC,SAAS,4BAA4B,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;QACtD,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;YAClB,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAM,IAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YACjC,IAAI,IAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,CAAC,EAAE,IAAI,IAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,IAAE,CAAC,CAAC;YAChD,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;SAClB;IACH,CAAC;IAED,SAAS,gBAAgB,CAAC,EAA4B;YAA1B,WAAW,iBAAA,EAAE,WAAW,iBAAA;QAClD,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,KAEK,UAAW,EAAX,2BAAW,EAAX,yBAAW,EAAX,IAAW,EAAE;gBADN,IAAA,aAAW,uCAAA;gBAErB,aAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;aAC1C;YACD,KAEK,UAAW,EAAX,2BAAW,EAAX,yBAAW,EAAX,IAAW,EAAE;gBADN,IAAA,aAAW,uCAAA;gBAErB,aAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;aAC1C;SACF;IACH,CAAC;IAED,SAAS,YAAY,CAAC,KAAK;QACzB,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,KAA2C,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE;gBAAvC,IAAA,gBAA4B,EAA1B,WAAW,iBAAA,EAAE,WAAW,iBAAA;gBACnC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACzC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;aAC1C;SACF;IACH,CAAC;IAED,gFAAgF;IAChF,SAAS,SAAS,CAAC,MAAM,EAAE,MAAM;QAC/B,IAAI,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QAC/D,KAAsC,UAAkB,EAAlB,KAAA,MAAM,CAAC,WAAW,EAAlB,cAAkB,EAAlB,IAAkB,EAAE;YAA/C,IAAA,WAAuB,EAAb,IAAI,YAAA,EAAE,KAAK,WAAA;YAC9B,IAAI,IAAI,KAAK,MAAM;gBAAE,MAAM;YAC3B,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;SACjB;QACD,KAAsC,UAAkB,EAAlB,KAAA,MAAM,CAAC,WAAW,EAAlB,cAAkB,EAAlB,IAAkB,EAAE;YAA/C,IAAA,WAAuB,EAAb,IAAI,YAAA,EAAE,KAAK,WAAA;YAC9B,IAAI,IAAI,KAAK,MAAM;gBAAE,MAAM;YAC3B,CAAC,IAAI,KAAK,CAAC;SACZ;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAED,gFAAgF;IAChF,SAAS,SAAS,CAAC,MAAM,EAAE,MAAM;QAC/B,IAAI,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QAC/D,KAAsC,UAAkB,EAAlB,KAAA,MAAM,CAAC,WAAW,EAAlB,cAAkB,EAAlB,IAAkB,EAAE;YAA/C,IAAA,WAAuB,EAAb,IAAI,YAAA,EAAE,KAAK,WAAA;YAC9B,IAAI,IAAI,KAAK,MAAM;gBAAE,MAAM;YAC3B,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;SACjB;QACD,KAAsC,UAAkB,EAAlB,KAAA,MAAM,CAAC,WAAW,EAAlB,cAAkB,EAAlB,IAAkB,EAAE;YAA/C,IAAA,WAAuB,EAAb,IAAI,YAAA,EAAE,KAAK,WAAA;YAC9B,IAAI,IAAI,KAAK,MAAM;gBAAE,MAAM;YAC3B,CAAC,IAAI,KAAK,CAAC;SACZ;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAhWD,wBAgWC", "sourcesContent": ["import { justify } from './align';\nimport { constant, maxValueBy, minValueBy, sumBy } from './helper';\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error('missing: ' + id);\n  return node;\n}\n\nfunction computeLinkBreadths({ nodes }) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport function Sankey() {\n  let x0 = 0,\n    y0 = 0,\n    x1 = 1,\n    y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8,\n    py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let depth;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey(arg) {\n    const graph = {\n      nodes: nodes(arg),\n      links: links(arg),\n    };\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function (graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function (_) {\n    return arguments.length ? ((id = typeof _ === 'function' ? _ : constant(_)), sankey) : id;\n  };\n\n  sankey.nodeAlign = function (_) {\n    return arguments.length ? ((align = typeof _ === 'function' ? _ : constant(_)), sankey) : align;\n  };\n\n  sankey.nodeDepth = function (_) {\n    return arguments.length ? ((depth = typeof _ === 'function' ? _ : _), sankey) : depth;\n  };\n\n  sankey.nodeSort = function (_) {\n    return arguments.length ? ((sort = _), sankey) : sort;\n  };\n\n  sankey.nodeWidth = function (_) {\n    return arguments.length ? ((dx = +_), sankey) : dx;\n  };\n\n  sankey.nodePadding = function (_) {\n    return arguments.length ? ((dy = py = +_), sankey) : dy;\n  };\n\n  sankey.nodes = function (_) {\n    return arguments.length ? ((nodes = typeof _ === 'function' ? _ : constant(_)), sankey) : nodes;\n  };\n\n  sankey.links = function (_) {\n    return arguments.length ? ((links = typeof _ === 'function' ? _ : constant(_)), sankey) : links;\n  };\n\n  sankey.linkSort = function (_) {\n    return arguments.length ? ((linkSort = _), sankey) : linkSort;\n  };\n\n  sankey.size = function (_) {\n    return arguments.length ? ((x0 = y0 = 0), (x1 = +_[0]), (y1 = +_[1]), sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function (_) {\n    return arguments.length\n      ? ((x0 = +_[0][0]), (x1 = +_[1][0]), (y0 = +_[0][1]), (y1 = +_[1][1]), sankey)\n      : [\n          [x0, y0],\n          [x1, y1],\n        ];\n  };\n\n  sankey.iterations = function (_) {\n    return arguments.length ? ((iterations = +_), sankey) : iterations;\n  };\n\n  function computeNodeLinks({ nodes, links }) {\n    nodes.forEach((node, idx) => {\n      node.index = idx;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    });\n\n    const nodeById = new Map(nodes.map((d) => [id(d), d]));\n\n    links.forEach((link, idx) => {\n      link.index = idx;\n      let { source, target } = link;\n      if (typeof source !== 'object') source = link.source = find(nodeById, source);\n      if (typeof target !== 'object') target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    });\n\n    if (linkSort != null) {\n      for (const { sourceLinks, targetLinks } of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({ nodes }) {\n    for (const node of nodes) {\n      node.value =\n        node.fixedValue === undefined\n          ? Math.max(sumBy(node.sourceLinks, value), sumBy(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({ nodes }) {\n    const n = nodes.length;\n    let current = new Set<any>(nodes);\n    let next = new Set();\n    let x = 0;\n    while (current.size) {\n      current.forEach((node) => {\n        node.depth = x;\n        for (const { target } of node.sourceLinks) {\n          next.add(target);\n        }\n      });\n      if (++x > n) throw new Error('circular link');\n      current = next;\n      next = new Set();\n    }\n\n    // 如果配置了 depth，则设置自定义 depth\n    if (depth) {\n      const maxDepth = Math.max(maxValueBy(nodes, (d: any) => d.depth) + 1, 0);\n\n      let node;\n      for (let i = 0; i < nodes.length; i++) {\n        node = nodes[i];\n        node.depth = depth.call(null, node, maxDepth);\n      }\n    }\n  }\n\n  function computeNodeHeights({ nodes }) {\n    const n = nodes.length;\n    let current = new Set<any>(nodes);\n    let next = new Set();\n    let x = 0;\n    while (current.size) {\n      current.forEach((node) => {\n        node.height = x;\n        for (const { source } of node.targetLinks) {\n          next.add(source);\n        }\n      });\n      if (++x > n) throw new Error('circular link');\n      current = next;\n      next = new Set();\n    }\n  }\n\n  function computeNodeLayers({ nodes }) {\n    const x = Math.max(maxValueBy(nodes, (d: any) => d.depth) + 1, 0);\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x).fill(0).map(() => []);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort)\n      for (const column of columns) {\n        column.sort(sort);\n      }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = minValueBy(columns, (c: any[]) => (y1 - y0 - (c.length - 1) * py) / sumBy(c, value)) as any as number;\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / ((maxValueBy(columns, (c: any[]) => c.length) as any as number) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const { source, value } of target.targetLinks) {\n          const v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        const dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      if (column.length) resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const { target, value } of source.sourceLinks) {\n          const v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        const dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      if (column.length) resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) (node.y0 += dy), (node.y1 += dy);\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) (node.y0 -= dy), (node.y1 -= dy);\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({ sourceLinks, targetLinks }) {\n    if (linkSort === undefined) {\n      for (const {\n        source: { sourceLinks },\n      } of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {\n        target: { targetLinks },\n      } of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const { sourceLinks, targetLinks } of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - ((source.sourceLinks.length - 1) * py) / 2;\n    for (const { target: node, width } of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const { source: node, width } of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - ((target.targetLinks.length - 1) * py) / 2;\n    for (const { source: node, width } of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const { target: node, width } of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n"]}