{"version": 3, "file": "helper.js", "sourceRoot": "", "sources": ["../../../../src/plots/sankey/sankey/helper.ts"], "names": [], "mappings": ";;;AAAA,SAAgB,QAAQ,CAAC,CAAM;IAC7B,OAAO;QACL,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;AACJ,CAAC;AAJD,4BAIC;AAED,SAAgB,KAAK,CAAC,GAAG,EAAE,IAAI;IAC7B,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACnB;IAED,OAAO,CAAC,CAAC;AACX,CAAC;AAPD,sBAOC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,GAAG,EAAE,IAAI;IAClC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC/B;IAED,OAAO,CAAC,CAAC;AACX,CAAC;AAPD,gCAOC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,GAAG,EAAE,IAAI;IAClC,IAAI,CAAC,GAAG,QAAQ,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC/B;IAED,OAAO,CAAC,CAAC;AACX,CAAC;AAPD,gCAOC", "sourcesContent": ["export function constant(x: any) {\n  return function () {\n    return x;\n  };\n}\n\nexport function sumBy(arr, func) {\n  let r = 0;\n  for (let i = 0; i < arr.length; i++) {\n    r += func(arr[i]);\n  }\n\n  return r;\n}\n\n/**\n * 计算最大值\n * @param arr\n * @param func\n */\nexport function maxValueBy(arr, func): number {\n  let r = -Infinity;\n  for (let i = 0; i < arr.length; i++) {\n    r = Math.max(func(arr[i]), r);\n  }\n\n  return r;\n}\n\n/**\n * 计算最小值\n * @param arr\n * @param func\n */\nexport function minValueBy(arr, func): number {\n  let r = Infinity;\n  for (let i = 0; i < arr.length; i++) {\n    r = Math.min(func(arr[i]), r);\n  }\n\n  return r;\n}\n"]}