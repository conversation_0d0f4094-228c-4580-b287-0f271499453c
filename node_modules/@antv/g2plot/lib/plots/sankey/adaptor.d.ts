import { Params } from '../../core/adaptor';
import { SankeyOptions } from './types';
/**
 * 动画
 * @param params
 */
export declare function animation(params: Params<SankeyOptions>): Params<SankeyOptions>;
/**
 * 节点拖动
 * @param params
 */
export declare function nodeDraggable(params: Params<SankeyOptions>): Params<SankeyOptions>;
/**
 * 图适配器
 * @param chart
 * @param options
 */
export declare function adaptor(params: Params<SankeyOptions>): Params<SankeyOptions>;
