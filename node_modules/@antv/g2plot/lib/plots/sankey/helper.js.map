{"version": 3, "file": "helper.js", "sourceRoot": "", "sources": ["../../../src/plots/sankey/helper.ts"], "names": [], "mappings": ";;;;AACA,qCAAiD;AACjD,yCAA+D;AAC/D,mCAAwC;AACxC,mCAA+D;AAG/D;;;;GAIG;AACH,SAAS,UAAU,CAAC,QAAgB;IAClC,OAAO,QAAQ,KAAK,WAAW,CAAC;AAClC,CAAC;AAED,SAAgB,iBAAiB,CAAC,SAAiB,EAAE,cAAsB,EAAE,KAAa;IACxF,OAAO,IAAA,oBAAY,EAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC;AACtE,CAAC;AAFD,8CAEC;AAED,SAAgB,mBAAmB,CAAC,WAAmB,EAAE,gBAAwB,EAAE,MAAc;IAC/F,OAAO,IAAA,oBAAY,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC;AAC7E,CAAC;AAFD,kDAEC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAAC,OAAsB,EAAE,KAAa,EAAE,MAAc;IAEtF,IAAA,QAAQ,GAaN,OAAO,SAbD,EACR,IAAI,GAYF,OAAO,KAZL,EACJ,WAAW,GAWT,OAAO,YAXE,EACX,WAAW,GAUT,OAAO,YAVE,EACX,WAAW,GAST,OAAO,YATE,EACX,SAAS,GAQP,OAAO,UARA,EACT,QAAQ,GAON,OAAO,SAPD,EACR,WAAW,GAMT,OAAO,YANE,EACX,gBAAgB,GAKd,OAAO,iBALO,EAChB,SAAS,GAIP,OAAO,UAJA,EACT,cAAc,GAGZ,OAAO,eAHK,EACd,SAAS,GAEP,OAAO,UAFA,EACT,KACE,OAAO,UADK,EAAd,SAAS,mBAAG,EAAE,KAAA,CACJ;IAEZ,IAAI,qBAA8B,CAAC;IAEnC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACzB,qBAAqB,GAAG,IAAA,kCAA2B,EACjD,IAAA,qBAAY,EAAC,IAAY,EAAE,WAAW,EAAE,WAAW,CAAC,EACpD,WAAW,EACX,WAAW,EACX,WAAW,EACX,SAAS,CACV,CAAC;KACH;SAAM;QACL,qBAAqB,GAAG,IAAI,CAAC;KAC9B;IAED,kBAAkB;IACZ,IAAA,KAAmB,IAAA,qBAAY,EACnC;QACE,SAAS,WAAA;QACT,WAAW,EAAE,mBAAmB,CAAC,WAAW,EAAE,gBAAgB,EAAE,MAAM,CAAC;QACvE,SAAS,EAAE,iBAAiB,CAAC,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC;QAC9D,QAAQ,UAAA;QACR,SAAS,WAAA;KACV,EACD,qBAA8C,CAC/C,EATO,KAAK,WAAA,EAAE,KAAK,WASnB,CAAC;IAEF,YAAY;IACZ,OAAO;QACL,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;YACpB,6CACK,IAAA,YAAI,EAAC,IAAI,yBAAG,GAAG,EAAE,GAAG,EAAE,MAAM,GAAK,SAAS,QAAE,KAC/C,MAAM,EAAE,IAAI,IACZ;QACJ,CAAC,CAAC;QACF,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;YACpB,2CACE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EACxB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EACxB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IACvC,IAAA,YAAI,EAAC,IAAI,yBAAG,GAAG,EAAE,GAAG,EAAE,OAAO,GAAK,SAAS,QAAE,KAChD,MAAM,EAAE,KAAK,IACb;QACJ,CAAC,CAAC;KACH,CAAC;AACJ,CAAC;AA7DD,oDA6DC", "sourcesContent": ["import { Data } from '../../types';\nimport { isRealNumber, pick } from '../../utils';\nimport { transformDataToNodeLinkData } from '../../utils/data';\nimport { cutoffCircle } from './circle';\nimport { sankeyLayout, SankeyLayoutInputData } from './layout';\nimport { SankeyOptions } from './types';\n\n/**\n * 是否是 node-link 类型的数据结构\n * @param dataTyp\n * @returns\n */\nfunction isNodeLink(dataType: string) {\n  return dataType === 'node-link';\n}\n\nexport function getNodeWidthRatio(nodeWidth: number, nodeWidthRatio: number, width: number) {\n  return isRealNumber(nodeWidth) ? nodeWidth / width : nodeWidthRatio;\n}\n\nexport function getNodePaddingRatio(nodePadding: number, nodePaddingRatio: number, height: number) {\n  return isRealNumber(nodePadding) ? nodePadding / height : nodePaddingRatio;\n}\n\n/**\n * 将桑基图配置经过 layout，生成最终的 view 数据\n * @param options\n * @param width\n * @param height\n */\nexport function transformToViewsData(options: SankeyOptions, width: number, height: number) {\n  const {\n    dataType,\n    data,\n    sourceField,\n    targetField,\n    weightField,\n    nodeAlign,\n    nodeSort,\n    nodePadding,\n    nodePaddingRatio,\n    nodeWidth,\n    nodeWidthRatio,\n    nodeDepth,\n    rawFields = [],\n  } = options;\n\n  let sankeyLayoutInputData: unknown;\n\n  if (!isNodeLink(dataType)) {\n    sankeyLayoutInputData = transformDataToNodeLinkData(\n      cutoffCircle(data as Data, sourceField, targetField),\n      sourceField,\n      targetField,\n      weightField,\n      rawFields\n    );\n  } else {\n    sankeyLayoutInputData = data;\n  }\n\n  // 3. layout 之后的数据\n  const { nodes, links } = sankeyLayout(\n    {\n      nodeAlign,\n      nodePadding: getNodePaddingRatio(nodePadding, nodePaddingRatio, height),\n      nodeWidth: getNodeWidthRatio(nodeWidth, nodeWidthRatio, width),\n      nodeSort,\n      nodeDepth,\n    },\n    sankeyLayoutInputData as SankeyLayoutInputData\n  );\n\n  // 4. 生成绘图数据\n  return {\n    nodes: nodes.map((node) => {\n      return {\n        ...pick(node, ['x', 'y', 'name', ...rawFields]),\n        isNode: true,\n      };\n    }),\n    edges: links.map((link) => {\n      return {\n        source: link.source.name,\n        target: link.target.name,\n        name: link.source.name || link.target.name,\n        ...pick(link, ['x', 'y', 'value', ...rawFields]),\n        isNode: false,\n      };\n    }),\n  };\n}\n"]}