{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/sankey/index.ts"], "names": [], "mappings": ";;;;AAAA,mCAAiC;AAEjC,wCAAuC;AAEvC,qCAA2C;AAC3C,qCAAoC;AACpC,uCAA0D;AAC1D,mCAAgD;AAChD,UAAU;AACV,0BAAwB;AAKxB;;GAEG;AACH;IAA4B,kCAAmB;IAA/C;QAAA,qEAqGC;QApGC,WAAW;QACJ,UAAI,GAAW,QAAQ,CAAC;;IAmGjC,CAAC;IAjGQ,wBAAiB,GAAxB;QACE,OAAO;YACL,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,IAAI;YACrB,SAAS,EAAE;gBACT,OAAO,EAAE,CAAC;gBACV,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC;aACb;YACD,SAAS,EAAE;gBACT,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,CAAC;aACb;YACD,SAAS,EAAE;gBACT,MAAM,EAAE;oBACN,KAAK,EAAE;wBACL,OAAO,EAAE,GAAG;wBACZ,SAAS,EAAE,CAAC;qBACb;iBACF;aACF;YACD,KAAK,EAAE;gBACL,SAAS,EAAE,UAAC,EAAQ;wBAAN,IAAI,UAAA;oBAAO,OAAA,IAAI;gBAAJ,CAAI;gBAC7B,QAAQ,EAAE,UAAC,CAAW;oBACpB,IAAM,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY;oBACvC,OAAO;wBACL,KAAK,EAAE;4BACL,IAAI,EAAE,SAAS;4BACf,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO;yBACpC;wBACD,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;qBACzB,CAAC;gBACJ,CAAC;gBACD,MAAM,EAAE;oBACN;wBACE,IAAI,EAAE,cAAc;qBACrB;iBACF;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,KAAK;gBAChB,WAAW,EAAE,KAAK;gBAClB,MAAM,EAAE,KAAK;gBACb,sCAAsC;gBACtC,WAAW,EAAE,UAAC,KAAK;oBACjB,OAAO,CAAC,IAAA,UAAG,EAAC,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAC5C,CAAC;gBACD,SAAS,EAAE,UAAC,KAAY;oBACd,IAAA,MAAM,GAAoB,KAAK,OAAzB,EAAE,MAAM,GAAY,KAAK,OAAjB,EAAE,KAAK,GAAK,KAAK,MAAV,CAAW;oBACxC,OAAO;wBACL,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM;wBAC9B,KAAK,OAAA;qBACN,CAAC;gBACJ,CAAC;aACF;YACD,cAAc,EAAE,KAAK;YACrB,gBAAgB,EAAE,IAAI;YACtB,SAAS,EAAE;gBACT,MAAM,EAAE;oBACN,SAAS,EAAE,SAAS;iBACrB;gBACD,KAAK,EAAE;oBACL,SAAS,EAAE,SAAS;iBACrB;aACF;SACF,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,2BAAU,GAAjB,UAAkB,IAAU;QAC1B,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QAEtB,IAAA,KAAmB,IAAA,6BAAoB,EAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAxF,KAAK,WAAA,EAAE,KAAK,WAA4E,CAAC;QAEjG,IAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,IAAI,CAAC,KAAK,EAAE,wBAAa,CAAC,CAAC;QAC1D,IAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,IAAI,CAAC,KAAK,EAAE,wBAAa,CAAC,CAAC;QAE1D,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC5B,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACO,iCAAgB,GAA1B;QACE,OAAO,iBAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACO,kCAAiB,GAA3B;QACE,OAAO,MAAM,CAAC,iBAAiB,EAAE,CAAC;IACpC,CAAC;IACH,aAAC;AAAD,CAAC,AArGD,CAA4B,WAAI,GAqG/B;AArGY,wBAAM", "sourcesContent": ["import { get } from '@antv/util';\nimport { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { Data, Datum } from '../../types';\nimport { findViewById } from '../../utils';\nimport { adaptor } from './adaptor';\nimport { EDGES_VIEW_ID, NODES_VIEW_ID } from './constant';\nimport { transformToViewsData } from './helper';\n// 桑基图内置交互\nimport './interactions';\nimport { SankeyOptions } from './types';\n\nexport type { SankeyOptions };\n\n/**\n *  桑基图 Sankey\n */\nexport class Sankey extends Plot<SankeyOptions> {\n  /** 图表类型 */\n  public type: string = 'sankey';\n\n  static getDefaultOptions(): Partial<SankeyOptions> {\n    return {\n      appendPadding: 8,\n      syncViewPadding: true,\n      nodeStyle: {\n        opacity: 1,\n        fillOpacity: 1,\n        lineWidth: 1,\n      },\n      edgeStyle: {\n        opacity: 0.3,\n        lineWidth: 0,\n      },\n      edgeState: {\n        active: {\n          style: {\n            opacity: 0.8,\n            lineWidth: 0,\n          },\n        },\n      },\n      label: {\n        formatter: ({ name }) => name,\n        callback: (x: number[]) => {\n          const isLast = x[1] === 1; // 最后一列靠边的节点\n          return {\n            style: {\n              fill: '#545454',\n              textAlign: isLast ? 'end' : 'start',\n            },\n            offsetX: isLast ? -8 : 8,\n          };\n        },\n        layout: [\n          {\n            type: 'hide-overlap',\n          },\n        ],\n      },\n      tooltip: {\n        showTitle: false,\n        showMarkers: false,\n        shared: false,\n        // 内置：node 不显示 tooltip，edge 显示 tooltip\n        showContent: (items) => {\n          return !get(items, [0, 'data', 'isNode']);\n        },\n        formatter: (datum: Datum) => {\n          const { source, target, value } = datum;\n          return {\n            name: source + ' -> ' + target,\n            value,\n          };\n        },\n      },\n      nodeWidthRatio: 0.008,\n      nodePaddingRatio: 0.01,\n      animation: {\n        appear: {\n          animation: 'wave-in',\n        },\n        enter: {\n          animation: 'wave-in',\n        },\n      },\n    };\n  }\n\n  /**\n   * @override\n   * @param data\n   */\n  public changeData(data: Data) {\n    this.updateOption({ data });\n\n    const { nodes, edges } = transformToViewsData(this.options, this.chart.width, this.chart.height);\n\n    const nodesView = findViewById(this.chart, NODES_VIEW_ID);\n    const edgesView = findViewById(this.chart, EDGES_VIEW_ID);\n\n    nodesView.changeData(nodes);\n    edgesView.changeData(edges);\n  }\n\n  /**\n   * 获取适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<SankeyOptions> {\n    return adaptor;\n  }\n\n  /**\n   * 获取 条形图 默认配置\n   */\n  protected getDefaultOptions() {\n    return Sankey.getDefaultOptions();\n  }\n}\n"]}