{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/tiny-line/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { MappingOptions } from '../../adaptor/geometries/base';\nimport { PointGeometryOptions } from '../../adaptor/geometries/point';\nimport { Options, StyleAttr } from '../../types';\n\n/** mini 图类型定义需要 omit 很多的 G2 Options 配置 */\nexport interface TinyLineOptions extends Omit<Options, 'data' | 'legend' | 'label'> {\n  /**\n   * @title 具体的数据\n   */\n  readonly data: number[];\n  /**\n   * @title 是否平滑\n   * @default false\n   */\n  readonly smooth?: boolean;\n  /**\n   * @title 是否连接空数据\n   * @default false\n   */\n  readonly connectNulls?: boolean;\n  /**\n   * @title 折线图形样式\n   */\n  readonly lineStyle?: StyleAttr;\n  /**\n   * @title 折线点图形样式\n   */\n  readonly point?: MappingOptions & Pick<PointGeometryOptions, 'state'>;\n}\n"]}