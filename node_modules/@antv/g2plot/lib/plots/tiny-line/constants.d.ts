export declare const X_FIELD = "x";
export declare const Y_FIELD = "y";
export declare const DEFAULT_TOOLTIP_OPTIONS: {
    showTitle: boolean;
    shared: boolean;
    showMarkers: boolean;
    customContent: (x: string, data: any[]) => string;
    containerTpl: string;
    itemTpl: string;
    domStyles: {
        'g2-tooltip': {
            padding: string;
            fontSize: string;
        };
    };
    showCrosshairs: boolean;
    crosshairs: {
        type: "x";
    };
};
/**
 * 默认配置项
 */
export declare const DEFAULT_OPTIONS: {
    appendPadding: number;
    tooltip: {
        showTitle: boolean;
        shared: boolean;
        showMarkers: boolean;
        customContent: (x: string, data: any[]) => string;
        containerTpl: string;
        itemTpl: string;
        domStyles: {
            'g2-tooltip': {
                padding: string;
                fontSize: string;
            };
        };
        showCrosshairs: boolean;
        crosshairs: {
            type: "x";
        };
    };
    animation: {};
};
