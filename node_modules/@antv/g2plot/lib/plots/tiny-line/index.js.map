{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/tiny-line/index.ts"], "names": [], "mappings": ";;;;AACA,wCAAuC;AACvC,qCAA0C;AAC1C,yCAA8C;AAE9C,iCAAsC;AAItC;IAA8B,oCAAqB;IAAnD;QAAA,qEAiCC;QAxBC,WAAW;QACJ,UAAI,GAAW,WAAW,CAAC;;IAuBpC,CAAC;IAhCC;;;OAGG;IACI,0BAAiB,GAAxB;QACE,OAAO,2BAAe,CAAC;IACzB,CAAC;IAKD;;;OAGG;IACI,6BAAU,GAAjB,UAAkB,IAA6B;QAC7C,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;QACtB,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAC;QAChC,IAAA,cAAI,EAAC,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;QACzB,KAAK,CAAC,UAAU,CAAC,IAAA,mBAAW,EAAC,IAAI,CAAC,CAAC,CAAC;IACtC,CAAC;IAES,oCAAiB,GAA3B;QACE,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACO,mCAAgB,GAA1B;QACE,OAAO,iBAAO,CAAC;IACjB,CAAC;IACH,eAAC;AAAD,CAAC,AAjCD,CAA8B,WAAI,GAiCjC;AAjCY,4BAAQ", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor, meta } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constants';\nimport { TinyLineOptions } from './types';\nimport { getTinyData } from './utils';\n\nexport type { TinyLineOptions };\n\nexport class TinyLine extends Plot<TinyLineOptions> {\n  /**\n   * 获取默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<TinyLineOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'tiny-line';\n\n  /**\n   * @override\n   * @param data\n   */\n  public changeData(data: TinyLineOptions['data']) {\n    this.updateOption({ data });\n    const { chart, options } = this;\n    meta({ chart, options });\n    chart.changeData(getTinyData(data));\n  }\n\n  protected getDefaultOptions() {\n    return TinyLine.getDefaultOptions();\n  }\n\n  /**\n   * 获取 迷你折线图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<TinyLineOptions> {\n    return adaptor;\n  }\n}\n"]}