{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/tiny-line/utils.ts"], "names": [], "mappings": ";;;AAAA,mCAAiC;AAEjC,SAAgB,WAAW,CAAC,IAAc;IACxC,OAAO,IAAA,UAAG,EAAC,IAAI,IAAI,EAAE,EAAE,UAAC,CAAS,EAAE,CAAS,IAAK,OAAA,CAAC,EAAE,CAAC,EAAE,UAAG,CAAC,CAAE,EAAE,CAAC,GAAA,EAAE,CAAC,EAAlB,CAAkB,CAAC,CAAC;AACvE,CAAC;AAFD,kCAEC", "sourcesContent": ["import { map } from '@antv/util';\n\nexport function getTinyData(data: number[]) {\n  return map(data || [], (y: number, x: number) => ({ x: `${x}`, y }));\n}\n"]}