/**
 * 默认配置项
 */
export declare const DEFAULT_OPTIONS: {
    appendPadding: number;
    tooltip: {
        showTitle: boolean;
        shared: boolean;
        showMarkers: boolean;
        customContent: (x: string, data: any[]) => string;
        containerTpl: string;
        itemTpl: string;
        domStyles: {
            'g2-tooltip': {
                padding: string;
                fontSize: string;
            };
        };
        showCrosshairs: boolean;
        crosshairs: {
            type: "x";
        };
    };
    color: string;
    areaStyle: {
        fillOpacity: number;
    };
    line: {
        size: number;
        color: string;
    };
    animation: {};
};
