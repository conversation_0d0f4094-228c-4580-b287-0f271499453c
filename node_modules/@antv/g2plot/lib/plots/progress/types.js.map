{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/progress/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ColorAttr, Options, StyleAttr } from '../../types';\n\n/** mini 图类型定义需要 omit 很多的 G2 Options 配置 */\nexport interface ProgressOptions extends Omit<Options, 'data' | 'color'> {\n  /**\n   * @title 进度百分比\n   */\n  readonly percent: number;\n  /**\n   * @title 条图宽度占比\n   * @description 范围[0-1]\n   */\n  readonly barWidthRatio?: number;\n  /**\n   * @title 进度条颜色\n   */\n  readonly color?: ColorAttr;\n  /**\n   * @title 进度条样式\n   */\n  readonly progressStyle?: StyleAttr;\n}\n"]}