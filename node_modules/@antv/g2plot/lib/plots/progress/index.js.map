{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/progress/index.ts"], "names": [], "mappings": ";;;;AACA,wCAAuC;AACvC,qCAAoC;AACpC,yCAA8C;AAE9C,iCAA0C;AAI1C;IAA8B,oCAAqB;IAAnD;QAAA,qEA+BC;QAtBC,WAAW;QACJ,UAAI,GAAW,SAAS,CAAC;;IAqBlC,CAAC;IA9BC;;;OAGG;IACI,0BAAiB,GAAxB;QACE,OAAO,2BAAe,CAAC;IACzB,CAAC;IAKD;;;OAGG;IACI,6BAAU,GAAjB,UAAkB,OAAe;QAC/B,IAAI,CAAC,YAAY,CAAC,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;QAC/B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAA,uBAAe,EAAC,OAAO,CAAC,CAAC,CAAC;IAClD,CAAC;IAES,oCAAiB,GAA3B;QACE,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACO,mCAAgB,GAA1B;QACE,OAAO,iBAAO,CAAC;IACjB,CAAC;IACH,eAAC;AAAD,CAAC,AA/BD,CAA8B,WAAI,GA+BjC;AA/BY,4BAAQ", "sourcesContent": ["import { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { adaptor } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constants';\nimport { ProgressOptions } from './types';\nimport { getProgressData } from './utils';\n\nexport type { ProgressOptions };\n\nexport class Progress extends Plot<ProgressOptions> {\n  /**\n   * 获取 仪表盘 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<ProgressOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'process';\n\n  /**\n   * 更新数据\n   * @param percent\n   */\n  public changeData(percent: number) {\n    this.updateOption({ percent });\n    this.chart.changeData(getProgressData(percent));\n  }\n\n  protected getDefaultOptions() {\n    return Progress.getDefaultOptions();\n  }\n\n  /**\n   * 获取 进度图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<ProgressOptions> {\n    return adaptor;\n  }\n}\n"]}