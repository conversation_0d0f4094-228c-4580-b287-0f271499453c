{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/progress/utils.ts"], "names": [], "mappings": ";;;AAAA,mCAAmC;AACnC,6CAAkD;AAElD;;GAEG;AACH,SAAgB,eAAe,CAAC,OAAe;IAC7C,IAAM,YAAY,GAAG,IAAA,YAAK,EAAC,IAAA,qBAAY,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtE,OAAO;QACL;YACE,yBAAyB;YACzB,OAAO,EAAE,UAAG,YAAY,CAAE;YAC1B,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,YAAY;SACtB;QACD;YACE,OAAO,EAAE,UAAG,YAAY,CAAE;YAC1B,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,CAAC;SACX;KACF,CAAC;AACJ,CAAC;AAfD,0CAeC", "sourcesContent": ["import { clamp } from '@antv/util';\nimport { isRealNumber } from '../../utils/number';\n\n/**\n * 获取进度条数据\n */\nexport function getProgressData(percent: number) {\n  const clampPercent = clamp(isRealNumber(percent) ? percent : 0, 0, 1);\n  return [\n    {\n      // 用于 progressStyle 的回调方法\n      current: `${clampPercent}`,\n      type: 'current',\n      percent: clampPercent,\n    },\n    {\n      current: `${clampPercent}`,\n      type: 'target',\n      percent: 1,\n    },\n  ];\n}\n"]}