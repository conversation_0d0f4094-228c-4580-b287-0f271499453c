{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/scatter/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import {\n  AnnotationPosition,\n  BrushCfg,\n  Options,\n  RegionPositionBaseOption,\n  ShapeAttr,\n  ShapeStyle,\n  SizeAttr,\n  StyleAttr,\n  TextOption,\n  TextStyle,\n} from '../../types';\n\ninterface Labels extends Omit<TextOption, 'position'> {\n  position?: AnnotationPosition;\n}\n\ninterface QuadrantOptions {\n  /**\n   * @title x 基准线\n   * @description x 方向上的象限分割基准线\n   * @default 0\n   */\n  readonly xBaseline?: number;\n  /**\n   * @title y 基准线\n   * @description y 方向上的象限分割基准线\n   * @default 0\n   */\n  readonly yBaseline?: number;\n  /**\n   * @title 配置象限分割线的样式\n   */\n  readonly lineStyle?: RegionPositionBaseOption;\n  /**\n   * @title 象限样式\n   */\n  readonly regionStyle?: RegionPositionBaseOption[];\n  /**\n   * @title 象限文本\n   */\n  readonly labels?: Labels[];\n}\n\nexport interface RegressionLineOptions {\n  /**\n   * @title 是否顶层显示\n   * @default false\n   */\n  readonly top?: boolean;\n  /**\n   * @title 回归线类型\n   */\n  readonly type?: string;\n  /**\n   * @title 配置回归线样式\n   */\n  readonly style?: ShapeStyle;\n  /**\n   * @title 自定义算法\n   * @description  [[0,0],[100,100]]\n   */\n  readonly algorithm?: Array<[number, number]> | ((data: any) => Array<[number, number]>);\n  /**\n   * @title 显示回归方程式\n   * @description 默认为不显示回归方程式\n   */\n  readonly showEquation?: boolean;\n  /**\n   * @title 自定义回归方程式\n   * @description 只有当自定义 algorithm 时生效\n   */\n  readonly equation?: string;\n  /**\n   * @title 回归线方程式样式\n   * @description 自定义文本样式，请参考 TextStyle 配置\n   */\n  readonly equationStyle?: TextStyle;\n}\n\nexport interface ScatterOptions extends Options {\n  /**\n   * @title x 轴字段\n   */\n  readonly xField: string;\n  /**\n   * @title y 轴字段\n   */\n  readonly yField: string;\n  /**\n   * @title 数据调整类型\n   * @description 数据调整类型 'jitter' | 'stack' | 'symmetric' | 'dodge'\n   */\n  readonly type?: 'jitter' | 'stack' | 'symmetric' | 'dodge';\n  /**\n   * @title 点大小映射对应的数据字段名\n   */\n  readonly sizeField?: string;\n  /**\n   * @title size 对应的图例\n   */\n  readonly sizeLegend?: Options['legend'];\n  /**\n   * @title 散点图大小\n   */\n  readonly size?: SizeAttr;\n  /**\n   * @title 点形状映射对应的数据字段名\n   */\n  readonly shapeField?: string;\n  /**\n   * @title shape 对应的图例\n   */\n  readonly shapeLegend?: Options['legend'];\n  /**\n   * @title 散点图形状\n   */\n  readonly shape?: ShapeAttr;\n  /**\n   * @title 散点图样式\n   */\n  readonly pointStyle?: StyleAttr;\n  /**\n   * @title 点颜色映射对应的数据字段名\n   */\n  readonly colorField?: string;\n\n  // 图表标注组件\n\n  /**\n   * @title 四象限组件\n   */\n  readonly quadrant?: QuadrantOptions;\n  /**\n   * @title 归曲线\n   */\n  readonly regressionLine?: RegressionLineOptions;\n  /**\n   * @title 图表交互\n   */\n  readonly brush?: BrushCfg;\n}\n"]}