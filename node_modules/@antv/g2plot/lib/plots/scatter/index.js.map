{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/scatter/index.ts"], "names": [], "mappings": ";;;;AAAA,+BAAiE;AAEjE,wCAAuC;AACvC,qCAAyC;AACzC,qCAA4D;AAC5D,uCAA6C;AAC7C,0BAAwB;AAKxB;IAA6B,mCAAoB;IAY/C,iBAAY,SAA+B,EAAE,OAAuB;QAApE,YACE,kBAAM,SAAS,EAAE,OAAO,CAAC,SAe1B;QAnBD,WAAW;QACJ,UAAI,GAAW,SAAS,CAAC;QAK9B,sBAAsB;QACtB,KAAI,CAAC,EAAE,CAAC,qBAAgB,CAAC,aAAa,EAAE,UAAC,GAAG;;YAC1C,gBAAgB;YACV,IAAA,KAAqB,KAAI,EAAvB,OAAO,aAAA,EAAE,KAAK,WAAS,CAAC;YAChC,IAAI,CAAA,MAAA,GAAG,CAAC,IAAI,0CAAE,MAAM,MAAK,wBAAmB,CAAC,MAAM,EAAE;gBACnD,IAAM,YAAY,GAAG,KAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACjE,IAAA,cAAI,EAAC,EAAE,KAAK,OAAA,EAAE,OAAO,wCAAO,OAAO,KAAE,IAAI,EAAE,YAAY,GAAE,EAAE,CAAC,CAAC;aAC9D;YAED,IAAI,CAAA,MAAA,GAAG,CAAC,IAAI,0CAAE,MAAM,MAAK,wBAAmB,CAAC,KAAK,EAAE;gBAClD,IAAA,cAAI,EAAC,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;aAC1B;QACH,CAAC,CAAC,CAAC;;IACL,CAAC;IA3BD;;;OAGG;IACI,yBAAiB,GAAxB;QACE,OAAO,0BAAe,CAAC;IACzB,CAAC;IAuBD;;;OAGG;IACI,4BAAU,GAAjB,UAAkB,IAA4B;QAC5C,IAAI,CAAC,YAAY,CAAC,IAAA,0BAAgB,EAAC,IAAA,kBAAU,EAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC,CAAC,CAAC;QACtE,IAAA,KAAqB,IAAI,EAAvB,OAAO,aAAA,EAAE,KAAK,WAAS,CAAC;QAChC,IAAA,cAAI,EAAC,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACO,kCAAgB,GAA1B;QACE,OAAO,iBAAO,CAAC;IACjB,CAAC;IAES,mCAAiB,GAA3B;QACE,OAAO,OAAO,CAAC,iBAAiB,EAAE,CAAC;IACrC,CAAC;IACH,cAAC;AAAD,CAAC,AAnDD,CAA6B,WAAI,GAmDhC;AAnDY,0BAAO", "sourcesContent": ["import { BRUSH_FILTER_EVENTS, VIEW_LIFE_CIRCLE } from '@antv/g2';\nimport { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { deepAssign } from '../../utils';\nimport { adaptor, meta, transformOptions } from './adaptor';\nimport { DEFAULT_OPTIONS } from './constant';\nimport './interactions';\nimport { ScatterOptions } from './types';\n\nexport type { ScatterOptions };\n\nexport class Scatter extends Plot<ScatterOptions> {\n  /**\n   * 获取 散点图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<ScatterOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'scatter';\n\n  constructor(container: string | HTMLElement, options: ScatterOptions) {\n    super(container, options);\n\n    // 监听 brush 事件，处理 meta\n    this.on(VIEW_LIFE_CIRCLE.BEFORE_RENDER, (evt) => {\n      // 运行时，读取 option\n      const { options, chart } = this;\n      if (evt.data?.source === BRUSH_FILTER_EVENTS.FILTER) {\n        const filteredData = this.chart.filterData(this.chart.getData());\n        meta({ chart, options: { ...options, data: filteredData } });\n      }\n\n      if (evt.data?.source === BRUSH_FILTER_EVENTS.RESET) {\n        meta({ chart, options });\n      }\n    });\n  }\n\n  /**\n   * @override\n   * @param data\n   */\n  public changeData(data: ScatterOptions['data']) {\n    this.updateOption(transformOptions(deepAssign({}, this.options, { data })));\n    const { options, chart } = this;\n    meta({ chart, options });\n    this.chart.changeData(data);\n  }\n\n  /**\n   * 获取 散点图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<ScatterOptions> {\n    return adaptor;\n  }\n\n  protected getDefaultOptions() {\n    return Scatter.getDefaultOptions();\n  }\n}\n"]}