import { Params } from '../../core/adaptor';
import { ScatterOptions } from './types';
/**
 * 散点图默认美观
 * ① data.length === 1 ② 所有数据 y 值相等 ③ 所有数据 x 值相等
 * @param params
 * @returns params
 */
export declare function transformOptions(options: ScatterOptions): ScatterOptions;
/**
 * meta 配置
 * @param params
 */
export declare function meta(params: Params<ScatterOptions>): Params<ScatterOptions>;
/**
 * tooltip 配置
 * @param params
 */
export declare function tooltip(params: Params<ScatterOptions>): Params<ScatterOptions>;
/**
 * 散点图适配器
 * @param chart
 * @param options
 */
export declare function adaptor(params: Params<ScatterOptions>): Params<import("../..").Options>;
