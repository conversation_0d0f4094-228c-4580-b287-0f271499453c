{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/bullet/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Types } from '@antv/g2';\nimport { ColorAttr, Datum, Options, SizeAttr, StyleAttr } from '../../types';\n\ntype GeometryLabelAttr = Types.GeometryLabelCfg | ((datum: Datum) => Types.GeometryLabelCfg);\n\ntype BulletAttr<T> = {\n  measure?: T;\n  target?: T;\n  range?: T;\n};\nexport interface BulletOptions extends Omit<Options, 'color' | 'label' | 'style'> {\n  /**\n   * @title 弹图标题\n   * @description 用于区分不同的类型\n   */\n  readonly xField?: string;\n  /**\n   * @title 测量字段\n   * @description 使用数据条的长度，表示实际数值字段，所表示值为 number[]\n   */\n  readonly measureField: string;\n  /**\n   * @title 范围字段\n   * @description 使用背景色条的长度，表示区间范围 [20,50,100], 所表示值为 number[]\n   */\n  readonly rangeField: string;\n  /**\n   * @title 目标字段\n   * @description 使用测量标记的刻度轴位置，表示目标值,所表示值为数值\n   */\n  readonly targetField: string;\n  /**\n   * @title 标签\n   * @description 包含了 measure,target,range\n   */\n  readonly label?: BulletAttr<GeometryLabelAttr | false>;\n  /**\n   * @title 尺寸\n   * @description 包含了 measure,target,range\n   */\n  readonly size?: BulletAttr<SizeAttr>;\n  /**\n   * @title 颜色\n   * @description 包含了 measure,target,range\n   */\n  readonly color?: BulletAttr<ColorAttr>;\n  /**\n   * @title 项目符号样式\n   * @description 包含了 measure,target,range\n   */\n  readonly bulletStyle?: BulletAttr<StyleAttr>;\n  /**\n   * @title 布局方向选择\n   * @default \"horizontal\"\n   */\n  layout?: 'horizontal' | 'vertical';\n}\n"]}