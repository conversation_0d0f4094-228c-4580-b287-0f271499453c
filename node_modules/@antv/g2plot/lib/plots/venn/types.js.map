{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/venn/types.ts"], "names": [], "mappings": ";;AAEA,uCAAkD", "sourcesContent": ["import { Types } from '@antv/g2';\nimport { Datum, Options, StyleAttr } from '../../types';\nimport { ID_FIELD, PATH_FIELD } from './constant';\n\nexport type VennData = (Types.Datum & { sets: string[]; [PATH_FIELD]: string; [ID_FIELD]: string })[];\n\n/** 配置类型定义 */\nexport interface VennOptions extends Options {\n  /**\n   * @title 韦恩图数据\n   */\n  readonly data: Types.Datum[];\n  /**\n   * @title 集合字段\n   */\n  readonly setsField: string;\n  /**\n   * @title 大小字段\n   */\n  readonly sizeField: string;\n\n  // 韦恩图 样式\n\n  /**\n   * @title 颜色\n   */\n  readonly color?: string | string[] | ((datum: Datum, defaultColor?: string) => string);\n  /**\n   * @title 并集合的颜色混合方式\n   * @description  可选项: 参考 https://gka.github.io/chroma.js/#chroma-blend\n   * @default \"multiply\"\n   */\n  readonly blendMode?: string;\n  /**\n   * @title point 样式\n   */\n  readonly pointStyle?: StyleAttr;\n}\n\nexport type CustomInfo = { offsetY: number; offsetX: number } & Pick<VennOptions, 'label'>;\n"]}