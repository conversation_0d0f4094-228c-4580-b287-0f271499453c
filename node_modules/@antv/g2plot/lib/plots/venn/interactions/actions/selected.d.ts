declare const ElementSelectedAction: any;
declare const ElementSingleSelectedAction: any;
/**
 * 韦恩图元素 多选交互
 */
export declare class VennElementSelected extends ElementSelectedAction {
    /**
     * 同步所有元素的位置
     */
    protected syncElementsPos(): void;
    /** 激活图形元素 */
    selected(): void;
    /** toggle 图形元素激活状态 */
    toggle(): void;
    /** 重置 */
    reset(): void;
}
/**
 * 韦恩图元素 单选交互
 */
export declare class VennElementSingleSelected extends ElementSingleSelectedAction {
    /**
     * 同步所有元素的位置
     */
    protected syncElementsPos(): void;
    /** 激活图形元素 */
    selected(): void;
    /** toggle 图形元素激活状态 */
    toggle(): void;
    /** 重置 */
    reset(): void;
}
export {};
