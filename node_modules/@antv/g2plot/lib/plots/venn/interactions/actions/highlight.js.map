{"version": 3, "file": "highlight.js", "sourceRoot": "", "sources": ["../../../../../src/plots/venn/interactions/actions/highlight.ts"], "names": [], "mappings": ";;;;AAAA,+BAA0C;AAC1C,gCAA+C;AAE/C,IAAM,sBAAsB,GAAQ,IAAA,mBAAc,EAAC,mBAAmB,CAAC,CAAC;AAExE;IAA0C,gDAAsB;IAAhE;;IA+BA,CAAC;IA9BC;;OAEG;IACO,8CAAe,GAAzB;QACE,IAAA,2BAAoB,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,aAAa;IACN,wCAAS,GAAhB;QACE,iBAAM,SAAS,WAAE,CAAC;QAClB,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,sBAAsB;IACf,qCAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QACf,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,SAAS;IACF,oCAAK,GAAZ;QACE,iBAAM,KAAK,WAAE,CAAC;QACd,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,SAAS;IACF,oCAAK,GAAZ;QACE,iBAAM,KAAK,WAAE,CAAC;QACd,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IACH,2BAAC;AAAD,CAAC,AA/BD,CAA0C,sBAAsB,GA+B/D;AA/BY,oDAAoB", "sourcesContent": ["import { getActionClass } from '@antv/g2';\nimport { placeElementsOrdered } from '../util';\n\nconst ElementHighlightAction: any = getActionClass('element-highlight');\n\nexport class VennElementHighlight extends ElementHighlightAction {\n  /**\n   * 同步所有元素的位置\n   */\n  protected syncElementsPos() {\n    placeElementsOrdered(this.context.view);\n  }\n\n  /** 高亮图形元素 */\n  public highlight() {\n    super.highlight();\n    this.syncElementsPos();\n  }\n\n  /** toggle 图形元素高亮状态 */\n  public toggle() {\n    super.toggle();\n    this.syncElementsPos();\n  }\n\n  /** 清楚 */\n  public clear() {\n    super.clear();\n    this.syncElementsPos();\n  }\n\n  /** 重置 */\n  public reset() {\n    super.reset();\n    this.syncElementsPos();\n  }\n}\n"]}