{"version": 3, "file": "selected.js", "sourceRoot": "", "sources": ["../../../../../src/plots/venn/interactions/actions/selected.ts"], "names": [], "mappings": ";;;;AAAA,+BAA0C;AAC1C,gCAA+C;AAE/C,IAAM,qBAAqB,GAAQ,IAAA,mBAAc,EAAC,kBAAkB,CAAC,CAAC;AACtE,IAAM,2BAA2B,GAAQ,IAAA,mBAAc,EAAC,yBAAyB,CAAC,CAAC;AAEnF;;GAEG;AACH;IAAyC,+CAAqB;IAA9D;;IAyBA,CAAC;IAxBC;;OAEG;IACO,6CAAe,GAAzB;QACE,IAAA,2BAAoB,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,aAAa;IACN,sCAAQ,GAAf;QACE,iBAAM,QAAQ,WAAE,CAAC;QACjB,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,sBAAsB;IACf,oCAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QACf,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,SAAS;IACF,mCAAK,GAAZ;QACE,iBAAM,KAAK,WAAE,CAAC;QACd,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IACH,0BAAC;AAAD,CAAC,AAzBD,CAAyC,qBAAqB,GAyB7D;AAzBY,kDAAmB;AA2BhC;;GAEG;AACH;IAA+C,qDAA2B;IAA1E;;IAyBA,CAAC;IAxBC;;OAEG;IACO,mDAAe,GAAzB;QACE,IAAA,2BAAoB,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,aAAa;IACN,4CAAQ,GAAf;QACE,iBAAM,QAAQ,WAAE,CAAC;QACjB,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,sBAAsB;IACf,0CAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QACf,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,SAAS;IACF,yCAAK,GAAZ;QACE,iBAAM,KAAK,WAAE,CAAC;QACd,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IACH,gCAAC;AAAD,CAAC,AAzBD,CAA+C,2BAA2B,GAyBzE;AAzBY,8DAAyB", "sourcesContent": ["import { getActionClass } from '@antv/g2';\nimport { placeElementsOrdered } from '../util';\n\nconst ElementSelectedAction: any = getActionClass('element-selected');\nconst ElementSingleSelectedAction: any = getActionClass('element-single-selected');\n\n/**\n * 韦恩图元素 多选交互\n */\nexport class VennElementSelected extends ElementSelectedAction {\n  /**\n   * 同步所有元素的位置\n   */\n  protected syncElementsPos() {\n    placeElementsOrdered(this.context.view);\n  }\n\n  /** 激活图形元素 */\n  public selected() {\n    super.selected();\n    this.syncElementsPos();\n  }\n\n  /** toggle 图形元素激活状态 */\n  public toggle() {\n    super.toggle();\n    this.syncElementsPos();\n  }\n\n  /** 重置 */\n  public reset() {\n    super.reset();\n    this.syncElementsPos();\n  }\n}\n\n/**\n * 韦恩图元素 单选交互\n */\nexport class VennElementSingleSelected extends ElementSingleSelectedAction {\n  /**\n   * 同步所有元素的位置\n   */\n  protected syncElementsPos() {\n    placeElementsOrdered(this.context.view);\n  }\n\n  /** 激活图形元素 */\n  public selected() {\n    super.selected();\n    this.syncElementsPos();\n  }\n\n  /** toggle 图形元素激活状态 */\n  public toggle() {\n    super.toggle();\n    this.syncElementsPos();\n  }\n\n  /** 重置 */\n  public reset() {\n    super.reset();\n    this.syncElementsPos();\n  }\n}\n"]}