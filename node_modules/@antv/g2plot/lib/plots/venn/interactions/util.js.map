{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/plots/venn/interactions/util.ts"], "names": [], "mappings": ";;;AAEA,0BAA0B;AAC1B,SAAgB,oBAAoB,CAAC,IAAU;IAC7C,IAAI,CAAC,IAAI,EAAE;QACT,OAAO;KACR;IACD,IAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC7C,QAAQ,CAAC,OAAO,CAAC,UAAC,IAAI;QACpB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;AACL,CAAC;AARD,oDAQC", "sourcesContent": ["import { View } from '@antv/g2';\n\n/** tofront: 同步所有元素的位置  */\nexport function placeElementsOrdered(view: View) {\n  if (!view) {\n    return;\n  }\n  const elements = view.geometries[0].elements;\n  elements.forEach((elem) => {\n    elem.shape.toFront();\n  });\n}\n"]}