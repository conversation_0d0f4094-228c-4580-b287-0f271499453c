{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/plots/pie/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Options, ShapeAttr, Statistic, StyleAttr } from '../../types';\nimport { Label } from '../../types/label';\n\nexport type StatisticData = {\n  title: string;\n  value: string | number | null;\n};\n\nexport interface PieOptions extends Options {\n  /**\n   * @title 角度映射字段\n   */\n  readonly angleField: string;\n  /**\n   * @title 颜色映射字段\n   */\n  readonly colorField: string;\n  /**\n   * @title 饼图半径\n   */\n  readonly radius?: number;\n  /**\n   * @title 饼图内半径\n   */\n  readonly innerRadius?: number;\n  /**\n   * @title 饼图标签\n   * @description type: 'inner' | 'outer' | 'spider'\n   */\n  readonly label?: Label;\n  /**\n   * @title 饼图图形样式\n   */\n  readonly pieStyle?: StyleAttr;\n  // 设置扇形图\n  /**\n   * @title 圆环的开始角度\n   */\n  readonly startAngle?: number;\n  /**\n   * @title 圆环的结束角度\n   */\n  readonly endAngle?: number;\n  /**\n   * @title 指标卡组件\n   * @description 显示在环图中心，可以代替tooltip，显示环图数据的总计值和各项数据,启用 statistic 组件的同时将自动关闭tooltip\n   */\n  readonly statistic?: Statistic;\n  /**\n   * @title 饼图形状映射\n   * @descriptio 自定义饼图形状\n   */\n  readonly shape?: ShapeAttr;\n}\n"]}