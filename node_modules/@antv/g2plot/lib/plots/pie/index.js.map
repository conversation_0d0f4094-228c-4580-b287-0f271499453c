{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/plots/pie/index.ts"], "names": [], "mappings": ";;;;AAAA,+BAAmD;AAEnD,wCAAuC;AACvC,qCAAiD;AACjD,qCAAmD;AACnD,uCAA6C;AAC7C,0BAAwB;AAExB,iCAAoC;AAIpC;IAAyB,+BAAgB;IAAzC;QAAA,qEAuDC;QA9CC,WAAW;QACJ,UAAI,GAAW,KAAK,CAAC;;IA6C9B,CAAC;IAtDC;;;OAGG;IACI,qBAAiB,GAAxB;QACE,OAAO,0BAAe,CAAC;IACzB,CAAC;IAKD;;;OAGG;IACI,wBAAU,GAAjB,UAAkB,IAAwB;QACxC,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,qBAAgB,CAAC,kBAAkB,EACnC,UAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,qBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,CACtE,CAAC;QACF,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;QACzB,IAAA,UAAU,GAAK,IAAI,CAAC,OAAO,WAAjB,CAAkB;QACpC,IAAM,QAAQ,GAAG,IAAA,0BAAkB,EAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAClE,IAAM,OAAO,GAAG,IAAA,0BAAkB,EAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACrD,uBAAuB;QACvB,IAAI,IAAA,iBAAS,EAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,IAAA,iBAAS,EAAC,OAAO,EAAE,UAAU,CAAC,EAAE;YACrE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;SACvB;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,gDAAgD;YAChD,IAAA,uBAAa,EAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACzB;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,qBAAgB,CAAC,iBAAiB,EAClC,UAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,qBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CACrE,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,+BAAiB,GAA3B;QACE,OAAO,GAAG,CAAC,iBAAiB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACO,8BAAgB,GAA1B;QACE,OAAO,iBAAO,CAAC;IACjB,CAAC;IACH,UAAC;AAAD,CAAC,AAvDD,CAAyB,WAAI,GAuD5B;AAvDY,kBAAG", "sourcesContent": ["import { Event, VIEW_LIFE_CIRCLE } from '@antv/g2';\nimport { Adaptor } from '../../core/adaptor';\nimport { Plot } from '../../core/plot';\nimport { processIllegalData } from '../../utils';\nimport { adaptor, pieAnnotation } from './adaptor';\nimport { DEFAULT_OPTIONS } from './contants';\nimport './interactions';\nimport { PieOptions } from './types';\nimport { isAllZero } from './utils';\n\nexport type { PieOptions };\n\nexport class Pie extends Plot<PieOptions> {\n  /**\n   * 获取 饼图 默认配置项\n   * 供外部使用\n   */\n  static getDefaultOptions(): Partial<PieOptions> {\n    return DEFAULT_OPTIONS;\n  }\n\n  /** 图表类型 */\n  public type: string = 'pie';\n\n  /**\n   * 更新数据\n   * @param data\n   */\n  public changeData(data: PieOptions['data']) {\n    this.chart.emit(\n      VIEW_LIFE_CIRCLE.BEFORE_CHANGE_DATA,\n      Event.fromData(this.chart, VIEW_LIFE_CIRCLE.BEFORE_CHANGE_DATA, null)\n    );\n    const prevOptions = this.options;\n    const { angleField } = this.options;\n    const prevData = processIllegalData(prevOptions.data, angleField);\n    const curData = processIllegalData(data, angleField);\n    // 如果上一次或当前数据全为 0，则重新渲染\n    if (isAllZero(prevData, angleField) || isAllZero(curData, angleField)) {\n      this.update({ data });\n    } else {\n      this.updateOption({ data });\n      this.chart.data(curData);\n      // todo 后续让 G2 层在 afterrender 之后，来重绘 annotations\n      pieAnnotation({ chart: this.chart, options: this.options });\n      this.chart.render(true);\n    }\n\n    this.chart.emit(\n      VIEW_LIFE_CIRCLE.AFTER_CHANGE_DATA,\n      Event.fromData(this.chart, VIEW_LIFE_CIRCLE.AFTER_CHANGE_DATA, null)\n    );\n  }\n\n  /**\n   * 获取 饼图 默认配置项, 供 base 获取\n   */\n  protected getDefaultOptions(): Partial<PieOptions> {\n    return Pie.getDefaultOptions();\n  }\n\n  /**\n   * 获取 饼图 的适配器\n   */\n  protected getSchemaAdaptor(): Adaptor<PieOptions> {\n    return adaptor;\n  }\n}\n"]}