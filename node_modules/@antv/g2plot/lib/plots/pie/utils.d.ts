import { Data } from '../../types';
import { PieOptions } from './types';
/**
 * 获取总计值
 * @param data
 * @param field
 */
export declare function getTotalValue(data: Data, field: string): any;
/**
 * pie label offset adaptor
 */
export declare function adaptOffset(type: string, offset?: string | number): string | number;
/**
 * 判断数据是否全部为 0
 * @param data
 * @param angleField
 */
export declare function isAllZero(data: PieOptions['data'], angleField: string): boolean;
