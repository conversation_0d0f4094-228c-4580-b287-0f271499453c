{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/plots/pie/utils.ts"], "names": [], "mappings": ";;;AAAA,mCAAmD;AAEnD,qCAAiD;AAGjD;;;;GAIG;AACH,SAAgB,aAAa,CAAC,IAAU,EAAE,KAAa;IACrD,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAA,WAAI,EAAC,IAAI,EAAE,UAAC,IAAI;QACd,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;YACnC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;SACtB;IACH,CAAC,CAAC,CAAC;IACH,OAAO,KAAK,CAAC;AACf,CAAC;AARD,sCAQC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,IAAY,EAAE,MAAwB;IAChE,IAAI,aAAa,CAAC;IAClB,QAAQ,IAAI,EAAE;QACZ,KAAK,OAAO;YACV,aAAa,GAAG,MAAM,CAAC;YACvB,IAAI,IAAA,eAAQ,EAAC,MAAM,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC5C,OAAO,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC;aAC/D;YACD,OAAO,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC;QAC7C,KAAK,OAAO;YACV,aAAa,GAAG,EAAE,CAAC;YACnB,IAAI,IAAA,eAAQ,EAAC,MAAM,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC5C,OAAO,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC;aAC/D;YACD,OAAO,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC;QAC7C;YACE,OAAO,MAAM,CAAC;KACjB;AACH,CAAC;AAlBD,kCAkBC;AAED;;;;GAIG;AACH,SAAgB,SAAS,CAAC,IAAwB,EAAE,UAAkB;IACpE,OAAO,IAAA,YAAK,EAAC,IAAA,0BAAkB,EAAC,IAAI,EAAE,UAAU,CAAC,EAAE,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,CAAC;AACjF,CAAC;AAFD,8BAEC", "sourcesContent": ["import { each, every, isString } from '@antv/util';\nimport { Data } from '../../types';\nimport { processIllegalData } from '../../utils';\nimport { PieOptions } from './types';\n\n/**\n * 获取总计值\n * @param data\n * @param field\n */\nexport function getTotalValue(data: Data, field: string) {\n  let total = null;\n  each(data, (item) => {\n    if (typeof item[field] === 'number') {\n      total += item[field];\n    }\n  });\n  return total;\n}\n\n/**\n * pie label offset adaptor\n */\nexport function adaptOffset(type: string, offset?: string | number): string | number {\n  let defaultOffset;\n  switch (type) {\n    case 'inner':\n      defaultOffset = '-30%';\n      if (isString(offset) && offset.endsWith('%')) {\n        return parseFloat(offset) * 0.01 > 0 ? defaultOffset : offset;\n      }\n      return offset < 0 ? offset : defaultOffset;\n    case 'outer':\n      defaultOffset = 12;\n      if (isString(offset) && offset.endsWith('%')) {\n        return parseFloat(offset) * 0.01 < 0 ? defaultOffset : offset;\n      }\n      return offset > 0 ? offset : defaultOffset;\n    default:\n      return offset;\n  }\n}\n\n/**\n * 判断数据是否全部为 0\n * @param data\n * @param angleField\n */\nexport function isAllZero(data: PieOptions['data'], angleField: string): boolean {\n  return every(processIllegalData(data, angleField), (d) => d[angleField] === 0);\n}\n"]}