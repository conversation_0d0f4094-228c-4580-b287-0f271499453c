import { Params } from '../../core/adaptor';
import { PieOptions } from './types';
/**
 * statistic options 处理
 * 1. 默认继承 default options 的样式
 * 2. 默认使用 meta 的 formatter
 */
export declare function transformStatisticOptions(options: PieOptions): PieOptions;
/**
 * statistic 中心文本配置
 * @param params
 */
export declare function pieAnnotation(params: Params<PieOptions>): Params<PieOptions>;
/**
 * Interaction 配置 (饼图特殊的 interaction, 中心文本变更的时候，需要将一些配置参数传进去）
 * @param params
 */
export declare function interaction(params: Params<PieOptions>): Params<PieOptions>;
/**
 * 饼图适配器
 * @param chart
 * @param options
 */
export declare function adaptor(params: Params<PieOptions>): Params<PieOptions>;
