{"version": 3, "file": "lab.js", "sourceRoot": "", "sources": ["../src/lab.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAElC,iBAAiB;AACjB,IAAY,KAIX;AAJD,WAAY,KAAK;IACf,oBAAW,CAAA;IACX,sBAAa,CAAA;IACb,0BAAiB,CAAA;AACnB,CAAC,EAJW,KAAK,GAAL,aAAK,KAAL,aAAK,QAIhB;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,KAAY,EAAE,QAAgB;IACnD,OAAO,CAAC,IAAI,CACV,KAAK,KAAK,KAAK,CAAC,GAAG;QACjB,CAAC,CAAC,gBAAS,QAAQ,4CAAyC;QAC5D,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI;YACtB,CAAC,CAAC,gBAAS,QAAQ,yDAAsD;YACzE,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM;gBACxB,CAAC,CAAC,gBAAS,QAAQ,2DAAgD,QAAQ,8BAA0B;gBACrG,CAAC,CAAC,qBAAqB,CAC1B,CAAC;AACJ,CAAC;AAVD,wBAUC;AAED;;GAEG;AACH;IAAA;IAKA,CAAC;IAJC,sBAAW,gBAAS;aAApB;YACE,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAClC,OAAO,SAAG,CAAC;QACb,CAAC;;;OAAA;IACH,UAAC;AAAD,CAAC,AALD,IAKC;AALY,kBAAG", "sourcesContent": ["import { Mix } from './plots/mix';\n\n/** 实验室图表所处的阶段 */\nexport enum Stage {\n  DEV = 'DEV',\n  BETA = 'BETA',\n  STABLE = 'STABLE',\n}\n\n/**\n * 不同阶段打印一些消息给开发者\n * @param stage\n */\nexport function notice(stage: Stage, plotType: string) {\n  console.warn(\n    stage === Stage.DEV\n      ? `Plot '${plotType}' is in DEV stage, just give us issues.`\n      : stage === Stage.BETA\n      ? `Plot '${plotType}' is in BETA stage, DO NOT use it in production env.`\n      : stage === Stage.STABLE\n      ? `Plot '${plotType}' is in STABLE stage, import it by \"import { ${plotType} } from '@antv/g2plot'\".`\n      : 'invalid Stage type.'\n  );\n}\n\n/**\n * 实验室图表，实验室中的图表分成不同的阶段。\n */\nexport class Lab {\n  static get MultiView() {\n    notice(Stage.STABLE, 'MultiView');\n    return Mix;\n  }\n}\n"]}