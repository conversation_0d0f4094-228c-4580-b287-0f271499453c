export declare const version = "2.4.33";
import * as G2 from '@antv/g2';
/** 开放一些通用的 adaptor 通道方法，实验阶段：不保证稳定性 */
import { animation, annotation, interaction, legend, scale, theme, tooltip } from './adaptor/common';
import { registerLocale } from './core/locale';
/** 各个 geometry 的 adaptor，可以让开发者更快的构造图形 */
export { area, interval, line, point, polygon, schema } from './adaptor/geometries';
export type { AreaGeometryOptions, IntervalGeometryOptions, LineGeometryOptions, PointGeometryOptions, PolygonGeometryOptions, SchemaGeometryOptions, } from './adaptor/geometries';
/** Adaptor 及其参数的类型定义 */
export type { Adaptor, Params } from './core/adaptor';
/** 全局变量 */
export { setGlobal } from './core/global';
/** G2Plot 的 Plot 基类 */
export { Plot } from './core/plot';
/** 对于没有开发完成的图表，可以暂时先放到 Lab 下面，先做体验，稳定后放到根 export */
export { Lab } from './lab';
export { Area } from './plots/area';
export type { AreaOptions } from './plots/area';
export { Bar } from './plots/bar';
export type { BarOptions } from './plots/bar';
export { BidirectionalBar } from './plots/bidirectional-bar';
export type { BidirectionalBarOptions } from './plots/bidirectional-bar';
export { Box } from './plots/box';
export type { BoxOptions } from './plots/box';
export { Bullet } from './plots/bullet';
export type { BulletOptions } from './plots/bullet';
export { Chord } from './plots/chord';
export type { ChordOptions } from './plots/chord';
export { CirclePacking } from './plots/circle-packing';
export type { CirclePackingOptions } from './plots/circle-packing';
export { Column } from './plots/column';
export type { ColumnOptions } from './plots/column';
export { DualAxes } from './plots/dual-axes';
export type { DualAxesOptions } from './plots/dual-axes';
export { Facet } from './plots/facet';
export type { FacetOptions } from './plots/facet';
export { Funnel, FUNNEL_CONVERSATION_FIELD } from './plots/funnel';
export type { FunnelOptions } from './plots/funnel';
export { Gauge } from './plots/gauge';
export type { GaugeOptions } from './plots/gauge';
export { Heatmap } from './plots/heatmap';
export type { HeatmapOptions } from './plots/heatmap';
export { Histogram } from './plots/histogram';
export type { HistogramOptions } from './plots/histogram';
export { Line } from './plots/line';
export type { LineOptions } from './plots/line';
export { addWaterWave, Liquid } from './plots/liquid';
export type { LiquidOptions } from './plots/liquid';
export { Mix as Mix, Mix as MultiView } from './plots/mix';
export type { MixOptions, MixOptions as MultiViewOptions } from './plots/mix';
export { Pie } from './plots/pie';
export type { PieOptions } from './plots/pie';
export { Progress } from './plots/progress';
export type { ProgressOptions } from './plots/progress';
export { Radar } from './plots/radar';
export type { RadarOptions } from './plots/radar';
export { RadialBar } from './plots/radial-bar';
export type { RadialBarOptions } from './plots/radial-bar';
export { RingProgress } from './plots/ring-progress';
export type { RingProgressOptions } from './plots/ring-progress';
export { Rose } from './plots/rose';
export type { RoseOptions } from './plots/rose';
export { Sankey } from './plots/sankey';
export type { SankeyOptions } from './plots/sankey';
export { Scatter } from './plots/scatter';
export type { ScatterOptions } from './plots/scatter';
export { Stock } from './plots/stock';
export type { StockOptions } from './plots/stock';
export { Sunburst } from './plots/sunburst';
export type { SunburstOptions } from './plots/sunburst';
export { TinyArea } from './plots/tiny-area';
export type { TinyAreaOptions } from './plots/tiny-area';
export { TinyColumn } from './plots/tiny-column';
export type { TinyColumnOptions } from './plots/tiny-column';
export { TinyLine } from './plots/tiny-line';
export type { TinyLineOptions } from './plots/tiny-line';
export { Treemap } from './plots/treemap';
export type { TreemapOptions } from './plots/treemap';
export { Venn } from './plots/venn';
export type { VennOptions } from './plots/venn';
export { Violin } from './plots/violin';
export type { ViolinOptions } from './plots/violin';
export { Waterfall } from './plots/waterfall';
export type { WaterfallOptions } from './plots/waterfall';
export { WordCloud } from './plots/word-cloud';
export type { WordCloudOptions } from './plots/word-cloud';
/** 所有开放图表都使用 G2Plot.P 作为入口开发，理论上官方的所有图表都可以走 G2Plot.P 的入口（暂时不处理） */
export { P } from './plugin';
export * from './types';
/** 开发 adaptor 可能会用到的方法或一些工具方法，不强制使用 */
export { flow, measureTextWidth } from './utils';
/** 开放 getCanvasPatterng 方法 */
export { getCanvasPattern } from './utils/pattern';
export { G2 };
/** 透出 国际化 工具函数，便于使用 */
export { registerLocale };
export declare const adaptors: {
    scale: typeof scale;
    legend: typeof legend;
    tooltip: typeof tooltip;
    annotation: typeof annotation;
    interaction: typeof interaction;
    theme: typeof theme;
    animation: typeof animation;
};
