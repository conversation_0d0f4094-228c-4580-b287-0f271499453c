{"version": 3, "file": "path-2-absolute.js", "sourceRoot": "", "sources": ["../src/path-2-absolute.ts"], "names": [], "mappings": "AAAA,OAAO,eAAe,MAAM,qBAAqB,CAAC;AAClD,IAAM,QAAQ,GAAG,OAAO,CAAC;AAEzB,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC;IACtB,OAAO;QACL,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACrB,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,OAAO,UAAU,cAAc,CAAC,UAAkB;IACvD,IAAM,SAAS,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;IAE9C,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;QACnC,OAAO;YACL,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAE;SACd,CAAC;KACH;IACD,IAAI,WAAW,GAAG,KAAK,CAAC,CAAC,4BAA4B;IACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACzC,IAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,oBAAoB;QACpB,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAClE,WAAW,GAAG,IAAI,CAAC;YACnB,MAAM;SACP;KACF;IACD,kBAAkB;IAClB,8BAA8B;IAC9B,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,SAAS,CAAC;KAClB;IAED,IAAM,GAAG,GAAG,EAAE,CAAC;IACf,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,GAAG,CAAC;IACR,IAAI,IAAI,CAAC;IACT,IAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACxC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACd,EAAE,GAAG,CAAC,CAAC;QACP,EAAE,GAAG,CAAC,CAAC;QACP,KAAK,EAAE,CAAC;QACR,GAAG,CAAC,CAAC,CAAC,GAAG,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAE,CAAC;KACxB;IAED,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QACtD,IAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACxB,IAAM,SAAS,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,uBAAuB;QACrD,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,IAAM,KAAK,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;QAChC,IAAI,GAAG,KAAK,KAAK,EAAE;YACjB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YACb,QAAQ,KAAK,EAAE;gBACb,KAAK,GAAG;oBACN,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAClB,MAAM;gBACR,KAAK,GAAG;oBACN,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAClB,MAAM;gBACR,KAAK,GAAG;oBACN,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAClB,MAAM;gBACR,KAAK,GAAG;oBACN,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAChB,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;oBACV,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;oBACV,MAAM,CAAC,WAAW;gBACpB;oBACE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;wBAC3C,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;qBACnC;aACJ;SACF;aAAM,EAAE,gBAAgB;YACvB,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;SAClB;QACD,8BAA8B;QAC9B,QAAQ,KAAK,EAAE;YACb,KAAK,GAAG;gBACN,CAAC,GAAG,CAAC,EAAE,CAAC;gBACR,CAAC,GAAG,CAAC,EAAE,CAAC;gBACR,MAAM;YACR,KAAK,GAAG;gBACN,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,GAAG,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAE,CAAC;gBAClB,MAAM;YACR,KAAK,GAAG;gBACN,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,GAAG,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAE,CAAC;gBAClB,MAAM;YACR,KAAK,GAAG;gBACN,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,yBAAyB;gBACzB,kBAAkB;gBAClB,IAAM,SAAS,GAAG,UAAU,CAAC,CAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAE,EAAE,CAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;gBAC7F,CAAC,GAAG,CAAE,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE,CAAC;gBAC9C,MAAM;YACR,KAAK,GAAG;gBACN,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpB,qBAAqB;gBACrB,oBAAoB;gBACpB,IAAM,QAAM,GAAG,SAAS,CAAC,MAAM,CAAC;gBAChC,IAAM,SAAS,GAAG,UAAU,CAC1B,CAAE,SAAS,CAAC,QAAM,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,QAAM,GAAG,CAAC,CAAC,CAAE,EAChD,CAAE,SAAS,CAAC,QAAM,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,QAAM,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC;gBACpD,CAAC,GAAG,CAAE,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE,CAAC;gBAC1D,MAAM;YACR,KAAK,GAAG;gBACN,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACrB,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACrB,MAAM,CAAC,WAAW;YACpB;gBACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SACvB;QACD,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACb;IAED,OAAO,GAAG,CAAC;AACb,CAAC"}