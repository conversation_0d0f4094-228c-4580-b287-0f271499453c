{"version": 3, "file": "auto-wrap.js", "sourceRoot": "", "sources": ["../../../src/axis/overlap/auto-wrap.ts"], "names": [], "mappings": ";;;AACA,mCAAkC;AAClC,wCAAuD;AAEvD,IAAM,SAAS,GAAG,IAAI,CAAC;AAEvB,SAAS,SAAS,CAAC,KAAe,EAAE,WAAmB;IACnD,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChC,IAAM,WAAW,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;IAC1C,IAAM,UAAU,GAAG,aAAM,CAAC,IAAI,CAAC,CAAC;IAChC,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAI,WAAW,GAAG,WAAW,EAAE;QAC3B,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,UAAU,CAAC,CAAC;QAC1E,IAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAC,YAAY,CAAC,CAAC;QAC5C,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC5B,UAAU,GAAG,IAAI,CAAC;KACrB;IACD,OAAO,UAAU,CAAC;AACtB,CAAC;AAED,SAAS,QAAQ,CAAC,GAAW,EAAE,YAAoB;IAC/C,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,GAAI;QAC3C,IAAM,UAAU,GAAG,mBAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,UAAU,IAAI,YAAY,EAAE;YAChC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC,IAAI,mBAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9B,KAAK,EAAE,CAAC;YACR,UAAU,GAAG,KAAK,CAAC;SACpB;aAAM;YACL,MAAM;SACP;KACN;IACD,oBAAoB;IACpB,IAAM,WAAW,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC,UAAU,EAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC3E,OAAO,WAAW,CAAC;AACvB,CAAC;AAED,SAAgB,UAAU,CAAC,UAAkB,EAAE,WAAmB;IAC9D,IAAM,QAAQ,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;IAC1C,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,WAAI,CAAC,QAAQ,EAAE,UAAC,KAAK;QACjB,IAAM,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAC1C,OAAO,GAAG,OAAO,IAAI,GAAG,CAAC;IAC7B,CAAC,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;AACnB,CAAC;AARD,gCAQC"}