{"version": 3, "file": "line.js", "sourceRoot": "", "sources": ["../../src/annotation/line.ts"], "names": [], "mappings": ";;;AACA,mCAAgD;AAChD,+DAAyD;AAGzD,2CAAoD;AACpD,uCAAkC;AAClC,qCAAiD;AAEjD;IAA6B,0CAAiC;IAA9D;;IA4HA,CAAC;IA3HC;;;;OAIG;IACI,sCAAa,GAApB;QACE,IAAM,GAAG,GAAG,iBAAM,aAAa,WAAE,CAAC;QAClC,6CACK,GAAG,KACN,IAAI,EAAE,YAAY,EAClB,IAAI,EAAE,MAAM,EACZ,YAAY,EAAE,QAAQ,EACtB,KAAK,EAAE,IAAI,EACX,GAAG,EAAE,IAAI,EACT,KAAK,EAAE,EAAE,EACT,IAAI,EAAE,IAAI,EACV,UAAU,EAAE;gBACV,KAAK,EAAE;oBACL,IAAI,EAAE,eAAK,CAAC,SAAS;oBACrB,QAAQ,EAAE,EAAE;oBACZ,SAAS,EAAE,QAAQ;oBACnB,YAAY,EAAE,QAAQ;oBACtB,UAAU,EAAE,eAAK,CAAC,UAAU;iBAC7B;gBACD,IAAI,EAAE;oBACJ,QAAQ,EAAE,QAAQ;oBAClB,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,CAAC;oBACV,OAAO,EAAE,CAAC;oBACV,KAAK,EAAE;wBACL,MAAM,EAAE,eAAK,CAAC,SAAS;wBACvB,SAAS,EAAE,CAAC;qBACb;iBACF;aACF,IACD;IACJ,CAAC;IAES,oCAAW,GAArB,UAAsB,KAAa;QACjC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACzB;IACH,CAAC;IAED,MAAM;IACE,mCAAU,GAAlB,UAAmB,KAAa;QAC9B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YACnB,IAAI,EAAE,MAAM;YACZ,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YAC7B,IAAI,EAAE,iBAAiB;YACvB,KAAK,qBACH,EAAE,EAAE,KAAK,CAAC,CAAC,EACX,EAAE,EAAE,KAAK,CAAC,CAAC,EACX,EAAE,EAAE,GAAG,CAAC,CAAC,EACT,EAAE,EAAE,GAAG,CAAC,CAAC,IACN,KAAK,CACT;SACF,CAAC,CAAC;IACL,CAAC;IAED,eAAe;IACP,sCAAa,GAArB,UAAsB,KAAY,EAAE,GAAU,EAAE,QAAgB;QAC9D,IAAI,OAAO,CAAC;QACZ,IAAI,QAAQ,KAAK,OAAO,EAAE;YACxB,OAAO,GAAG,CAAC,CAAC;SACb;aAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE;YAChC,OAAO,GAAG,GAAG,CAAC;SACf;aAAM,IAAI,eAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAC7D,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;SACxC;aAAM,IAAI,eAAQ,CAAC,QAAQ,CAAC,EAAE;YAC7B,OAAO,GAAG,QAAQ,CAAC;SACpB;aAAM;YACL,OAAO,GAAG,CAAC,CAAC;SACb;QAED,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,EAAE;YAC9B,OAAO,GAAG,CAAC,CAAC;SACb;QAED,OAAO;YACL,CAAC,EAAE,wBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;YAC7C,CAAC,EAAE,wBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,WAAW;IACH,oCAAW,GAAnB,UAAoB,KAAa;QAC/B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACpB,IAAA,QAAQ,GACgE,IAAI,SADpE,EAAE,OAAO,GACuD,IAAI,QAD3D,EAAE,KAAK,GACgD,IAAI,MADpD,EAAE,OAAO,GACuC,IAAI,QAD3C,EAAE,OAAO,GAC8B,IAAI,QADlC,EAAE,UAAU,GACkB,IAAI,WADtB,EAC5D,SAAS,GAAqE,IAAI,UAAzE,EAAE,YAAY,GAAuD,IAAI,aAA3D,EAAE,gBAAgB,GAAqC,IAAI,iBAAzC,EAAE,UAAU,GAAyB,IAAI,WAA7B,EAAE,KAAuB,IAAI,WAAT,EAAlB,UAAU,mBAAG,KAAK,KAAA,CAAU;QACrF,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QACvD,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC;QAC5B,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC;QAE5B,IAAM,GAAG,GAAW;YAClB,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;YAClC,IAAI,EAAE,sBAAsB;YAC5B,CAAC,GAAA;YACD,CAAC,GAAA;YACD,OAAO,SAAA;YACP,KAAK,OAAA;YACL,SAAS,WAAA;YACT,YAAY,cAAA;YACZ,gBAAgB,kBAAA;YAChB,UAAU,YAAA;YACV,UAAU,YAAA;SACX,CAAC;QAEF,SAAS;QACT,IAAI,UAAU,EAAE;YACd,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAClD,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/C;QAED,mBAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACxB,CAAC;IACH,qBAAC;AAAD,CAAC,AA5HD,CAA6B,yBAAc,GA4H1C;AAED,kBAAe,cAAc,CAAC"}