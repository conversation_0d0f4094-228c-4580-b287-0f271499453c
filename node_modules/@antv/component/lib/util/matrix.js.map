{"version": 3, "file": "matrix.js", "sourceRoot": "", "sources": ["../../src/util/matrix.ts"], "names": [], "mappings": ";;;AACA,iDAAoD;AAGpD,IAAM,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnD,SAAgB,gBAAgB,CAAC,KAAY,EAAE,KAAa,EAAE,MAAuB;IAAvB,uBAAA,EAAA,uBAAuB;IACnF,IAAI,CAAC,KAAK,EAAE;QACV,yBAAyB;QACzB,OAAO,IAAI,CAAC;KACb;IACD,IAAM,CAAC,GAAG,iBAAG,CAAC,SAAS,CAAC,MAAM,EAAE;QAC9B,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QACzB,CAAC,GAAG,EAAE,KAAK,CAAC;QACZ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;KACxB,CAAC,CAAC;IACH,OAAO,CAAC,CAAC;AACX,CAAC;AAXD,4CAWC;AAED,SAAgB,oBAAoB,CAAC,KAAY,EAAE,aAAwB;IACzE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACxB,yBAAyB;QACzB,OAAO,IAAI,CAAC;KACb;IACD,OAAO,iBAAG,CAAC,SAAS,CAAC,aAAa,IAAI,cAAc,EAAE,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,CAAC;AAND,oDAMC;AAED,aAAa;AACb,SAAgB,gBAAgB,CAAC,MAIhC;IACC,IAAM,OAAO,GAA6B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACpD,IAAM,GAAG,GAA+B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAClD,kBAAI,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACzC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC;AATD,4CASC;AACD,UAAU;AACV,SAAS,YAAY,CAAC,MAAM,EAAE,CAAC;IAC7B,IAAM,GAAG,GAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,kBAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACnC,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAgB,gBAAgB,CAAC,MAAgB,EAAE,IAAU;IAC3D,IAAM,OAAO,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7D,IAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9D,IAAM,UAAU,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAChE,IAAM,WAAW,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,OAAO;QACL,CAAC,EAAE,IAAI;QACP,CAAC,EAAE,IAAI;QACP,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,KAAK,EAAE,IAAI,GAAG,IAAI;QAClB,MAAM,EAAE,IAAI,GAAG,IAAI;KACpB,CAAC;AACJ,CAAC;AAnBD,4CAmBC;AAED,SAAgB,WAAW,CAAC,KAAe,EAAE,MAAc,EAAE,CAAS,EAAE,CAAS;IAC/E,IAAI,MAAM,EAAE;QACV,IAAM,MAAM,GAAG,gBAAgB,CAAC,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;QACrE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KACzB;AACH,CAAC;AALD,kCAKC;AAED,SAAgB,cAAc,CAAC,KAAe,EAAE,CAAS,EAAE,CAAS;IAClE,IAAM,eAAe,GAAG,oBAAoB,CAAC,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,CAAC;IACvD,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;AACxC,CAAC;AAHD,wCAGC"}