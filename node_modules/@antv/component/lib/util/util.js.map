{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/util/util.ts"], "names": [], "mappings": ";;;AACA,mCAA4D;AAG5D,SAAgB,aAAa,CAAC,OAA0B;IACtD,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,MAAM,GAAG,CAAC,CAAC;IAEf,IAAI,eAAQ,CAAC,OAAO,CAAC,EAAE;QACrB,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;KACvC;SAAM,IAAI,cAAO,CAAC,OAAO,CAAC,EAAE;QAC3B,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACjB,KAAK,GAAG,CAAC,YAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACrD,MAAM,GAAG,CAAC,YAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtD,IAAI,GAAG,CAAC,YAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;KAChD;IAED,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACpC,CAAC;AAhBD,sCAgBC;AAED,SAAgB,QAAQ,CAAC,SAAsB;IAC7C,IAAM,QAAQ,GAAG,SAAS,CAAC,UAAU,CAAC;IACtC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC/B,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QACpC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;KACpC;AACH,CAAC;AAND,4BAMC;AAED,SAAgB,QAAQ,CAAC,QAAQ,EAAE,KAAK;IACtC,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,YAAU,KAAK,YAAS,CAAC,CAAC,CAAC;AAC1E,CAAC;AAFD,4BAEC;AAED,SAAgB,YAAY,CAAC,MAAc;IACjC,IAAA,KAAK,GAAU,MAAM,MAAhB,EAAE,GAAG,GAAK,MAAM,IAAX,CAAY;IAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACtC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACtC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACtC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACtC,OAAO;QACL,CAAC,EAAE,IAAI;QACP,CAAC,EAAE,IAAI;QACP,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,KAAK,EAAE,IAAI,GAAG,IAAI;QAClB,MAAM,EAAE,IAAI,GAAG,IAAI;KACpB,CAAC;AACJ,CAAC;AAhBD,oCAgBC;AAED,SAAgB,YAAY,CAAC,MAAe;IAC1C,IAAM,EAAE,GAAa,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,CAAC,EAAP,CAAO,CAAC,CAAC;IACpD,IAAM,EAAE,GAAa,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,CAAC,EAAP,CAAO,CAAC,CAAC;IACpD,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,EAAE,CAAC,CAAC;IAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,EAAE,CAAC,CAAC;IAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,EAAE,CAAC,CAAC;IAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,EAAE,CAAC,CAAC;IAC7B,OAAO;QACL,CAAC,EAAE,IAAI;QACP,CAAC,EAAE,IAAI;QACP,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,KAAK,EAAE,IAAI,GAAG,IAAI;QAClB,MAAM,EAAE,IAAI,GAAG,IAAI;KACpB,CAAC;AACJ,CAAC;AAjBD,oCAiBC;AAED,SAAgB,UAAU,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc;IAC5E,IAAM,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC;IACvB,IAAM,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC;IAExB,OAAO;QACL,CAAC,GAAA;QACD,CAAC,GAAA;QACD,KAAK,OAAA;QACL,MAAM,QAAA;QACN,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QACP,cAAc;QACd,iCAAiC;QACjC,4BAA4B;QAC5B,qDAAqD;QACrD,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QAC5B,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;KAC7B,CAAC;AACJ,CAAC;AAlBD,gCAkBC;AAED,SAAgB,iBAAiB,CAAC,GAAW,EAAE,GAAW,EAAE,OAAe;IACzE,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,CAAC;AAC7C,CAAC;AAFD,8CAEC;AAED,SAAgB,cAAc,CAAC,MAAa,EAAE,MAAc,EAAE,KAAa;IACzE,OAAO;QACL,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM;QACtC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM;KACvC,CAAC;AACJ,CAAC;AALD,wCAKC;AAED,SAAgB,QAAQ,CAAC,EAAS,EAAE,EAAS;IAC3C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACvB,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACvB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACtC,CAAC;AAJD,4BAIC;AAEY,QAAA,IAAI,GAAG,UAAC,QAAgB;IACnC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;QACzB,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF;;;GAGG;AACU,QAAA,IAAI,GAAG,UAAC,CAAS,EAAE,CAAS,EAAE,CAAyB;IAAzB,kBAAA,EAAA,aAAI,MAAM,CAAC,OAAO,EAAI,GAAG,CAAA;IAClE,OAAA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AAA7E,CAA6E,CAAC;AAEhF,SAAgB,aAAa,CAAC,IAAU,EAAE,IAAU;IAClD,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC;AAC1D,CAAC;AAND,sCAMC;AAED,SAAgB,SAAS,CAAC,IAAU,EAAE,IAAU;IAC9C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC;AAC1D,CAAC;AAND,8BAMC;AAED,SAAgB,eAAe,CAAC,OAAiB;IAC/C,IAAM,SAAS,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IACpC,IAAM,QAAQ,GAAG,SAAS,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;IAClD,IAAI,IAAI,CAAC;IACT,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE;QACtB,WAAW;QACX,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;SAAM;QACL,IAAI,MAAI,GAAG,QAAQ,CAAC;QACpB,IAAI,MAAI,GAAG,CAAC,QAAQ,CAAC;QACrB,IAAI,MAAI,GAAG,QAAQ,CAAC;QACpB,IAAI,MAAI,GAAG,CAAC,QAAQ,CAAC;QACrB,IAAM,QAAQ,GAAI,OAAkB,CAAC,WAAW,EAAE,CAAC;QACnD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,WAAI,CAAC,QAAQ,EAAE,UAAC,KAAe;gBAC7B,IAAI,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;oBACxB,kBAAkB;oBAClB,IAAI,KAAK,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;wBACzD,OAAO,IAAI,CAAC;qBACb;oBACD,IAAM,GAAG,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;oBACnC,WAAW;oBACX,IAAM,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC7D,IAAM,UAAU,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;oBAChE,IAAM,QAAQ,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC9D,IAAM,WAAW,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;oBACjE,WAAW;oBACX,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjF,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjF,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjF,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEjF,IAAI,OAAO,GAAG,MAAI,EAAE;wBAClB,MAAI,GAAG,OAAO,CAAC;qBAChB;oBAED,IAAI,OAAO,GAAG,MAAI,EAAE;wBAClB,MAAI,GAAG,OAAO,CAAC;qBAChB;oBAED,IAAI,OAAO,GAAG,MAAI,EAAE;wBAClB,MAAI,GAAG,OAAO,CAAC;qBAChB;oBAED,IAAI,OAAO,GAAG,MAAI,EAAE;wBAClB,MAAI,GAAG,OAAO,CAAC;qBAChB;iBACF;YACH,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,MAAI,GAAG,CAAC,CAAC;YACT,MAAI,GAAG,CAAC,CAAC;YACT,MAAI,GAAG,CAAC,CAAC;YACT,MAAI,GAAG,CAAC,CAAC;SACV;QACD,IAAI,GAAG,UAAU,CAAC,MAAI,EAAE,MAAI,EAAE,MAAI,GAAG,MAAI,EAAE,MAAI,GAAG,MAAI,CAAC,CAAC;KACzD;IACD,IAAI,QAAQ,EAAE;QACZ,OAAO,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACtC;SAAM;QACL,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AA9DD,0CA8DC;AAED,SAAgB,UAAU,CAAC,OAAiB,EAAE,UAAoB;IAChE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE;QAC/C,aAAa;QACb,OAAO;KACR;IACD,IAAM,YAAY,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;IAC1C,IAAI,CAAC,YAAY,EAAE;QACjB,qBAAqB;QACrB,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU;QACjC,OAAO;KACR;IACD,IAAM,OAAO,GAAG;QACd,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC;QAC9B,KAAK,EAAE,YAAY,CAAC,IAAI,EAAE;KAC3B,CAAC;IACF,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC3B,CAAC;AAhBD,gCAgBC;AAED,SAAgB,IAAI,CAAC,MAAM;IACzB,OAAU,MAAM,OAAI,CAAC;AACvB,CAAC;AAFD,oBAEC;AAED,SAAgB,YAAY,CAAC,KAAY,EAAE,GAAU,EAAE,QAAgB,EAAE,MAAc;IACrF,IAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACxC,IAAM,aAAa,GAAG,MAAM,GAAG,UAAU,CAAC,CAAC,sBAAsB;IACjE,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,QAAQ,KAAK,OAAO,EAAE;QACxB,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC;KAC7B;SAAM,IAAI,QAAQ,KAAK,KAAK,EAAE;QAC7B,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC;KAC7B;IACD,OAAO;QACL,CAAC,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;QAC7C,CAAC,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;KAC9C,CAAC;AACJ,CAAC;AAbD,oCAaC"}