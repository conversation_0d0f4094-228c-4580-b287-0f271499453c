import { IGroup } from '@antv/g-base';
export declare function getDefault(): typeof fixedAngle;
/**
 * 固定角度旋转文本
 * @param  {boolean} isVertical  是否垂直方向
 * @param  {IGroup}  labelsGroup 文本的 group
 * @param  {number}  limitLength 限定长度
 * @param  {number}  customRotate 自定义旋转角度
 * @return {boolean}             是否发生了旋转
 */
export declare function fixedAngle(isVertical: boolean, labelsGroup: IGroup, limitLength: number, customRotate?: number): boolean;
/**
 * 非固定角度旋转文本
 * @param  {boolean} isVertical  是否垂直方向
 * @param  {IGroup}  labelsGroup 文本的 group
 * @param  {number}  limitLength 限定长度
 * @return {boolean}             是否发生了旋转
 */
export declare function unfixedAngle(isVertical: boolean, labelsGroup: IGroup, limitLength: number): boolean;
