{"version": 3, "file": "region-filter.js", "sourceRoot": "", "sources": ["../../src/annotation/region-filter.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AACzC,OAAO,cAAc,MAAM,6BAA6B,CAAC;AAGzD,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAE5C;IAAqC,0CAAyC;IAA9E;;IA8DA,CAAC;IA7DC;;;OAGG;IACI,8CAAa,GAApB;QACE,IAAM,GAAG,GAAG,iBAAM,aAAa,WAAE,CAAC;QAClC,6BACK,GAAG,KACN,IAAI,EAAE,YAAY,EAClB,IAAI,EAAE,cAAc,EACpB,YAAY,EAAE,QAAQ,EACtB,KAAK,EAAE,IAAI,EACX,GAAG,EAAE,IAAI,EACT,KAAK,EAAE,IAAI,EACX,KAAK,EAAE,EAAE,IACT;IACJ,CAAC;IAES,4CAAW,GAArB,UAAsB,KAAa;QAAnC,iBAkCC;QAjCC,IAAM,KAAK,GAAU,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvC,IAAM,GAAG,GAAU,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAEnC,sBAAsB;QACtB,IAAM,KAAK,GAAW,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YACzC,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;YACtC,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;QAEH,4BAA4B;QAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,UAAC,KAAa,EAAE,QAAgB;YACvD,IAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC/B,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAClC,KAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC7B,KAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACnB,EAAE,EAAE,KAAI,CAAC,YAAY,CAAC,WAAS,IAAI,SAAI,QAAU,CAAC;gBAClD,OAAO,EAAE,KAAK;gBACd,IAAI,MAAA;gBACJ,KAAK,OAAA;aACN,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,UAAU;QACV,IAAM,QAAQ,GAAG,YAAY,CAAC,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC;QAC9C,KAAK,CAAC,OAAO,CAAC;YACZ,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE;gBACL,CAAC,EAAE,QAAQ,CAAC,IAAI;gBAChB,CAAC,EAAE,QAAQ,CAAC,IAAI;gBAChB,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,MAAM,EAAE,QAAQ,CAAC,MAAM;aACxB;SACF,CAAC,CAAC;IACL,CAAC;IAEO,iDAAgB,GAAxB,UAAyB,IAAgB;QACvC,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;SACpC;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IACzC,CAAC;IACH,6BAAC;AAAD,CAAC,AA9DD,CAAqC,cAAc,GA8DlD;AAED,eAAe,sBAAsB,CAAC"}