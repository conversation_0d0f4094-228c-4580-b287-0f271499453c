import { BBox, Point } from '../types';
interface OutSides {
    left: boolean;
    right: boolean;
    top: boolean;
    bottom: boolean;
}
export declare function getOutSides(x: number, y: number, width: number, height: number, limitBox: BBox): OutSides;
export declare function getPointByPosition(x: number, y: number, offset: number, width: number, height: number, position: string): Point;
export declare function getAlignPoint(x: number, y: number, offset: number, width: number, height: number, position: string, limitBox?: BBox): Point;
export {};
