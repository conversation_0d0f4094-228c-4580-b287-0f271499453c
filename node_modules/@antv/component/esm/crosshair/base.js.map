{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/crosshair/base.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,cAAc,MAAM,6BAA6B,CAAC;AAEzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAClD,OAAO,KAAK,MAAM,eAAe,CAAC;AAClC,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAE7C;IAAoF,iCAAc;IAAlG;;IA8HA,CAAC;IA7HQ,qCAAa,GAApB;QACE,IAAM,GAAG,GAAG,iBAAM,aAAa,WAAE,CAAC;QAClC,6BACK,GAAG,KACN,IAAI,EAAE,WAAW,EACjB,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,EAAE,EACR,IAAI,EAAE,IAAI,EACV,cAAc,EAAE,EAAE,EAClB,OAAO,EAAE,KAAK,EACd,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,KAAK,EAAE;wBACL,SAAS,EAAE,CAAC;wBACZ,MAAM,EAAE,KAAK,CAAC,SAAS;qBACxB;iBACF;gBACD,IAAI,EAAE;oBACJ,QAAQ,EAAE,OAAO;oBACjB,MAAM,EAAE,EAAE;oBACV,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,IAAI;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,KAAK,CAAC,SAAS;wBACrB,SAAS,EAAE,QAAQ;wBACnB,YAAY,EAAE,QAAQ;wBACtB,UAAU,EAAE,KAAK,CAAC,UAAU;qBAC7B;iBACF;gBACD,cAAc,EAAE;oBACd,OAAO,EAAE,CAAC;oBACV,KAAK,EAAE;wBACL,MAAM,EAAE,KAAK,CAAC,SAAS;qBACxB;iBACF;aACF,IACD;IACJ,CAAC;IAES,mCAAW,GAArB,UAAsB,KAAa;QACjC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACpB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACxB;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACpB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACvB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;SAC9B;IACH,CAAC;IAWS,kCAAU,GAApB,UAAqB,KAAa;QAChC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACtB,IAAA,KAAK,GAA0B,IAAI,MAA9B,EAAE,UAAU,GAAc,IAAI,WAAlB,EAAE,OAAO,GAAK,IAAI,QAAT,CAAU;QAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YACnB,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACtC,IAAI,MAAM,GAAG,IAAI,CAAC;YAClB,IAAI,UAAU,EAAE;gBACd,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpC,MAAM,GAAG,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;aAC7C;YACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACnB,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,gBAAgB;gBACtB,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC7B,KAAK,iCACA,SAAS,KACZ,IAAI,EAAE,OAAO,EACb,MAAM,QAAA,KACH,KAAK,CACT;aACF,CAAC,CAAC;SACJ;IACH,CAAC;IAIS,kCAAU,GAApB,UAAqB,KAAa;QAChC,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAChC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YACnB,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,gBAAgB;YACtB,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YAC7B,KAAK,aACH,IAAI,MAAA,IACD,KAAK,CACT;SACF,CAAC,CAAC;IACL,CAAC;IAED,UAAU;IACF,wCAAgB,GAAxB,UAAyB,KAAa;QACpC,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACzC,IAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;QACjD,IAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAElD,IAAI,cAAc,IAAI,SAAS,EAAE;YAC/B,IAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YACrC,IAAM,OAAO,GAAG,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB;YAC5E,IAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;YACnC,IAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBAC3C,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,2BAA2B;gBACjC,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC;gBACxC,KAAK,aACH,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAC1B,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAC1B,KAAK,EAAE,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAC/C,MAAM,EAAE,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EACjD,MAAM,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAC7B,KAAK,CACT;aACF,CAAC,CAAC;YACH,eAAe,CAAC,MAAM,EAAE,CAAC;SAC1B;IACH,CAAC;IACH,oBAAC;AAAD,CAAC,AA9HD,CAAoF,cAAc,GA8HjG;AAED,eAAe,aAAa,CAAC"}