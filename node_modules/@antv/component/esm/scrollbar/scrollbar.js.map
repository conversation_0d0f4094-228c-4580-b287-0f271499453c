{"version": 3, "file": "scrollbar.js", "sourceRoot": "", "sources": ["../../src/scrollbar/scrollbar.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAElD,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AACvD,OAAO,cAAc,MAAM,6BAA6B,CAAC;AAgBzD,IAAM,aAAa,GAAmB;IACpC,UAAU,EAAE,eAAe;IAC3B,UAAU,EAAE,kBAAkB;IAC9B,IAAI,EAAE,CAAC;IACP,OAAO,EAAE,OAAO;CACjB,CAAC;AAEF,MAAM,CAAC,IAAM,aAAa,GAAmB;IAC3C,OAAO;IACP,OAAO,EAAE,aAAa;IACtB,eAAe;IACf,KAAK,EAAE;QACL,UAAU,EAAE,iBAAiB;KAC9B;CACF,CAAC;AAyBF;IAA+B,6BAA4B;IAA3D;QAAA,qEAySC;QArSS,iBAAW,GAAG,IAAI,CAAC;QAmJnB,kBAAY,GAAG,UAAC,QAAiB,IAAK,OAAA,UAAC,CAAQ;YACrD,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,CAAC,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YACjC,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YACjF,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YAEjF,YAAY;YACZ,KAAI,CAAC,QAAQ,GAAG,KAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;YAE1D,KAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC,EAV6C,CAU7C,CAAC;QAEM,oBAAc,GAAG;YACvB,IAAM,YAAY,GAAG,KAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,IAAI,KAAI,CAAC,QAAQ,EAAE;gBACjB,MAAM,GAAG;oBACP,gBAAgB,CAAC,YAAY,EAAE,WAAW,EAAE,KAAI,CAAC,WAAW,CAAC;oBAC7D,gBAAgB,CAAC,YAAY,EAAE,UAAU,EAAE,KAAI,CAAC,SAAS,CAAC;oBAC1D,gBAAgB,CAAC,YAAY,EAAE,aAAa,EAAE,KAAI,CAAC,SAAS,CAAC;iBAC9D,CAAC;aACH;iBAAM;gBACL,MAAM,GAAG;oBACP,gBAAgB,CAAC,YAAY,EAAE,WAAW,EAAE,KAAI,CAAC,WAAW,CAAC;oBAC7D,gBAAgB,CAAC,YAAY,EAAE,SAAS,EAAE,KAAI,CAAC,SAAS,CAAC;oBACzD,2CAA2C;oBAC3C,gBAAgB,CAAC,YAAY,EAAE,YAAY,EAAE,KAAI,CAAC,SAAS,CAAC;iBAC7D,CAAC;aACH;YACD,KAAI,CAAC,WAAW,GAAG;gBACjB,MAAM,CAAC,OAAO,CAAC,UAAC,CAAC;oBACf,CAAC,CAAC,MAAM,EAAE,CAAC;gBACb,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,YAAY;QACZ,4BAA4B;QACpB,iBAAW,GAAG,UAAC,CAAa;YAC5B,IAAA,KAAgC,KAAI,CAAC,GAAG,EAAtC,YAAY,kBAAA,EAAE,WAAW,iBAAa,CAAC;YAC/C,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,IAAM,OAAO,GAAG,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YACxE,IAAM,OAAO,GAAG,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YACxE,UAAU;YACV,IAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;YAChD,wEAAwE;YACxE,IAAM,IAAI,GAAG,MAAM,GAAG,KAAI,CAAC,QAAQ,CAAC;YACpC,eAAe;YACf,KAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;YAEvB,KAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC;QAEM,eAAS,GAAG,UAAC,CAAQ;YAC3B,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,KAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC,CAAC;QAEF,mBAAmB;QACX,kBAAY,GAAG,UAAC,CAAQ;YACxB,IAAA,KAAmC,KAAI,CAAC,GAAG,EAAzC,YAAY,kBAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,QAAQ,cAAa,CAAC;YAClD,IAAM,YAAY,GAAG,KAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,IAAM,IAAI,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;YAC1C,IAAA,OAAO,GAAc,CAAC,QAAf,EAAE,OAAO,GAAK,CAAC,QAAN,CAAO;YAC/B,IAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;YAE7G,IAAM,SAAS,GAAG,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC7C,KAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC,CAAC;QAEM,sBAAgB,GAAG;YACjB,IAAA,UAAU,GAAK,KAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,WAAzB,CAA0B;YAC5C,KAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC7D,KAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC,CAAC;QAEM,qBAAe,GAAG;YAChB,IAAA,UAAU,GAAK,KAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,WAA3B,CAA4B;YAC9C,KAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC7D,KAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC,CAAC;;IAiEJ,CAAC;IAlSQ,4BAAQ,GAAf,UAAgB,GAAW,EAAE,GAAW;QACtC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QAC1B,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACjC,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC3C,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAChD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SACzB;IACH,CAAC;IAEM,4BAAQ,GAAf;QACE,IAAM,GAAG,GAAW,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAM,GAAG,GAAW,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAE9C,OAAO,EAAE,GAAG,KAAA,EAAE,GAAG,KAAA,EAAE,CAAC;IACtB,CAAC;IAEM,4BAAQ,GAAf,UAAgB,KAAa;QAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC;YACV,WAAW,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC;SAChG,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;YAC/B,aAAa,eAAA;YACb,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;SACvB,CAAC,CAAC;IACL,CAAC;IAEM,4BAAQ,GAAf;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9F,CAAC;IAEM,iCAAa,GAApB;QACE,IAAM,GAAG,GAAG,iBAAM,aAAa,WAAE,CAAC;QAClC,6BACK,GAAG,KACN,IAAI,EAAE,WAAW,EACjB,YAAY,EAAE,IAAI,EAClB,WAAW,EAAE,EAAE,EACf,WAAW,EAAE,CAAC,EACd,KAAK,EAAE,aAAa,IACpB;IACJ,CAAC;IAES,+BAAW,GAArB,UAAsB,KAAa;QACjC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAES,+BAAW,GAArB;QACE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACpC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YAChB,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;SACjB,CAAC,CAAC;IACL,CAAC;IAES,6BAAS,GAAnB;QACE,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,cAAc;IACN,oCAAgB,GAAxB,UAAyB,KAAa;QAC9B,IAAA,KAAwC,IAAI,CAAC,GAAG,EAA9C,QAAQ,cAAA,EAAE,aAAuB,EAAvB,KAAK,mBAAG,EAAE,OAAO,EAAE,EAAE,EAAE,KAAa,CAAC;QACjD,IAAA,KAA2C,OAAO,CAAC,EAAE,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,OAAO,EAAlF,OAAO,aAAA,EAAE,UAAU,gBAAA,EAAQ,SAAS,UAA8C,CAAC;QAC3F,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QAE9C,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACpC,CAAC,CAAC;gBACE,EAAE,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC;gBAChB,EAAE,EAAE,IAAI,GAAG,CAAC;gBACZ,EAAE,EAAE,QAAQ,GAAG,IAAI,GAAG,CAAC;gBACvB,EAAE,EAAE,IAAI,GAAG,CAAC;gBACZ,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,UAAU;gBAClB,OAAO,SAAA;aACR;YACH,CAAC,CAAC;gBACE,EAAE,EAAE,IAAI,GAAG,CAAC;gBACZ,EAAE,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC;gBAChB,EAAE,EAAE,IAAI,GAAG,CAAC;gBACZ,EAAE,EAAE,QAAQ,GAAG,IAAI,GAAG,CAAC;gBACvB,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,UAAU;gBAClB,OAAO,SAAA;aACR,CAAC;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YAC1B,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YAC9B,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,MAAM;YACZ,KAAK,OAAA;SACN,CAAC,CAAC;IACL,CAAC;IAED,cAAc;IACN,oCAAgB,GAAxB,UAAyB,KAAa;QAC9B,IAAA,KAAmC,IAAI,CAAC,GAAG,EAAzC,WAAW,iBAAA,EAAE,QAAQ,cAAA,EAAE,KAAK,WAAa,CAAC;QAC5C,IAAA,KAA2C,OAAO,CAAC,EAAE,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,OAAO,EAA5E,SAAS,UAAA,EAAE,OAAO,aAAA,EAAE,UAAU,gBAA8C,CAAC;QAC3F,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QAE9C,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACpC,CAAC,CAAC;gBACE,EAAE,EAAE,WAAW,GAAG,IAAI,GAAG,CAAC;gBAC1B,EAAE,EAAE,IAAI,GAAG,CAAC;gBACZ,EAAE,EAAE,WAAW,GAAG,QAAQ,GAAG,IAAI,GAAG,CAAC;gBACrC,EAAE,EAAE,IAAI,GAAG,CAAC;gBACZ,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,UAAU;gBAClB,OAAO,SAAA;gBACP,MAAM,EAAE,SAAS;aAClB;YACH,CAAC,CAAC;gBACE,EAAE,EAAE,IAAI,GAAG,CAAC;gBACZ,EAAE,EAAE,WAAW,GAAG,IAAI,GAAG,CAAC;gBAC1B,EAAE,EAAE,IAAI,GAAG,CAAC;gBACZ,EAAE,EAAE,WAAW,GAAG,QAAQ,GAAG,IAAI,GAAG,CAAC;gBACrC,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,UAAU;gBAClB,OAAO,SAAA;gBACP,MAAM,EAAE,SAAS;aAClB,CAAC;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YAC1B,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YAC9B,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,MAAM;YACZ,KAAK,OAAA;SACN,CAAC,CAAC;IACL,CAAC;IAEO,8BAAU,GAAlB;QACE,IAAM,KAAK,GAAW,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;QAChD,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAEpC,KAAK,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QAChD,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAErC,IAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9D,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1C,IAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9D,UAAU,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClD,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAClD,CAAC;IAqFO,mCAAe,GAAvB;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACxC,IAAM,MAAM,GAAG,SAAS,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEpD,OAAO,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC;IAEO,iCAAa,GAArB,UAAsB,MAAc;QAC5B,IAAA,KAAyB,IAAI,CAAC,GAAG,EAA/B,QAAQ,cAAA,EAAE,QAAQ,cAAa,CAAC;QACxC,IAAI,SAAS,GAAG,MAAM,CAAC;QACvB,IAAI,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAE;YAChC,SAAS,GAAG,QAAQ,GAAG,QAAQ,CAAC;SACjC;aAAM,IAAI,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAE;YACvC,SAAS,GAAG,CAAC,CAAC;SACf;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,wBAAI,GAAZ;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACxC,IAAM,MAAM,GAAG,SAAS,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEpD,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,IAAI,EAAE,CAAC;SACf;IACH,CAAC;IAEO,qCAAiB,GAAzB,UAA0B,MAAc;QAChC,IAAA,KAAgD,IAAI,CAAC,GAAG,EAAtD,WAAW,iBAAA,EAAE,YAAY,kBAAA,EAAE,QAAQ,cAAA,EAAE,IAAI,UAAa,CAAC;QAC/D,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,SAAS,KAAK,WAAW,EAAE;YAC7B,2BAA2B;YAC3B,OAAO;SACR;QACD,IAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAErD,IAAI,YAAY,EAAE;YAChB,UAAU,CAAC,IAAI,CAAC;gBACd,EAAE,EAAE,SAAS,GAAG,IAAI,GAAG,CAAC;gBACxB,EAAE,EAAE,SAAS,GAAG,QAAQ,GAAG,IAAI,GAAG,CAAC;aACpC,CAAC,CAAC;SACJ;aAAM;YACL,UAAU,CAAC,IAAI,CAAC;gBACd,EAAE,EAAE,SAAS,GAAG,IAAI,GAAG,CAAC;gBACxB,EAAE,EAAE,SAAS,GAAG,QAAQ,GAAG,IAAI,GAAG,CAAC;aACpC,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAEO,oCAAgB,GAAxB,UAAyB,MAAc;QAC/B,IAAA,KAAqD,IAAI,CAAC,GAAG,EAA9C,aAAa,iBAAA,EAAE,QAAQ,cAAA,EAAE,QAAQ,cAAa,CAAC;QACpE,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,MAAM,CAAC;QAC9B,OAAO;QACP,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,WAAW,EAAE,MAAM;YACnB,KAAK,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SACnD,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;YAC/B,aAAa,eAAA;YACb,KAAK,EAAE,MAAM;SACd,CAAC,CAAC;IACL,CAAC;IACH,gBAAC;AAAD,CAAC,AAzSD,CAA+B,cAAc,GAyS5C"}