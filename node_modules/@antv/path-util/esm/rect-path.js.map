{"version": 3, "file": "rect-path.js", "sourceRoot": "", "sources": ["../src/rect-path.ts"], "names": [], "mappings": "AAIA,MAAM,CAAC,OAAO,UAAU,QAAQ,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAU;IACrF,IAAI,CAAC,EAAE;QACL,OAAO;YACL,CAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAE;YACrB,CAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAE;YACrB,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;YAC5B,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE;YACrB,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;YAC7B,CAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAE;YACrB,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE;YAC9B,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE;YACrB,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE;YAC7B,CAAE,GAAG,CAAE;SACR,CAAC;KACH;IACD,OAAO;QACL,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAE;QACb,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAE;QACb,CAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAE;QACb,CAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;QACd,CAAE,GAAG,CAAE;KACR,CAAC;IACF,uCAAuC;AACzC,CAAC"}