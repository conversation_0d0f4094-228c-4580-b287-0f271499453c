{"version": 3, "file": "fill-path-by-diff.js", "sourceRoot": "", "sources": ["../src/fill-path-by-diff.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAOpC,SAAS,UAAU,CAAC,GAAW,EAAE,GAAW,EAAE,MAAc;IAC1D,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,IAAI,GAAG,GAAG,MAAM,CAAC;IACjB,IAAI,GAAG,GAAG,GAAG,EAAE;QACb,GAAG,GAAG,GAAG,CAAC;QACV,IAAI,GAAG,KAAK,CAAC;KACd;IACD,IAAI,GAAG,GAAG,GAAG,EAAE;QACb,GAAG,GAAG,GAAG,CAAC;QACV,IAAI,GAAG,KAAK,CAAC;KACd;IACD,OAAO;QACL,IAAI,MAAA;QACJ,GAAG,KAAA;KACJ,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,IAAM,mBAAmB,GAAG,UAAU,MAAc,EAAE,MAAc;IAClE,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;IAChC,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;IAChC,IAAI,aAAa,EAAE,aAAa,CAAC;IACjC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE;QACtC,OAAO,IAAI,CAAC;KACb;IACD,IAAM,IAAI,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;KACzB;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;KACzB;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE;QACnC,aAAa,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE;YACnC,aAAa,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC9B,IAAI,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE;gBACzC,IAAI,GAAG,CAAC,CAAC;aACV;iBAAM;gBACL,IAAI,GAAG,CAAC,CAAC;aACV;YACD,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACnC,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACnC,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;YAC7C,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;SAC3C;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,OAAO,UAAU,cAAc,CAAC,MAAc,EAAE,MAAc;IACnE,IAAM,UAAU,GAAG,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACvD,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;IAC9B,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;IAChC,IAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,yBAAyB;IACzB,aAAa;IACb,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,KAAK,SAAS,EAAE;QAClD,uBAAuB;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAC/B,MAAM,GAAG,CAAC,CAAC;YACX,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE;gBACvC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE;oBAC9B,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;oBAC3B,MAAM,GAAG,CAAC,CAAC;iBACZ;aACF;YACD,KAAK,GAAG,MAAM,CAAC;YACf,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE;gBAC7B,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;aACjE;SACF;QACD,kBAAkB;QAClB,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5C,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACzB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,EAAE;gBAC7B,aAAa;gBACb,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACnD;iBAAM;gBACL,aAAa;gBACb,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACzB;SACF;KACF;IAED,aAAa;IACb,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;IAC1B,IAAI,SAAS,GAAG,SAAS,EAAE;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBACxE,aAAa;gBACb,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;aACxD;iBAAM;gBACL,aAAa;gBACb,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;aACpC;SACF;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}