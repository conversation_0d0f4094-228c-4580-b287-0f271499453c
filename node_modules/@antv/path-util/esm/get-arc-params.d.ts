/**
 * 判断两个点是否重合，点坐标的格式为 [x, y]
 * @param {Array} point1 第一个点
 * @param {Array} point2 第二个点
 */
export declare function isSamePoint(point1: any, point2: any): boolean;
export default function getArcParams(startPoint: any, params: any): {
    cx: number;
    cy: number;
    rx: any;
    ry: any;
    startAngle: number;
    endAngle: number;
    xRotation: number;
    arcFlag: any;
    sweepFlag: any;
};
