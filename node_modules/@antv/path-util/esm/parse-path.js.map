{"version": 3, "file": "parse-path.js", "sourceRoot": "", "sources": ["../src/parse-path.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAC;AAEpD,IAAM,SAAS,GAAG,gCAAgC,CAAC;AACnD,IAAM,QAAQ,GAAG,YAAY,CAAC;AAE9B,SAAS,SAAS,CAAC,CAAS;IAC1B,IAAI,IAAI,GAAG,CAAC,IAAK,EAAwB,CAAC;IAC1C,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QACjB,OAAO,IAAI,CAAC;KACb;IAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,EAAE,UAAC,IAAI,EAAE,KAAK;YACrB,aAAa;YACb,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC5B,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC9B,aAAa;gBACb,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,aAAa;gBACb,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;aACf;YACD,aAAa;YACb,IAAI,CAAC,IAAI,EAAE,UAAU,GAAG,EAAE,CAAC;gBACzB,aAAa;gBACb,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACf,aAAa;oBACb,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;iBAChB;YACH,CAAC,CAAC,CAAC;YACH,aAAa;YACb,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAED,eAAe,SAAS,CAAC"}