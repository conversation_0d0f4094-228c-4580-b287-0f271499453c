import Attribute from './attributes/base';
import Color from './attributes/color';
import Opacity from './attributes/opacity';
import Position from './attributes/position';
import Shape from './attributes/shape';
import Size from './attributes/size';
import { getAttribute, registerAttribute } from './factory';
export { registerAttribute, getAttribute, Attribute, Color, Opacity, Position, Shape, Size, };
export * from './interface';
