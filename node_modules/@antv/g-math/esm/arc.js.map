{"version": 3, "file": "arc.js", "sourceRoot": "", "sources": ["../src/arc.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,QAAQ,CAAC;AACzC,OAAO,OAAO,MAAM,WAAW,CAAC;AAGhC,QAAQ;AACR,SAAS,aAAa,CACpB,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,SAAiB,EACjB,UAAkB,EAClB,QAAgB,EAChB,KAAa;IAEb,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtG,CAAC;AAED,QAAQ;AACR,SAAS,aAAa,CACpB,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,SAAiB,EACjB,UAAkB,EAClB,QAAgB,EAChB,KAAa;IAEb,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtG,CAAC;AAED,QAAQ;AACR,SAAS,QAAQ,CAAC,EAAU,EAAE,EAAU,EAAE,SAAiB;IACzD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;AACrD,CAAC;AAED,QAAQ;AACR,SAAS,QAAQ,CAAC,EAAU,EAAE,EAAU,EAAE,SAAiB;IACzD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC;AAED,aAAa;AACb,SAAS,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,SAAiB,EAAE,KAAa;IAC3F,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACtG,CAAC;AAED,aAAa;AACb,SAAS,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,SAAiB,EAAE,KAAa;IAC3F,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACtG,CAAC;AAED,aAAa;AACb,SAAS,QAAQ,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU;IAC9D,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3C,gBAAgB;IAChB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC/C,CAAC;AAED,aAAa;AACb,SAAS,QAAQ,CAAC,EAAU,EAAE,EAAU,EAAE,KAAa;IACrD,OAAO;QACL,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;QACvB,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;KACxB,CAAC;AACJ,CAAC;AAED,KAAK;AACL,SAAS,MAAM,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa;IACjD,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC5B,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAChD,CAAC;AAED,eAAe;IACb;;;;;;;;;;OAUG;IACH,GAAG,EAAH,UAAI,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,SAAiB,EAAE,UAAkB,EAAE,QAAgB;QACzG,IAAM,IAAI,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;QACzC,IAAI,IAAI,GAAG,QAAQ,CAAC;QACpB,IAAI,IAAI,GAAG,CAAC,QAAQ,CAAC;QACrB,IAAM,EAAE,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE;YACzD,IAAM,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC;YACxB,IAAI,UAAU,GAAG,QAAQ,EAAE;gBACzB,IAAI,UAAU,GAAG,MAAM,IAAI,MAAM,GAAG,QAAQ,EAAE;oBAC5C,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACjB;aACF;iBAAM;gBACL,IAAI,QAAQ,GAAG,MAAM,IAAI,MAAM,GAAG,UAAU,EAAE;oBAC5C,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACjB;aACF;SACF;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAM,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,GAAG,IAAI,EAAE;gBACZ,IAAI,GAAG,CAAC,CAAC;aACV;YACD,IAAI,CAAC,GAAG,IAAI,EAAE;gBACZ,IAAI,GAAG,CAAC,CAAC;aACV;SACF;QAED,IAAM,IAAI,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;QACzC,IAAI,IAAI,GAAG,QAAQ,CAAC;QACpB,IAAI,IAAI,GAAG,CAAC,QAAQ,CAAC;QACrB,IAAM,EAAE,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE;YACzD,IAAM,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC;YACxB,IAAI,UAAU,GAAG,QAAQ,EAAE;gBACzB,IAAI,UAAU,GAAG,MAAM,IAAI,MAAM,GAAG,QAAQ,EAAE;oBAC5C,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACjB;aACF;iBAAM;gBACL,IAAI,QAAQ,GAAG,MAAM,IAAI,MAAM,GAAG,UAAU,EAAE;oBAC5C,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACjB;aACF;SACF;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAM,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,GAAG,IAAI,EAAE;gBACZ,IAAI,GAAG,CAAC,CAAC;aACV;YACD,IAAI,CAAC,GAAG,IAAI,EAAE;gBACZ,IAAI,GAAG,CAAC,CAAC;aACV;SACF;QAED,OAAO;YACL,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,IAAI;YACP,KAAK,EAAE,IAAI,GAAG,IAAI;YAClB,MAAM,EAAE,IAAI,GAAG,IAAI;SACpB,CAAC;IACJ,CAAC;IACD;;;;;;;;;;OAUG;IACH,MAAM,EAAN,UAAO,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,SAAiB,EAAE,UAAkB,EAAE,QAAgB,IAAG,CAAC;IAClH;;;;;;;;;;;;OAYG;IACH,YAAY,EAAZ,UACE,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,SAAiB,EACjB,UAAkB,EAClB,QAAgB,EAChB,EAAU,EACV,EAAU;QAEV,gCAAgC;QAChC,IAAM,cAAc,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;QACrD,IAAA,EAAE,GAAQ,cAAc,GAAtB,EAAE,EAAE,GAAI,cAAc,GAAlB,CAAmB;QAChC,cAAc;QACd,IAAI,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC/D,aAAa;QACb,IAAM,KAAK,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;QACjE,UAAU;QACV,IAAI,KAAK,GAAG,UAAU,EAAE;YACtB,SAAS;YACT,aAAa,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,CAAC;SAC9C;aAAM,IAAI,KAAK,GAAG,QAAQ,EAAE;YAC3B,SAAS;YACT,aAAa,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;SAC5C;QACD,oBAAoB;QACpB,IAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACnE,OAAO;YACL,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE;YACjB,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE;SAClB,CAAC;IACJ,CAAC;IACD,aAAa,EAAb,UACE,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,SAAiB,EACjB,UAAkB,EAClB,QAAgB,EAChB,EAAU,EACV,EAAU;QAEV,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC/D,OAAO,QAAQ,CAAC,YAAY,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAC1D,CAAC;IACD,OAAO,EAAP,UACE,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,SAAiB,EACjB,UAAkB,EAClB,QAAgB,EAChB,CAAS;QAET,IAAM,KAAK,GAAG,CAAC,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;QACvD,OAAO;YACL,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC;YACxC,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC;SACzC,CAAC;IACJ,CAAC;IACD,YAAY,EAAZ,UACE,EAAU,EACV,EAAU,EACV,EAAU,EACV,EAAU,EACV,SAAiB,EACjB,UAAkB,EAClB,QAAgB,EAChB,CAAS;QAET,IAAM,KAAK,GAAG,CAAC,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;QACvD,IAAM,EAAE,GAAG,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACjF,IAAM,EAAE,GAAG,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACjF,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;CACF,CAAC"}