import { BBox, Point } from './types';
declare const _default: {
    /**
     * 计算线段的包围盒
     * @param {number} x1 起始点 x
     * @param {number} y1 起始点 y
     * @param {number} x2 结束点 x
     * @param {number} y2 结束点 y
     * @return {object} 包围盒对象
     */
    box(x1: number, y1: number, x2: number, y2: number): BBox;
    /**
     * 线段的长度
     * @param {number} x1 起始点 x
     * @param {number} y1 起始点 y
     * @param {number} x2 结束点 x
     * @param {number} y2 结束点 y
     * @return {number} 距离
     */
    length(x1: number, y1: number, x2: number, y2: number): number;
    /**
     * 根据比例获取点
     * @param {number} x1 起始点 x
     * @param {number} y1 起始点 y
     * @param {number} x2 结束点 x
     * @param {number} y2 结束点 y
     * @param {number} t 指定比例
     * @return {object} 包含 x, y 的点
     */
    pointAt(x1: number, y1: number, x2: number, y2: number, t: number): Point;
    /**
     * 点到线段的距离
     * @param {number} x1 起始点 x
     * @param {number} y1 起始点 y
     * @param {number} x2 结束点 x
     * @param {number} y2 结束点 y
     * @param {number} x  测试点 x
     * @param {number} y  测试点 y
     * @return {number} 距离
     */
    pointDistance(x1: number, y1: number, x2: number, y2: number, x: number, y: number): number;
    /**
     * 点到直线的距离，而不是点到线段的距离
     * @param {number} x1 起始点 x
     * @param {number} y1 起始点 y
     * @param {number} x2 结束点 x
     * @param {number} y2 结束点 y
     * @param {number} x  测试点 x
     * @param {number} y  测试点 y
     * @return {number} 距离
     */
    pointToLine(x1: number, y1: number, x2: number, y2: number, x: number, y: number): number;
    /**
     * 线段的角度
     * @param {number} x1 起始点 x
     * @param {number} y1 起始点 y
     * @param {number} x2 结束点 x
     * @param {number} y2 结束点 y
     * @return {number} 导数
     */
    tangentAngle(x1: number, y1: number, x2: number, y2: number): number;
};
export default _default;
