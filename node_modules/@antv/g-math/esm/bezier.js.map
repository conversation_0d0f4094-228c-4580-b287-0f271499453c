{"version": 3, "file": "bezier.js", "sourceRoot": "", "sources": ["../src/bezier.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAGlC,IAAM,OAAO,GAAG,MAAM,CAAC;AACvB;;;;;;;GAOG;AACH,MAAM,UAAU,YAAY,CAC1B,IAAc,EACd,IAAc,EACd,CAAS,EACT,CAAS,EACT,SAAuC,EACvC,MAAe;IAEf,IAAI,CAAS,CAAC;IACd,IAAI,CAAC,GAAG,QAAQ,CAAC;IACjB,IAAM,EAAE,GAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE9B,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,MAAM,IAAI,MAAM,GAAG,GAAG,EAAE;QAC1B,MAAM,GAAG,MAAM,GAAG,EAAE,CAAC;KACtB;IACD,IAAM,YAAY,GAAG,CAAC,GAAG,MAAM,CAAC;IAEhC,IAAI,QAAQ,GAAG,YAAY,GAAG,EAAE,CAAC;IAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE;QAChC,IAAM,EAAE,GAAG,CAAC,GAAG,YAAY,CAAC;QAC5B,IAAM,EAAE,GAAe,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5G,IAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,EAAE,GAAG,CAAC,EAAE;YACV,CAAC,GAAG,EAAE,CAAC;YACP,CAAC,GAAG,EAAE,CAAC;SACR;KACF;IACD,OAAO;IACP,IAAI,CAAC,KAAK,CAAC,EAAE;QACX,OAAO;YACL,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;YACV,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;SACX,CAAC;KACH;IACD,IAAI,CAAC,KAAK,CAAC,EAAE;QACX,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,OAAO;YACL,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YAClB,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACnB,CAAC;KACH;IACD,CAAC,GAAG,QAAQ,CAAC;IAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QAC3B,IAAI,QAAQ,GAAG,OAAO,EAAE;YACtB,MAAM;SACP;QAED,IAAM,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC;QAC1B,IAAM,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC;QAE1B,IAAM,EAAE,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpG,IAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;YACvB,CAAC,GAAG,IAAI,CAAC;YACT,CAAC,GAAG,EAAE,CAAC;SACR;aAAM;YACL,IAAM,EAAE,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACpG,IAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;gBACvB,CAAC,GAAG,IAAI,CAAC;gBACT,CAAC,GAAG,EAAE,CAAC;aACR;iBAAM;gBACL,QAAQ,IAAI,GAAG,CAAC;aACjB;SACF;KACF;IAED,OAAO;QACL,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC;AACJ,CAAC;AAED,8EAA8E;AAC9E,MAAM,UAAU,UAAU,CAAC,IAAc,EAAE,IAAc;IACvD,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QAC9B,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACpC,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACpC,WAAW,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;KAC7C;IACD,OAAO,WAAW,GAAG,CAAC,CAAC;AACzB,CAAC"}