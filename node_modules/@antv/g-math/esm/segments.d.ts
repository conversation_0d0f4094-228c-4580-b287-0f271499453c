import { Point, PointTuple } from './types';
export declare function lengthOfSegment(points: PointTuple[]): number;
/**
 * 按照比例在数据片段中获取点
 * @param {array} points 点的集合
 * @param {number} t 百分比 0-1
 * @return {object} 点的坐标
 */
export declare function pointAtSegments(points: PointTuple[], t: number): Point;
/**
 * 按照比例在数据片段中获取切线的角度
 * @param {array} points 点的集合
 * @param {number} t 百分比 0-1
 */
export declare function angleAtSegments(points: PointTuple[], t: number): number;
export declare function distanceAtSegment(points: PointTuple[], x: number, y: number): number;
