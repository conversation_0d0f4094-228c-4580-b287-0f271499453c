import { Point } from './types';
declare function cubicAt(p0: number, p1: number, p2: number, p3: number, t: number): number;
declare function extrema(p0: number, p1: number, p2: number, p3: number): any[];
declare const _default: {
    extrema: typeof extrema;
    box(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, x4: number, y4: number): import("./types").BBox;
    length(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, x4: number, y4: number): any;
    nearestPoint(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, x4: number, y4: number, x0: number, y0: number, length?: number): Point;
    pointDistance(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, x4: number, y4: number, x0: number, y0: number, length?: number): number;
    interpolationAt: typeof cubicAt;
    pointAt(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, x4: number, y4: number, t: number): Point;
    divide(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, x4: number, y4: number, t: number): number[][];
    tangentAngle(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, x4: number, y4: number, t: number): number;
};
export default _default;
