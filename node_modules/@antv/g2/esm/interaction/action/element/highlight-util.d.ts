import { View } from '../../../chart';
import Element from '../../../geometry/element/';
/**
 * @ignore
 * 清理 highlight 效果
 * @param view View 或者 Chart
 */
export declare function clearHighlight(view: View): void;
type MatchCallback = (el: Element) => boolean;
/**
 * @ignore
 * 设置多个元素的 highlight
 * @param elements 元素集合
 * @param callback 设置回调函数
 * @param enable 设置或者取消
 */
export declare function setHighlightBy(elements: Element[], callback: MatchCallback, enable: boolean): void;
export {};
