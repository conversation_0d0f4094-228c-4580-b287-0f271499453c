import Element from '../../../geometry/element/';
import Highlight, { Callback } from './highlight';
/**
 * Highlight x
 * @ignore
 */
declare class HighlightX extends Highlight {
    protected setElementHighlight(el: Element, callback: Callback): void;
    protected setStateByElement(element: Element, enable: boolean): void;
    /**
     * 切换状态
     */
    toggle(): void;
}
export default HighlightX;
