import Element from '../../../geometry/element/';
import ElementSingleState from './single-state';
/**
 * @ignore
 * 单个 Element Highlight 的 Action
 */
declare class ElementSingleHighlight extends ElementSingleState {
    protected stateName: string;
    /**
     * Element Highlight
     */
    highlight(): void;
    protected setElementState(element: Element, enable: boolean): void;
    clear(): void;
}
export default ElementSingleHighlight;
