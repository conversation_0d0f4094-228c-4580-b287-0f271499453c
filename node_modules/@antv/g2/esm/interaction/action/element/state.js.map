{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/element/state.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAG9C,OAAO,EACL,iBAAiB,EACjB,mBAAmB,EACnB,WAAW,EACX,eAAe,EACf,eAAe,EACf,eAAe,EACf,MAAM,GACP,MAAM,SAAS,CAAC;AACjB,OAAO,SAAS,MAAM,cAAc,CAAC;AAErC,SAAS,OAAO,CAAC,KAAK;IACpB,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,MAAM,CAAC,CAAC;AAClD,CAAC;AAED;;;;GAIG;AACH;IAA2B,gCAAS;IAApC;QAAA,qEAoFC;QAnFW,0BAAoB,GAAG,CAAC,WAAW,CAAC,CAAC;;IAmFjD,CAAC;IAjFC,aAAa;IACL,mCAAY,GAApB,UAAqB,IAAc,EAAE,IAAI;QACvC,IAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACzC,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAC,KAAK;YACnC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,iBAAiB;IACT,0CAAmB,GAA3B,UAA4B,SAAS,EAAE,IAAc,EAAE,MAAe;QACpE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,IAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC7D,CAAC;IAED,yBAAyB;IACf,wCAAiB,GAA3B,UAA4B,OAAgB,EAAE,MAAe;QAC3D,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC;IAED,0BAA0B;IAChB,iCAAU,GAApB,UAAqB,OAAgB,EAAE,KAAa,EAAE,IAAc;QAClE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,KAAK,GAAG,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3C,IAAM,KAAK,GAAG,eAAe,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9C,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7D,CAAC;IAES,6CAAsB,GAAhC,UAAiC,QAAmB,EAAE,KAAa,EAAE,IAAc,EAAE,MAAe;QAApG,iBAMC;QALC,IAAI,CAAC,QAAQ,EAAE,UAAC,EAAE;YAChB,IAAI,KAAI,CAAC,UAAU,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE;gBACpC,EAAE,CAAC,QAAQ,CAAC,KAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACrC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,eAAe;IACL,qCAAc,GAAxB,UAAyB,MAAe;QACtC,IAAM,OAAO,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,OAAO,EAAE;YACX,mBAAmB;YACnB,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBACjC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aACzC;SACF;aAAM;YACL,WAAW;YACX,IAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzD,kBAAkB;YAClB,IAAI,MAAM,CAAC,cAAc,CAAC,EAAE;gBAClB,IAAA,IAAI,GAAgB,cAAc,KAA9B,EAAE,SAAS,GAAK,cAAc,UAAnB,CAAoB;gBAC3C,IAAI,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;oBAC5D,IAAM,OAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;oBACxC,OAAO;oBACP,IAAI,OAAK,IAAI,OAAK,CAAC,SAAS,IAAI,OAAK,CAAC,OAAO,IAAI,OAAO,CAAC,OAAK,CAAC,SAAS,CAAC,KAAK,OAAO,CAAC,OAAK,CAAC,OAAO,CAAC,EAAE;wBACpG,OAAO;qBACR;oBACD,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;iBACnD;aACF;SACF;IACH,CAAC;IAED;;OAEG;IACI,6BAAM,GAAb;QACE,IAAM,OAAO,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,OAAO,EAAE;YACX,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC;SAC1C;IACH,CAAC;IAED;;OAEG;IACI,4BAAK,GAAZ;QACE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IACH,mBAAC;AAAD,CAAC,AApFD,CAA2B,SAAS,GAoFnC;AAED,eAAe,YAAY,CAAC", "sourcesContent": ["import { each, isNil, get } from '@antv/util';\nimport { ListItem } from '../../../dependents';\nimport Element from '../../../geometry/element/';\nimport {\n  getCurrentElement,\n  getDelegationObject,\n  getElements,\n  getElementValue,\n  getScaleByField,\n  isElementChange,\n  isList,\n} from '../util';\nimport StateBase from './state-base';\n\nfunction getItem(shape) {\n  return get(shape.get('delegateObject'), 'item');\n}\n\n/**\n * 状态量 Action 的基类，允许多个 Element 同时拥有某个状态\n * @class\n * @ignore\n */\nclass ElementState extends StateBase {\n  protected ignoreListItemStates = ['unchecked'];\n\n  // 是否忽略触发的列表项\n  private isItemIgnore(item: ListItem, list) {\n    const states = this.ignoreListItemStates;\n    const filtered = states.filter((state) => {\n      return list.hasState(item, state);\n    });\n    return !!filtered.length;\n  }\n\n  // 设置由组件选项导致的状态变化\n  private setStateByComponent(component, item: ListItem, enable: boolean) {\n    const view = this.context.view;\n    const field = component.get('field');\n    const elements = getElements(view);\n    this.setElementsStateByItem(elements, field, item, enable);\n  }\n\n  // 处理触发源由 element 导致的状态变化\n  protected setStateByElement(element: Element, enable: boolean) {\n    this.setElementState(element, enable);\n  }\n\n  /** 组件的选项是否同 element 匹配 */\n  protected isMathItem(element: Element, field: string, item: ListItem) {\n    const view = this.context.view;\n    const scale = getScaleByField(view, field);\n    const value = getElementValue(element, field);\n    return !isNil(value) && item.name === scale.getText(value);\n  }\n\n  protected setElementsStateByItem(elements: Element[], field: string, item: ListItem, enable: boolean) {\n    each(elements, (el) => {\n      if (this.isMathItem(el, field, item)) {\n        el.setState(this.stateName, enable);\n      }\n    });\n  }\n\n  /** 设置状态是否激活 */\n  protected setStateEnable(enable: boolean) {\n    const element = getCurrentElement(this.context);\n    if (element) {\n      // 触发源由于 element 导致\n      if (isElementChange(this.context)) {\n        this.setStateByElement(element, enable);\n      }\n    } else {\n      // 触发源由组件导致\n      const delegateObject = getDelegationObject(this.context);\n      // 如果触发源时列表，图例、坐标轴\n      if (isList(delegateObject)) {\n        const { item, component } = delegateObject;\n        if (item && component && !this.isItemIgnore(item, component)) {\n          const event = this.context.event.gEvent;\n          // 防止闪烁\n          if (event && event.fromShape && event.toShape && getItem(event.fromShape) === getItem(event.toShape)) {\n            return;\n          }\n          this.setStateByComponent(component, item, enable);\n        }\n      }\n    }\n  }\n\n  /**\n   * 切换状态\n   */\n  public toggle() {\n    const element = getCurrentElement(this.context);\n    if (element) {\n      const hasState = element.hasState(this.stateName);\n      this.setElementState(element, !hasState);\n    }\n  }\n\n  /**\n   * 取消当前时间影响的状态\n   */\n  public reset() {\n    this.setStateEnable(false);\n  }\n}\n\nexport default ElementState;\n"]}