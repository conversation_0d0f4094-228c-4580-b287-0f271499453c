{"version": 3, "file": "state-base.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/element/state-base.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAElC,OAAO,MAAM,MAAM,SAAS,CAAC;AAC7B,OAAO,EAAE,kBAAkB,EAAE,MAAM,SAAS,CAAC;AAE7C;;;;;GAKG;AACH;IAAiC,6BAAM;IAAvC;QAAA,qEAiDC;QAhDC;;WAEG;QACO,eAAS,GAAW,EAAE,CAAC;;IA6CnC,CAAC;IArCC;;;OAGG;IACO,4BAAQ,GAAlB,UAAmB,OAAgB;QACjC,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IACD;;;OAGG;IACO,mCAAe,GAAzB,UAA0B,OAAgB,EAAE,MAAe;QACzD,OAAO;QACP,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,4BAAQ,GAAf;QACE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,yBAAK,GAAZ;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAES,kCAAc,GAAxB,UAAyB,IAAI;QAA7B,iBAKC;QAJC,IAAM,QAAQ,GAAG,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,EAAE,UAAC,EAAW;YACzB,KAAI,CAAC,eAAe,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IACH,gBAAC;AAAD,CAAC,AAjDD,CAAiC,MAAM,GAiDtC;AAED,eAAe,SAAS,CAAC", "sourcesContent": ["import { each } from '@antv/util';\nimport Element from '../../../geometry/element/';\nimport Action from '../base';\nimport { getElementsByState } from '../util';\n\n/**\n * 状态量 Action 的基类\n * @abstract\n * @class\n * @ignore\n */\nabstract class StateBase extends Action {\n  /**\n   * 状态名称\n   */\n  protected stateName: string = '';\n\n  /**\n   * 设置状态是否激活\n   * @param enable 状态值\n   */\n  protected abstract setStateEnable(enable: boolean);\n\n  /**\n   * 是否具有某个状态\n   * @param element 图表 Element 元素\n   */\n  protected hasState(element: Element): boolean {\n    return element.hasState(this.stateName);\n  }\n  /**\n   * 设置状态激活\n   * @param enable 状态值\n   */\n  protected setElementState(element: Element, enable: boolean) {\n    // 防止闪烁\n    element.setState(this.stateName, enable);\n  }\n\n  /**\n   * 设置状态\n   */\n  public setState() {\n    this.setStateEnable(true);\n  }\n\n  /**\n   * 清除所有 Element 的状态\n   */\n  public clear() {\n    const view = this.context.view;\n    this.clearViewState(view);\n  }\n\n  protected clearViewState(view) {\n    const elements = getElementsByState(view, this.stateName);\n    each(elements, (el: Element) => {\n      this.setElementState(el, false);\n    });\n  }\n}\n\nexport default StateBase;\n"]}