export { ICanvas, IElement, IGroup, IShape, PathCommand, BBox, Point, ShapeAttrs, Event, AbstractGroup, AbstractShape, } from '@antv/g-base';
export type IG = any;
export { registerAdjust, getAdjust, Adjust } from '@antv/adjust';
export { getAttribute, Attribute } from '@antv/attr';
export { Color } from '@antv/attr';
export { getCoordinate, registerCoordinate, Coordinate, CoordinateCfg } from '@antv/coord';
export { getScale, registerScale, Scale, ScaleConfig, Tick } from '@antv/scale';
import { Annotation, Axis, Component, Crosshair, Grid, GroupComponent, HtmlComponent, Legend, Slider, Tooltip, Scrollbar } from '@antv/component';
export { CategoryLegendCfg, CircleAxisCfg, LineAxisCfg, GroupComponentCfg, ListItem, AxisLineCfg, AxisTickLineCfg, AxisSubTickLineCfg, AxisTitleCfg, AxisLabelCfg, GridLineCfg, LegendMarkerCfg, LegendTitleCfg, LegendBackgroundCfg, LegendItemNameCfg, LegendItemValueCfg, LegendPageNavigatorCfg, ContinueLegendCfg, ContinueLegendTrackCfg, ContinueLegendRailCfg, ContinueLegendLabelCfg, ContinueLegendHandlerCfg, CrosshairLineCfg, CrosshairTextCfg, CrosshairTextBackgroundCfg, SliderCfg, TrendCfg, EnhancedTextCfg, LineAnnotationTextCfg, IComponent, IList, } from '@antv/component';
export { HtmlComponent, GroupComponent, Component, Crosshair };
export { Annotation };
declare const LineAxis: typeof Axis.Line, CircleAxis: typeof Axis.Circle;
export { LineAxis, CircleAxis };
declare const LineGrid: typeof Grid.Line, CircleGrid: typeof Grid.Circle;
export { LineGrid, CircleGrid };
declare const CategoryLegend: typeof Legend.Category, ContinuousLegend: typeof Legend.Continuous;
export { CategoryLegend, ContinuousLegend };
declare const HtmlTooltip: typeof Tooltip.Html;
export { HtmlTooltip };
export { Slider };
export { Scrollbar };
