import { Coordinate } from '@antv/coord';
import { IGroup } from '@antv/g-base';
/** label 的必要配置 */
type Cfg = {
    data: any;
    origin: any;
    animateCfg: any;
    coordinate: Coordinate;
};
/**
 * @desc 更新 label (目前没有根据 id 索引，还是会存在一点小问题的，只能根据 idx 索引)
 * @done shape 属性更新
 * @done shape delete
 * @done shape append
 *
 * @param fromShape old labelShape
 * @param toShape new labelShape
 * @param cfg
 */
export declare function updateLabel(fromShape: IGroup, toShape: IGroup, cfg: Cfg): void;
export {};
