{"version": 3, "file": "update-label.js", "sourceRoot": "", "sources": ["../../src/component/update-label.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AACvC,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAUnD;;;;;;;;;GASG;AACH,MAAM,UAAU,WAAW,CAAC,SAAiB,EAAE,OAAe,EAAE,GAAQ;IAC9D,IAAA,IAAI,GAAqC,GAAG,KAAxC,EAAE,MAAM,GAA6B,GAAG,OAAhC,EAAE,UAAU,GAAiB,GAAG,WAApB,EAAE,UAAU,GAAK,GAAG,WAAR,CAAS;IACrD,IAAM,gBAAgB,GAAG,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAEnD,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5B,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAChC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IACxC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IACxC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;IAEjD,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,GAAG;QACrD,IAAM,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC,GAAG,CAAW,CAAC;QACvD,IAAI,CAAC,OAAO,EAAE;YACZ,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACjC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACxB;aAAM;YACL,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC5B,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAChC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YACxC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAExC,IAAM,QAAQ,GAAG,eAAe,CAAC,SAAmB,EAAE,OAAO,CAAC,CAAC;YAC/D,IAAI,gBAAgB,EAAE;gBACpB,SAAS,CAAC,SAAmB,EAAE,gBAAgB,EAAE;oBAC/C,OAAO,EAAE,QAAQ;oBACjB,UAAU,YAAA;iBACX,CAAC,CAAC;aACJ;iBAAM;gBACL,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1B;YACD,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE;gBACrB,WAAW,CAAC,SAAgB,EAAE,OAAc,EAAE,GAAG,CAAC,CAAC;aACpD;SACF;IACH,CAAC,CAAC,CAAC;IAEH,SAAS;IACT,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,UAAC,KAAK,EAAE,GAAG;QACrC,IAAI,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,IAAI,GAAG,IAAI,SAAS,CAAC,QAAQ,EAAE,EAAE;YACnE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBACpB,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aACtB;SACF;IACH,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { Coordinate } from '@antv/coord';\nimport { IGroup, IShape } from '@antv/g-base';\nimport { each, get, isArray } from '@antv/util';\nimport { doAnimate } from '../animate';\nimport { getReplaceAttrs } from '../util/graphics';\n\n/** label 的必要配置 */\ntype Cfg = {\n  data: any;\n  origin: any;\n  animateCfg: any;\n  coordinate: Coordinate;\n};\n\n/**\n * @desc 更新 label (目前没有根据 id 索引，还是会存在一点小问题的，只能根据 idx 索引)\n * @done shape 属性更新\n * @done shape delete\n * @done shape append\n *\n * @param fromShape old labelShape\n * @param toShape new labelShape\n * @param cfg\n */\nexport function updateLabel(fromShape: IGroup, toShape: IGroup, cfg: Cfg): void {\n  const { data, origin, animateCfg, coordinate } = cfg;\n  const updateAnimateCfg = get(animateCfg, 'update');\n\n  fromShape.set('data', data);\n  fromShape.set('origin', origin);\n  fromShape.set('animateCfg', animateCfg);\n  fromShape.set('coordinate', coordinate);\n  fromShape.set('visible', toShape.get('visible'));\n\n  (fromShape.getChildren() || []).forEach((fromChild, idx) => {\n    const toChild = toShape.getChildByIndex(idx) as IShape;\n    if (!toChild) {\n      fromShape.removeChild(fromChild);\n      fromChild.remove(true);\n    } else {\n      fromChild.set('data', data);\n      fromChild.set('origin', origin);\n      fromChild.set('animateCfg', animateCfg);\n      fromChild.set('coordinate', coordinate);\n\n      const newAttrs = getReplaceAttrs(fromChild as IShape, toChild);\n      if (updateAnimateCfg) {\n        doAnimate(fromChild as IShape, updateAnimateCfg, {\n          toAttrs: newAttrs,\n          coordinate,\n        });\n      } else {\n        fromChild.attr(newAttrs);\n      }\n      if (toChild.isGroup()) {\n        updateLabel(fromChild as any, toChild as any, cfg);\n      }\n    }\n  });\n\n  // append\n  each(toShape.getChildren(), (child, idx) => {\n    if (isArray(fromShape.getChildren()) && idx >= fromShape.getCount()) {\n      if (!child.destroyed) {\n        fromShape.add(child);\n      }\n    }\n  });\n}\n"]}