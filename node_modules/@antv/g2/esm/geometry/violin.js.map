{"version": 3, "file": "violin.js", "sourceRoot": "", "sources": ["../../src/geometry/violin.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AACjC,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAE3C,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,QAAQ,MAAM,QAAQ,CAAC;AAC9B,+BAA+B;AAC/B,OAAO,gBAAgB,CAAC;AAExB;;;GAGG;AACH;IAAoC,0BAA0B;IAA9D;QAAA,qEAkDC;QAjDiB,UAAI,GAAW,QAAQ,CAAC;QACxB,eAAS,GAAW,QAAQ,CAAC;QACnC,oBAAc,GAAY,IAAI,CAAC;;IA+C3C,CAAC;IA3CC;;;;OAIG;IACO,qCAAoB,GAA9B,UAA+B,MAAa;QAC1C,IAAM,GAAG,GAAG,iBAAM,oBAAoB,YAAC,MAAM,CAAC,CAAC;QAE/C,oBAAoB;QACpB,IAAI,IAAI,CAAC;QACT,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,QAAQ,EAAE;YACZ,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM;YACN,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACnC,IAAM,eAAe,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;YACxD,IAAI,GAAG,IAAI,GAAG,eAAe,CAAC;SAC/B;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;aACzC;YACD,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;SACzB;QACD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QACzD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACO,+BAAc,GAAxB;QACU,IAAA,eAAe,GAAK,IAAI,gBAAT,CAAU;QACjC,IAAM,SAAS,GAAG,eAAe,CAAC,IAAI;YACpC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,IAAI,CAAC,UAAU;gBACjB,CAAC,CAAC,IAAI,CAAC,UAAU;gBACjB,CAAC,CAAC,MAAM,CAAC;QACX,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,cAAc;QACd,OAAO,eAAe,CAAC,IAAI,CAAC;QAC5B,iBAAM,cAAc,WAAE,CAAC;IACzB,CAAC;IACH,aAAC;AAAD,CAAC,AAlDD,CAAoC,QAAQ,GAkD3C", "sourcesContent": ["import { get } from '@antv/util';\nimport { FIELD_ORIGIN } from '../constant';\nimport { Datum, ViolinShapePoint } from '../interface';\nimport { getXDimensionLength } from '../util/coordinate';\nimport { getDefaultSize } from './util/shape-size';\nimport Geometry from './base';\n/** 引入 Path 对应的 ShapeFactory */\nimport './shape/violin';\n\n/**\n * Violin 几何标记。\n * 用于绘制小提琴图。\n */\nexport default class Violin extends Geometry<ViolinShapePoint> {\n  public readonly type: string = 'violin';\n  public readonly shapeType: string = 'violin';\n  protected generatePoints: boolean = true;\n  /** size 私有映射字段 */\n  private _sizeField: string;\n\n  /**\n   * 获取 Shape 的关键点数据。\n   * @param record\n   * @returns\n   */\n  protected createShapePointsCfg(record: Datum) {\n    const cfg = super.createShapePointsCfg(record);\n\n    // 计算每个 shape 的 size\n    let size;\n    const sizeAttr = this.getAttribute('size');\n    if (sizeAttr) {\n      size = this.getAttributeValues(sizeAttr, record)[0];\n      // 归一化\n      const coordinate = this.coordinate;\n      const coordinateWidth = getXDimensionLength(coordinate);\n      size = size / coordinateWidth;\n    } else {\n      if (!this.defaultSize) {\n        this.defaultSize = getDefaultSize(this);\n      }\n      size = this.defaultSize;\n    }\n    cfg.size = size;\n    cfg._size = get(record[FIELD_ORIGIN], [this._sizeField]);\n    return cfg;\n  }\n\n  /**\n   * @override\n   */\n  protected initAttributes() {\n    const { attributeOption } = this;\n    const sizeField = attributeOption.size\n      ? attributeOption.size.fields[0]\n      : this._sizeField\n      ? this._sizeField\n      : 'size';\n    this._sizeField = sizeField;\n    // fixme 干啥要删掉\n    delete attributeOption.size;\n    super.initAttributes();\n  }\n}\n"]}