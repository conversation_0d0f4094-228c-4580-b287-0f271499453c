import { IGroup, IShape, BBox } from '../../../../dependents';
import { LabelItem } from '../../interface';
/**
 * point-adjust-position layout 的配置类型
 */
export interface PointAdjustPositionLayoutCfg {
    offset?: number;
}
/**
 * 适用于 point geometry 的数据标签位置自动调整布局方法
 * @param items
 * @param labels
 * @param shapes
 * @param region
 * @param cfg
 */
export declare function pathAdjustPosition(items: LabelItem[], labels: IGroup[], shapes: IShape[] | IGroup[], region: BBox, cfg: PointAdjustPositionLayoutCfg): void;
