{"version": 3, "file": "arc-2-cubic.js", "sourceRoot": "", "sources": ["../../src/process/arc-2-cubic.ts"], "names": [], "mappings": "AAAA,IAAM,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAA;AAEvB,IAAM,YAAY,GAAG,UAAC,EAAkC,EAAE,EAAU,EAAE,EAAU,EAAE,MAAc,EAAE,MAAc,EAAE,OAAe,EAAE,OAAe;QAA1H,CAAC,OAAA,EAAE,CAAC,OAAA;IAC1B,CAAC,IAAI,EAAE,CAAA;IACP,CAAC,IAAI,EAAE,CAAA;IAEP,IAAM,EAAE,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAA;IAClC,IAAM,EAAE,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAA;IAElC,OAAO;QACL,CAAC,EAAE,EAAE,GAAG,OAAO;QACf,CAAC,EAAE,EAAE,GAAG,OAAO;KAChB,CAAA;AACH,CAAC,CAAA;AAED,IAAM,aAAa,GAAG,UAAC,IAAY,EAAE,IAAY;IAC/C,4CAA4C;IAC5C,qEAAqE;IACrE,IAAM,CAAC,GAAG,IAAI,KAAK,kBAAkB;QACnC,CAAC,CAAC,cAAc;QAChB,CAAC,CAAC,IAAI,KAAK,CAAC,kBAAkB;YAC5B,CAAC,CAAC,CAAC,cAAc;YACjB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;IAEhC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACzB,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACzB,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAA;IAChC,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAA;IAEhC,OAAO;QACL;YACE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;YACd,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;SACf;QACD;YACE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;YACd,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;SACf;QACD;YACE,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,EAAE;SACN;KACF,CAAA;AACH,CAAC,CAAA;AAED,IAAM,WAAW,GAAG,UAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU;IACjE,IAAM,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAE7C,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAA;IAE3B,IAAI,GAAG,GAAG,CAAC,EAAE;QACX,GAAG,GAAG,CAAC,CAAA;KACR;IAED,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE;QACZ,GAAG,GAAG,CAAC,CAAC,CAAA;KACT;IAED,OAAO,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAC9B,CAAC,CAAA;AAED,IAAM,YAAY,GAAG,UACnB,EAAO,EACP,EAAO,EACP,EAAO,EACP,EAAO,EACP,EAAU,EACV,EAAU,EACV,YAAoB,EACpB,SAAiB,EACjB,MAAc,EACd,MAAc,EACd,GAAW,EACX,GAAW;IAEX,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IAC5B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;IAC9B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;IAE9B,IAAI,QAAQ,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA;IAE9D,IAAI,QAAQ,GAAG,CAAC,EAAE;QAChB,QAAQ,GAAG,CAAC,CAAA;KACb;IAED,QAAQ,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA;IAC3C,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAEtE,IAAM,QAAQ,GAAG,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;IACzC,IAAM,QAAQ,GAAG,QAAQ,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;IAE1C,IAAM,OAAO,GAAG,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,QAAQ,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAA;IACrE,IAAM,OAAO,GAAG,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,QAAQ,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAA;IAErE,IAAM,GAAG,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAA;IACjC,IAAM,GAAG,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAA;IACjC,IAAM,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAA;IAClC,IAAM,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAA;IAElC,IAAI,IAAI,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;IACtC,IAAI,IAAI,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;IAE1C,IAAI,SAAS,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE;QAC/B,IAAI,IAAI,GAAG,CAAA;KACZ;IAED,IAAI,SAAS,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE;QAC/B,IAAI,IAAI,GAAG,CAAA;KACZ;IAED,OAAO,CAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAE,CAAA;AACzC,CAAC,CAAA;AAED,IAAM,WAAW,GAAG,UAAC,EAUpB;QATC,EAAE,QAAA,EACF,EAAE,QAAA,EACF,EAAE,QAAA,EACF,EAAE,QAAA,EACF,EAAE,QAAA,EACF,EAAE,QAAA,EACF,qBAAiB,EAAjB,aAAa,mBAAG,CAAC,KAAA,EACjB,oBAAgB,EAAhB,YAAY,mBAAG,CAAC,KAAA,EAChB,iBAAa,EAAb,SAAS,mBAAG,CAAC,KAAA;IAEb,IAAM,MAAM,GAAG,EAAE,CAAA;IAEjB,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;QACxB,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;KACvD;IAED,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC,CAAA;IAClD,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC,CAAA;IAElD,IAAM,GAAG,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAA;IAC3D,IAAM,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAA;IAE5D,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;QAC1B,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;KACvD;IAED,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IACjB,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAEjB,IAAM,MAAM,GACV,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IAEpC,IAAI,MAAM,GAAG,CAAC,EAAE;QACd,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACvB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;KACxB;IAEG,IAAA,KAAmC,YAAY,CACjD,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,YAAY,EACZ,SAAS,EACT,MAAM,EACN,MAAM,EACN,GAAG,EACH,GAAG,CACJ,EAbK,OAAO,QAAA,EAAE,OAAO,QAAA,EAAE,IAAI,QAAA,EAAE,IAAI,QAajC,CAAA;IAED,4DAA4D;IAC5D,2EAA2E;IAC3E,4EAA4E;IAC5E,8DAA8D;IAC9D,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;IACtC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,SAAS,EAAE;QACrC,KAAK,GAAG,GAAG,CAAA;KACZ;IAED,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;IAE9C,IAAI,IAAI,QAAQ,CAAA;IAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;QACjC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QACtC,IAAI,IAAI,IAAI,CAAA;KACb;IAED,OAAO,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK;QACf,IAAA,KAAmB,YAAY,CAAC,KAAK,CAAE,CAAC,CAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,EAAhF,EAAE,OAAA,EAAK,EAAE,OAAuE,CAAA;QACrF,IAAA,KAAmB,YAAY,CAAC,KAAK,CAAE,CAAC,CAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,EAAhF,EAAE,OAAA,EAAK,EAAE,OAAuE,CAAA;QACrF,IAAA,KAAW,YAAY,CAAC,KAAK,CAAE,CAAC,CAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,EAA3E,CAAC,OAAA,EAAE,CAAC,OAAuE,CAAA;QAEnF,OAAO,EAAE,EAAE,IAAA,EAAE,EAAE,IAAA,EAAE,EAAE,IAAA,EAAE,EAAE,IAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAA;IACjC,CAAC,CAAC,CAAA;AACJ,CAAC,CAAA;AAED,MAAM,UAAU,UAAU,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,KAAa,EAAE,GAAW,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU;IACvI,IAAM,MAAM,GAAG,WAAW,CAAC;QACzB,EAAE,EAAE,EAAE;QACN,EAAE,EAAE,EAAE;QACN,EAAE,EAAE,EAAE;QACN,EAAE,EAAE,EAAE;QACN,EAAE,IAAA;QACF,EAAE,IAAA;QACF,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,GAAG;QACjB,SAAS,EAAE,EAAE;KACd,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,GAAG;QACrB,IAAA,EAAE,GAAuB,GAAG,GAA1B,EAAE,EAAE,GAAmB,GAAG,GAAtB,EAAE,EAAE,GAAe,GAAG,GAAlB,EAAE,EAAE,GAAW,GAAG,GAAd,EAAE,CAAC,GAAQ,GAAG,EAAX,EAAE,CAAC,GAAK,GAAG,EAAR,CAAS;QACrC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC,EAAE,EAAc,CAAC,CAAC;AACrB,CAAC"}