{"version": 3, "file": "path-2-segments.js", "sourceRoot": "", "sources": ["../src/path-2-segments.ts"], "names": [], "mappings": ";;AAAA,mDAA4C;AAC5C,mDAA+C;AAC/C,2CAAqC;AAErC,MAAM;AACN,SAAS,UAAU,CAAC,KAAK,EAAE,MAAM;IAC/B,OAAO,CAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC;AACpF,CAAC;AAED,SAAwB,WAAW,CAAC,IAAI;IACtC,IAAI,GAAG,IAAA,oBAAS,EAAC,IAAI,CAAC,CAAC;IACvB,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,IAAI,YAAY,GAAG,IAAI,CAAC,CAAC,OAAO;IAChC,IAAI,UAAU,GAAG,IAAI,CAAC,CAAC,gBAAgB;IACvC,IAAI,cAAc,GAAG,IAAI,CAAC,CAAC,iBAAiB;IAC5C,IAAI,uBAAuB,GAAG,CAAC,CAAC,CAAC,gBAAgB;IACjD,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QAC9B,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,UAAU,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACzB,IAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC1B,mBAAmB;QACnB,IAAM,OAAO,GAAG;YACd,OAAO,SAAA;YACP,QAAQ,EAAE,YAAY;YACtB,MAAM,QAAA;YACN,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,IAAI;SACjB,CAAC;QACF,QAAQ,OAAO,EAAE;YACf,KAAK,GAAG;gBACN,cAAc,GAAG,CAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAE,CAAC;gBAC1C,uBAAuB,GAAG,CAAC,CAAC;gBAC5B,MAAM;YACR,KAAK,GAAG;gBACN,IAAM,SAAS,GAAG,IAAA,wBAAY,EAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBACrD,OAAO,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;gBACjC,MAAM;YACR;gBACE,MAAM;SACT;QACD,IAAI,OAAO,KAAK,GAAG,EAAE;YACnB,wBAAwB;YACxB,YAAY,GAAG,cAAc,CAAC;YAC9B,qDAAqD;YACrD,UAAU,GAAG,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC,CAAC;SAChD;aAAM;YACL,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;YAC1B,YAAY,GAAG,CAAE,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAE,CAAC;SACrD;QACD,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACvC,iCAAiC;YACjC,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC3C,IAAI,QAAQ,CAAC,uBAAuB,CAAC,EAAE;gBACrC,kCAAkC;gBAClC,QAAQ,CAAC,uBAAuB,CAAC,CAAC,QAAQ,GAAG,YAAY,CAAC;aAC3D;SACF;QACD,OAAO,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC;QACvC,2CAA2C;QAC3C,IACE,QAAQ,CAAC,uBAAuB,CAAC;YACjC,IAAA,4BAAW,EAAC,YAAY,EAAE,QAAQ,CAAC,uBAAuB,CAAC,CAAC,YAAY,CAAC,EACzE;YACA,QAAQ,CAAC,uBAAuB,CAAC,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SAC/D;QACD,IAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAE,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/G,OAAO,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;QACjC,kCAAkC;QAC1B,IAAA,QAAQ,GAAK,OAAO,SAAZ,CAAa;QAC7B,IAAI,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YACvC,OAAO,CAAC,YAAY,GAAG,CAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAE,CAAC;YACxF,OAAO,CAAC,UAAU,GAAG,CAAE,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAE,CAAC;SACvF;aAAM,IAAI,OAAO,KAAK,GAAG,EAAE;YAC1B,iBAAiB;YACjB,IAAM,EAAE,GAAG,CAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAE,CAAC;YACpC,2BAA2B;YAC3B,OAAO,CAAC,YAAY,GAAG,CAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC;YACpE,OAAO,CAAC,UAAU,GAAG,CAAE,YAAY,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC;SAC3E;aAAM,IAAI,OAAO,KAAK,GAAG,EAAE;YAC1B,IAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACnC,IAAM,EAAE,GAAG,UAAU,CAAC,UAAU,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YACzD,IAAI,UAAU,CAAC,OAAO,KAAK,GAAG,EAAE;gBAC9B,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC;gBACtB,OAAO,CAAC,YAAY,GAAG,CAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC;gBACpE,OAAO,CAAC,UAAU,GAAG,CAAE,YAAY,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC;aAC3E;iBAAM;gBACL,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;gBACvB,OAAO,CAAC,YAAY,GAAG,CAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAE,CAAC;gBACxF,OAAO,CAAC,UAAU,GAAG,CAAE,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAE,CAAC;aACvF;SACF;aAAM,IAAI,OAAO,KAAK,GAAG,EAAE;YAC1B,gBAAgB;YAChB,IAAM,GAAG,GAAG,CAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAE,CAAC;YACrC,IAAM,GAAG,GAAG,CAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAE,CAAC;YACrC,OAAO,CAAC,YAAY,GAAG,CAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;YACtE,OAAO,CAAC,UAAU,GAAG,CAAE,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;YAE5E,2DAA2D;YAC3D,IAAI,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBAClE,OAAO,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3D;YACD,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC9D,OAAO,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aACzD;SACF;aAAM,IAAI,OAAO,KAAK,GAAG,EAAE;YAC1B,IAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACnC,IAAM,GAAG,GAAG,UAAU,CAAC,UAAU,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAC1D,IAAM,GAAG,GAAG,CAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAE,CAAC;YACrC,IAAI,UAAU,CAAC,OAAO,KAAK,GAAG,EAAE;gBAC9B,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,iBAAiB;gBACxC,OAAO,CAAC,YAAY,GAAG,CAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;gBACtE,OAAO,CAAC,UAAU,GAAG,CAAE,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;aAC7E;iBAAM;gBACL,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,kBAAkB;gBAC1C,OAAO,CAAC,YAAY,GAAG,CAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;gBACtE,OAAO,CAAC,UAAU,GAAG,CAAE,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;aAC7E;SACF;aAAM,IAAI,OAAO,KAAK,GAAG,EAAE;YAC1B,IAAI,CAAC,GAAG,KAAK,CAAC;YACR,IAAA,KAQF,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,EAP5B,UAAM,EAAN,EAAE,mBAAG,CAAC,KAAA,EACN,UAAM,EAAN,EAAE,mBAAG,CAAC,KAAA,EACN,UAAM,EAAN,EAAE,mBAAG,CAAC,KAAA,EACN,UAAM,EAAN,EAAE,mBAAG,CAAC,KAAA,EACN,iBAAa,EAAb,SAAS,mBAAG,CAAC,KAAA,EACb,kBAAc,EAAd,UAAU,mBAAG,CAAC,KAAA,EACd,gBAAY,EAAZ,QAAQ,mBAAG,CAAC,KACgB,CAAC;YAC/B,IAAI,SAAS,KAAK,CAAC,EAAE;gBACnB,CAAC,IAAI,CAAC,CAAC,CAAC;aACT;YACD,IAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAC/C,IAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAC/C,OAAO,CAAC,YAAY,GAAG,CAAE,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,CAAE,CAAC;YAC5E,IAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAC1D,IAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAC1D,OAAO,CAAC,UAAU,GAAG,CAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAE,CAAC;SAC/D;QACD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACxB;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AArID,8BAqIC"}