{"version": 3, "file": "text.js", "sourceRoot": "", "sources": ["../../src/util/text.ts"], "names": [], "mappings": ";;;;AAAA,mCAAiE;AAEjE,qCAA6C;AAQ7C;;GAEG;AACU,QAAA,gBAAgB,GAAG,IAAA,cAAO,EACrC,UAAC,IAAS,EAAE,IAAe;IAAf,qBAAA,EAAA,SAAe;IACjB,IAAA,QAAQ,GAAqD,IAAI,SAAzD,EAAE,UAAU,GAAyC,IAAI,WAA7C,EAAE,UAAU,GAA6B,IAAI,WAAjC,EAAE,SAAS,GAAkB,IAAI,UAAtB,EAAE,WAAW,GAAK,IAAI,YAAT,CAAU;IAC1E,IAAM,GAAG,GAAG,IAAA,0BAAgB,GAAE,CAAC;IAC/B,GAAI,CAAC,IAAI,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAG,QAAQ,OAAI,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACxF,OAAO,GAAI,CAAC,WAAW,CAAC,IAAA,eAAQ,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;AAC5D,CAAC,EACD,UAAC,IAAS,EAAE,IAAe;IAAf,qBAAA,EAAA,SAAe;IAAK,OAAA,uBAAC,IAAI,kBAAK,IAAA,aAAM,EAAC,IAAI,CAAC,UAAE,IAAI,CAAC,EAAE,CAAC;AAAhC,CAAgC,CACjE,CAAC;AAEF;;;;;;;;GAQG;AACI,IAAM,eAAe,GAAG,UAAC,IAAS,EAAE,QAAgB,EAAE,IAAW;IACtE,IAAM,IAAI,GAAG,EAAE,CAAC,CAAC,cAAc;IAC/B,IAAM,SAAS,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAEhD,IAAI,QAAQ,CAAC;IAEb,IAAI,CAAC,IAAA,eAAQ,EAAC,IAAI,CAAC,EAAE;QACnB,QAAQ,GAAG,IAAA,eAAQ,EAAC,IAAI,CAAC,CAAC;KAC3B;SAAM;QACL,QAAQ,GAAG,IAAI,CAAC;KACjB;IAED,IAAI,SAAS,GAAG,QAAQ,CAAC;IAEzB,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,WAAW;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,YAAY,CAAC;IAEjB,IAAI,IAAA,wBAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,QAAQ,EAAE;QAC5C,OAAO,IAAI,CAAC;KACb;IAED,2BAA2B;IAC3B,OAAO,IAAI,EAAE;QACX,QAAQ;QACR,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAEvC,OAAO;QACP,YAAY,GAAG,IAAA,wBAAgB,EAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAEnD,aAAa;QACb,IAAI,YAAY,GAAG,SAAS,GAAG,SAAS,EAAE;YACxC,IAAI,YAAY,GAAG,SAAS,EAAE;gBAC5B,MAAM;aACP;SACF;QAED,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEpB,eAAe;QACf,SAAS,IAAI,YAAY,CAAC;QAC1B,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEjC,YAAY;QACZ,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACnB;KACF;IAED,gCAAgC;IAChC,OAAO,IAAI,EAAE;QACX,QAAQ;QACR,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEpC,OAAO;QACP,YAAY,GAAG,IAAA,wBAAgB,EAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAEnD,aAAa;QACb,IAAI,YAAY,GAAG,SAAS,GAAG,SAAS,EAAE;YACxC,MAAM;SACP;QAED,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpB,eAAe;QACf,SAAS,IAAI,YAAY,CAAC;QAC1B,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE9B,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACnB;KACF;IAED,OAAO,UAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAK,CAAC;AAC5B,CAAC,CAAC;AAzEW,QAAA,eAAe,mBAyE1B", "sourcesContent": ["import { isString, memoize, values, toString } from '@antv/util';\nimport * as CSS from 'csstype';\nimport { getCanvasContext } from './context';\n\ntype FontFace = CSS.Properties;\n\ntype Font = Pick<FontFace, 'fontFamily' | 'fontWeight' | 'fontStyle' | 'fontVariant'> & {\n  fontSize?: number;\n};\n\n/**\n * 计算文本在画布中的宽度\n */\nexport const measureTextWidth = memoize(\n  (text: any, font: Font = {}): number => {\n    const { fontSize, fontFamily, fontWeight, fontStyle, fontVariant } = font;\n    const ctx = getCanvasContext();\n    ctx!.font = [fontStyle, fontVariant, fontWeight, `${fontSize}px`, fontFamily].join(' ');\n    return ctx!.measureText(isString(text) ? text : '').width;\n  },\n  (text: any, font: Font = {}) => [text, ...values(font)].join('')\n);\n\n/**\n * 获取文本的 ... 文本。\n * 算法（减少每次 measureText 的长度，measureText 的性能跟字符串时间相关）：\n * 1. 先通过 STEP 逐步计算，找到最后一个小于 maxWidth 的字符串\n * 2. 然后对最后这个字符串二分计算\n * @param text 需要计算的文本, 由于历史原因 除了支持string，还支持空值,number和数组等\n * @param maxWidth\n * @param font\n */\nexport const getEllipsisText = (text: any, maxWidth: number, font?: Font) => {\n  const STEP = 16; // 每次 16，调参工程师\n  const DOT_WIDTH = measureTextWidth('...', font);\n\n  let leftText;\n\n  if (!isString(text)) {\n    leftText = toString(text);\n  } else {\n    leftText = text;\n  }\n\n  let leftWidth = maxWidth;\n\n  const r = []; // 最终的分段字符串\n  let currentText;\n  let currentWidth;\n\n  if (measureTextWidth(text, font) <= maxWidth) {\n    return text;\n  }\n\n  // 首先通过 step 计算，找出最大的未超出长度的\n  while (true) {\n    // 更新字符串\n    currentText = leftText.substr(0, STEP);\n\n    // 计算宽度\n    currentWidth = measureTextWidth(currentText, font);\n\n    // 超出剩余宽度，则停止\n    if (currentWidth + DOT_WIDTH > leftWidth) {\n      if (currentWidth > leftWidth) {\n        break;\n      }\n    }\n\n    r.push(currentText);\n\n    // 没有超出，则计算剩余宽度\n    leftWidth -= currentWidth;\n    leftText = leftText.substr(STEP);\n\n    // 字符串整体没有超出\n    if (!leftText) {\n      return r.join('');\n    }\n  }\n\n  // 最下的最后一个 STEP，使用 1 递增（用二分效果更高）\n  while (true) {\n    // 更新字符串\n    currentText = leftText.substr(0, 1);\n\n    // 计算宽度\n    currentWidth = measureTextWidth(currentText, font);\n\n    // 超出剩余宽度，则停止\n    if (currentWidth + DOT_WIDTH > leftWidth) {\n      break;\n    }\n\n    r.push(currentText);\n    // 没有超出，则计算剩余宽度\n    leftWidth -= currentWidth;\n    leftText = leftText.substr(1);\n\n    if (!leftText) {\n      return r.join('');\n    }\n  }\n\n  return `${r.join('')}...`;\n};\n"]}