import { DIRECTION } from '../constant';
import { Coordinate, Scale } from '../dependents';
/**
 * @ignore
 * get the grid theme by type, will mix the common cfg of axis
 * @param theme
 * @param direction
 * @returns theme object
 */
export declare function getGridThemeCfg(theme: object, direction: DIRECTION | 'common'): object;
/**
 * @ignore
 * get axis grid items
 * @param coordinate
 * @param scale
 * @param dim
 * @return items
 */
export declare function getLineGridItems(coordinate: Coordinate, scale: Scale, dim: string, alignTick?: boolean): any[];
/**
 * @ignore
 * get
 * @param coordinate
 * @param xScale
 * @param yScale
 * @param dim
 * @returns items
 */
export declare function getCircleGridItems(coordinate: Coordinate, xScale: Scale, yScale: Scale, alignTick: boolean, dim: string): any[];
/**
 * @ignore
 * show grid or not
 * @param axisTheme
 * @param axisOption
 */
export declare function showGrid(axisTheme: any, axisOption: any): boolean;
