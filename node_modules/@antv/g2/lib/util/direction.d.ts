import { DIRECTION } from '../constant';
import { Coordinate } from '../dependents';
import { Position } from '../interface';
import { BBox } from './bbox';
/**
 * @ignore
 * 方位常量转实际的 bbox 位置大小
 * @param parentBBox
 * @param bbox
 * @param direction
 */
export declare function directionToPosition(parentBBox: BBox, bbox: BBox, direction: DIRECTION): Position;
/**
 * @ignore
 * get direction after coordinate translate
 * @param direction
 * @param coordinate
 */
export declare function getTranslateDirection(direction: DIRECTION, coordinate: Coordinate): DIRECTION;
