import { View } from '../chart';
import Geometry from '../geometry/base';
import { MappingDatum, Point, TooltipCfg, TooltipTitle } from '../interface';
/**
 * @ignore
 * Finds data from geometry by point
 * @param point canvas point
 * @param data an item of geometry.dataArray
 * @param geometry
 * @returns
 */
export declare function findDataByPoint(point: Point, data: MappingDatum[], geometry: Geometry): any;
/**
 * @ignore
 * Gets tooltip items
 * @param data
 * @param geometry
 * @param [title]
 * @returns
 */
export declare function getTooltipItems(data: MappingDatum, geometry: Geometry, title?: TooltipTitle, showNil?: boolean): any[];
/**
 * 不进行递归查找
 */
export declare function findItemsFromView(view: View, point: Point, tooltipCfg: TooltipCfg): any[];
export declare function findItemsFromViewRecurisive(view: View, point: Point, tooltipCfg: TooltipCfg): any[];
