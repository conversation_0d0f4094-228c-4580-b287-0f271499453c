{"version": 3, "file": "stat.js", "sourceRoot": "", "sources": ["../../src/util/stat.ts"], "names": [], "mappings": ";;;;AAAA,mCAA8C;AAE9C;;;GAGG;AACH,SAAgB,SAAS,CAAC,KAAe;IACvC,IAAM,GAAG,4CAAO,KAAK,SAAC,CAAC;IACvB,MAAM;IACN,GAAG,CAAC,IAAI,CAAC,UAAC,CAAS,EAAE,CAAS;QAC5B,OAAO,CAAC,GAAG,CAAC,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,IAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IAEvB,SAAS;IACT,IAAI;IACJ,IAAI,GAAG,KAAK,CAAC,EAAE;QACb,OAAO,CAAC,CAAC;KACV;IAED,KAAK;IACL,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;QACjB,OAAO,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC3B;IAED,KAAK;IACL,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC/C,CAAC;AAtBD,8BAsBC;AAED;;;GAGG;AACH,SAAgB,OAAO,CAAC,KAAe;IACrC,IAAM,GAAG,GAAG,IAAA,aAAM,EAChB,KAAK,EACL,UAAC,CAAS,EAAE,GAAW;QACrB,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAA,eAAQ,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC,EACD,CAAC,CACF,CAAC;IAEF,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;AACrD,CAAC;AAVD,0BAUC", "sourcesContent": ["import { reduce, isNumber } from '@antv/util';\n\n/**\n * 获得中位数\n * @param array\n */\nexport function getMedian(array: number[]) {\n  const arr = [...array];\n  // 先排序\n  arr.sort((a: number, b: number) => {\n    return a - b;\n  });\n\n  const len = arr.length;\n\n  // median\n  // 0\n  if (len === 0) {\n    return 0;\n  }\n\n  // 奇数\n  if (len % 2 === 1) {\n    return arr[(len - 1) / 2];\n  }\n\n  // 偶数\n  return (arr[len / 2] + arr[len / 2 - 1]) / 2;\n}\n\n/**\n * 获得平均值\n * @param array\n */\nexport function getMean(array: number[]) {\n  const sum = reduce(\n    array,\n    (r: number, num: number) => {\n      return (r += isNaN(num) || !isNumber(num) ? 0 : num);\n    },\n    0\n  );\n\n  return array.length === 0 ? 0 : sum / array.length;\n}\n"]}