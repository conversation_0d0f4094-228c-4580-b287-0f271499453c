/** @ignore */
export declare const MarkerSymbols: {
    hexagon: (x: number, y: number, r: number) => (string | number)[][];
    bowtie: (x: number, y: number, r: number) => (string | number)[][];
    cross: (x: number, y: number, r: number) => (string | number)[][];
    tick: (x: number, y: number, r: number) => (string | number)[][];
    plus: (x: number, y: number, r: number) => (string | number)[][];
    hyphen: (x: number, y: number, r: number) => (string | number)[][];
    line: (x: number, y: number, r: number) => (string | number)[][];
};
