import { Size } from '../interface';
/**
 * @ignore
 * calculate the chart size
 * @param ele DOM element
 * @param autoFit should auto fit
 * @param width chart width which is set by user
 * @param height chart height which is set by user
 * @returns the chart width and height
 */
export declare function getChartSize(ele: HTMLElement, autoFit: boolean, width: number, height: number): Size;
/**
 * @ignore
 * remove html element from its parent
 * @param dom
 */
export declare function removeDom(dom: HTMLElement): void;
/** @ignore */
export { createDom, modifyCSS } from '@antv/dom-util';
