{"version": 3, "file": "transform.js", "sourceRoot": "", "sources": ["../../src/util/transform.ts"], "names": [], "mappings": ";;;AAAA,iDAAwC;AAGxC,IAAM,SAAS,GAAgD,iBAAG,CAAC,SAAS,CAAC;AAEpE,8BAAS;AAElB;;;;;GAKG;AACH,SAAgB,SAAS,CAAC,OAAwB,EAAE,CAAS,EAAE,CAAS;IACtE,IAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC5B,CAAC;AAHD,8BAGC;AAED;;;;GAIG;AACH,SAAgB,eAAe,CAAC,OAAiB,EAAE,YAAoB;IAC/D,IAAA,KAAW,OAAO,CAAC,IAAI,EAAE,EAAvB,CAAC,OAAA,EAAE,CAAC,OAAmB,CAAC;IAChC,IAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE;QAC5C,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACb,CAAC,GAAG,EAAE,YAAY,CAAC;QACnB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;KACZ,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AARD,0CAQC;AAED;;;;GAIG;AACH,SAAgB,MAAM,CAAC,OAAwB,EAAE,YAAoB;IACnE,IAAM,MAAM,GAAG,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACtD,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC5B,CAAC;AAHD,wBAGC;AAED;;;GAGG;AACH,SAAgB,iBAAiB;IAC/B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC,CAAC;AAFD,8CAEC;AAED;;;;GAIG;AACH,SAAgB,IAAI,CAAC,OAAwB,EAAE,KAAa;IAC1D,IAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IAC/B,IAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACtC,IAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACtC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAEjC,IAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE;QAC5C,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACb,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC;QACnB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;KACZ,CAAC,CAAC;IACH,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC5B,CAAC;AAZD,oBAYC", "sourcesContent": ["import { ext } from '@antv/matrix-util';\nimport { IElement, IGroup, IShape } from '../dependents';\n\nconst transform: (m: number[], actions: any[][]) => number[] = ext.transform;\n\nexport { transform };\n\n/**\n * 对元素进行平移操作。\n * @param element 进行变换的元素\n * @param x x 方向位移\n * @param y y 方向位移\n */\nexport function translate(element: IGroup | IShape, x: number, y: number) {\n  const matrix = transform(element.getMatrix(), [['t', x, y]]);\n  element.setMatrix(matrix);\n}\n\n/**\n * 获取元素旋转矩阵 (以元素的左上角为旋转点)\n * @param element 进行变换的元素\n * @param rotateRadian 旋转弧度\n */\nexport function getRotateMatrix(element: IElement, rotateRadian: number) {\n  const { x, y } = element.attr();\n  const matrix = transform(element.getMatrix(), [\n    ['t', -x, -y],\n    ['r', rotateRadian],\n    ['t', x, y],\n  ]);\n  return matrix;\n}\n\n/**\n * 对元素进行旋转操作。\n * @param element 进行变换的元素\n * @param rotateRadian 旋转弧度\n */\nexport function rotate(element: IGroup | IShape, rotateRadian: number) {\n  const matrix = getRotateMatrix(element, rotateRadian);\n  element.setMatrix(matrix);\n}\n\n/**\n * 获取元矩阵。\n * @returns identity matrix\n */\nexport function getIdentityMatrix(): number[] {\n  return [1, 0, 0, 0, 1, 0, 0, 0, 1];\n}\n\n/**\n * 围绕图形中心点进行缩放\n * @param element 进行缩放的图形元素\n * @param ratio 缩放比例\n */\nexport function zoom(element: IGroup | IShape, ratio: number) {\n  const bbox = element.getBBox();\n  const x = (bbox.minX + bbox.maxX) / 2;\n  const y = (bbox.minY + bbox.maxY) / 2;\n  element.applyToMatrix([x, y, 1]);\n\n  const matrix = transform(element.getMatrix(), [\n    ['t', -x, -y],\n    ['s', ratio, ratio],\n    ['t', x, y],\n  ]);\n  element.setMatrix(matrix);\n}\n"]}