/**
 * @ignore
 * Determines whether between is
 * @param value
 * @param start
 * @param end
 * @returns true if between
 */
export declare function isBetween(value: number, start: number, end: number): boolean;
/**
 * @ignore
 * pads the current string/array with a given value (repeated, if needed) so that the resulting reaches a given length.
 * The padding is applied from the end of the current value.
 *
 * @param source
 * @param targetLength
 * @param padValue
 * @returns
 */
export declare function padEnd(source: string | any[], targetLength: number, padValue: any): string | any[];
/**
 * @ignore
 * omit keys of an object.
 * @param obj
 * @param keys
 */
export declare function omit<T = any>(obj: T, keys: string[]): T;
/**
 * @ignore
 * @param sourceArray
 * @param targetArray
 * @param map
 */
export declare function uniq(sourceArray: any[], targetArray?: any[], map?: Map<any, boolean>): any[];
