{"version": 3, "file": "tooltip.js", "sourceRoot": "", "sources": ["../../src/util/tooltip.ts"], "names": [], "mappings": ";;;;AAAA,mCAaoB;AAEpB,wCAAwD;AAIxD,iCAAkD;AAElD,SAAS,SAAS,CAAC,EAAO,EAAE,EAAO,EAAE,KAAY;IAC/C,IAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IACnC,IAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IAEnC,OAAO,IAAA,oBAAa,EAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAY,EAAE,QAAkB;IACxD,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;IACvC,IAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;IACpC,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;IAC3B,IAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACzC,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAE1B,IAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAE7C,IAAI,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;IAC3B,IAAI,UAAU,CAAC,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,EAAE;QACrD,MAAM,GAAG,QAAQ,CAAC,CAAC,6BAA6B;KACjD;IACD,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,YAAY,CAAC,IAAU,EAAE,KAAY,EAAE,QAAkB;IAChE,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;IACvC,IAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;IACpC,IAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;IAC5B,IAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7C,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAE5C,IAAM,MAAM,GAAG,IAAA,WAAI,EAAC,IAAI,EAAE,UAAC,GAAU;QACnC,IAAM,UAAU,GAAG,GAAG,CAAC,uBAAY,CAAC,CAAC;QACrC,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC;IAC5E,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACzC,CAAC;AAED,IAAM,YAAY,GAAG,IAAA,cAAO,EAAC,UAAC,KAAY;IACxC,IAAI,KAAK,CAAC,UAAU,EAAE;QACpB,OAAO,CAAC,CAAC;KACV;IACD,IAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,cAAc;IAChD,IAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;IAClC,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,IAAI,GAAG,GAAG,GAAG,CAAC;IAEd,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,KAAK,EAAE,EAAE;QAC3C,IAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QACjC,mBAAmB;QACnB,IAAM,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,YAAY,GAAG,GAAG,EAAE;YACtB,GAAG,GAAG,YAAY,CAAC;SACpB;QACD,IAAI,YAAY,GAAG,GAAG,EAAE;YACtB,GAAG,GAAG,YAAY,CAAC;SACpB;KACF;IACD,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC;AAEH;;;;;GAKG;AACH,SAAS,eAAe,CAAC,UAAiB,EAAE,QAAkB,EAAE,KAAmB;IACjF,IAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;IACvD,IAAM,MAAM,GAAG,YAAY,CAAC,SAAS,EAAE,CAAC;IACxC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;IAE/B,IAAM,UAAU,GAAG,IAAA,iBAAU,EAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACnE,IAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;IAEtC,6DAA6D;IAC7D,oDAAoD;IACpD,IAAM,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC;IAEpH,OAAO,IAAA,iBAAU,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;AAC5E,CAAC;AAED,SAAS,sBAAsB,CAAC,QAAkB;IAChD,IAAM,UAAU,GAAG,IAAA,aAAM,EAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC/C,OAAO,IAAA,aAAM,EAAC,UAAU,EAAE,UAAC,SAAoB,IAAK,OAAA,IAAA,eAAQ,EAAC,sBAAW,EAAE,SAAS,CAAC,IAAI,CAAC,EAArC,CAAqC,CAAC,CAAC;AAC7F,CAAC;AAED,SAAS,oBAAoB,CAAC,QAAkB;;IAC9C,IAAM,UAAU,GAAG,sBAAsB,CAAC,QAAQ,CAAC,CAAC;IACpD,IAAI,KAAK,CAAC;;QACV,KAAwB,IAAA,eAAA,iBAAA,UAAU,CAAA,sCAAA,8DAAE;YAA/B,IAAM,SAAS,uBAAA;YAClB,IAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE;gBACjC,IAAM,WAAW,GAAG,IAAA,UAAG,EAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC5D,IAAM,gBAAgB,GAAG,IAAA,sBAAc,EAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9F,IAAI,gBAAgB,KAAK,KAAK,EAAE;oBAC9B,6BAA6B;oBAC7B,KAAK,GAAG,QAAQ,CAAC;oBACjB,MAAM;iBACP;aACF;SACF;;;;;;;;;IAED,IAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;IACpC,IAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;IAEpC,OAAO,KAAK,IAAI,MAAM,IAAI,MAAM,CAAC;AACnC,CAAC;AAED,SAAS,eAAe,CAAC,UAAiB,EAAE,UAAiB;IAC3D,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;IAC/B,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IAEhC,IAAI,IAAA,cAAO,EAAC,KAAK,CAAC,EAAE;QAClB,IAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,UAAC,SAAS;YAChC,OAAO,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACxB;IACD,OAAO,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACnC,CAAC;AAED,iCAAiC;AACjC,SAAS,cAAc,CAAC,UAAiB,EAAE,QAAkB;IAC3D,IAAI,SAAgB,CAAC;IACrB,IAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;IAC9C,IAAI,WAAW,CAAC,MAAM,EAAE;QACtB,oBAAoB;QACpB,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;KAC5B;IACD,IAAI,SAAS,EAAE;QACb,IAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAC9B,OAAO,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;KAC7C;IAED,IAAM,UAAU,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IAClD,OAAO,IAAA,eAAO,EAAC,UAAU,CAAC,CAAC;AAC7B,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,eAAe,CAAC,KAAY,EAAE,IAAoB,EAAE,QAAkB;IACpF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,OAAO,IAAI,CAAC;KACb;IAED,IAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;IACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;IACpC,IAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;IAEpC,IAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;IAC5B,IAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;IAE5B,IAAI,GAAG,GAAG,IAAI,CAAC;IAEf,4BAA4B;IAC5B,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,OAAO,EAAE;QAC1D,uBAAuB;QACvB,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QACvC,IAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY;QAC1D,IAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;QACjD,IAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;QAEjD,IAAI,GAAG,GAAG,QAAQ,CAAC;QACnB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChD,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YACxB,IAAM,UAAU,GAAG,GAAG,CAAC,uBAAY,CAAC,CAAC;YACrC,IAAM,KAAK,GAAG,SAAA,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAI,CAAC,CAAA,GAAG,SAAA,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAI,CAAC,CAAA,CAAC;YAC5E,IAAI,KAAK,GAAG,GAAG,EAAE;gBACf,GAAG,GAAG,KAAK,CAAC;gBACZ,GAAG,GAAG,GAAG,CAAC;aACX;SACF;QAED,OAAO,GAAG,CAAC;KACZ;IAED,8BAA8B;IAC9B,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACtB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACjC,IAAM,MAAM,GAAG,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACjD,IAAM,WAAW,GAAG,KAAK,CAAC,uBAAY,CAAC,CAAC,MAAM,CAAC,CAAC;IAChD,IAAM,WAAW,GAAG,KAAK,CAAC,uBAAY,CAAC,CAAC,MAAM,CAAC,CAAC;IAChD,IAAM,UAAU,GAAG,IAAI,CAAC,uBAAY,CAAC,CAAC,MAAM,CAAC,CAAC;IAC9C,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,IAAA,cAAO,EAAC,WAAW,CAAC,CAAC,CAAC,uBAAuB;IAEjF,aAAa;IACb,IAAI,IAAA,cAAO,EAAC,WAAW,CAAC,EAAE;QACxB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChD,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3B,IAAM,UAAU,GAAG,MAAM,CAAC,uBAAY,CAAC,CAAC;YACxC,qCAAqC;YACrC,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;gBAC1G,IAAI,QAAQ,EAAE;oBACZ,uBAAuB;oBACvB,IAAI,CAAC,IAAA,cAAO,EAAC,GAAG,CAAC,EAAE;wBACjB,GAAG,GAAG,EAAE,CAAC;qBACV;oBACD,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAClB;qBAAM;oBACL,GAAG,GAAG,MAAM,CAAC;oBACb,MAAM;iBACP;aACF;SACF;QACD,IAAI,IAAA,cAAO,EAAC,GAAG,CAAC,EAAE;YAChB,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;SAC1C;KACF;SAAM;QACL,IAAI,IAAI,SAAA,CAAC;QACT,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE;YACjD,+BAA+B;YAC/B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChD,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC3B,IAAM,UAAU,GAAG,MAAM,CAAC,uBAAY,CAAC,CAAC;gBACxC,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;oBACjD,IAAI,QAAQ,EAAE;wBACZ,IAAI,CAAC,IAAA,cAAO,EAAC,GAAG,CAAC,EAAE;4BACjB,GAAG,GAAG,EAAE,CAAC;yBACV;wBACD,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBAClB;yBAAM;wBACL,GAAG,GAAG,MAAM,CAAC;wBACb,MAAM;qBACP;iBACF;qBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,MAAM,EAAE;oBACzD,IAAI,GAAG,MAAM,CAAC;oBACd,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;iBACxB;aACF;YAED,IAAI,IAAA,cAAO,EAAC,GAAG,CAAC,EAAE;gBAChB,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;aAC1C;SACF;aAAM;YACL,gCAAgC;YAChC,IACE,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBACjF,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,EAC5C;gBACA,UAAU;gBACV,OAAO,IAAI,CAAC;aACb;YAED,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAC9B,IAAI,SAAS,SAAA,CAAC;YACd,OAAO,QAAQ,IAAI,OAAO,EAAE;gBAC1B,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,uBAAY,CAAC,CAAC,MAAM,CAAC,CAAC;gBACnD,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;oBACnC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC;iBACxB;gBAED,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;oBACtD,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC;oBACzB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;oBACvB,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;iBAC5B;qBAAM;oBACL,IAAI,OAAO,KAAK,CAAC,EAAE;wBACjB,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;qBAChB;oBACD,OAAO,GAAG,SAAS,GAAG,CAAC,CAAC;iBACzB;aACF;SACF;QAED,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,SAAS;YACT,IACE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAY,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC;gBAC/D,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAY,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,EAC/D;gBACA,IAAI,GAAG,IAAI,CAAC;aACb;SACF;KACF;IAED,IAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,aAAa;IAClE,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAY,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE;QAC3F,GAAG,GAAG,IAAI,CAAC;KACZ;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AA/ID,0CA+IC;AAED;;;;;;;GAOG;AACH,SAAgB,eAAe,CAC7B,IAAkB,EAClB,QAAkB,EAClB,KAAwB,EACxB,OAAwB;;IADxB,sBAAA,EAAA,UAAwB;IACxB,wBAAA,EAAA,eAAwB;IAExB,IAAM,UAAU,GAAG,IAAI,CAAC,uBAAY,CAAC,CAAC;IACtC,IAAM,YAAY,GAAG,eAAe,CAAC,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IAClE,IAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;IACrC,IAAA,YAAY,GAAK,QAAQ,CAAC,KAAK,aAAnB,CAAoB;IACxC,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,IAAI,IAAI,CAAC;IACT,IAAI,KAAK,CAAC;IAEV,SAAS,OAAO,CAAC,QAAQ,EAAE,SAAS;QAClC,IAAI,OAAO,IAAI,CAAC,CAAC,IAAA,YAAK,EAAC,SAAS,CAAC,IAAI,SAAS,KAAK,EAAE,CAAC,EAAE;YACtD,gBAAgB;YAChB,IAAM,IAAI,GAAG;gBACX,KAAK,EAAE,YAAY;gBACnB,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,IAAI;gBACjB,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,YAAY;gBACjC,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAClB;IACH,CAAC;IAED,IAAI,IAAA,eAAQ,EAAC,aAAa,CAAC,EAAE;QACnB,IAAA,MAAM,GAAe,aAAa,OAA5B,EAAE,QAAQ,GAAK,aAAa,SAAlB,CAAmB;QAC3C,IAAI,QAAQ,EAAE;YACZ,YAAY;YACZ,IAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,UAAC,KAAa;gBAC9C,OAAO,IAAI,CAAC,uBAAY,CAAC,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YACH,IAAM,GAAG,GAAG,QAAQ,wDAAI,cAAc,UAAC,CAAC;YACxC,IAAM,OAAO,sBACX,IAAI,EAAE,IAAI,CAAC,uBAAY,CAAC,EACxB,WAAW,EAAE,IAAI,EACjB,KAAK,EAAE,YAAY,EACnB,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,YAAY,EACjC,MAAM,EAAE,IAAI,IACT,GAAG,CACP,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACrB;aAAM;YACL,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;;gBAC/B,KAAoB,IAAA,WAAA,iBAAA,MAAM,CAAA,8BAAA,kDAAE;oBAAvB,IAAM,KAAK,mBAAA;oBACd,IAAI,CAAC,IAAA,YAAK,EAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;wBAC7B,4BAA4B;wBAC5B,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;wBAC5B,IAAI,GAAG,IAAA,eAAO,EAAC,KAAK,CAAC,CAAC;wBACtB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;wBACzC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;qBACtB;iBACF;;;;;;;;;SACF;KACF;SAAM;QACL,IAAM,UAAU,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAClD,2BAA2B;QAC3B,KAAK,GAAG,eAAe,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAChD,IAAI,GAAG,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC5C,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACtB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AArED,0CAqEC;AAED,SAAS,yBAAyB,CAAC,QAAkB,EAAE,KAAK,EAAE,KAAK,EAAE,UAAsB;;IACjF,IAAA,OAAO,GAAK,UAAU,QAAf,CAAgB;IAC/B,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;IACrC,IAAI,CAAC,IAAA,cAAO,EAAC,SAAS,CAAC,EAAE;QACvB,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,sBAAsB;;YAChD,KAAmB,IAAA,cAAA,iBAAA,SAAS,CAAA,oCAAA,2DAAE;gBAAzB,IAAM,IAAI,sBAAA;gBACb,IAAM,MAAM,GAAG,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACtD,IAAI,MAAM,EAAE;oBACV,IAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;oBAChD,IAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;oBAChD,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,OAAO,EAAE;wBAClD,qBAAqB;wBACrB,gCAAgC;wBAChC,IAAM,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;wBAChE,IAAI,KAAK,CAAC,MAAM,EAAE;4BAChB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBACpB;qBACF;iBACF;aACF;;;;;;;;;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,yBAAyB,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAsB;IACvE,IAAA,OAAO,GAAK,UAAU,QAAf,CAAgB;IAC/B,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;IACrC,IAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACnD,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QACxD,IAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;QACpD,IAAM,KAAK,GAAG,eAAe,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACrE,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACpB;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,IAAU,EAAE,KAAY,EAAE,UAAsB;;IAChF,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,eAAe;IACf,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IAC3B,IAAA,MAAM,GAAsB,UAAU,OAAhC,EAAE,KAAK,GAAe,UAAU,MAAzB,EAAE,QAAQ,GAAK,UAAU,SAAf,CAAgB;;QAC/C,KAAuB,IAAA,eAAA,iBAAA,UAAU,CAAA,sCAAA,8DAAE;YAA9B,IAAM,QAAQ,uBAAA;YACjB,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,aAAa,KAAK,KAAK,EAAE;gBACxD,2BAA2B;gBAC3B,IAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACnC,IAAI,YAAY,SAAA,CAAC;gBACjB,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACvD,WAAW;oBACX,YAAY,GAAG,yBAAyB,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;iBAC9E;qBAAM,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACrE,kDAAkD;oBAClD,YAAY,GAAG,yBAAyB,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;iBAC9E;qBAAM;oBACL,IAAI,MAAM,KAAK,KAAK,EAAE;wBACpB,YAAY,GAAG,yBAAyB,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;qBAC9E;yBAAM;wBACL,YAAY,GAAG,yBAAyB,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;qBAC9E;iBACF;gBACD,IAAI,YAAY,CAAC,MAAM,EAAE;oBACvB,IAAI,QAAQ,EAAE;wBACZ,YAAY,CAAC,OAAO,EAAE,CAAC;qBACxB;oBACD,2DAA2D;oBAC3D,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC3B;aACF;SACF;;;;;;;;;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAlCD,8CAkCC;AAED,SAAgB,2BAA2B,CAAC,IAAU,EAAE,KAAY,EAAE,UAAsB;;IAC1F,IAAI,MAAM,GAAG,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;;QAExD,aAAa;QACb,KAAwB,IAAA,KAAA,iBAAA,IAAI,CAAC,KAAK,CAAA,gBAAA,4BAAE;YAA/B,IAAM,SAAS,WAAA;YAClB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;SACzE;;;;;;;;;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AATD,kEASC", "sourcesContent": ["import {\n  contains,\n  filter,\n  find,\n  isArray,\n  isEmpty,\n  isFunction,\n  isNil,\n  isNumberEqual,\n  isObject,\n  memoize,\n  get,\n  values,\n} from '@antv/util';\nimport { View } from '../chart';\nimport { FIELD_ORIGIN, GROUP_ATTRS } from '../constant';\nimport { Attribute, Scale } from '../dependents';\nimport Geometry from '../geometry/base';\nimport { Data, Datum, MappingDatum, Point, TooltipCfg, TooltipTitle } from '../interface';\nimport { getName, inferScaleType } from './scale';\n\nfunction snapEqual(v1: any, v2: any, scale: Scale) {\n  const value1 = scale.translate(v1);\n  const value2 = scale.translate(v2);\n\n  return isNumberEqual(value1, value2);\n}\n\nfunction getXValueByPoint(point: Point, geometry: Geometry): number {\n  const coordinate = geometry.coordinate;\n  const xScale = geometry.getXScale();\n  const range = xScale.range;\n  const rangeMax = range[range.length - 1];\n  const rangeMin = range[0];\n\n  const invertPoint = coordinate.invert(point);\n\n  let xValue = invertPoint.x;\n  if (coordinate.isPolar && xValue > (1 + rangeMax) / 2) {\n    xValue = rangeMin; // 极坐标下，scale 的 range 被做过特殊处理\n  }\n  return xScale.translate(xScale.invert(xValue));\n}\n\nfunction filterYValue(data: Data, point: Point, geometry: Geometry) {\n  const coordinate = geometry.coordinate;\n  const yScale = geometry.getYScale();\n  const yField = yScale.field;\n  const invertPoint = coordinate.invert(point);\n  const yValue = yScale.invert(invertPoint.y);\n\n  const result = find(data, (obj: Datum) => {\n    const originData = obj[FIELD_ORIGIN];\n    return originData[yField][0] <= yValue && originData[yField][1] >= yValue;\n  });\n  return result || data[data.length - 1];\n}\n\nconst getXDistance = memoize((scale: Scale) => {\n  if (scale.isCategory) {\n    return 1;\n  }\n  const scaleValues = scale.values; // values 是无序的\n  const length = scaleValues.length;\n  let min = scale.translate(scaleValues[0]);\n  let max = min;\n\n  for (let index = 0; index < length; index++) {\n    const value = scaleValues[index];\n    // 时间类型需要 translate\n    const numericValue = scale.translate(value);\n    if (numericValue < min) {\n      min = numericValue;\n    }\n    if (numericValue > max) {\n      max = numericValue;\n    }\n  }\n  return (max - min) / (length - 1);\n});\n\n/**\n * 获得 tooltip 的 title\n * @param originData\n * @param geometry\n * @param title\n */\nfunction getTooltipTitle(originData: Datum, geometry: Geometry, title: TooltipTitle): string {\n  const positionAttr = geometry.getAttribute('position');\n  const fields = positionAttr.getFields();\n  const scales = geometry.scales;\n\n  const titleField = isFunction(title) || !title ? fields[0] : title;\n  const titleScale = scales[titleField];\n\n  // 如果创建了该字段对应的 scale，则通过 scale.getText() 方式取值，因为用户可能对数据进行了格式化\n  // 如果没有对应的 scale，则从原始数据中取值，如果原始数据中仍不存在，则直接放回 title 值\n  const tooltipTitle = titleScale ? titleScale.getText(originData[titleField]) : originData[titleField] || titleField;\n\n  return isFunction(title) ? title(tooltipTitle, originData) : tooltipTitle;\n}\n\nfunction getAttributesForLegend(geometry: Geometry) {\n  const attributes = values(geometry.attributes);\n  return filter(attributes, (attribute: Attribute) => contains(GROUP_ATTRS, attribute.type));\n}\n\nfunction getTooltipValueScale(geometry: Geometry) {\n  const attributes = getAttributesForLegend(geometry);\n  let scale;\n  for (const attribute of attributes) {\n    const tmpScale = attribute.getScale(attribute.type);\n    if (tmpScale && tmpScale.isLinear) {\n      const tmpScaleDef = get(geometry.scaleDefs, tmpScale.field);\n      const inferedScaleType = inferScaleType(tmpScale, tmpScaleDef, attribute.type, geometry.type);\n      if (inferedScaleType !== 'cat') {\n        // 如果指定字段是非 position 的，同时是连续的\n        scale = tmpScale;\n        break;\n      }\n    }\n  }\n\n  const xScale = geometry.getXScale();\n  const yScale = geometry.getYScale();\n\n  return scale || yScale || xScale;\n}\n\nfunction getTooltipValue(originData: Datum, valueScale: Scale) {\n  const field = valueScale.field;\n  const value = originData[field];\n\n  if (isArray(value)) {\n    const texts = value.map((eachValue) => {\n      return valueScale.getText(eachValue);\n    });\n    return texts.join('-');\n  }\n  return valueScale.getText(value);\n}\n\n// 根据原始数据获取 tooltip item 中 name 值\nfunction getTooltipName(originData: Datum, geometry: Geometry) {\n  let nameScale: Scale;\n  const groupScales = geometry.getGroupScales();\n  if (groupScales.length) {\n    // 如果存在分组类型，取第一个分组类型\n    nameScale = groupScales[0];\n  }\n  if (nameScale) {\n    const field = nameScale.field;\n    return nameScale.getText(originData[field]);\n  }\n\n  const valueScale = getTooltipValueScale(geometry);\n  return getName(valueScale);\n}\n\n/**\n * @ignore\n * Finds data from geometry by point\n * @param point canvas point\n * @param data an item of geometry.dataArray\n * @param geometry\n * @returns\n */\nexport function findDataByPoint(point: Point, data: MappingDatum[], geometry: Geometry) {\n  if (data.length === 0) {\n    return null;\n  }\n\n  const geometryType = geometry.type;\n  const xScale = geometry.getXScale();\n  const yScale = geometry.getYScale();\n\n  const xField = xScale.field;\n  const yField = yScale.field;\n\n  let rst = null;\n\n  // 热力图采用最小逼近策略查找 point 击中的数据\n  if (geometryType === 'heatmap' || geometryType === 'point') {\n    // 将 point 画布坐标转换为原始数据值\n    const coordinate = geometry.coordinate;\n    const invertPoint = coordinate.invert(point); // 转换成归一化的数据\n    const x = xScale.invert(invertPoint.x); // 转换为原始值\n    const y = yScale.invert(invertPoint.y); // 转换为原始值\n\n    let min = Infinity;\n    for (let index = 0; index < data.length; index++) {\n      const obj = data[index];\n      const originData = obj[FIELD_ORIGIN];\n      const range = (originData[xField] - x) ** 2 + (originData[yField] - y) ** 2;\n      if (range < min) {\n        min = range;\n        rst = obj;\n      }\n    }\n\n    return rst;\n  }\n\n  // 其他 Geometry 类型按照 x 字段数据进行查找\n  const first = data[0];\n  let last = data[data.length - 1];\n  const xValue = getXValueByPoint(point, geometry);\n  const firstXValue = first[FIELD_ORIGIN][xField];\n  const firstYValue = first[FIELD_ORIGIN][yField];\n  const lastXValue = last[FIELD_ORIGIN][xField];\n  const isYArray = yScale.isLinear && isArray(firstYValue); // 考虑 x 维度相同，y 是数组区间的情况\n\n  // 如果 x 的值是数组\n  if (isArray(firstXValue)) {\n    for (let index = 0; index < data.length; index++) {\n      const record = data[index];\n      const originData = record[FIELD_ORIGIN];\n      // xValue 在 originData[xField] 的数值区间内\n      if (xScale.translate(originData[xField][0]) <= xValue && xScale.translate(originData[xField][1]) >= xValue) {\n        if (isYArray) {\n          // 层叠直方图场景，x 和 y 都是数组区间\n          if (!isArray(rst)) {\n            rst = [];\n          }\n          rst.push(record);\n        } else {\n          rst = record;\n          break;\n        }\n      }\n    }\n    if (isArray(rst)) {\n      rst = filterYValue(rst, point, geometry);\n    }\n  } else {\n    let next;\n    if (!xScale.isLinear && xScale.type !== 'timeCat') {\n      // x 轴对应的数据为非线性以及非时间类型的数据采用遍历查找\n      for (let index = 0; index < data.length; index++) {\n        const record = data[index];\n        const originData = record[FIELD_ORIGIN];\n        if (snapEqual(originData[xField], xValue, xScale)) {\n          if (isYArray) {\n            if (!isArray(rst)) {\n              rst = [];\n            }\n            rst.push(record);\n          } else {\n            rst = record;\n            break;\n          }\n        } else if (xScale.translate(originData[xField]) <= xValue) {\n          last = record;\n          next = data[index + 1];\n        }\n      }\n\n      if (isArray(rst)) {\n        rst = filterYValue(rst, point, geometry);\n      }\n    } else {\n      // x 轴对应的数据为线性以及时间类型，进行二分查找，性能更好\n      if (\n        (xValue > xScale.translate(lastXValue) || xValue < xScale.translate(firstXValue)) &&\n        (xValue > xScale.max || xValue < xScale.min)\n      ) {\n        // 不在数据范围内\n        return null;\n      }\n\n      let firstIdx = 0;\n      let lastIdx = data.length - 1;\n      let middleIdx;\n      while (firstIdx <= lastIdx) {\n        middleIdx = Math.floor((firstIdx + lastIdx) / 2);\n        const item = data[middleIdx][FIELD_ORIGIN][xField];\n        if (snapEqual(item, xValue, xScale)) {\n          return data[middleIdx];\n        }\n\n        if (xScale.translate(item) <= xScale.translate(xValue)) {\n          firstIdx = middleIdx + 1;\n          last = data[middleIdx];\n          next = data[middleIdx + 1];\n        } else {\n          if (lastIdx === 0) {\n            last = data[0];\n          }\n          lastIdx = middleIdx - 1;\n        }\n      }\n    }\n\n    if (last && next) {\n      // 计算最逼近的\n      if (\n        Math.abs(xScale.translate(last[FIELD_ORIGIN][xField]) - xValue) >\n        Math.abs(xScale.translate(next[FIELD_ORIGIN][xField]) - xValue)\n      ) {\n        last = next;\n      }\n    }\n  }\n\n  const distance = getXDistance(geometry.getXScale()); // 每个分类间的平均间距\n  if (!rst && Math.abs(xScale.translate(last[FIELD_ORIGIN][xField]) - xValue) <= distance / 2) {\n    rst = last;\n  }\n\n  return rst;\n}\n\n/**\n * @ignore\n * Gets tooltip items\n * @param data\n * @param geometry\n * @param [title]\n * @returns\n */\nexport function getTooltipItems(\n  data: MappingDatum,\n  geometry: Geometry,\n  title: TooltipTitle = '',\n  showNil: boolean = false\n) {\n  const originData = data[FIELD_ORIGIN];\n  const tooltipTitle = getTooltipTitle(originData, geometry, title);\n  const tooltipOption = geometry.tooltipOption;\n  const { defaultColor } = geometry.theme;\n  const items = [];\n  let name;\n  let value;\n\n  function addItem(itemName, itemValue) {\n    if (showNil || (!isNil(itemValue) && itemValue !== '')) {\n      // 值为 null的时候，忽视\n      const item = {\n        title: tooltipTitle,\n        data: originData, // 原始数据\n        mappingData: data, // 映射后的数据\n        name: itemName,\n        value: itemValue,\n        color: data.color || defaultColor,\n        marker: true,\n      };\n\n      items.push(item);\n    }\n  }\n\n  if (isObject(tooltipOption)) {\n    const { fields, callback } = tooltipOption;\n    if (callback) {\n      // 用户定义了回调函数\n      const callbackParams = fields.map((field: string) => {\n        return data[FIELD_ORIGIN][field];\n      });\n      const cfg = callback(...callbackParams);\n      const itemCfg = {\n        data: data[FIELD_ORIGIN], // 原始数据\n        mappingData: data, // 映射后的数据\n        title: tooltipTitle,\n        color: data.color || defaultColor,\n        marker: true, // 默认展示 marker\n        ...cfg,\n      };\n\n      items.push(itemCfg);\n    } else {\n      const scales = geometry.scales;\n      for (const field of fields) {\n        if (!isNil(originData[field])) {\n          // 字段数据为null, undefined 时不显示\n          const scale = scales[field];\n          name = getName(scale);\n          value = scale.getText(originData[field]);\n          addItem(name, value);\n        }\n      }\n    }\n  } else {\n    const valueScale = getTooltipValueScale(geometry);\n    // 字段数据为null ,undefined时不显示\n    value = getTooltipValue(originData, valueScale);\n    name = getTooltipName(originData, geometry);\n    addItem(name, value);\n  }\n  return items;\n}\n\nfunction getTooltipItemsByFindData(geometry: Geometry, point, title, tooltipCfg: TooltipCfg) {\n  const { showNil } = tooltipCfg;\n  const result = [];\n  const dataArray = geometry.dataArray;\n  if (!isEmpty(dataArray)) {\n    geometry.sort(dataArray); // 先进行排序，便于 tooltip 查找\n    for (const data of dataArray) {\n      const record = findDataByPoint(point, data, geometry);\n      if (record) {\n        const elementId = geometry.getElementId(record);\n        const element = geometry.elementsMap[elementId];\n        if (geometry.type === 'heatmap' || element.visible) {\n          // Heatmap 没有 Element\n          // 如果图形元素隐藏了，怎不再 tooltip 上展示相关数据\n          const items = getTooltipItems(record, geometry, title, showNil);\n          if (items.length) {\n            result.push(items);\n          }\n        }\n      }\n    }\n  }\n\n  return result;\n}\n\nfunction getTooltipItemsByHitShape(geometry, point, title, tooltipCfg: TooltipCfg) {\n  const { showNil } = tooltipCfg;\n  const result = [];\n  const container = geometry.container;\n  const shape = container.getShape(point.x, point.y);\n  if (shape && shape.get('visible') && shape.get('origin')) {\n    const mappingData = shape.get('origin').mappingData;\n    const items = getTooltipItems(mappingData, geometry, title, showNil);\n    if (items.length) {\n      result.push(items);\n    }\n  }\n\n  return result;\n}\n\n/**\n * 不进行递归查找\n */\nexport function findItemsFromView(view: View, point: Point, tooltipCfg: TooltipCfg) {\n  const result = [];\n  // 先从 view 本身查找\n  const geometries = view.geometries;\n  const { shared, title, reversed } = tooltipCfg;\n  for (const geometry of geometries) {\n    if (geometry.visible && geometry.tooltipOption !== false) {\n      // geometry 可见同时未关闭 tooltip\n      const geometryType = geometry.type;\n      let tooltipItems;\n      if (['point', 'edge', 'polygon'].includes(geometryType)) {\n        // 始终通过图形拾取\n        tooltipItems = getTooltipItemsByHitShape(geometry, point, title, tooltipCfg);\n      } else if (['area', 'line', 'path', 'heatmap'].includes(geometryType)) {\n        // 如果是 'area', 'line', 'path'，始终通过数据查找方法查找 tooltip\n        tooltipItems = getTooltipItemsByFindData(geometry, point, title, tooltipCfg);\n      } else {\n        if (shared !== false) {\n          tooltipItems = getTooltipItemsByFindData(geometry, point, title, tooltipCfg);\n        } else {\n          tooltipItems = getTooltipItemsByHitShape(geometry, point, title, tooltipCfg);\n        }\n      }\n      if (tooltipItems.length) {\n        if (reversed) {\n          tooltipItems.reverse();\n        }\n        // geometry 有可能会有多个 item，因为用户可以设置 geometry.tooltip('x*y*z')\n        result.push(tooltipItems);\n      }\n    }\n  }\n\n  return result;\n}\n\nexport function findItemsFromViewRecurisive(view: View, point: Point, tooltipCfg: TooltipCfg) {\n  let result = findItemsFromView(view, point, tooltipCfg);\n\n  // 递归查找，并合并结果\n  for (const childView of view.views) {\n    result = result.concat(findItemsFromView(childView, point, tooltipCfg));\n  }\n\n  return result;\n}\n"]}