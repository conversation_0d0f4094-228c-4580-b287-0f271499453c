/**
 * @fileoverview 设置画布的箭头，参看：https://www.w3school.com.cn/jsref/prop_style_cursor.asp
 * <AUTHOR>
 */
import Action from './base';
/**
 * 鼠标形状的 Action
 * @ignore
 */
declare class CursorAction extends Action {
    private setCursor;
    /**
     * 默认光标（通常是一个箭头）
     */
    default(): void;
    /** 光标呈现为指示链接的指针（一只手） */
    pointer(): void;
    /** 此光标指示某对象可被移动。 */
    move(): void;
    /** 光标呈现为十字线。 */
    crosshair(): void;
    /** 此光标指示程序正忙（通常是一只表或沙漏）。 */
    wait(): void;
    /** 此光标指示可用的帮助（通常是一个问号或一个气球）。 */
    help(): void;
    /** 此光标指示文本。 */
    text(): void;
    /**
     * 此光标指示矩形框的边缘可被向右（东）移动。
     */
    eResize(): void;
    /**
     * 此光标指示矩形框的边缘可被向左（西）移动。
     */
    wResize(): void;
    /**
     * 此光标指示矩形框的边缘可被向上（北）移动。
     */
    nResize(): void;
    /**
     * 此光标指示矩形框的边缘可被向下（南）移动。
     */
    sResize(): void;
    /**
     * 光标指示可移动的方向 右上方（东北）
     */
    neResize(): void;
    /**
     * 光标指示可移动的方向 左上方（西北）
     */
    nwResize(): void;
    /**
     * 光标指示可移动的方向右下方（东南）
     */
    seResize(): void;
    /**
     * 光标指示可移动的方向左下方（西南）
     */
    swResize(): void;
    /**
     * 光标指示可以在上下方向移动
     */
    nsResize(): void;
    /**
     * 光标指示可以在左右方向移动
     */
    ewResize(): void;
    /**
     * 光标显示可以被放大
     */
    zoomIn(): void;
    /**
     * 光标显示可以缩小尺寸
     */
    zoomOut(): void;
}
export default CursorAction;
