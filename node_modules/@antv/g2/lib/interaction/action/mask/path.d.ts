import MaskBase from './base';
/**
 * 生成 mask 的路径
 * @param points
 * @returns
 */
export declare function getMaskPath(points: any): any[];
export declare function getMaskAttrs(points: any): {
    path: any[];
};
/**
 * @ignore
 * 多个点构成的 Path 辅助框 Action
 */
declare class PathMask extends MaskBase {
    protected getMaskPath(): any[];
    protected getMaskAttrs(): {
        path: any[];
    };
    /**
     * 添加一个点
     */
    addPoint(): void;
}
export default PathMask;
