{"version": 3, "file": "smooth-path.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/mask/smooth-path.ts"], "names": [], "mappings": ";;;;AAAA,gCAAoC;AACpC,wDAA8B;AAE9B;;;;GAIG;AACH,SAAgB,WAAW,CAAC,MAAM;IAChC,OAAO,IAAA,gBAAS,EAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACjC,CAAC;AAFD,kCAEC;AAED,SAAgB,YAAY,CAAC,MAAM;IACjC,OAAO;QACL,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC;KAC1B,CAAC;AACJ,CAAC;AAJD,oCAIC;AAED;;;GAGG;AACH;IAA6B,0CAAQ;IAArC;;IAOA,CAAC;IANW,oCAAW,GAArB;QACE,OAAO,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IACS,qCAAY,GAAtB;QACE,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IACH,qBAAC;AAAD,CAAC,AAPD,CAA6B,cAAQ,GAOpC;AAED,kBAAe,cAAc,CAAC", "sourcesContent": ["import { getSpline } from '../util';\nimport PathMask from './path';\n\n/**\n * 生成 mask 的路径\n * @param points\n * @returns\n */\nexport function getMaskPath(points) {\n  return getSpline(points, true);\n}\n\nexport function getMaskAttrs(points) {\n  return {\n    path: getMaskPath(points),\n  };\n}\n\n/**\n * Smooth path mask\n * @ignore\n */\nclass SmoothPathMask extends PathMask {\n  protected getMaskPath() {\n    return getMaskPath(this.points);\n  }\n  protected getMaskAttrs() {\n    return getMaskAttrs(this.points);\n  }\n}\n\nexport default SmoothPathMask;\n"]}