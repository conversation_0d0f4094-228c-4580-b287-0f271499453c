import PathMask from './path';
/**
 * 生成 mask 的路径
 * @param points
 * @returns
 */
export declare function getMaskPath(points: any): import("@antv/g-base").PathCommand[];
export declare function getMaskAttrs(points: any): {
    path: import("@antv/g-base").PathCommand[];
};
/**
 * Smooth path mask
 * @ignore
 */
declare class SmoothPathMask extends PathMask {
    protected getMaskPath(): import("@antv/g-base").PathCommand[];
    protected getMaskAttrs(): {
        path: import("@antv/g-base").PathCommand[];
    };
}
export default SmoothPathMask;
