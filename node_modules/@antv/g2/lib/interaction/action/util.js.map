{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../src/interaction/action/util.ts"], "names": [], "mappings": ";;;AAAA,mCAA2C;AAK3C,uDAAgF;AAChF,wCAA2C;AAC3C,6CAAsD;AAGtD,SAAS,WAAW,CAAC,OAA4B,EAAE,SAAiB;IAClE,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,IAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;IAC/B,OAAO,kBAAkB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAClD,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,SAAS,EAAE,SAAiB;IACnD,IAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;IACnC,IAAA,KAAK,GAAa,QAAQ,MAArB,EAAE,MAAM,GAAK,QAAQ,OAAb,CAAc;IACnC,OAAO,KAAK,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,SAAS,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC;AAChF,CAAC;AAED;;;;;GAKG;AACH,SAAS,kBAAkB,CAAC,SAAS,EAAE,SAAiB;IACtD,IAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;IAC3C,OAAO,eAAe,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;AACjE,CAAC;AAED;;;;;GAKG;AACH,SAAS,oBAAoB,CAAC,OAA4B,EAAE,SAAiB;IACnE,IAAA,UAAU,GAAK,OAAO,CAAC,KAAK,WAAlB,CAAmB;IACrC,OAAO,UAAU,CAAC,GAAG,CAAC,UAAC,SAAS,IAAK,OAAA,kBAAkB,CAAC,SAAS,EAAE,SAAS,CAAC,EAAxC,CAAwC,CAAC,CAAC,MAAM,CAAC,UAAC,IAAI,IAAK,OAAA,CAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;AAC1G,CAAC;AAED,SAAS,WAAW,CAAC,OAA4B,EAAE,SAAiB;IAClE,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,IAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;IAC/B,OAAO,sBAAsB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACtD,CAAC;AAED;;;;;GAKG;AACH,SAAS,sBAAsB,CAAC,SAAS,EAAE,SAAiB;IAC1D,OAAO,eAAe,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC/E,CAAC;AAED;;;;;GAKG;AACH,SAAS,oBAAoB,CAAC,OAA4B,EAAE,SAAiB;IACnE,IAAA,UAAU,GAAK,OAAO,CAAC,KAAK,WAAlB,CAAmB;IACrC,OAAO,UAAU,CAAC,GAAG,CAAC,UAAC,SAAS,IAAK,OAAA,sBAAsB,CAAC,SAAS,EAAE,SAAS,CAAC,EAA5C,CAA4C,CAAC,CAAC;AACrF,CAAC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAAC,OAA4B;IAC5D,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,IAAI,OAAO,CAAC;IACZ,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC5B,IAAI,MAAM,EAAE;QACV,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;KACjC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AARD,8CAQC;AAED;;;;GAIG;AACH,SAAgB,mBAAmB,CAAC,OAA4B;IAC9D,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC5B,IAAI,cAAc,CAAC;IACnB,IAAI,MAAM,EAAE;QACV,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;KAC/C;IACD,OAAO,cAAc,CAAC;AACxB,CAAC;AARD,kDAQC;AAED,SAAgB,eAAe,CAAC,OAA4B;IAC1D,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;IACnC,qCAAqC;IACrC,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;QAChH,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAPD,0CAOC;AAED;;;;GAIG;AACH,SAAgB,MAAM,CAAC,cAA2B;IAChD,OAAO,cAAc,IAAI,cAAc,CAAC,SAAS,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;AACzF,CAAC;AAFD,wBAEC;AAED;;;;GAIG;AACH,SAAgB,QAAQ,CAAC,cAA2B;IAClD,OAAO,cAAc,IAAI,cAAc,CAAC,SAAS,IAAI,cAAc,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC3F,CAAC;AAFD,4BAEC;AAED;;;;GAIG;AACH,SAAgB,MAAM,CAAC,OAA4B;IACjD,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC5B,OAAO,CAAC,MAAM,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,GAAG,CAAC,MAAM,CAAC,MAAK,MAAM,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC;AAC/E,CAAC;AAJD,wBAIC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,OAA4B;;IACzD,OAAO,CAAA,MAAA,OAAO,CAAC,KAAK,CAAC,MAAM,0CAAE,GAAG,CAAC,MAAM,CAAC,MAAK,YAAY,CAAC;AAC5D,CAAC;AAFD,wCAEC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAAC,OAA4B,EAAE,SAAiB;IAC/E,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;IAEpC,eAAe;IACf,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE;QAC3B,OAAO,sBAAsB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;KACnD;IAED,QAAQ;IACR,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE;QACjC,IAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO;SACR;QACD,OAAO,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KAClD;IACD,IAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACjD,iBAAiB;IACjB,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,IAAI,CAAC;KACb;IACD,OAAO,oBAAoB,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACtD,CAAC;AAtBD,8CAsBC;AAED;;;;GAIG;AACH,SAAS,sBAAsB,CAAC,OAA4B,EAAE,SAAiB;IACrE,IAAA,MAAM,GAAK,OAAO,CAAC,KAAK,OAAlB,CAAmB;IACjC,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE;QACjC,IAAM,YAAY,GAAG,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC9D,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,OAAO,YAAY,CAAC,OAAO,CAAC,UAAC,QAAQ,IAAK,OAAA,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAzC,CAAyC,CAAC,CAAC;SACtF;QACD,OAAO,IAAI,CAAC;KACb;IACD,IAAM,YAAY,GAAG,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAC9D,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3B,OAAO,YAAY,CAAC,OAAO,CAAC,UAAC,QAAQ,IAAK,OAAA,oBAAoB,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,EAA5C,CAA4C,CAAC,CAAC;KACzF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CAAC,OAA4B,EAAE,OAAa,EAAE,SAAiB;IACnG,eAAe;IACf,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE;QAC3B,OAAO,6BAA6B,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;KACnE;IAED,QAAQ;IACR,IAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACjD,iBAAiB;IACjB,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,IAAI,CAAC;KACb;IACD,OAAO,4BAA4B,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC;AAbD,wDAaC;AAED;;;;;;GAMG;AACH,SAAS,4BAA4B,CAAC,QAAQ,EAAE,OAA4B,EAAE,OAAa;IACzF,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1B,IAAM,KAAK,GAAG,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/E,IAAM,GAAG,GAAG,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IACnF,IAAM,GAAG,GAAG;QACV,IAAI,EAAE,KAAK,CAAC,CAAC;QACb,IAAI,EAAE,KAAK,CAAC,CAAC;QACb,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,CAAC,CAAC;KACZ,CAAC;IACF,OAAO,oBAAoB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;;GAMG;AACH,SAAS,6BAA6B,CAAC,OAA4B,EAAE,OAAa,EAAE,SAAiB;IACnG,IAAM,YAAY,GAAG,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAC9D,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3B,OAAO,YAAY,CAAC,OAAO,CAAC,UAAC,QAAQ,IAAK,OAAA,4BAA4B,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,EAAxD,CAAwD,CAAC,CAAC;KACrG;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CAAC,IAAU;IACpC,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,IAAI,GAAG,GAAc,EAAE,CAAC;IACxB,IAAA,WAAI,EAAC,UAAU,EAAE,UAAC,IAAc;QAC9B,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACnC,IAAA,WAAI,EAAC,IAAI,CAAC,KAAK,EAAE,UAAC,OAAO;YACvB,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;KACJ;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAbD,kCAaC;AAED;;;;;;GAMG;AACH,SAAgB,kBAAkB,CAAC,IAAU,EAAE,KAAa,EAAE,KAAU;IACtE,IAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,OAAO,QAAQ,CAAC,MAAM,CAAC,UAAC,EAAE;QACxB,OAAO,eAAe,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC;IAC9C,CAAC,CAAC,CAAC;AACL,CAAC;AALD,gDAKC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,IAAU,EAAE,SAAiB;IAC9D,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,IAAI,GAAG,GAAc,EAAE,CAAC;IACxB,IAAA,WAAI,EAAC,UAAU,EAAE,UAAC,IAAc;QAC9B,IAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,UAAC,EAAE,IAAK,OAAA,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAtB,CAAsB,CAAC,CAAC;QACpE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IACH,OAAO,GAAG,CAAC;AACb,CAAC;AARD,gDAQC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,OAAgB,EAAE,KAAK;IACrD,IAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;IACjC,IAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC;IAC1B,IAAI,KAAK,CAAC;IACV,IAAI,IAAA,cAAO,EAAC,MAAM,CAAC,EAAE;QACnB,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;KAC1B;SAAM;QACL,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;KACvB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAVD,0CAUC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,IAAI,EAAE,IAAI;IACtC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7G,CAAC;AAFD,sCAEC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAAC,IAAU,EAAE,GAAG;IAClD,IAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,IAAM,GAAG,GAAG,EAAE,CAAC;IACf,IAAA,WAAI,EAAC,QAAQ,EAAE,UAAC,EAAE;QAChB,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;QACvB,IAAM,SAAS,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;QACxC,IAAI,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACd;IACH,CAAC,CAAC,CAAC;IACH,OAAO,GAAG,CAAC;AACb,CAAC;AAXD,oDAWC;AACD,SAAS,YAAY,CAAC,IAAW;IAC/B,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAA,WAAI,EAAC,IAAI,EAAE,UAAC,GAAG;QACb,IAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,OAAO,KAAK,GAAG,EAAE;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACzC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aACnC;SACF;aAAM;YACL,IAAM,QAAM,GAAG,GAAG,CAAC,MAAM,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,QAAM,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,QAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACjD;IACH,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,IAAU,EAAE,IAAW;IACvD,IAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,IAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IAClC,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAC,EAAW;QACtC,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;QACvB,IAAI,WAAW,CAAC;QAChB,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE;YAChC,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;SAChD;aAAM;YACL,IAAM,SAAS,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;YACxC,WAAW,GAAG,IAAA,eAAQ,EAAC,SAAS,CAAC,CAAC;SACnC;QACD,OAAO,IAAA,+BAAmB,EAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IACH,OAAO,GAAG,CAAC;AACb,CAAC;AAfD,8CAeC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,IAAI;IAChC,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,UAAC,EAAmB,IAAK,OAAA,EAAE,CAAC,SAAS,EAAZ,CAAY,CAAC,CAAC;AACzE,CAAC;AAFD,sCAEC;AAED,cAAc;AACd,SAAgB,QAAQ,CAAC,EAAS,EAAE,EAAS;IAC3C,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACvB,IAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACvB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACtC,CAAC;AAJD,4BAIC;AAED,cAAc;AACd,SAAgB,SAAS,CAAC,MAAe,EAAE,CAAU;IACnD,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;QACtB,OAAO,IAAA,kBAAW,EAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KACnC;IACD,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,IAAM,GAAG,GAAG,EAAE,CAAC;IACf,IAAA,WAAI,EAAC,MAAM,EAAE,UAAC,KAAK;QACjB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAClB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;IACH,IAAM,IAAI,GAAG,IAAA,wBAAiB,EAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAC7C,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,OAAO,IAAI,CAAC;AACd,CAAC;AAbD,8BAaC;AAED;;;;;GAKG;AACH,SAAgB,OAAO,CAAC,GAAS,EAAE,KAAY;IAC7C,OAAO,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;AAC3F,CAAC;AAFD,0BAEC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,IAAU;IACpC,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,IAAI,QAAQ,GAAG,IAAI,CAAC;IACpB,IAAI,MAAM,EAAE;QACV,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,KAAK,IAAI,EAAZ,CAAY,CAAC,CAAC;KACvD;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAPD,kCAOC;AAED,SAAS,eAAe,CAAC,IAAU,EAAE,KAAY;IAC/C,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;IACnC,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC;AACD;;;;;;GAMG;AACH,SAAgB,eAAe,CAAC,IAAU,EAAE,OAAa,EAAE,KAAY;IACrE,IAAM,WAAW,GAAG,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjD,OAAO,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACtD,CAAC;AAHD,0CAGC;AAED;;;;;;;;;;;GAWG;AACH,SAAgB,WAAW,CAAC,OAAiB,EAAE,MAAc,EAAE,MAAc,EAAE,MAAc;IAC3F,IAAI,IAAI,GAAG,KAAK,CAAC;IACjB,IAAA,WAAI,EAAC,OAAO,EAAE,UAAC,CAAC;QACd,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE;YAChE,IAAI,GAAG,IAAI,CAAC;YACZ,OAAO,KAAK,CAAC;SACd;IACH,CAAC,CAAC,CAAC;IACH,OAAO,IAAI,CAAC;AACd,CAAC;AATD,kCASC;AAED,4CAA4C;AAC5C,SAAgB,eAAe,CAAC,IAAU,EAAE,KAAa;IACvD,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE;QACxB,IAAA,WAAI,EAAC,IAAI,CAAC,KAAK,EAAE,UAAC,OAAO;YACvB,KAAK,GAAG,eAAe,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACxC,IAAI,KAAK,EAAE;gBACT,OAAO,KAAK,CAAC,CAAC,OAAO;aACtB;QACH,CAAC,CAAC,CAAC;KACJ;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAXD,0CAWC", "sourcesContent": ["import { each, isArray } from '@antv/util';\nimport { View } from '../../chart';\nimport { BBox, PathCommand, Point } from '../../dependents';\nimport Geometry from '../../geometry/base';\nimport Element from '../../geometry/element/';\nimport { catmullRom2bezier, getLinePath } from '../../geometry/shape/util/path';\nimport { toPoints } from '../../util/bbox';\nimport { isPolygonsIntersect } from '@antv/path-util';\nimport { ComponentOption, IInteractionContext, LooseObject } from '../../interface';\n\nfunction getMaskBBox(context: IInteractionContext, tolerance: number) {\n  const event = context.event;\n  const maskShape = event.target;\n  return getMaskBBoxByShape(maskShape, tolerance);\n}\n\n/**\n * 如果 mask BBox 过小则不返回\n */\nfunction isValidMaskBBox(maskShape, tolerance: number) {\n  const maskBBox = maskShape.getCanvasBBox();\n  const { width, height } = maskBBox;\n  return width > 0 && height > 0 && (width >= tolerance || height >= tolerance);\n}\n\n/**\n * 通过 maskShape 获取 mask 的 canvasBBox\n * @param maskShape\n * @param tolerance\n * @returns\n */\nfunction getMaskBBoxByShape(maskShape, tolerance: number) {\n  const maskBBox = maskShape.getCanvasBBox();\n  return isValidMaskBBox(maskShape, tolerance) ? maskBBox : null;\n}\n\n/**\n * 获取 multiple 模式下 mask 的 canvasBBox 数组\n * @param context 上下文\n * @param tolerance box 宽高小于则不返回\n * @returns\n */\nfunction getMultiMaskBBoxList(context: IInteractionContext, tolerance: number) {\n  const { maskShapes } = context.event;\n  return maskShapes.map((maskShape) => getMaskBBoxByShape(maskShape, tolerance)).filter((bBox) => !!bBox);\n}\n\nfunction getMaskPath(context: IInteractionContext, tolerance: number) {\n  const event = context.event;\n  const maskShape = event.target;\n  return getMaskPathByMaskShape(maskShape, tolerance);\n}\n\n/**\n * 通过 maskShape 获取 mask path\n * @param maskShape\n * @param tolerance box 宽高小于则不返回\n * @returns\n */\nfunction getMaskPathByMaskShape(maskShape, tolerance: number) {\n  return isValidMaskBBox(maskShape, tolerance) ? maskShape.attr('path') : null;\n}\n\n/**\n * 获取 multiple 模式下 mask path 数组\n * @param context 上下文\n * @param tolerance box 宽高小于则不返回\n * @returns\n */\nfunction getMultiMaskPathList(context: IInteractionContext, tolerance: number) {\n  const { maskShapes } = context.event;\n  return maskShapes.map((maskShape) => getMaskPathByMaskShape(maskShape, tolerance));\n}\n\n/**\n * 获取当前事件相关的图表元素\n * @param context 交互的上下文\n * @ignore\n */\nexport function getCurrentElement(context: IInteractionContext): Element {\n  const event = context.event;\n  let element;\n  const target = event.target;\n  if (target) {\n    element = target.get('element');\n  }\n  return element;\n}\n\n/**\n * 获取委托对象\n * @param context 上下文\n * @ignore\n */\nexport function getDelegationObject(context: IInteractionContext): LooseObject {\n  const event = context.event;\n  const target = event.target;\n  let delegateObject;\n  if (target) {\n    delegateObject = target.get('delegateObject');\n  }\n  return delegateObject;\n}\n\nexport function isElementChange(context: IInteractionContext) {\n  const event = context.event.gEvent;\n  // 在同一个 element 内部移动，label 和 shape 之间\n  if (event && event.fromShape && event.toShape && event.fromShape.get('element') === event.toShape.get('element')) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * 是否是列表组件\n * @param delegateObject 委托对象\n * @ignore\n */\nexport function isList(delegateObject: LooseObject): boolean {\n  return delegateObject && delegateObject.component && delegateObject.component.isList();\n}\n\n/**\n * 是否是滑块组件\n * @param delegateObject 委托对象\n * @ignore\n */\nexport function isSlider(delegateObject: LooseObject): boolean {\n  return delegateObject && delegateObject.component && delegateObject.component.isSlider();\n}\n\n/**\n * 是否由 mask 触发\n * @param context 上下文\n * @ignore\n */\nexport function isMask(context: IInteractionContext): boolean {\n  const event = context.event;\n  const target = event.target;\n  return (target && target?.get('name') === 'mask') || isMultipleMask(context);\n}\n\n/**\n * 是否由 multiple mask 触发\n * @param context\n * @returns\n */\nexport function isMultipleMask(context: IInteractionContext): boolean {\n  return context.event.target?.get('name') === 'multi-mask';\n}\n\n/**\n * 获取被遮挡的 elements\n * @param context 上下文\n * @ignore\n */\nexport function getMaskedElements(context: IInteractionContext, tolerance: number): Element[] {\n  const target = context.event.target;\n\n  // multiple 模式下\n  if (isMultipleMask(context)) {\n    return getMultiMaskedElements(context, tolerance);\n  }\n\n  // 正常模式下\n  if (target.get('type') === 'path') {\n    const maskPath = getMaskPath(context, tolerance);\n    if (!maskPath) {\n      return;\n    }\n    return getElementsByPath(context.view, maskPath);\n  }\n  const maskBBox = getMaskBBox(context, tolerance);\n  // 如果 bbox 过小则不返回\n  if (!maskBBox) {\n    return null;\n  }\n  return getIntersectElements(context.view, maskBBox);\n}\n\n/**\n * 获取 multiple 模式下被 mask 遮挡的 elements\n * @param context 上下文\n * @returns\n */\nfunction getMultiMaskedElements(context: IInteractionContext, tolerance: number): Element[] {\n  const { target } = context.event;\n  if (target.get('type') === 'path') {\n    const maskPathList = getMultiMaskPathList(context, tolerance);\n    if (maskPathList.length > 0) {\n      return maskPathList.flatMap((maskPath) => getElementsByPath(context.view, maskPath));\n    }\n    return null;\n  }\n  const maskBBoxList = getMultiMaskBBoxList(context, tolerance);\n  if (maskBBoxList.length > 0) {\n    return maskBBoxList.flatMap((maskBBox) => getIntersectElements(context.view, maskBBox));\n  }\n  return null;\n}\n\n/**\n * @ignore\n */\nexport function getSiblingMaskElements(context: IInteractionContext, sibling: View, tolerance: number) {\n  // multiple 模式下\n  if (isMultipleMask(context)) {\n    return getSiblingMultiMaskedElements(context, sibling, tolerance);\n  }\n\n  // 正常模式下\n  const maskBBox = getMaskBBox(context, tolerance);\n  // 如果 bbox 过小则不返回\n  if (!maskBBox) {\n    return null;\n  }\n  return getSiblingMaskElementsByBBox(maskBBox, context, sibling);\n}\n\n/**\n * 通过 mashBBox 获取 sibling 模式下被 mask 遮挡的 elements\n * @param maskBBox\n * @param context 上下文\n * @param sibling sibling view\n * @returns\n */\nfunction getSiblingMaskElementsByBBox(maskBBox, context: IInteractionContext, sibling: View) {\n  const view = context.view;\n  const start = getSiblingPoint(view, sibling, { x: maskBBox.x, y: maskBBox.y });\n  const end = getSiblingPoint(view, sibling, { x: maskBBox.maxX, y: maskBBox.maxY });\n  const box = {\n    minX: start.x,\n    minY: start.y,\n    maxX: end.x,\n    maxY: end.y,\n  };\n  return getIntersectElements(sibling, box);\n}\n\n/**\n * 获取 sibling 模式下被 multiple mask 遮挡的 elements\n * @param context 上下文\n * @param sibling sibling view\n * @param tolerance box 宽高小于则不返回\n * @returns\n */\nfunction getSiblingMultiMaskedElements(context: IInteractionContext, sibling: View, tolerance: number): Element[] {\n  const maskBBoxList = getMultiMaskBBoxList(context, tolerance);\n  if (maskBBoxList.length > 0) {\n    return maskBBoxList.flatMap((maskBBox) => getSiblingMaskElementsByBBox(maskBBox, context, sibling));\n  }\n  return null;\n}\n\n/**\n * 获取所有的图表元素\n * @param view View/Chart\n * @ignore\n */\nexport function getElements(view: View): Element[] {\n  const geometries = view.geometries;\n  let rst: Element[] = [];\n  each(geometries, (geom: Geometry) => {\n    const elements = geom.elements;\n    rst = rst.concat(elements);\n  });\n  if (view.views && view.views.length) {\n    each(view.views, (subView) => {\n      rst = rst.concat(getElements(subView));\n    });\n  }\n  return rst;\n}\n\n/**\n * 获取所有的图表元素\n * @param view View/Chart\n * @param field 字段名\n * @param value 字段值\n * @ignore\n */\nexport function getElementsByField(view: View, field: string, value: any) {\n  const elements = getElements(view);\n  return elements.filter((el) => {\n    return getElementValue(el, field) === value;\n  });\n}\n\n/**\n * 根据状态名获取图表元素\n * @param view View/Chart\n * @param stateName 状态名\n * @ignore\n */\nexport function getElementsByState(view: View, stateName: string): Element[] {\n  const geometries = view.geometries;\n  let rst: Element[] = [];\n  each(geometries, (geom: Geometry) => {\n    const elements = geom.getElementsBy((el) => el.hasState(stateName));\n    rst = rst.concat(elements);\n  });\n  return rst;\n}\n\n/**\n * 获取图表元素对应字段的值\n * @param element 图表元素\n * @param field 字段名\n * @ignore\n */\nexport function getElementValue(element: Element, field) {\n  const model = element.getModel();\n  const record = model.data;\n  let value;\n  if (isArray(record)) {\n    value = record[0][field];\n  } else {\n    value = record[field];\n  }\n  return value;\n}\n\n/**\n * 两个包围盒是否相交\n * @param box1 包围盒1\n * @param box2 包围盒2\n * @ignore\n */\nexport function intersectRect(box1, box2) {\n  return !(box2.minX > box1.maxX || box2.maxX < box1.minX || box2.minY > box1.maxY || box2.maxY < box1.minY);\n}\n\n/**\n * 获取包围盒内的图表元素\n * @param view View/Chart\n * @param box 包围盒\n * @ignore\n */\nexport function getIntersectElements(view: View, box) {\n  const elements = getElements(view);\n  const rst = [];\n  each(elements, (el) => {\n    const shape = el.shape;\n    const shapeBBox = shape.getCanvasBBox();\n    if (intersectRect(box, shapeBBox)) {\n      rst.push(el);\n    }\n  });\n  return rst;\n}\nfunction pathToPoints(path: any[]) {\n  const points = [];\n  each(path, (seg) => {\n    const command = seg[0];\n    if (command !== 'A') {\n      for (let i = 1; i < seg.length; i = i + 2) {\n        points.push([seg[i], seg[i + 1]]);\n      }\n    } else {\n      const length = seg.length;\n      points.push([seg[length - 2], seg[length - 1]]);\n    }\n  });\n  return points;\n}\n/**\n * 获取包围盒内的图表元素\n * @param view View/Chart\n * @param path 路径\n * @ignore\n */\nexport function getElementsByPath(view: View, path: any[]) {\n  const elements = getElements(view);\n  const points = pathToPoints(path);\n  const rst = elements.filter((el: Element) => {\n    const shape = el.shape;\n    let shapePoints;\n    if (shape.get('type') === 'path') {\n      shapePoints = pathToPoints(shape.attr('path'));\n    } else {\n      const shapeBBox = shape.getCanvasBBox();\n      shapePoints = toPoints(shapeBBox);\n    }\n    return isPolygonsIntersect(points, shapePoints);\n  });\n  return rst;\n}\n\n/**\n * 获取当前 View 的所有组件\n * @param view View/Chart\n * @ignore\n */\nexport function getComponents(view) {\n  return view.getComponents().map((co: ComponentOption) => co.component);\n}\n\n/** @ignore */\nexport function distance(p1: Point, p2: Point) {\n  const dx = p2.x - p1.x;\n  const dy = p2.y - p1.y;\n  return Math.sqrt(dx * dx + dy * dy);\n}\n\n/** @ignore */\nexport function getSpline(points: Point[], z: boolean): PathCommand[] {\n  if (points.length <= 2) {\n    return getLinePath(points, false);\n  }\n  const first = points[0];\n  const arr = [];\n  each(points, (point) => {\n    arr.push(point.x);\n    arr.push(point.y);\n  });\n  const path = catmullRom2bezier(arr, z, null);\n  path.unshift(['M', first.x, first.y]);\n  return path;\n}\n\n/**\n * 检测点是否在包围盒内\n * @param box 包围盒\n * @param point 点\n * @ignore\n */\nexport function isInBox(box: BBox, point: Point) {\n  return box.x <= point.x && box.maxX >= point.x && box.y <= point.y && box.maxY > point.y;\n}\n\n/**\n * 获取同 view 同一级的 views\n * @param view 当前 view\n * @returns 同一级的 views\n * @ignore\n */\nexport function getSilbings(view: View): View[] {\n  const parent = view.parent;\n  let siblings = null;\n  if (parent) {\n    siblings = parent.views.filter((sub) => sub !== view);\n  }\n  return siblings;\n}\n\nfunction point2Normalize(view: View, point: Point): Point {\n  const coord = view.getCoordinate();\n  return coord.invert(point);\n}\n/**\n * 将 view 上的一点转换成另一个 view 的点\n * @param view 当前的 view\n * @param sibling 同一层级的 view\n * @param point 指定点\n * @ignore\n */\nexport function getSiblingPoint(view: View, sibling: View, point: Point): Point {\n  const normalPoint = point2Normalize(view, point);\n  return sibling.getCoordinate().convert(normalPoint);\n}\n\n/**\n * 是否在记录中，临时因为所有的 view 中的数据不是引用，而使用的方法\n * 不同 view 上对数据的引用不相等，导致无法直接用 includes\n * 假设 x, y 值相等时是同一条数据，这个假设不完全正确，而改成 isEqual 则成本太高\n * 后面改成同一个引用时可以修改回来\n * @param records\n * @param record\n * @param xFiled\n * @param yField\n * @returns\n * @ignore\n */\nexport function isInRecords(records: object[], record: object, xFiled: string, yField: string) {\n  let isIn = false;\n  each(records, (r) => {\n    if (r[xFiled] === record[xFiled] && r[yField] === record[yField]) {\n      isIn = true;\n      return false;\n    }\n  });\n  return isIn;\n}\n\n// 级联获取 field 对应的 scale，如果 view 上没有，遍历子 view\nexport function getScaleByField(view: View, field: string) {\n  let scale = view.getScaleByField(field);\n  if (!scale && view.views) {\n    each(view.views, (subView) => {\n      scale = getScaleByField(subView, field);\n      if (scale) {\n        return false; // 终止循环\n      }\n    });\n  }\n  return scale;\n}\n"]}