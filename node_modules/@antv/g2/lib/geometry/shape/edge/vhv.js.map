{"version": 3, "file": "vhv.js", "sourceRoot": "", "sources": ["../../../../src/geometry/shape/edge/vhv.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAIlC,gCAAwC;AACxC,+CAA6C;AAE7C,IAAM,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;AAE7B,SAAS,UAAU,CAAC,IAAW,EAAE,EAAS;IACxC,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,CAAC,IAAI,CAAC;QACV,CAAC,EAAE,IAAI,CAAC,CAAC;QACT,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,cAAc;KACzD,CAAC,CAAC;IACH,MAAM,CAAC,IAAI,CAAC;QACV,CAAC,EAAE,EAAE,CAAC,CAAC;QACP,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,cAAc;KACzD,CAAC,CAAC;IACH,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEhB,IAAM,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,IAAA,WAAI,EAAC,MAAM,EAAE,UAAC,KAAK;QACjB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,OAAO,IAAI,CAAC;AACd,CAAC;AAED,IAAA,oBAAa,EAAC,MAAM,EAAE,KAAK,EAAE;IAC3B,IAAI,YAAC,GAAc,EAAE,SAAiB;QACpC,IAAM,KAAK,GAAG,IAAA,oBAAQ,EAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QACtD,IAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAU,EAAE,MAAM,CAAC,CAAC,CAAU,CAAC,CAAC,CAAC;QAChF,OAAO,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;YAChC,KAAK,wCACA,KAAK,KACR,IAAI,MAAA,GACL;SACF,CAAC,CAAC;IACL,CAAC;IACD,SAAS,YAAC,SAAyB;QACjC,OAAO;YACL,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE;gBACL,CAAC,EAAE,GAAG;gBACN,IAAI,EAAE,SAAS,CAAC,KAAK;aACtB;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["import { each } from '@antv/util';\nimport { IGroup } from '../../../dependents';\nimport { Point, ShapeInfo, ShapeMarkerCfg } from '../../../interface';\n\nimport { registerShape } from '../base';\nimport { getStyle } from '../util/get-style';\n\nconst CORNER_PERCENT = 1 / 3;\n\nfunction getVHVPath(from: Point, to: Point) {\n  const points = [];\n  points.push({\n    x: from.x,\n    y: from.y * (1 - CORNER_PERCENT) + to.y * CORNER_PERCENT,\n  });\n  points.push({\n    x: to.x,\n    y: from.y * (1 - CORNER_PERCENT) + to.y * CORNER_PERCENT,\n  });\n  points.push(to);\n\n  const path = [['M', from.x, from.y]];\n  each(points, (point) => {\n    path.push(['L', point.x, point.y]);\n  });\n\n  return path;\n}\n\nregisterShape('edge', 'vhv', {\n  draw(cfg: ShapeInfo, container: IGroup) {\n    const style = getStyle(cfg, true, false, 'lineWidth');\n    const points = cfg.points;\n    const path = this.parsePath(getVHVPath(points[0] as Point, points[1] as Point));\n    return container.addShape('path', {\n      attrs: {\n        ...style,\n        path,\n      },\n    });\n  },\n  getMarker(markerCfg: ShapeMarkerCfg) {\n    return {\n      symbol: 'circle',\n      style: {\n        r: 4.5,\n        fill: markerCfg.color,\n      },\n    };\n  },\n});\n"]}