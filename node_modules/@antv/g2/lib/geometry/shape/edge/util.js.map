{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/geometry/shape/edge/util.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAGlC;;;;;;GAMG;AACH,SAAgB,QAAQ,CAAC,IAAW,EAAE,EAAS;IAC7C,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/G,CAAC;AAFD,4BAEC;AAED;;;;;;GAMG;AACH,SAAgB,QAAQ,CAAC,EAAS,EAAE,MAAa;IAC/C,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,CAAC,IAAI,CAAC;QACV,CAAC,EAAE,MAAM,CAAC,CAAC;QACX,CAAC,EAAE,MAAM,CAAC,CAAC;KACZ,CAAC,CAAC;IACH,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEhB,IAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAClB,IAAA,WAAI,EAAC,MAAM,EAAE,UAAC,KAAK;QACjB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC;AACb,CAAC;AAdD,4BAcC", "sourcesContent": ["import { each } from '@antv/util';\nimport { Point } from '../../../interface';\n\n/**\n * @ignore\n * Gets cpath\n * @param from\n * @param to\n * @returns\n */\nexport function getCPath(from: Point, to: Point) {\n  return ['C', (from.x * 1) / 2 + (to.x * 1) / 2, from.y, (from.x * 1) / 2 + (to.x * 1) / 2, to.y, to.x, to.y];\n}\n\n/**\n * @ignore\n * Gets qpath\n * @param to\n * @param center\n * @returns\n */\nexport function getQPath(to: Point, center: Point) {\n  const points = [];\n  points.push({\n    x: center.x,\n    y: center.y,\n  });\n  points.push(to);\n\n  const sub = ['Q'];\n  each(points, (point) => {\n    sub.push(point.x, point.y);\n  });\n\n  return sub;\n}\n"]}