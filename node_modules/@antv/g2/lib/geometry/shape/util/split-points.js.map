{"version": 3, "file": "split-points.js", "sourceRoot": "", "sources": ["../../../../src/geometry/shape/util/split-points.ts"], "names": [], "mappings": ";;;AAAA,mCAAqC;AAGrC;;;;;;;;;;GAUG;AACH,SAAgB,WAAW,CAAC,GAAe;IACzC,uDAAuD;IACvD,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAChB,IAAM,CAAC,GAAG,IAAA,cAAO,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAE3C,OAAO,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK,EAAE,KAAK;QACxB,OAAO;YACL,CAAC,EAAE,IAAA,cAAO,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,EAAE,KAAK;SACT,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAXD,kCAWC", "sourcesContent": ["import { isArray } from '@antv/util';\nimport { Point, RangePoint } from '../../../interface';\n\n/**\n * @ignore\n * 拆分点数据\n * @example\n * // result: [{x: 20, y: 20}, {x: 20, y: 30}]\n * splitPoints({x: 20,y: [20, 30]});\n * @example\n * // result: [{x: 20, y: 20}, {x: 30, y: 30}]\n * splitPoints({x: [20, 30],y: [20, 30]});\n * @param obj\n */\nexport function splitPoints(obj: RangePoint): Point[] {\n  // y 有可能是数组，对应原始数据中 y 为一个区间数据，如 [19, 30]，为了统一也将 x 转换为数组\n  const x = obj.x;\n  const y = isArray(obj.y) ? obj.y : [obj.y];\n\n  return y.map((eachY, index) => {\n    return {\n      x: isArray(x) ? x[index] : x,\n      y: eachY,\n    };\n  });\n}\n"]}