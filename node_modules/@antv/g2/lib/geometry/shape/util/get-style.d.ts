import { ShapeInfo } from '../../../interface';
/**
 * @ignore
 * 获取 Shape 的图形属性
 * @param cfg
 * @param isStroke 是否需要描边
 * @param isFill 是否需要填充
 * @param [sizeName] 可选，表示图形大小的属性，lineWidth 或者 r
 * @returns
 */
export declare function getStyle(cfg: ShapeInfo, isStroke: boolean, isFill: boolean, sizeName?: string): {
    [x: string]: any;
};
/**
 * 获取 矩形背景 的样式
 * @param cfg
 */
export declare function getBackgroundRectStyle(cfg?: ShapeInfo): any;
