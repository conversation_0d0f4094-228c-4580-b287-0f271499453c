{"version": 3, "file": "tick.js", "sourceRoot": "", "sources": ["../../../../src/geometry/shape/interval/tick.ts"], "names": [], "mappings": ";;;AAAA,mCAAqC;AAIrC,gCAAwC;AACxC,+CAA6C;AAE7C,8BAA8B;AAC9B,SAAS,aAAa,CAAC,SAAqB;;IAClC,IAAA,CAAC,GAAkB,SAAS,EAA3B,EAAE,CAAC,GAAe,SAAS,EAAxB,EAAE,EAAE,GAAW,SAAS,GAApB,EAAE,IAAI,GAAK,SAAS,KAAd,CAAe;IACrC,IAAI,IAAI,CAAC;IACT,IAAI,IAAI,CAAC;IACT,IAAI,IAAA,cAAO,EAAC,CAAC,CAAC,EAAE;QACd,KAAA,eAAe,CAAC,IAAA,EAAf,IAAI,QAAA,EAAE,IAAI,QAAA,CAAM;KAClB;SAAM;QACL,IAAI,GAAG,EAAE,CAAC;QACV,IAAI,GAAG,CAAC,CAAC;KACV;IAED,IAAM,IAAI,GAAI,CAAY,GAAG,IAAI,GAAG,CAAC,CAAC;IACtC,IAAM,IAAI,GAAI,CAAY,GAAG,IAAI,GAAG,CAAC,CAAC;IAEtC,aAAa;IACb,YAAY;IACZ,QAAQ;IACR,YAAY;IACZ,OAAO;QACL,EAAE,CAAC,EAAE,CAAW,EAAE,CAAC,EAAE,IAAI,EAAE;QAC3B,EAAE,CAAC,EAAE,CAAW,EAAE,CAAC,EAAE,IAAI,EAAE;QAC3B,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;QACpB,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;QACpB,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;QACpB,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE;KACrB,CAAC;AACJ,CAAC;AAED,qBAAqB;AACrB,SAAS,WAAW,CAAC,MAAe;IAClC,OAAO;QACL,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAChC,CAAC;AACJ,CAAC;AAED,kCAAkC;AAClC,IAAA,oBAAa,EAAC,UAAU,EAAE,MAAM,EAAE;IAChC,SAAS,YAAC,UAAsB;QAC9B,OAAO,aAAa,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC;IACD,IAAI,YAAC,GAAc,EAAE,SAAiB;QACpC,IAAM,KAAK,GAAG,IAAA,oBAAQ,EAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACzC,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,MAAiB,CAAC,CAAC,CAAC;QAChE,IAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;YACvC,KAAK,wCACA,KAAK,KACR,IAAI,MAAA,GACL;YACD,IAAI,EAAE,UAAU;SACjB,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IACD,SAAS,YAAC,SAAyB;QACzB,IAAA,KAAK,GAAK,SAAS,MAAd,CAAe;QAC5B,OAAO;YACL,MAAM,EAAE,UAAC,CAAS,EAAE,CAAS,EAAE,CAAS;gBACtC,OAAO;oBACL,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;oBACvB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;oBACvB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;oBACf,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;oBACf,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;oBACvB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;iBACxB,CAAC;YACJ,CAAC;YACD,KAAK,EAAE;gBACL,CAAC,EAAE,CAAC;gBACJ,MAAM,EAAE,KAAK;aACd;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["import { isArray } from '@antv/util';\nimport { IGroup } from '../../../dependents';\nimport { Point, ShapeInfo, ShapeMarkerCfg, ShapePoint } from '../../../interface';\n\nimport { registerShape } from '../base';\nimport { getStyle } from '../util/get-style';\n\n// 根据数据点生成 tick shape 的 6 个关键点\nfunction getTickPoints(pointInfo: ShapePoint): Point[] {\n  const { x, y, y0, size } = pointInfo;\n  let yMin;\n  let yMax;\n  if (isArray(y)) {\n    [yMin, yMax] = y;\n  } else {\n    yMin = y0;\n    yMax = y;\n  }\n\n  const xMax = (x as number) + size / 2;\n  const xMin = (x as number) - size / 2;\n\n  // tick 关键点顺序\n  // 4 - 1 - 5\n  //     |\n  // 2 - 0 - 3\n  return [\n    { x: x as number, y: yMin },\n    { x: x as number, y: yMax },\n    { x: xMin, y: yMin },\n    { x: xMax, y: yMin },\n    { x: xMin, y: yMax },\n    { x: xMax, y: yMax },\n  ];\n}\n\n// 根据 tick 关键点绘制 path\nfunction getTickPath(points: Point[]) {\n  return [\n    ['M', points[0].x, points[0].y],\n    ['L', points[1].x, points[1].y],\n    ['M', points[2].x, points[2].y],\n    ['L', points[3].x, points[3].y],\n    ['M', points[4].x, points[4].y],\n    ['L', points[5].x, points[5].y],\n  ];\n}\n\n/** I 形状柱状图，常用于 error bar chart */\nregisterShape('interval', 'tick', {\n  getPoints(shapePoint: ShapePoint) {\n    return getTickPoints(shapePoint);\n  },\n  draw(cfg: ShapeInfo, container: IGroup) {\n    const style = getStyle(cfg, true, false);\n    const path = this.parsePath(getTickPath(cfg.points as Point[]));\n    const shape = container.addShape('path', {\n      attrs: {\n        ...style,\n        path,\n      },\n      name: 'interval',\n    });\n\n    return shape;\n  },\n  getMarker(markerCfg: ShapeMarkerCfg) {\n    const { color } = markerCfg;\n    return {\n      symbol: (x: number, y: number, r: number) => {\n        return [\n          ['M', x - r / 2, y - r],\n          ['L', x + r / 2, y - r],\n          ['M', x, y - r],\n          ['L', x, y + r],\n          ['M', x - r / 2, y + r],\n          ['L', x + r / 2, y + r],\n        ];\n      },\n      style: {\n        r: 5,\n        stroke: color,\n      },\n    };\n  },\n});\n"]}