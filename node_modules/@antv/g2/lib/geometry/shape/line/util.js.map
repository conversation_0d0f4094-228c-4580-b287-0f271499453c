{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/geometry/shape/line/util.ts"], "names": [], "mappings": ";;;AAEA,IAAM,WAAW,GAAG;IAClB,IAAI,EAAE,UAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QACpC,OAAO;YACL,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SAChB,CAAC;IACJ,CAAC;IACD,GAAG,EAAE,UAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QACnC,OAAO;YACL,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SAChB,CAAC;IACJ,CAAC;IACD,IAAI,EAAE,UAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QACpC,OAAO;YACL,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SAChB,CAAC;IACJ,CAAC;IACD,MAAM,EAAE,UAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QACtC,OAAO;YACL,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAClC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACvC,CAAC;IACJ,CAAC;IACD,EAAE,EAAE,UAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QAClC,OAAO;YACL,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACzB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACjB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACjB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;SAC1B,CAAC;IACJ,CAAC;IACD,EAAE,EAAE,UAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QAClC,OAAO;YACL,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACzB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACjB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACjB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;SAC1B,CAAC;IACJ,CAAC;IACD,GAAG,EAAE,UAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QACnC,OAAO;YACL,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YAC3B,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACzB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACzB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACzB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACzB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;SAC1B,CAAC;IACJ,CAAC;IACD,GAAG,EAAE,UAAC,CAAS,EAAE,CAAS;QACxB,eAAe;QACf,OAAO;YACL,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;YACrB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;YACX,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACf,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;SACtB,CAAC;IACJ,CAAC;CACF,CAAC;AAEF;;;;;;GAMG;AACH,SAAgB,aAAa,CAAC,SAAyB,EAAE,SAAiB;IAChE,IAAA,KAAK,GAAK,SAAS,MAAd,CAAe;IAC5B,OAAO;QACL,MAAM,EAAE,WAAW,CAAC,SAAS,CAAC;QAC9B,KAAK,EAAE;YACL,SAAS,EAAE,CAAC;YACZ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,KAAK;SACd;KACF,CAAC;AACJ,CAAC;AAVD,sCAUC", "sourcesContent": ["import { ShapeMarkerCfg } from '../../../interface';\n\nconst LineSymbols = {\n  line: (x: number, y: number, r: number) => {\n    return [\n      ['M', x - r, y],\n      ['L', x + r, y],\n    ];\n  },\n  dot: (x: number, y: number, r: number) => {\n    return [\n      ['M', x - r, y],\n      ['L', x + r, y],\n    ];\n  },\n  dash: (x: number, y: number, r: number) => {\n    return [\n      ['M', x - r, y],\n      ['L', x + r, y],\n    ];\n  },\n  smooth: (x: number, y: number, r: number) => {\n    return [\n      ['M', x - r, y],\n      ['A', r / 2, r / 2, 0, 1, 1, x, y],\n      ['A', r / 2, r / 2, 0, 1, 0, x + r, y],\n    ];\n  },\n  hv: (x: number, y: number, r: number) => {\n    return [\n      ['M', x - r - 1, y - 2.5],\n      ['L', x, y - 2.5],\n      ['L', x, y + 2.5],\n      ['L', x + r + 1, y + 2.5],\n    ];\n  },\n  vh: (x: number, y: number, r: number) => {\n    return [\n      ['M', x - r - 1, y + 2.5],\n      ['L', x, y + 2.5],\n      ['L', x, y - 2.5],\n      ['L', x + r + 1, y - 2.5],\n    ];\n  },\n  hvh: (x: number, y: number, r: number) => {\n    return [\n      ['M', x - (r + 1), y + 2.5],\n      ['L', x - r / 2, y + 2.5],\n      ['L', x - r / 2, y - 2.5],\n      ['L', x + r / 2, y - 2.5],\n      ['L', x + r / 2, y + 2.5],\n      ['L', x + r + 1, y + 2.5],\n    ];\n  },\n  vhv: (x: number, y: number) => {\n    // 宽 13px，高 8px\n    return [\n      ['M', x - 5, y + 2.5],\n      ['L', x - 5, y],\n      ['L', x, y],\n      ['L', x, y - 3],\n      ['L', x, y + 3],\n      ['L', x + 6.5, y + 3],\n    ];\n  },\n};\n\n/**\n * Gets line marker\n * @ignore\n * @param markerCfg\n * @param shapeType\n * @returns 返回 Line 的 marker 配置\n */\nexport function getLineMarker(markerCfg: ShapeMarkerCfg, shapeType: string) {\n  const { color } = markerCfg;\n  return {\n    symbol: LineSymbols[shapeType],\n    style: {\n      lineWidth: 2,\n      r: 6,\n      stroke: color,\n    },\n  };\n}\n"]}