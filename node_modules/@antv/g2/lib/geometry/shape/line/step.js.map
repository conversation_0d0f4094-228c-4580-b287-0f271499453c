{"version": 3, "file": "step.js", "sourceRoot": "", "sources": ["../../../../src/geometry/shape/line/step.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAIlC,gCAAwC;AACxC,2DAAwD;AACxD,+CAA6C;AAC7C,+BAAuC;AAEvC,IAAM,mBAAmB,GAAG,UAAC,KAAY,EAAE,SAAgB,EAAE,SAAiB;IAC5E,IAAM,CAAC,GAAG,KAAK,CAAC,CAAW,CAAC;IAC5B,IAAM,CAAC,GAAG,KAAK,CAAC,CAAW,CAAC;IAC5B,IAAM,KAAK,GAAG,SAAS,CAAC,CAAW,CAAC;IACpC,IAAM,KAAK,GAAG,SAAS,CAAC,CAAW,CAAC;IACpC,IAAI,MAAM,CAAC;IAEX,QAAQ,SAAS,EAAE;QACjB,KAAK,IAAI;YACP,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAA,EAAE,CAAC,CAAC;YAC3B,MAAM;QACR,KAAK,IAAI;YACP,MAAM,GAAG,CAAC,EAAE,CAAC,GAAA,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3B,MAAM;QACR,KAAK,KAAK;YACR,IAAM,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAChC,MAAM,GAAG;gBACP,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,GAAA,EAAE;gBACjB,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE;aACzB,CAAC;YACF,MAAM;QACR,KAAK,KAAK;YACR,IAAM,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YAChC,MAAM,GAAG;gBACP,EAAE,CAAC,GAAA,EAAE,CAAC,EAAE,OAAO,EAAE;gBACjB,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE;aACzB,CAAC;YACF,MAAM;QACR;YACE,MAAM;KACT;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,SAAS,oBAAoB,CAAC,MAAe,EAAE,SAAiB;IAC9D,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAA,WAAI,EAAC,MAAM,EAAE,UAAC,KAAY,EAAE,KAAK;QAC/B,IAAM,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACpC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnB,IAAI,SAAS,EAAE;YACb,IAAM,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YAC1E,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;SAC1C;IACH,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,oBAAoB;AACpB,SAAS,kBAAkB,CAAC,MAAe;IACzC,OAAO,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK,EAAE,KAAK;QAC7B,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;AACL,CAAC;AAED,QAAQ;AACR,SAAS,wBAAwB,CAAC,GAAc,EAAE,SAAiB;IACjE,IAAM,MAAM,GAAG,IAAA,+BAAa,EAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,6BAA6B;IAC9G,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,IAAA,WAAI,EAAC,MAAM,EAAE,UAAC,cAAc;QAC1B,IAAM,iBAAiB,GAAG,oBAAoB,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QAC1E,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,6CACK,IAAA,oBAAQ,EAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,KAC1C,IAAI,MAAA,IACJ;AACJ,CAAC;AAED,YAAY;AACZ,IAAA,WAAI,EAAC,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,UAAC,SAAS;IACzC,IAAA,oBAAa,EAAC,MAAM,EAAE,SAAS,EAAE;QAC/B,IAAI,YAAC,GAAc,EAAE,SAAiB;YACpC,IAAM,KAAK,GAAG,wBAAwB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YACvD,IAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC;gBAC/B,IAAI,EAAE,MAAM;gBACZ,KAAK,OAAA;gBACL,IAAI,EAAE,MAAM;aACb,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACf,CAAC;QACD,SAAS,YAAC,SAAyB;YACjC,OAAO,IAAA,oBAAa,EAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAC7C,CAAC;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { each } from '@antv/util';\nimport { IGroup } from '../../../dependents';\nimport { Point, ShapeInfo, ShapeMarkerCfg } from '../../../interface';\n\nimport { registerShape } from '../base';\nimport { getPathPoints } from '../util/get-path-points';\nimport { getStyle } from '../util/get-style';\nimport { getLineMarker } from './util';\n\nconst interpolateCallback = (point: Point, nextPoint: Point, shapeType: string) => {\n  const x = point.x as number;\n  const y = point.y as number;\n  const nextX = nextPoint.x as number;\n  const nextY = nextPoint.y as number;\n  let result;\n\n  switch (shapeType) {\n    case 'hv':\n      result = [{ x: nextX, y }];\n      break;\n    case 'vh':\n      result = [{ x, y: nextY }];\n      break;\n    case 'hvh':\n      const middleX = (nextX + x) / 2;\n      result = [\n        { x: middleX, y },\n        { x: middleX, y: nextY },\n      ];\n      break;\n    case 'vhv':\n      const middleY = (y + nextY) / 2;\n      result = [\n        { x, y: middleY },\n        { x: nextX, y: middleY },\n      ];\n      break;\n    default:\n      break;\n  }\n\n  return result;\n};\n\nfunction getInterpolatePoints(points: Point[], shapeType: string) {\n  let result = [];\n  each(points, (point: Point, index) => {\n    const nextPoint = points[index + 1];\n    result.push(point);\n    if (nextPoint) {\n      const interpolatePoint = interpolateCallback(point, nextPoint, shapeType);\n      result = result.concat(interpolatePoint);\n    }\n  });\n  return result;\n}\n\n// 插值的图形path，不考虑null\nfunction getInterpolatePath(points: Point[]) {\n  return points.map((point, index) => {\n    return index === 0 ? ['M', point.x, point.y] : ['L', point.x, point.y];\n  });\n}\n\n// 插值的图形\nfunction getInterpolateShapeAttrs(cfg: ShapeInfo, shapeType: string) {\n  const points = getPathPoints(cfg.points, cfg.connectNulls, cfg.showSinglePoint); // 根据 connectNulls 值处理 points\n  let path = [];\n  each(points, (eachLinePoints) => {\n    const interpolatePoints = getInterpolatePoints(eachLinePoints, shapeType);\n    path = path.concat(getInterpolatePath(interpolatePoints));\n  });\n\n  return {\n    ...getStyle(cfg, true, false, 'lineWidth'),\n    path,\n  };\n}\n\n// step line\neach(['hv', 'vh', 'hvh', 'vhv'], (shapeType) => {\n  registerShape('line', shapeType, {\n    draw(cfg: ShapeInfo, container: IGroup) {\n      const attrs = getInterpolateShapeAttrs(cfg, shapeType);\n      const shape = container.addShape({\n        type: 'path',\n        attrs,\n        name: 'line',\n      });\n\n      return shape;\n    },\n    getMarker(markerCfg: ShapeMarkerCfg) {\n      return getLineMarker(markerCfg, shapeType);\n    },\n  });\n});\n"]}