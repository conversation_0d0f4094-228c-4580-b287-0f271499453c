import { ShapeAttrs } from '../../../dependents';
import { Point, Position, RangePoint } from '../../../interface';
/**
 * 获取折线图 path
 */
export declare function getPath(points: Point[] | RangePoint[], isInCircle: boolean, isStack?: boolean, smooth?: boolean, constraint?: Position[], style?: ShapeAttrs): any;
declare const LineShapeFactory: import("../../../interface").ShapeFactory;
export default LineShapeFactory;
