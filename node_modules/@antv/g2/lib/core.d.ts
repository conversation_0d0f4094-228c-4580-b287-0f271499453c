export declare const VERSION = "4.2.11";
export { Chart, View, Event } from './chart';
export { Controller as ComponentController } from './chart/controller/base';
export { default as TooltipController } from './chart/controller/tooltip';
export { default as Geometry } from './geometry/base';
export { default as Element } from './geometry/element';
export { default as GeometryLabel } from './geometry/label/base';
export { Interaction, Action } from './interaction';
export { Facet } from './facet';
export { default as InteractionAction } from './interaction/action/base';
export { registerComponentController } from './chart';
export { registerGeometry } from './chart';
export { registerShape, registerShapeFactory, getShapeFactory } from './geometry/shape/base';
export { registerGeometryLabel, registerGeometryLabelLayout, getGeometryLabel, getGeometryLabelLayout, } from './geometry/label';
export { getInteraction, registerInteraction, registerAction, getActionClass } from './interaction';
export { getFacet, registerFacet } from './facet';
export { getTheme, registerTheme } from './theme';
export { registerEngine, getEngine } from './engine';
export { registerAnimation, getAnimation } from './animate/animation';
export { LAYER, DIRECTION } from './constant';
import * as Types from './interface';
export type { Types };
export { IGroup, ShapeAttrs, Coordinate, Scale, ScaleConfig } from './dependents';
import { getMappingValue } from './util/attr';
import { getLegendItems } from './util/legend';
import { getAngle, getSectorPath, polarToCartesian } from './util/graphics';
import { rotate, translate, zoom } from './util/transform';
import { getTooltipItems } from './util/tooltip';
import { getDelegationObject } from './interaction/action/util';
import { getPathPoints } from './geometry/shape/util/get-path-points';
import { getPath } from './geometry/shape/line';
export declare const Util: {
    getLegendItems: typeof getLegendItems;
    translate: typeof translate;
    rotate: typeof rotate;
    zoom: typeof zoom;
    transform: (m: number[], actions: any[][]) => number[];
    getAngle: typeof getAngle;
    getSectorPath: typeof getSectorPath;
    polarToCartesian: typeof polarToCartesian;
    getDelegationObject: typeof getDelegationObject;
    getTooltipItems: typeof getTooltipItems;
    getMappingValue: typeof getMappingValue;
    getPath: typeof getPath;
    getPathPoints: typeof getPathPoints;
};
