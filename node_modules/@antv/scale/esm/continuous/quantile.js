import { __extends } from "tslib";
import Quantize from './quantize';
var Quantile = /** @class */ (function (_super) {
    __extends(Quantile, _super);
    function Quantile() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.type = 'quantile';
        return _this;
    }
    Quantile.prototype.initCfg = function () {
        this.tickMethod = 'quantile';
        this.tickCount = 5;
        this.nice = true;
    };
    return Quantile;
}(Quantize));
export default Quantile;
//# sourceMappingURL=quantile.js.map