function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ReconciliationFilledSvg from "@ant-design/icons-svg/es/asn/ReconciliationFilled";
import AntdIcon from "../components/AntdIcon";
const ReconciliationFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: ReconciliationFilledSvg
}));

/**![reconciliation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY3NiA2MjNjLTE4LjggMC0zNCAxNS4yLTM0IDM0czE1LjIgMzQgMzQgMzQgMzQtMTUuMiAzNC0zNC0xNS4yLTM0LTM0LTM0em0yMDQtNDU1SDY2OGMwLTMwLjktMjUuMS01Ni01Ni01NmgtODBjLTMwLjkgMC01NiAyNS4xLTU2IDU2SDI2NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjAwaC04OGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NDQ4YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDMzNmMxNy43IDAgMzItMTQuMyAzMi0zMnYtMTZoMzY4YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjIwMGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNDQ4IDg0OEgxNzZWNjE2aDI3MnYyMzJ6bTAtMjk2SDE3NnYtODhoMjcydjg4em0yMC0yNzJ2LTQ4aDcydi01Nmg2NHY1Nmg3MnY0OEg0Njh6bTE4MCAxNjh2NTZjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04di01NmMwLTQuNCAzLjYtOCA4LThoNDhjNC40IDAgOCAzLjYgOCA4em0yOCAzMDFjLTUwLjggMC05Mi00MS4yLTkyLTkyczQxLjItOTIgOTItOTIgOTIgNDEuMiA5MiA5Mi00MS4yIDkyLTkyIDkyem05Mi0yNDVjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04di05NmMwLTQuNCAzLjYtOCA4LThoNDhjNC40IDAgOCAzLjYgOCA4djk2em0tOTIgNjFjLTUwLjggMC05MiA0MS4yLTkyIDkyczQxLjIgOTIgOTIgOTIgOTItNDEuMiA5Mi05Mi00MS4yLTkyLTkyLTkyem0wIDEyNmMtMTguOCAwLTM0LTE1LjItMzQtMzRzMTUuMi0zNCAzNC0zNCAzNCAxNS4yIDM0IDM0LTE1LjIgMzQtMzQgMzR6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(ReconciliationFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ReconciliationFilled';
}
export default RefIcon;