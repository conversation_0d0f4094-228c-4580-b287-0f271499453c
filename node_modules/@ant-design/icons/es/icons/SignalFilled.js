function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SignalFilledSvg from "@ant-design/icons-svg/es/asn/SignalFilled";
import AntdIcon from "../components/AntdIcon";
const SignalFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: SignalFilledSvg
}));

/**![signal](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik01ODQgMzUySDQ0MGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NTQ0YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDE0NGMxNy43IDAgMzItMTQuMyAzMi0zMlYzODRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTg5MiA2NEg3NDhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmgxNDRjMTcuNyAwIDMyLTE0LjMgMzItMzJWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6TTI3NiA2NDBIMTMyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyNTZjMCAxNy43IDE0LjMgMzIgMzIgMzJoMTQ0YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjY3MmMwLTE3LjctMTQuMy0zMi0zMi0zMnoiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(SignalFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SignalFilled';
}
export default RefIcon;