function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import Ali<PERSON>baOutlinedSvg from "@ant-design/icons-svg/es/asn/AlibabaOutlined";
import AntdIcon from "../components/AntdIcon";
const AlibabaOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: AlibabaOutlinedSvg
}));

/**![alibaba](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYwMi45IDY2OS44Yy0zNy4yIDIuNi0zMy42LTE3LjMtMTEuNS00Ni4yIDUwLjQtNjcuMiAxNDMuNy0xNTguNSAxNDcuOS0yMjUuMiA1LjgtODYuNi04MS4zLTExMy40LTE3MS0xMTMuNC02Mi40IDEuNi0xMjcgMTguOS0xNzEgMzQuNi0xNTEuNiA1My41LTI0Ni42IDEzNy41LTMwNi45IDIzMi02Mi40IDkzLjQtNDMgMTgzLjIgOTEuOCAxODUuOCAxMDEuOC00LjIgMTcwLjUtMzIuNSAyMzkuNy02OC4yLjUgMC0xOTIuNSA1NS4xLTI2My45IDE0LjctNy45LTQuMi0xNS43LTEwLTE3LjgtMjYuMiAwLTMzLjEgNTQuNi02Ny43IDg2LjYtNzguN3YtNTYuN2M2NC41IDIyLjYgMTQwLjYgMTYuMyAyMDUuNy0zMiAyLjEgNS44IDQuMiAxMy4xIDMuNyAyMWgxMWMyLjYtMjIuNi0xMi42LTQ0LjYtMzcuOC00Ni4yIDcuMyA1LjggMTIuNiAxMC41IDE1LjIgMTQuN2wtMSAxLS41LjVjLTgzLjkgNTguOC0xNjUuMyAzMS41LTE3My4xIDI5LjlsNDYuNy00NS43LTEzLjEtMzMuMWM5Mi45LTMyLjUgMTY5LjUtNTYuMiAyOTYuOS03OC43bC0yOC41LTIzIDE0LjctOC45Yzc1LjUgMjEgMTI2LjQgMzYuNyAxMjMuOCA3Ni42LTEgNi44LTMuNyAxNC43LTcuOSAyMy4xQzY2MC4xIDQ2Ni4xIDU5NCA1MzggNTY3LjIgNTY5Yy0xNy4zIDIwLjUtMzQuNiAzOS40LTQ2LjcgNTguMy0xMy42IDE5LjQtMjAuNSAzNy4zLTIxIDUzLjUgMi42IDEzMS44IDM5MS40LTYxLjkgNDY4LTExMi45LTExMS43IDQ3LjgtMjMyLjkgOTMuNS0zNjQuNiAxMDEuOXptODUtMzAyLjljMi44IDUuMiA0LjEgMTEuNiA0LjEgMTkuMS0uMS02LjgtMS40LTEzLjMtNC4xLTE5LjF6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(AlibabaOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AlibabaOutlined';
}
export default RefIcon;