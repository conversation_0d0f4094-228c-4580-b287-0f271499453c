function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import LeftCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/LeftCircleOutlined";
import AntdIcon from "../components/AntdIcon";
const LeftCircleOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: LeftCircleOutlinedSvg
}));

/**![left-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYwMy4zIDMyNy41bC0yNDYgMTc4YTcuOTUgNy45NSAwIDAwMCAxMi45bDI0NiAxNzhjNS4zIDMuOCAxMi43IDAgMTIuNy02LjVWNjQzYzAtMTAuMi00LjktMTkuOS0xMy4yLTI1LjlMNDU3LjQgNTEybDE0NS40LTEwNS4yYzguMy02IDEzLjItMTUuNiAxMy4yLTI1LjlWMzM0YzAtNi41LTcuNC0xMC4zLTEyLjctNi41eiIgLz48cGF0aCBkPSJNNTEyIDY0QzI2NC42IDY0IDY0IDI2NC42IDY0IDUxMnMyMDAuNiA0NDggNDQ4IDQ0OCA0NDgtMjAwLjYgNDQ4LTQ0OFM3NTkuNCA2NCA1MTIgNjR6bTAgODIwYy0yMDUuNCAwLTM3Mi0xNjYuNi0zNzItMzcyczE2Ni42LTM3MiAzNzItMzcyIDM3MiAxNjYuNiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzIgMzcyeiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(LeftCircleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LeftCircleOutlined';
}
export default RefIcon;