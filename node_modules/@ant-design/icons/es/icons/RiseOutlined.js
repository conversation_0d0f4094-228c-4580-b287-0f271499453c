function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RiseOutlinedSvg from "@ant-design/icons-svg/es/asn/RiseOutlined";
import AntdIcon from "../components/AntdIcon";
const RiseOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: RiseOutlinedSvg
}));

/**![rise](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxNyAyMTEuMWwtMTk5LjIgMjRjLTYuNi44LTkuNCA4LjktNC43IDEzLjZsNTkuMyA1OS4zLTIyNiAyMjYtMTAxLjgtMTAxLjdjLTYuMy02LjMtMTYuNC02LjItMjIuNiAwTDEwMC4zIDc1NC4xYTguMDMgOC4wMyAwIDAwMCAxMS4zbDQ1IDQ1LjJjMy4xIDMuMSA4LjIgMy4xIDExLjMgMEw0MzMuMyA1MzQgNTM1IDYzNS43YzYuMyA2LjIgMTYuNCA2LjIgMjIuNiAwTDgyOSAzNjQuNWw1OS4zIDU5LjNhOC4wMSA4LjAxIDAgMDAxMy42LTQuN2wyNC0xOTkuMmMuNy01LjEtMy43LTkuNS04LjktOC44eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(RiseOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RiseOutlined';
}
export default RefIcon;