function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import LineHeightOutlinedSvg from "@ant-design/icons-svg/es/asn/LineHeightOutlined";
import AntdIcon from "../components/AntdIcon";
const LineHeightOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: LineHeightOutlinedSvg
}));

/**![line-height](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY0OCAxNjBIMTA0Yy00LjQgMC04IDMuNi04IDh2MTI4YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTY0aDE2OHY1NjBoLTkyYy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDI2NGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOGgtOTJWMjMyaDE2OHY2NGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjE2OGMwLTQuNC0zLjYtOC04LTh6bTI3Mi44IDU0Nkg4NTZWMzE4aDY0LjhjNiAwIDkuNC03IDUuNy0xMS43TDgyNS43IDE3OC43YTcuMTQgNy4xNCAwIDAwLTExLjMgMEw3MTMuNiAzMDYuM2E3LjIzIDcuMjMgMCAwMDUuNyAxMS43SDc4NHYzODhoLTY0LjhjLTYgMC05LjQgNy01LjcgMTEuN2wxMDAuOCAxMjcuNWMyLjkgMy43IDguNSAzLjcgMTEuMyAwbDEwMC44LTEyNy41YTcuMiA3LjIgMCAwMC01LjYtMTEuN3oiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(LineHeightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LineHeightOutlined';
}
export default RefIcon;