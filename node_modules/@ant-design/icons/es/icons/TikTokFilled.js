function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TikTokFilledSvg from "@ant-design/icons-svg/es/asn/TikTokFilled";
import AntdIcon from "../components/AntdIcon";
const TikTokFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: TikTokFilledSvg
}));

/**![tik-tok](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNOTEyIDIyNC45NkM5MTIgMTYyLjU3IDg2MS40MiAxMTIgNzk5LjA0IDExMkgyMjQuOTZDMTYyLjU3IDExMiAxMTIgMTYyLjU3IDExMiAyMjQuOTZ2NTc0LjA4QzExMiA4NjEuNDMgMTYyLjU4IDkxMiAyMjQuOTYgOTEyaDU3NC4wOEM4NjEuNDIgOTEyIDkxMiA4NjEuNDMgOTEyIDc5OS4wNHpNNzc0Ljc2IDQ2MC45MmMtNTEuNjIuNTctOTkuNzEtMTUuMDMtMTQxLjk0LTQzLjkzdjIwMi44N2ExOTIuMyAxOTIuMyAwIDAxLTE0OSAxODcuODVjLTExOS4wNiAyNy4xNy0yMTkuODYtNTguOTUtMjMyLjU3LTE2MS44My0xMy4zLTEwMi44OSA1Mi4zMi0xOTMuMDYgMTUyLjg5LTIxMy4yOSAxOS42NS00LjA0IDQ5LjItNC4wNCA2NC40Ni0uNTd2MTA4LjY2Yy00LjctMS4xNS05LjA5LTIuMzEtMTMuNzEtMi44OS0zOS4zLTYuOTQtNzcuMzcgMTIuNzItOTIuOTggNDguNTUtMTUuNiAzNS44NC01LjE2IDc3LjQ1IDI2LjYzIDEwMS43MyAyNi41OSAyMC44IDU2LjA5IDIzLjcgODYuMTQgOS44MiAzMC4wNi0xMy4yOSA0Ni4yMS0zNy41NiA0OS42OC03MC41LjU4LTQuNjMuNTQtOS44NC41NC0xNS4wNFYyMjIuMjFjMC0xMC45OS4wOS0xMC41IDExLjA3LTEwLjVoODYuMTJjNi4zNiAwIDguNjcuOSA5LjI1IDguNDMgNC42MiA2Ny4wNCA1NS41MyAxMjQuMTQgMTIwLjg0IDEzMi44MSA2Ljk0IDEuMTYgMTQuMzcgMS42MiAyMi41OCAyLjJ6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(TikTokFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TikTokFilled';
}
export default RefIcon;