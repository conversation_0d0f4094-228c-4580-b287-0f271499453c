function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PictureFilledSvg from "@ant-design/icons-svg/es/asn/PictureFilled";
import AntdIcon from "../components/AntdIcon";
const PictureFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: PictureFilledSvg
}));

/**![picture](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */
const RefIcon = /*#__PURE__*/React.forwardRef(PictureFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PictureFilled';
}
export default RefIcon;