function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import LeftSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/LeftSquareOutlined";
import AntdIcon from "../components/AntdIcon";
const LeftSquareOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: LeftSquareOutlinedSvg
}));

/**![left-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM2NS4zIDUxOC41bDI0NiAxNzhjNS4zIDMuOCAxMi43IDAgMTIuNy02LjV2LTQ2LjljMC0xMC4yLTQuOS0xOS45LTEzLjItMjUuOUw0NjUuNCA1MTJsMTQ1LjQtMTA1LjJjOC4zLTYgMTMuMi0xNS42IDEzLjItMjUuOVYzMzRjMC02LjUtNy40LTEwLjMtMTIuNy02LjVsLTI0NiAxNzhhOC4wNSA4LjA1IDAgMDAwIDEzeiIgLz48cGF0aCBkPSJNODgwIDExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgNzI4SDE4NFYxODRoNjU2djY1NnoiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(LeftSquareOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LeftSquareOutlined';
}
export default RefIcon;