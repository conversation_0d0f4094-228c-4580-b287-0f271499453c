function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SwapLeftOutlinedSvg from "@ant-design/icons-svg/es/asn/SwapLeftOutlined";
import AntdIcon from "../components/AntdIcon";
const SwapLeftOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: SwapLeftOutlinedSvg
}));

/**![swap-left](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3MiA1NzJIMjY2LjhsMTQ0LjMtMTgzYzQuMS01LjIuNC0xMy02LjMtMTNIMzQwYy05LjggMC0xOS4xIDQuNS0yNS4xIDEyLjJsLTE2NCAyMDhjLTE2LjUgMjEtMS42IDUxLjggMjUuMSA1MS44aDY5NmM0LjQgMCA4LTMuNiA4LTh2LTYwYzAtNC40LTMuNi04LTgtOHoiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(SwapLeftOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SwapLeftOutlined';
}
export default RefIcon;