function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ReconciliationTwoToneSvg from "@ant-design/icons-svg/es/asn/ReconciliationTwoTone";
import AntdIcon from "../components/AntdIcon";
const ReconciliationTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: ReconciliationTwoToneSvg
}));

/**![reconciliation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc0MCAzNDRINDA0VjI0MEgzMDR2MTYwaDE3NmMxNy43IDAgMzIgMTQuMyAzMiAzMnYzNjBoMzI4VjI0MEg3NDB2MTA0ek01ODQgNDQ4YzAtNC40IDMuNi04IDgtOGg0OGM0LjQgMCA4IDMuNiA4IDh2NTZjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04di01NnptOTIgMzAxYy01MC44IDAtOTItNDEuMi05Mi05MnM0MS4yLTkyIDkyLTkyIDkyIDQxLjIgOTIgOTItNDEuMiA5Mi05MiA5MnptOTItMzQxdjk2YzAgNC40LTMuNiA4LTggOGgtNDhjLTQuNCAwLTgtMy42LTgtOHYtOTZjMC00LjQgMy42LTggOC04aDQ4YzQuNCAwIDggMy42IDggOHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTY0MiA2NTdhMzQgMzQgMCAxMDY4IDAgMzQgMzQgMCAxMC02OCAweiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNTkyIDUxMmg0OGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDh6bTExMi0xMDR2OTZjMCA0LjQgMy42IDggOCA4aDQ4YzQuNCAwIDgtMy42IDgtOHYtOTZjMC00LjQtMy42LTgtOC04aC00OGMtNC40IDAtOCAzLjYtOCA4eiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNODgwIDE2OEg2NjhjMC0zMC45LTI1LjEtNTYtNTYtNTZoLTgwYy0zMC45IDAtNTYgMjUuMS01NiA1NkgyNjRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjIwMGgtODhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjQ0OGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgzMzZjMTcuNyAwIDMyLTE0LjMgMzItMzJ2LTE2aDM2OGMxNy43IDAgMzItMTQuMyAzMi0zMlYyMDBjMC0xNy43LTE0LjMtMzItMzItMzJ6bS00MTIgNjRoNzJ2LTU2aDY0djU2aDcydjQ4SDQ2OHYtNDh6bS0yMCA2MTZIMTc2VjYxNmgyNzJ2MjMyem0wLTI5NkgxNzZ2LTg4aDI3MnY4OHptMzkyIDI0MEg1MTJWNDMyYzAtMTcuNy0xNC4zLTMyLTMyLTMySDMwNFYyNDBoMTAwdjEwNGgzMzZWMjQwaDEwMHY1NTJ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik02NzYgNTY1Yy01MC44IDAtOTIgNDEuMi05MiA5MnM0MS4yIDkyIDkyIDkyIDkyLTQxLjIgOTItOTItNDEuMi05Mi05Mi05MnptMCAxMjZjLTE4LjggMC0zNC0xNS4yLTM0LTM0czE1LjItMzQgMzQtMzQgMzQgMTUuMiAzNCAzNC0xNS4yIDM0LTM0IDM0eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(ReconciliationTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ReconciliationTwoTone';
}
export default RefIcon;