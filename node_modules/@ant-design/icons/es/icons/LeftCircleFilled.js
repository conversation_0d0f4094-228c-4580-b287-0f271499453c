function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import LeftCircleFilledSvg from "@ant-design/icons-svg/es/asn/LeftCircleFilled";
import AntdIcon from "../components/AntdIcon";
const LeftCircleFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: LeftCircleFilledSvg
}));

/**![left-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xMDQgMzE2LjljMCAxMC4yLTQuOSAxOS45LTEzLjIgMjUuOUw0NTcuNCA1MTJsMTQ1LjQgMTA1LjJjOC4zIDYgMTMuMiAxNS42IDEzLjIgMjUuOVY2OTBjMCA2LjUtNy40IDEwLjMtMTIuNyA2LjVsLTI0Ni0xNzhhNy45NSA3Ljk1IDAgMDEwLTEyLjlsMjQ2LTE3OGE4IDggMCAwMTEyLjcgNi41djQ2Ljh6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(LeftCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LeftCircleFilled';
}
export default RefIcon;