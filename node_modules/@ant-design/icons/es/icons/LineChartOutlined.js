function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import LineChartOutlinedSvg from "@ant-design/icons-svg/es/asn/LineChartOutlined";
import AntdIcon from "../components/AntdIcon";
const LineChartOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: LineChartOutlinedSvg
}));

/**![line-chart](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4OCA3OTJIMjAwVjE2OGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2Njg4YzAgNC40IDMuNiA4IDggOGg3NTJjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6TTMwNS44IDYzNy43YzMuMSAzLjEgOC4xIDMuMSAxMS4zIDBsMTM4LjMtMTM3LjZMNTgzIDYyOC41YzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsMjc1LjQtMjc1LjNjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM2wtMzkuNi0zOS42YTguMDMgOC4wMyAwIDAwLTExLjMgMGwtMjMwIDIyOS45TDQ2MS40IDQwNGE4LjAzIDguMDMgMCAwMC0xMS4zIDBMMjY2LjMgNTg2LjdhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzkuNSAzOS43eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(LineChartOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LineChartOutlined';
}
export default RefIcon;