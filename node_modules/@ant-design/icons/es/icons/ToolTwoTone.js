function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ToolTwoToneSvg from "@ant-design/icons-svg/es/asn/ToolTwoTone";
import AntdIcon from "../components/AntdIcon";
const ToolTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: ToolTwoToneSvg
}));

/**![tool](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwNi44IDQ4OC43YTMyLjA1IDMyLjA1IDAgMDEtNDUuMyAwTDUzNyAzNjQuMmEzMi4wNSAzMi4wNSAwIDAxMC00NS4zbDEzMi45LTEzMi44YTE4NC4yIDE4NC4yIDAgMDAtMTQ0IDUzLjVjLTU4LjEgNTguMS02OS4zIDE0NS4zLTMzLjYgMjE0LjZMNDM5LjUgNTA3Yy0uMSAwLS4xLS4xLS4xLS4xTDIwOS4zIDczN2w3OS4yIDc5LjIgMjc0LTI3NC4xLjEuMSA4LjgtOC44YzY5LjMgMzUuNyAxNTYuNSAyNC41IDIxNC42LTMzLjYgMzkuMi0zOS4xIDU3LjMtOTIuMSA1My42LTE0My45TDcwNi44IDQ4OC43eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODc2LjYgMjM5LjVjLS41LS45LTEuMi0xLjgtMi0yLjUtNS01LTEzLjEtNS0xOC4xIDBMNjg0LjIgNDA5LjNsLTY3LjktNjcuOUw3ODguNyAxNjljLjgtLjggMS40LTEuNiAyLTIuNSAzLjYtNi4xIDEuNi0xMy45LTQuNS0xNy41LTk4LjItNTgtMjI2LjgtNDQuNy0zMTEuMyAzOS43LTY3IDY3LTg5LjIgMTYyLTY2LjUgMjQ3LjRsLTI5MyAyOTNjLTMgMy0yLjggNy45LjMgMTFsMTY5LjcgMTY5LjdjMy4xIDMuMSA4LjEgMy4zIDExIC4zbDI5Mi45LTI5Mi45Yzg1LjUgMjIuOCAxODAuNS43IDI0Ny42LTY2LjQgODQuNC04NC41IDk3LjctMjEzLjEgMzkuNy0zMTEuM3pNNzg2IDQ5OS44Yy01OC4xIDU4LjEtMTQ1LjMgNjkuMy0yMTQuNiAzMy42bC04LjggOC44LS4xLS4xLTI3NCAyNzQuMS03OS4yLTc5LjIgMjMwLjEtMjMwLjFzMCAuMS4xLjFsNTIuOC01Mi44Yy0zNS43LTY5LjMtMjQuNS0xNTYuNSAzMy42LTIxNC42YTE4NC4yIDE4NC4yIDAgMDExNDQtNTMuNUw1MzcgMzE4LjlhMzIuMDUgMzIuMDUgMCAwMDAgNDUuM2wxMjQuNSAxMjQuNWEzMi4wNSAzMi4wNSAwIDAwNDUuMyAwbDEzMi44LTEzMi44YzMuNyA1MS44LTE0LjQgMTA0LjgtNTMuNiAxNDMuOXoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(ToolTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ToolTwoTone';
}
export default RefIcon;