"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _SettingFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/SettingFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const SettingFilled = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _SettingFilled.default
}));

/**![setting](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMi41IDM5MC42Yy0yOS45IDAtNTcuOSAxMS42LTc5LjEgMzIuOC0yMS4xIDIxLjItMzIuOCA0OS4yLTMyLjggNzkuMSAwIDI5LjkgMTEuNyA1Ny45IDMyLjggNzkuMSAyMS4yIDIxLjEgNDkuMiAzMi44IDc5LjEgMzIuOCAyOS45IDAgNTcuOS0xMS43IDc5LjEtMzIuOCAyMS4xLTIxLjIgMzIuOC00OS4yIDMyLjgtNzkuMSAwLTI5LjktMTEuNy01Ny45LTMyLjgtNzkuMWExMTAuOTYgMTEwLjk2IDAgMDAtNzkuMS0zMi44em00MTIuMyAyMzUuNWwtNjUuNC01NS45YzMuMS0xOSA0LjctMzguNCA0LjctNTcuN3MtMS42LTM4LjgtNC43LTU3LjdsNjUuNC01NS45YTMyLjAzIDMyLjAzIDAgMDA5LjMtMzUuMmwtLjktMi42YTQ0Mi41IDQ0Mi41IDAgMDAtNzkuNi0xMzcuN2wtMS44LTIuMWEzMi4xMiAzMi4xMiAwIDAwLTM1LjEtOS41bC04MS4yIDI4LjljLTMwLTI0LjYtNjMuNC00NC05OS42LTU3LjVsLTE1LjctODQuOWEzMi4wNSAzMi4wNSAwIDAwLTI1LjgtMjUuN2wtMi43LS41Yy01Mi05LjQtMTA2LjgtOS40LTE1OC44IDBsLTIuNy41YTMyLjA1IDMyLjA1IDAgMDAtMjUuOCAyNS43bC0xNS44IDg1LjNhMzUzLjQ0IDM1My40NCAwIDAwLTk4LjkgNTcuM2wtODEuOC0yOS4xYTMyIDMyIDAgMDAtMzUuMSA5LjVsLTEuOCAyLjFhNDQ1LjkzIDQ0NS45MyAwIDAwLTc5LjYgMTM3LjdsLS45IDIuNmMtNC41IDEyLjUtLjggMjYuNSA5LjMgMzUuMmw2Ni4yIDU2LjVjLTMuMSAxOC44LTQuNiAzOC00LjYgNTcgMCAxOS4yIDEuNSAzOC40IDQuNiA1N2wtNjYgNTYuNWEzMi4wMyAzMi4wMyAwIDAwLTkuMyAzNS4ybC45IDIuNmMxOC4xIDUwLjMgNDQuOCA5Ni44IDc5LjYgMTM3LjdsMS44IDIuMWEzMi4xMiAzMi4xMiAwIDAwMzUuMSA5LjVsODEuOC0yOS4xYzI5LjggMjQuNSA2MyA0My45IDk4LjkgNTcuM2wxNS44IDg1LjNhMzIuMDUgMzIuMDUgMCAwMDI1LjggMjUuN2wyLjcuNWE0NDguMjcgNDQ4LjI3IDAgMDAxNTguOCAwbDIuNy0uNWEzMi4wNSAzMi4wNSAwIDAwMjUuOC0yNS43bDE1LjctODQuOWMzNi4yLTEzLjYgNjkuNi0zMi45IDk5LjYtNTcuNWw4MS4yIDI4LjlhMzIgMzIgMCAwMDM1LjEtOS41bDEuOC0yLjFjMzQuOC00MS4xIDYxLjUtODcuNCA3OS42LTEzNy43bC45LTIuNmM0LjMtMTIuNC42LTI2LjMtOS41LTM1em0tNDEyLjMgNTIuMmMtOTcuMSAwLTE3NS44LTc4LjctMTc1LjgtMTc1LjhzNzguNy0xNzUuOCAxNzUuOC0xNzUuOCAxNzUuOCA3OC43IDE3NS44IDE3NS44LTc4LjcgMTc1LjgtMTc1LjggMTc1Ljh6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(SettingFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SettingFilled';
}
var _default = exports.default = RefIcon;