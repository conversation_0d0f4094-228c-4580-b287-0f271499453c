"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _TaobaoCircleFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/TaobaoCircleFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const TaobaoCircleFilled = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _TaobaoCircleFilled.default
}));

/**![taobao-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0ek0zMTUuNyAyOTEuNWMyNy4zIDAgNDkuNSAyMi4xIDQ5LjUgNDkuNHMtMjIuMSA0OS40LTQ5LjUgNDkuNGE0OS40IDQ5LjQgMCAxMTAtOTguOHpNMzY2LjkgNTc4Yy0xMy42IDQyLjMtMTAuMiAyNi43LTY0LjQgMTQ0LjVsLTc4LjUtNDlzODcuNy03OS44IDEwNS42LTExNi4yYzE5LjItMzguNC0yMS4xLTU4LjktMjEuMS01OC45bC02MC4yLTM3LjUgMzIuNy01MC4yYzQ1LjQgMzMuNyA0OC43IDM2LjYgNzkuMiA2Ny4yIDIzLjggMjMuOSAyMC43IDU2LjggNi43IDEwMC4xem00MjcuMiA1NWMtMTUuMyAxNDMuOC0yMDIuNCA5MC4zLTIwMi40IDkwLjNsMTAuMi00MS4xIDQzLjMgOS4zYzgwIDUgNzIuMy02NC45IDcyLjMtNjQuOVY0MjNjLjYtNzcuMy03Mi42LTg1LjQtMjA0LjItMzguM2wzMC42IDguM2MtMi41IDktMTIuNSAyMy4yLTI1LjIgMzguNmgxNzZ2MzUuNmgtOTkuMXY0NC41aDk4Ljd2MzUuN2gtOTguN1Y2MjJjMTQuOS00LjggMjguNi0xMS41IDQwLjUtMjAuNWwtOC43LTMyLjUgNDYuNS0xNC40IDM4LjggOTQuOS01Ny4zIDIzLjktMTAuMi0zNy44Yy0yNS42IDE5LjUtNzguOCA0OC0xNzEuOCA0NS40LTk5LjIgMi42LTczLjctMTEyLTczLjctMTEybDIuNS0xLjNINDcyYy0uNSAxNC43LTYuNiAzOC43IDEuNyA1MS44IDYuOCAxMC44IDI0LjIgMTIuNiAzNS4zIDEzLjEgMS4zLjEgMi42LjEgMy45LjF2LTg1LjNoLTEwMXYtMzUuN2gxMDF2LTQ0LjVINDg3Yy0yMi43IDI0LjEtNDMuNSA0NC4xLTQzLjUgNDQuMWwtMzAuNi0yNi43YzIxLjctMjIuOSA0My4zLTU5LjEgNTYuOC04My4yLTEwLjkgNC40LTIyIDkuMi0zMy42IDE0LjItMTEuMiAxNC4zLTI0LjIgMjktMzguNyA0My41LjUuOC01MC0yOC40LTUwLTI4LjQgNTIuMi00NC40IDgxLjQtMTM5LjkgODEuNC0xMzkuOWw3Mi41IDIwLjRzLTUuOSAxNC0xOC40IDM1LjZjMjkwLjMtODIuMyAzMDcuNCA1MC41IDMwNy40IDUwLjVzMTkuMSA5MS44IDMuOCAyMzUuN3oiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(TaobaoCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TaobaoCircleFilled';
}
var _default = exports.default = RefIcon;