import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![medium-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzY4IDMxNy43bC00MC44IDM5LjFjLTMuNiAyLjctNS4zIDcuMS00LjYgMTEuNHYyODcuN2MtLjcgNC40IDEgOC44IDQuNiAxMS40bDQwIDM5LjF2OC43SDU2Ni40di04LjNsNDEuMy00MC4xYzQuMS00LjEgNC4xLTUuMyA0LjEtMTEuNFY0MjIuNWwtMTE1IDI5MS42aC0xNS41TDM0Ny41IDQyMi41VjYxOGMtMS4yIDguMiAxLjcgMTYuNSA3LjUgMjIuNGw1My44IDY1LjF2OC43SDI1NnYtOC43bDUzLjgtNjUuMWEyNi4xIDI2LjEgMCAwMDctMjIuNFYzOTJjLjctNi4zLTEuNy0xMi40LTYuNS0xNi43bC00Ny44LTU3LjZWMzA5SDQxMWwxMTQuNiAyNTEuNSAxMDAuOS0yNTEuM0g3Njh2OC41eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
