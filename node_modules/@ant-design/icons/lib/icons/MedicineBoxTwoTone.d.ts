import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![medicine-box](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI0NC4zIDMyOEwxODQgNTEzLjRWODQwaDY1NlY1MTMuNEw3NzkuNyAzMjhIMjQ0LjN6TTY2MCA2MjhjMCA0LjQtMy42IDgtOCA4SDU0NHYxMDhjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04VjYzNkgzNzJjLTQuNCAwLTgtMy42LTgtOHYtNDhjMC00LjQgMy42LTggOC04aDEwOFY0NjRjMC00LjQgMy42LTggOC04aDQ4YzQuNCAwIDggMy42IDggOHYxMDhoMTA4YzQuNCAwIDggMy42IDggOHY0OHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTY1MiA1NzJINTQ0VjQ2NGMwLTQuNC0zLjYtOC04LThoLTQ4Yy00LjQgMC04IDMuNi04IDh2MTA4SDM3MmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxMDh2MTA4YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LThWNjM2aDEwOGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTgzOS4yIDI3OC4xYTMyIDMyIDAgMDAtMzAuNC0yMi4xSDczNlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJIMzIwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYxMTJoLTcyLjhhMzEuOSAzMS45IDAgMDAtMzAuNCAyMi4xTDExMiA1MDJ2Mzc4YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlY1MDJsLTcyLjgtMjIzLjl6TTM2MCAxODRoMzA0djcySDM2MHYtNzJ6bTQ4MCA2NTZIMTg0VjUxMy40TDI0NC4zIDMyOGg1MzUuNEw4NDAgNTEzLjRWODQweiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
