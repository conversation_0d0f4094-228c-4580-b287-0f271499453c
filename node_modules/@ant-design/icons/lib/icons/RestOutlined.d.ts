import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![rest](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik01MDggNzA0Yzc5LjUgMCAxNDQtNjQuNSAxNDQtMTQ0cy02NC41LTE0NC0xNDQtMTQ0LTE0NCA2NC41LTE0NCAxNDQgNjQuNSAxNDQgMTQ0IDE0NHptMC0yMjRjNDQuMiAwIDgwIDM1LjggODAgODBzLTM1LjggODAtODAgODAtODAtMzUuOC04MC04MCAzNS44LTgwIDgwLTgweiIgLz48cGF0aCBkPSJNODMyIDI1NmgtMjguMWwtMzUuNy0xMjAuOWMtNC0xMy43LTE2LjUtMjMuMS0zMC43LTIzLjFoLTQ1MWMtMTQuMyAwLTI2LjggOS40LTMwLjcgMjMuMUwyMjAuMSAyNTZIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyOGMwIDQuNCAzLjYgOCA4IDhoNDUuOGw0Ny43IDU1OC43YTMyIDMyIDAgMDAzMS45IDI5LjNoNDI5LjJhMzIgMzIgMCAwMDMxLjktMjkuM0w4MDIuMiAzMjRIODU2YzQuNCAwIDgtMy42IDgtOHYtMjhjMC0xNy43LTE0LjMtMzItMzItMzJ6bS01MTguNi03NmgzOTcuMmwyMi40IDc2SDI5MWwyMi40LTc2em0zNzYuMiA2NjRIMzI2LjRMMjgyIDMyNGg0NTEuOWwtNDQuMyA1MjB6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
