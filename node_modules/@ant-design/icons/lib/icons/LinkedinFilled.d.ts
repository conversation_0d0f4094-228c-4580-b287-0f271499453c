import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![linkedin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNMzQ5LjMgNzkzLjdIMjMwLjZWNDExLjloMTE4Ljd2MzgxLjh6bS01OS4zLTQzNGE2OC44IDY4LjggMCAxMTY4LjgtNjguOGMtLjEgMzgtMzAuOSA2OC44LTY4LjggNjguOHptNTAzLjcgNDM0SDY3NS4xVjYwOGMwLTQ0LjMtLjgtMTAxLjItNjEuNy0xMDEuMi02MS43IDAtNzEuMiA0OC4yLTcxLjIgOTh2MTg4LjlINDIzLjdWNDExLjloMTEzLjh2NTIuMmgxLjZjMTUuOC0zMCA1NC41LTYxLjcgMTEyLjMtNjEuNyAxMjAuMiAwIDE0Mi4zIDc5LjEgMTQyLjMgMTgxLjl2MjA5LjR6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
