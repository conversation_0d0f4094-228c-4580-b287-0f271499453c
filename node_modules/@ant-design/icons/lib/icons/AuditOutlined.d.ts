import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![audit](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5NiAyNTBjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMzg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04SDI5NnptMTg0IDE0NEgyOTZjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04em0tNDggNDU4SDIwOFYxNDhoNTYwdjMyMGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjEwOGMwLTE3LjctMTQuMy0zMi0zMi0zMkgxNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjc4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgyNjRjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bTQ0MC04OEg3Mjh2LTM2LjZjNDYuMy0xMy44IDgwLTU2LjYgODAtMTA3LjQgMC02MS45LTUwLjEtMTEyLTExMi0xMTJzLTExMiA1MC4xLTExMiAxMTJjMCA1MC43IDMzLjcgOTMuNiA4MCAxMDcuNFY3NjRINTIwYy04LjggMC0xNiA3LjItMTYgMTZ2MTUyYzAgOC44IDcuMiAxNiAxNiAxNmgzNTJjOC44IDAgMTYtNy4yIDE2LTE2Vjc4MGMwLTguOC03LjItMTYtMTYtMTZ6TTY0NiA2MjBjMC0yNy42IDIyLjQtNTAgNTAtNTBzNTAgMjIuNCA1MCA1MC0yMi40IDUwLTUwIDUwLTUwLTIyLjQtNTAtNTB6bTE4MCAyNjZINTY2di02MGgyNjB2NjB6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
