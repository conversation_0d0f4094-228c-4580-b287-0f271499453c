import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![pull-request](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc4OCA3MDUuOVYxOTJjMC04LjgtNy4yLTE2LTE2LTE2SDYwMnYtNjguOGMwLTYtNy05LjQtMTEuNy01LjdMNDYyLjcgMjAyLjNhNy4xNCA3LjE0IDAgMDAwIDExLjNsMTI3LjUgMTAwLjhjNC43IDMuNyAxMS43LjQgMTEuNy01LjdWMjQwaDExNHY0NjUuOWMtNDQuMiAxNS03NiA1Ni45LTc2IDEwNi4xIDAgNjEuOCA1MC4yIDExMiAxMTIgMTEyczExMi01MC4yIDExMi0xMTJjLjEtNDkuMi0zMS43LTkxLTc1LjktMTA2LjF6TTc1MiA4NjBhNDguMDEgNDguMDEgMCAwMTAtOTYgNDguMDEgNDguMDEgMCAwMTAgOTZ6TTM4NCAyMTJjMC02MS44LTUwLjItMTEyLTExMi0xMTJzLTExMiA1MC4yLTExMiAxMTJjMCA0OS4yIDMxLjggOTEgNzYgMTA2LjFWNzA2Yy00NC4yIDE1LTc2IDU2LjktNzYgMTA2LjEgMCA2MS44IDUwLjIgMTEyIDExMiAxMTJzMTEyLTUwLjIgMTEyLTExMmMwLTQ5LjItMzEuOC05MS03Ni0xMDYuMVYzMTguMWM0NC4yLTE1LjEgNzYtNTYuOSA3Ni0xMDYuMXptLTE2MCAwYTQ4LjAxIDQ4LjAxIDAgMDE5NiAwIDQ4LjAxIDQ4LjAxIDAgMDEtOTYgMHptOTYgNjAwYTQ4LjAxIDQ4LjAxIDAgMDEtOTYgMCA0OC4wMSA0OC4wMSAwIDAxOTYgMHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
