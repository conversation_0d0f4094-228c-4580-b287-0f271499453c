import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![clock-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNzYuNSA1ODUuN2wtMjguNiAzOWE3Ljk5IDcuOTkgMCAwMS0xMS4yIDEuN0w0ODMuMyA1NjkuOGE3LjkyIDcuOTIgMCAwMS0zLjMtNi41VjI4OGMwLTQuNCAzLjYtOCA4LThoNDguMWM0LjQgMCA4IDMuNiA4IDh2MjQ3LjVsMTQyLjYgMTAzLjFjMy42IDIuNSA0LjQgNy41IDEuOCAxMS4xeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
