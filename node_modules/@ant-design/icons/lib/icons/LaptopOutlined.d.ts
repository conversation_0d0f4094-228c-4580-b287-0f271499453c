import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![laptop](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1Ni45IDg0NS4xTDg5Ni40IDYzMlYxNjhjMC0xNy43LTE0LjMtMzItMzItMzJoLTcwNGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NDY0TDY3LjkgODQ1LjFDNjAuNCA4NjYgNzUuOCA4ODggOTggODg4aDgyOC44YzIyLjIgMCAzNy42LTIyIDMwLjEtNDIuOXpNMjAwLjQgMjA4aDYyNHYzOTVoLTYyNFYyMDh6bTIyOC4zIDYwOGw4LjEtMzdoMTUwLjNsOC4xIDM3SDQyOC43em0yMjQgMGwtMTkuMS04Ni43Yy0uOC0zLjctNC4xLTYuMy03LjgtNi4zSDM5OC4yYy0zLjggMC03IDIuNi03LjggNi4zTDM3MS4zIDgxNkgxNTFsNDIuMy0xNDloNjM4LjJsNDIuMyAxNDlINjUyLjd6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
