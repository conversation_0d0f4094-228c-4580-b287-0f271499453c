import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![close-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNODgwIDExMmMxNy43IDAgMzIgMTQuMyAzMiAzMnY3MzZjMCAxNy43LTE0LjMgMzItMzIgMzJIMTQ0Yy0xNy43IDAtMzItMTQuMy0zMi0zMlYxNDRjMC0xNy43IDE0LjMtMzIgMzItMzJ6TTYzOS45OCAzMzguODJoLS4wNGwtLjA4LjA2TDUxMiA0NjYuNzUgMzg0LjE0IDMzOC44OGMtLjA0LS4wNS0uMDYtLjA2LS4wOC0uMDZhLjEyLjEyIDAgMDAtLjA3IDBjLS4wMyAwLS4wNS4wMS0uMDkuMDVsLTQ1LjAyIDQ1LjAyYS4yLjIgMCAwMC0uMDUuMDkuMTIuMTIgMCAwMDAgLjA3di4wMmEuMjcuMjcgMCAwMC4wNi4wNkw0NjYuNzUgNTEyIDMzOC44OCA2MzkuODZjLS4wNS4wNC0uMDYuMDYtLjA2LjA4YS4xMi4xMiAwIDAwMCAuMDdjMCAuMDMuMDEuMDUuMDUuMDlsNDUuMDIgNDUuMDJhLjIuMiAwIDAwLjA5LjA1LjEyLjEyIDAgMDAuMDcgMGMuMDIgMCAuMDQtLjAxLjA4LS4wNUw1MTIgNTU3LjI1bDEyNy44NiAxMjcuODdjLjA0LjA0LjA2LjA1LjA4LjA1YS4xMi4xMiAwIDAwLjA3IDBjLjAzIDAgLjA1LS4wMS4wOS0uMDVsNDUuMDItNDUuMDJhLjIuMiAwIDAwLjA1LS4wOS4xMi4xMiAwIDAwMC0uMDd2LS4wMmEuMjcuMjcgMCAwMC0uMDUtLjA2TDU1Ny4yNSA1MTJsMTI3Ljg3LTEyNy44NmMuMDQtLjA0LjA1LS4wNi4wNS0uMDhhLjEyLjEyIDAgMDAwLS4wN2MwLS4wMy0uMDEtLjA1LS4wNS0uMDlsLTQ1LjAyLTQ1LjAyYS4yLjIgMCAwMC0uMDktLjA1LjEyLjEyIDAgMDAtLjA3IDB6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
