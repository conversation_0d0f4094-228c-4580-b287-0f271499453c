import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![bell](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgxNiA3NjhoLTI0VjQyOGMwLTE0MS4xLTEwNC4zLTI1Ny43LTI0MC0yNzcuMVYxMTJjMC0yMi4xLTE3LjktNDAtNDAtNDBzLTQwIDE3LjktNDAgNDB2MzguOWMtMTM1LjcgMTkuNC0yNDAgMTM2LTI0MCAyNzcuMXYzNDBoLTI0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnYzMmMwIDQuNCAzLjYgOCA4IDhoMjE2YzAgNjEuOCA1MC4yIDExMiAxMTIgMTEyczExMi01MC4yIDExMi0xMTJoMjE2YzQuNCAwIDgtMy42IDgtOHYtMzJjMC0xNy43LTE0LjMtMzItMzItMzJ6TTUxMiA4ODhjLTI2LjUgMC00OC0yMS41LTQ4LTQ4aDk2YzAgMjYuNS0yMS41IDQ4LTQ4IDQ4ek0zMDQgNzY4VjQyOGMwLTU1LjYgMjEuNi0xMDcuOCA2MC45LTE0Ny4xUzQ1Ni40IDIyMCA1MTIgMjIwYzU1LjYgMCAxMDcuOCAyMS42IDE0Ny4xIDYwLjlTNzIwIDM3Mi40IDcyMCA0Mjh2MzQwSDMwNHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
