"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _PythonOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/PythonOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const PythonOutlined = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _PythonOutlined.default
}));

/**![python](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTU1IDc5MC41YTI4LjUgMjguNSAwIDEwNTcgMCAyOC41IDI4LjUgMCAwMC01NyAwbS0xNDMtNTU3YTI4LjUgMjguNSAwIDEwNTcgMCAyOC41IDI4LjUgMCAwMC01NyAwIiAvPjxwYXRoIGQ9Ik04MjEuNTIgMjk3LjcxSDcyNi4zdi05NS4yM2MwLTQ5LjktNDAuNTgtOTAuNDgtOTAuNDgtOTAuNDhIMzg4LjE5Yy00OS45IDAtOTAuNDggNDAuNTctOTAuNDggOTAuNDh2OTUuMjNoLTk1LjIzYy00OS45IDAtOTAuNDggNDAuNTgtOTAuNDggOTAuNDh2MjQ3LjYyYzAgNDkuOSA0MC41NyA5MC40OCA5MC40OCA5MC40OGg5NS4yM3Y5NS4yM2MwIDQ5LjkgNDAuNTggOTAuNDggOTAuNDggOTAuNDhoMjQ3LjYyYzQ5LjkgMCA5MC40OC00MC41NyA5MC40OC05MC40OFY3MjYuM2g5NS4yM2M0OS45IDAgOTAuNDgtNDAuNTggOTAuNDgtOTAuNDhWMzg4LjE5YzAtNDkuOS00MC41Ny05MC40OC05MC40OC05MC40OE0yMDIuNDggNjY5LjE0YTMzLjM3IDMzLjM3IDAgMDEtMzMuMzQtMzMuMzNWMzg4LjE5YTMzLjM3IDMzLjM3IDAgMDEzMy4zNC0zMy4zM2gyNzguNTdhMjguNTMgMjguNTMgMCAwMDI4LjU3LTI4LjU3IDI4LjUzIDI4LjUzIDAgMDAtMjguNTctMjguNThoLTEyNi4ydi05NS4yM2EzMy4zNyAzMy4zNyAwIDAxMzMuMzQtMzMuMzRoMjQ3LjYyYTMzLjM3IDMzLjM3IDAgMDEzMy4zMyAzMy4zNHYyNTYuNDdhMjQuNDcgMjQuNDcgMCAwMS0yNC40NyAyNC40OEgzNzkuMzNjLTQ1LjA0IDAtODEuNjIgMzYuNjYtODEuNjIgODEuNjJ2MTA0LjF6bTY1Mi4zOC0zMy4zM2EzMy4zNyAzMy4zNyAwIDAxLTMzLjM0IDMzLjMzSDU0Mi45NWEyOC41MyAyOC41MyAwIDAwLTI4LjU3IDI4LjU3IDI4LjUzIDI4LjUzIDAgMDAyOC41NyAyOC41OGgxMjYuMnY5NS4yM2EzMy4zNyAzMy4zNyAwIDAxLTMzLjM0IDMzLjM0SDM4OC4xOWEzMy4zNyAzMy4zNyAwIDAxLTMzLjMzLTMzLjM0VjU2NS4wNWEyNC40NyAyNC40NyAwIDAxMjQuNDctMjQuNDhoMjY1LjM0YzQ1LjA0IDAgODEuNjItMzYuNjcgODEuNjItODEuNjJ2LTEwNC4xaDk1LjIzYTMzLjM3IDMzLjM3IDAgMDEzMy4zNCAzMy4zNHoiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(PythonOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PythonOutlined';
}
var _default = exports.default = RefIcon;