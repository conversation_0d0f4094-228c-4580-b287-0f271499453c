import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![experiment](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIxOC45IDYzNi4zbDQyLjYgMjYuNmMuMS4xLjMuMi40LjNsMTIuNyA4IC4zLjNhMTg2LjkgMTg2LjkgMCAwMDk0LjEgMjUuMWM0NC45IDAgODcuMi0xNS43IDEyMS00My44YTI1Ni4yNyAyNTYuMjcgMCAwMTE2NC45LTU5LjljNTIuMyAwIDEwMi4yIDE1LjcgMTQ0LjYgNDQuNWw3LjkgNS0xMTEuNi0yODlWMTc5LjhoNjMuNWM0LjQgMCA4LTMuNiA4LThWMTIwYzAtNC40LTMuNi04LTgtOEgyNjQuN2MtNC40IDAtOCAzLjYtOCA4djUxLjljMCA0LjQgMy42IDggOCA4aDYzLjV2MTczLjZMMjE4LjkgNjM2LjN6bTMzMy0yMDMuMWMyMiAwIDM5LjkgMTcuOSAzOS45IDM5LjlTNTczLjkgNTEzIDU1MS45IDUxMyA1MTIgNDk1LjEgNTEyIDQ3My4xczE3LjktMzkuOSAzOS45LTM5Ljl6TTg3OCA4MjUuMWwtMjkuOS03Ny40LTg1LjctNTMuNS0uMS4xYy0uNy0uNS0xLjUtMS0yLjItMS41bC04LjEtNS0uMy0uM2MtMjktMTcuNS02Mi4zLTI2LjgtOTctMjYuOC00NC45IDAtODcuMiAxNS43LTEyMSA0My44YTI1Ni4yNyAyNTYuMjcgMCAwMS0xNjQuOSA1OS45Yy01MyAwLTEwMy41LTE2LjEtMTQ2LjItNDUuNmwtMjguOS0xOC4xTDE0NiA4MjUuMWMtMi44IDcuNC00LjMgMTUuMi00LjMgMjMgMCAzNS4yIDI4LjYgNjMuOCA2My44IDYzLjhoNjEyLjljNy45IDAgMTUuNy0xLjUgMjMtNC4zYTYzLjYgNjMuNiAwIDAwMzYuNi04Mi41eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
