import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![diff](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3NiAzOTkuMWMwLTMuOS0zLjEtNy4xLTctNy4xaC00MmMtMy44IDAtNyAzLjItNyA3LjFWNDg0aC04NC41Yy00LjEgMC03LjUgMy4xLTcuNSA3djQyYzAgMy44IDMuNCA3IDcuNSA3SDQyMHY4NC45YzAgMy45IDMuMiA3LjEgNyA3LjFoNDJjMy45IDAgNy0zLjIgNy03LjFWNTQwaDg0LjVjNC4xIDAgNy41LTMuMiA3LjUtN3YtNDJjMC0zLjktMy40LTctNy41LTdINDc2di04NC45ek01NjAuNSA3MDRoLTIyNWMtNC4xIDAtNy41IDMuMi03LjUgN3Y0MmMwIDMuOCAzLjQgNyA3LjUgN2gyMjVjNC4xIDAgNy41LTMuMiA3LjUtN3YtNDJjMC0zLjgtMy40LTctNy41LTd6bS03LjEtNTAyLjZjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjcwNGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg1MTJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzk3LjNjMC04LjUtMy40LTE2LjYtOS40LTIyLjZMNTUzLjQgMjAxLjR6TTY2NCA4ODhIMjMyVjI2NGgyODIuMkw2NjQgNDEzLjhWODg4em0xOTAuMi01ODEuNEw2MTEuMyA3Mi45Yy02LTUuNy0xMy45LTguOS0yMi4yLTguOUgyOTZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoMjc3bDIxOSAyMTAuNlY4MjRjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFYzMjkuNmMwLTguNy0zLjUtMTctOS44LTIzeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
