import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![backward](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4NS42IDI0OS45TDE5OC4yIDQ5OGMtOC4zIDcuMS04LjMgMjAuOCAwIDI3LjlsMjg3LjQgMjQ4LjJjMTAuNyA5LjIgMjYuNC45IDI2LjQtMTRWMjYzLjhjMC0xNC44LTE1LjctMjMuMi0yNi40LTEzLjl6bTMyMCAwTDUxOC4yIDQ5OGExOC42IDE4LjYgMCAwMC02LjIgMTRjMCA1LjIgMi4xIDEwLjQgNi4yIDE0bDI4Ny40IDI0OC4yYzEwLjcgOS4yIDI2LjQuOSAyNi40LTE0VjI2My44YzAtMTQuOC0xNS43LTIzLjItMjYuNC0xMy45eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
