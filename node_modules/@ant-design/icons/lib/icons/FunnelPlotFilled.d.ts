import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![funnel-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMzNi43IDU4NmgzNTAuNmw4NC45LTE0OEgyNTEuOHptNTQzLjQtNDMySDE0My45Yy0yNC41IDAtMzkuOCAyNi43LTI3LjUgNDhMMjE1IDM3NGg1OTRsOTguNy0xNzJjMTIuMi0yMS4zLTMuMS00OC0yNy42LTQ4ek0zNDkgODM4YzAgMTcuNyAxNC4yIDMyIDMxLjggMzJoMjYyLjRjMTcuNiAwIDMxLjgtMTQuMyAzMS44LTMyVjY1MEgzNDl2MTg4eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
