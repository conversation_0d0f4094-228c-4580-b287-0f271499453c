import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![pay-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnptMTU5LjYtNTg1aC01OS41Yy0zIDAtNS44IDEuNy03LjEgNC40bC05MC42IDE4MEg1MTFsLTkwLjYtMTgwYTggOCAwIDAwLTcuMS00LjRoLTYwLjdjLTEuMyAwLTIuNi4zLTMuOCAxLTMuOSAyLjEtNS4zIDctMy4yIDEwLjlMNDU3IDUxNS43aC02MS40Yy00LjQgMC04IDMuNi04IDh2MjkuOWMwIDQuNCAzLjYgOCA4IDhoODEuN1Y2MDNoLTgxLjdjLTQuNCAwLTggMy42LTggOHYyOS45YzAgNC40IDMuNiA4IDggOGg4MS43VjcxN2MwIDQuNCAzLjYgOCA4IDhoNTQuM2M0LjQgMCA4LTMuNiA4LTh2LTY4LjFoODJjNC40IDAgOC0zLjYgOC04VjYxMWMwLTQuNC0zLjYtOC04LThoLTgydi00MS41aDgyYzQuNCAwIDgtMy42IDgtOHYtMjkuOWMwLTQuNC0zLjYtOC04LThoLTYybDExMS4xLTIwNC44Yy42LTEuMiAxLTIuNSAxLTMuOC0uMS00LjQtMy43LTgtOC4xLTh6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
