import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![build](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxNiAyMTBIMzc2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyMzZIMTA4Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyNzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNTQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjU0NmgyMzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMjQyYzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNTA0IDY4aDIwMHYyMDBINDEyVjI3OHptLTY4IDQ2OEgxNDRWNTQ2aDIwMHYyMDB6bTI2OCAwSDQxMlY1NDZoMjAwdjIwMHptMjY4LTI2OEg2ODBWMjc4aDIwMHYyMDB6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
