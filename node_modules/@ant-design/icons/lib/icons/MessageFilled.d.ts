import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![message](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNC4zIDMzOC40YTQ0Ny41NyA0NDcuNTcgMCAwMC05Ni4xLTE0My4zIDQ0My4wOSA0NDMuMDkgMCAwMC0xNDMtOTYuM0E0NDMuOTEgNDQzLjkxIDAgMDA1MTIgNjRoLTJjLTYwLjUuMy0xMTkgMTIuMy0xNzQuMSAzNS45YTQ0NC4wOCA0NDQuMDggMCAwMC0xNDEuNyA5Ni41IDQ0NSA0NDUgMCAwMC05NSAxNDIuOEE0NDkuODkgNDQ5Ljg5IDAgMDA2NSA1MTQuMWMuMyA2OS40IDE2LjkgMTM4LjMgNDcuOSAxOTkuOXYxNTJjMCAyNS40IDIwLjYgNDYgNDUuOSA0NmgxNTEuOGE0NDcuNzIgNDQ3LjcyIDAgMDAxOTkuNSA0OGgyLjFjNTkuOCAwIDExNy43LTExLjYgMTcyLjMtMzQuM0E0NDMuMiA0NDMuMiAwIDAwODI3IDgzMC41YzQxLjItNDAuOSA3My42LTg4LjcgOTYuMy0xNDIgMjMuNS01NS4yIDM1LjUtMTEzLjkgMzUuOC0xNzQuNS4yLTYwLjktMTEuNi0xMjAtMzQuOC0xNzUuNnpNMzEyLjQgNTYwYy0yNi40IDAtNDcuOS0yMS41LTQ3LjktNDhzMjEuNS00OCA0Ny45LTQ4IDQ3LjkgMjEuNSA0Ny45IDQ4LTIxLjQgNDgtNDcuOSA0OHptMTk5LjYgMGMtMjYuNCAwLTQ3LjktMjEuNS00Ny45LTQ4czIxLjUtNDggNDcuOS00OCA0Ny45IDIxLjUgNDcuOSA0OC0yMS41IDQ4LTQ3LjkgNDh6bTE5OS42IDBjLTI2LjQgMC00Ny45LTIxLjUtNDcuOS00OHMyMS41LTQ4IDQ3LjktNDggNDcuOSAyMS41IDQ3LjkgNDgtMjEuNSA0OC00Ny45IDQ4eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
