import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![form](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwNCA1MTJoLTU2Yy00LjQgMC04IDMuNi04IDh2MzIwSDE4NFYxODRoMzIwYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04SDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NzM2YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlY1MjBjMC00LjQtMy42LTgtOC04eiIgLz48cGF0aCBkPSJNMzU1LjkgNTM0LjlMMzU0IDY1My44Yy0uMSA4LjkgNy4xIDE2LjIgMTYgMTYuMmguNGwxMTgtMi45YzItLjEgNC0uOSA1LjQtMi4zbDQxNS45LTQxNWMzLjEtMy4xIDMuMS04LjIgMC0xMS4zTDc4NS40IDExNC4zYy0xLjYtMS42LTMuNi0yLjMtNS43LTIuM3MtNC4xLjgtNS43IDIuM2wtNDE1LjggNDE1YTguMyA4LjMgMCAwMC0yLjMgNS42em02My41IDIzLjZMNzc5LjcgMTk5bDQ1LjIgNDUuMS0zNjAuNSAzNTkuNy00NS43IDEuMS43LTQ2LjR6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
