"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _SlackSquareOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/SlackSquareOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const SlackSquareOutlined = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _SlackSquareOutlined.default
}));

/**![slack-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNTI5IDMxMS40YzAtMjcuOCAyMi41LTUwLjQgNTAuMy01MC40IDI3LjggMCA1MC4zIDIyLjYgNTAuMyA1MC40djEzNC40YzAgMjcuOC0yMi41IDUwLjQtNTAuMyA1MC40LTI3LjggMC01MC4zLTIyLjYtNTAuMy01MC40VjMxMS40ek0zNjEuNSA1ODAuMmMwIDI3LjgtMjIuNSA1MC40LTUwLjMgNTAuNGE1MC4zNSA1MC4zNSAwIDAxLTUwLjMtNTAuNGMwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGg1MC4zdjUwLjR6bTEzNCAxMzQuNGMwIDI3LjgtMjIuNSA1MC40LTUwLjMgNTAuNC0yNy44IDAtNTAuMy0yMi42LTUwLjMtNTAuNFY1ODAuMmMwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGE1MC4zNSA1MC4zNSAwIDAxNTAuMyA1MC40djEzNC40em0tNTAuMi0yMTguNGgtMTM0Yy0yNy44IDAtNTAuMy0yMi42LTUwLjMtNTAuNCAwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGgxMzRjMjcuOCAwIDUwLjMgMjIuNiA1MC4zIDUwLjQtLjEgMjcuOS0yMi42IDUwLjQtNTAuMyA1MC40em0wLTEzNC40Yy0xMy4zIDAtMjYuMS01LjMtMzUuNi0xNC44UzM5NSAzMjQuOCAzOTUgMzExLjRjMC0yNy44IDIyLjUtNTAuNCA1MC4zLTUwLjQgMjcuOCAwIDUwLjMgMjIuNiA1MC4zIDUwLjR2NTAuNGgtNTAuM3ptMTM0IDQwMy4yYy0yNy44IDAtNTAuMy0yMi42LTUwLjMtNTAuNHYtNTAuNGg1MC4zYzI3LjggMCA1MC4zIDIyLjYgNTAuMyA1MC40IDAgMjcuOC0yMi41IDUwLjQtNTAuMyA1MC40em0xMzQtMTM0LjRoLTEzNGE1MC4zNSA1MC4zNSAwIDAxLTUwLjMtNTAuNGMwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGgxMzRjMjcuOCAwIDUwLjMgMjIuNiA1MC4zIDUwLjQgMCAyNy44LTIyLjUgNTAuNC01MC4zIDUwLjR6bTAtMTM0LjRINjYzdi01MC40YzAtMjcuOCAyMi41LTUwLjQgNTAuMy01MC40czUwLjMgMjIuNiA1MC4zIDUwLjRjMCAyNy44LTIyLjUgNTAuNC01MC4zIDUwLjR6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(SlackSquareOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SlackSquareOutlined';
}
var _default = exports.default = RefIcon;