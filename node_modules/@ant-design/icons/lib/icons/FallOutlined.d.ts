import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![fall](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNS45IDgwNGwtMjQtMTk5LjJjLS44LTYuNi04LjktOS40LTEzLjYtNC43TDgyOSA2NTkuNSA1NTcuNyAzODguM2MtNi4zLTYuMi0xNi40LTYuMi0yMi42IDBMNDMzLjMgNDkwIDE1Ni42IDIxMy4zYTguMDMgOC4wMyAwIDAwLTExLjMgMGwtNDUgNDUuMmE4LjAzIDguMDMgMCAwMDAgMTEuM0w0MjIgNTkxLjdjNi4yIDYuMyAxNi40IDYuMyAyMi42IDBMNTQ2LjQgNDkwbDIyNi4xIDIyNi01OS4zIDU5LjNhOC4wMSA4LjAxIDAgMDA0LjcgMTMuNmwxOTkuMiAyNGM1LjEuNyA5LjUtMy43IDguOC04Ljl6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
