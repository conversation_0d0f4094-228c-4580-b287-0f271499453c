import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![ci-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0tNjMuNiA2NTZjLTEwMyAwLTE2Mi40LTY4LjYtMTYyLjQtMTgyLjZ2LTQ5QzI4NiAzNzMuNSAzNDUuNCAzMDQgNDQ4LjMgMzA0Yzg4LjMgMCAxNTIuMyA1Ni45IDE1Mi4zIDEzOC4xIDAgMi40LTIgNC40LTQuNCA0LjRoLTUyLjZjLTQuMiAwLTcuNi0zLjItOC03LjQtNC00Ni4xLTM3LjYtNzcuNi04Ny03Ny42LTYxLjEgMC05NS42IDQ1LjQtOTUuNiAxMjYuOXY0OS4zYzAgODAuMyAzNC41IDEyNS4xIDk1LjYgMTI1LjEgNDkuMyAwIDgyLjgtMjkuNSA4Ny03Mi40LjQtNC4xIDMuOC03LjMgOC03LjNoNTIuN2MyLjQgMCA0LjQgMiA0LjQgNC40IDAgNzcuNC02NC4zIDEzMi41LTE1Mi4zIDEzMi41ek03MzggNzA0LjFjMCA0LjQtMy42IDgtOCA4aC01MC40Yy00LjQgMC04LTMuNi04LThWMzE5LjljMC00LjQgMy42LTggOC04SDczMGM0LjQgMCA4IDMuNiA4IDh2Mzg0LjJ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
