import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![delete-column](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik02NTEuMSA2NDEuOWE3Ljg0IDcuODQgMCAwMC01LjEtMS45aC01NC43Yy0yLjQgMC00LjYgMS4xLTYuMSAyLjlMNTEyIDczMC43bC03My4xLTg3LjhhOC4xIDguMSAwIDAwLTYuMS0yLjlIMzc4Yy0xLjkgMC0zLjcuNy01LjEgMS45YTcuOTcgNy45NyAwIDAwLTEgMTEuM0w0NzQuMiA3NzYgMzcxLjggODk4LjlhOC4wNiA4LjA2IDAgMDA2LjEgMTMuMmg1NC43YzIuNCAwIDQuNi0xLjEgNi4xLTIuOWw3My4xLTg3LjggNzMuMSA4Ny44YTguMSA4LjEgMCAwMDYuMSAyLjloNTVjMS45IDAgMy43LS43IDUuMS0xLjkgMy40LTIuOCAzLjktNy45IDEtMTEuM0w1NDkuOCA3NzZsMTAyLjQtMTIyLjljMi44LTMuNCAyLjMtOC40LTEuMS0xMS4yek00NzIgNTQ0aDgwYzQuNCAwIDgtMy42IDgtOFYxMjBjMC00LjQtMy42LTgtOC04aC04MGMtNC40IDAtOCAzLjYtOCA4djQxNmMwIDQuNCAzLjYgOCA4IDh6TTM1MCAzODZIMTg0VjEzNmMwLTMuMy0yLjctNi02LTZoLTYwYy0zLjMgMC02IDIuNy02IDZ2MjkyYzAgMTYuNiAxMy40IDMwIDMwIDMwaDIwOGMzLjMgMCA2LTIuNyA2LTZ2LTYwYzAtMy4zLTIuNy02LTYtNnptNTU2LTI1NmgtNjBjLTMuMyAwLTYgMi43LTYgNnYyNTBINjc0Yy0zLjMgMC02IDIuNy02IDZ2NjBjMCAzLjMgMi43IDYgNiA2aDIwOGMxNi42IDAgMzAtMTMuNCAzMC0zMFYxMzZjMC0zLjMtMi43LTYtNi02eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
