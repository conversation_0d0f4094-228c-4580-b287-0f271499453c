import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![contacts](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCAyMjRINzY4di01NmMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NTZINTQ4di01NmMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NTZIMzI4di01NmMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NTZIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjU3NmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMjU2YzAtMTcuNy0xNC4zLTMyLTMyLTMyek02NjEgNzM2aC00My45Yy00LjIgMC03LjYtMy4zLTcuOS03LjUtMy44LTUwLjYtNDYtOTAuNS05Ny4yLTkwLjVzLTkzLjQgNDAtOTcuMiA5MC41Yy0uMyA0LjItMy43IDcuNS03LjkgNy41SDM2M2E4IDggMCAwMS04LTguNGMyLjgtNTMuMyAzMi05OS43IDc0LjYtMTI2LjFhMTExLjggMTExLjggMCAwMS0yOS4xLTc1LjVjMC02MS45IDQ5LjktMTEyIDExMS40LTExMiA2MS41IDAgMTExLjQgNTAuMSAxMTEuNCAxMTIgMCAyOS4xLTExIDU1LjUtMjkuMSA3NS41IDQyLjcgMjYuNSA3MS44IDcyLjggNzQuNiAxMjYuMS40IDQuNi0zLjIgOC40LTcuOCA4LjR6TTUxMiA0NzRjLTI4LjUgMC01MS43IDIzLjMtNTEuNyA1MnMyMy4yIDUyIDUxLjcgNTJjMjguNSAwIDUxLjctMjMuMyA1MS43LTUycy0yMy4yLTUyLTUxLjctNTJ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
