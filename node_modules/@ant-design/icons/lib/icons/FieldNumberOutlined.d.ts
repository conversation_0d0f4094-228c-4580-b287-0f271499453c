import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![field-number](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik01MDggMjgwaC02My4zYy0zLjMgMC02IDIuNy02IDZ2MzQwLjJINDMzTDE5Ny40IDI4Mi42Yy0xLjEtMS42LTMtMi42LTQuOS0yLjZIMTI2Yy0zLjMgMC02IDIuNy02IDZ2NDY0YzAgMy4zIDIuNyA2IDYgNmg2Mi43YzMuMyAwIDYtMi43IDYtNlY0MDUuMWg1LjdsMjM4LjIgMzQ4LjNjMS4xIDEuNiAzIDIuNiA1IDIuNkg1MDhjMy4zIDAgNi0yLjcgNi02VjI4NmMwLTMuMy0yLjctNi02LTZ6bTM3OCA0MTNINTgyYy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDMwNGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHptLTE1Mi4yLTYzYzUyLjkgMCA5NS4yLTE3LjIgMTI2LjItNTEuNyAyOS40LTMyLjkgNDQtNzUuOCA0NC0xMjguOCAwLTUzLjEtMTQuNi05Ni41LTQ0LTEyOS4zLTMwLjktMzQuOC03My4yLTUyLjItMTI2LjItNTIuMi01My43IDAtOTUuOSAxNy41LTEyNi4zIDUyLjgtMjkuMiAzMy4xLTQzLjQgNzUuOS00My40IDEyOC43IDAgNTIuNCAxNC4zIDk1LjIgNDMuNSAxMjguMyAzMC42IDM0LjcgNzMgNTIuMiAxMjYuMiA1Mi4yem0tNzEuNS0yNjMuN2MxNi45LTIwLjYgNDAuMy0zMC45IDcxLjQtMzAuOSAzMS41IDAgNTQuOCA5LjYgNzEgMjkuMSAxNi40IDIwLjMgMjQuOSA0OC42IDI0LjkgODQuOSAwIDM2LjMtOC40IDY0LjEtMjQuOCA4My45LTE2LjUgMTkuNC00MCAyOS4yLTcxLjEgMjkuMi0zMS4yIDAtNTUtMTAuMy03MS40LTMwLjQtMTYuMy0yMC4xLTI0LjUtNDcuMy0yNC41LTgyLjYuMS0zNS44IDguMi02MyAyNC41LTgzLjJ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
