import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![pic-center](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1MiA3OTJINzJjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoODgwYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04em0wLTYzMkg3MmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg4ODBjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6TTg0OCA2NjBjOC44IDAgMTYtNy4yIDE2LTE2VjM4MGMwLTguOC03LjItMTYtMTYtMTZIMTc2Yy04LjggMC0xNiA3LjItMTYgMTZ2MjY0YzAgOC44IDcuMiAxNiAxNiAxNmg2NzJ6TTIzMiA0MzZoNTYwdjE1MkgyMzJWNDM2eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
