import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![gitlab](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMC41IDU1My4ybC0xMDktMzcwLjhjLTYuOC0yMC40LTIzLjEtMzQuMS00NC45LTM0LjFzLTM5LjUgMTIuMy00Ni4zIDMyLjdsLTcyLjIgMjE1LjRIMzg2LjJMMzE0IDE4MS4xYy02LjgtMjAuNC0yNC41LTMyLjctNDYuMy0zMi43cy0zOS41IDEzLjYtNDQuOSAzNC4xTDExMy45IDU1My4yYy00LjEgMTMuNiAxLjQgMjguNiAxMi4zIDM2LjhsMzg1LjQgMjg5IDM4Ni43LTI4OWMxMC44LTguMSAxNi4zLTIzLjEgMTIuMi0zNi44eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
