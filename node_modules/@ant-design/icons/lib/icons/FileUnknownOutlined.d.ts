import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-unknown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43TDYzOS40IDczLjRjLTYtNi0xNC4yLTkuNC0yMi43LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzExLjNjMC04LjUtMy40LTE2LjYtOS40LTIyLjZ6TTc5MC4yIDMyNkg2MDJWMTM3LjhMNzkwLjIgMzI2em0xLjggNTYySDIzMlYxMzZoMzAydjIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHpNNDAyIDU0OWMwIDUuNCA0LjQgOS41IDkuOCA5LjVoMzIuNGM1LjQgMCA5LjgtNC4yIDkuOC05LjQgMC0yOC4yIDI1LjgtNTEuNiA1OC01MS42czU4IDIzLjQgNTggNTEuNWMwIDI1LjMtMjEgNDcuMi00OS4zIDUwLjktMTkuMyAyLjgtMzQuNSAyMC4zLTM0LjcgNDAuMXYzMmMwIDUuNSA0LjUgMTAgMTAgMTBoMzJjNS41IDAgMTAtNC41IDEwLTEwdi0xMi4yYzAtNiA0LTExLjUgOS43LTEzLjMgNDQuNi0xNC40IDc1LTU0IDc0LjMtOTguOS0uOC01NS41LTQ5LjItMTAwLjgtMTA4LjUtMTAxLjYtNjEuNC0uNy0xMTEuNSA0NS42LTExMS41IDEwM3ptNzggMTk1YTMyIDMyIDAgMTA2NCAwIDMyIDMyIDAgMTAtNjQgMHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
