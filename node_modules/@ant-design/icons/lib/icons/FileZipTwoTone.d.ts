import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-zip](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM0NCA2MzBoMzJ2MmgtMzJ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik01MzQgMzUyVjEzNkgzNjB2NjRoNjR2NjRoLTY0djY0aDY0djY0aC02NHY2NGg2NHY2NGgtNjR2NjJoNjR2MTYwSDI5NlY1MjBoNjR2LTY0aC02NHYtNjRoNjR2LTY0aC02NHYtNjRoNjR2LTY0aC02NHYtNjRoLTY0djc1Mmg1NjBWMzk0SDU3NmE0MiA0MiAwIDAxLTQyLTQyeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODU0LjYgMjg4LjZMNjM5LjQgNzMuNGMtNi02LTE0LjEtOS40LTIyLjYtOS40SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2ODMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDY0MGMxNy43IDAgMzItMTQuMyAzMi0zMlYzMTEuM2MwLTguNS0zLjQtMTYuNy05LjQtMjIuN3pNNjAyIDEzNy44TDc5MC4yIDMyNkg2MDJWMTM3Ljh6TTc5MiA4ODhIMjMyVjEzNmg2NHY2NGg2NHYtNjRoMTc0djIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTI5NiAzOTJoNjR2NjRoLTY0em0wLTEyOGg2NHY2NGgtNjR6bTAgMzE4djE2MGgxMjhWNTgyaC02NHYtNjJoLTY0djYyem00OCA1MHYtMmgzMnY2NGgtMzJ2LTYyem0xNi00MzJoNjR2NjRoLTY0em0wIDI1Nmg2NHY2NGgtNjR6bTAtMTI4aDY0djY0aC02NHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
