import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![html5](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE0NS4yIDk2bDY2IDc0Ni42TDUxMiA5MjhsMjk5LjYtODUuNEw4NzguOSA5NkgxNDUuMnptNTk1IDE3Ny4xbC00LjggNDcuMi0xLjcgMTkuNUgzODIuM2w4LjIgOTQuMmgzMzUuMWwtMy4zIDI0LjMtMjEuMiAyNDIuMi0xLjcgMTYuMi0xODcgNTEuNnYuM2gtMS4ybC0uMy4xdi0uMWgtLjFsLTE4OC42LTUyTDMxMC44IDU3Mmg5MS4xbDYuNSA3My4yIDEwMi40IDI3LjdoLjRsMTAyLTI3LjYgMTEuNC0xMTguNkg1MTAuOXYtLjFIMzA2bC0yMi44LTI1My41LTEuNy0yNC4zaDQ2MC4zbC0xLjYgMjQuM3oiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
