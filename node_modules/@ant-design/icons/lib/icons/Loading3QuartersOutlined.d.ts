import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![loading-3-quarters](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAxMDI0Yy02OS4xIDAtMTM2LjItMTMuNS0xOTkuMy00MC4yQzI1MS43IDk1OCAxOTcgOTIxIDE1MCA4NzRjLTQ3LTQ3LTg0LTEwMS43LTEwOS44LTE2Mi43QzEzLjUgNjQ4LjIgMCA1ODEuMSAwIDUxMmMwLTE5LjkgMTYuMS0zNiAzNi0zNnMzNiAxNi4xIDM2IDM2YzAgNTkuNCAxMS42IDExNyAzNC42IDE3MS4zIDIyLjIgNTIuNCA1My45IDk5LjUgOTQuMyAxMzkuOSA0MC40IDQwLjQgODcuNSA3Mi4yIDEzOS45IDk0LjNDMzk1IDk0MC40IDQ1Mi42IDk1MiA1MTIgOTUyYzU5LjQgMCAxMTctMTEuNiAxNzEuMy0zNC42IDUyLjQtMjIuMiA5OS41LTUzLjkgMTM5LjktOTQuMyA0MC40LTQwLjQgNzIuMi04Ny41IDk0LjMtMTM5LjlDOTQwLjQgNjI5IDk1MiA1NzEuNCA5NTIgNTEyYzAtNTkuNC0xMS42LTExNy0zNC42LTE3MS4zYTQ0MC40NSA0NDAuNDUgMCAwMC05NC4zLTEzOS45IDQzNy43MSA0MzcuNzEgMCAwMC0xMzkuOS05NC4zQzYyOSA4My42IDU3MS40IDcyIDUxMiA3MmMtMTkuOSAwLTM2LTE2LjEtMzYtMzZzMTYuMS0zNiAzNi0zNmM2OS4xIDAgMTM2LjIgMTMuNSAxOTkuMyA0MC4yQzc3Mi4zIDY2IDgyNyAxMDMgODc0IDE1MGM0NyA0NyA4My45IDEwMS44IDEwOS43IDE2Mi43IDI2LjcgNjMuMSA0MC4yIDEzMC4yIDQwLjIgMTk5LjNzLTEzLjUgMTM2LjItNDAuMiAxOTkuM0M5NTggNzcyLjMgOTIxIDgyNyA4NzQgODc0Yy00NyA0Ny0xMDEuOCA4My45LTE2Mi43IDEwOS43LTYzLjEgMjYuOC0xMzAuMiA0MC4zLTE5OS4zIDQwLjN6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
