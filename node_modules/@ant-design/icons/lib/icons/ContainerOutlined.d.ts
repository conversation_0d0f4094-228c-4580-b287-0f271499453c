import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![container](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6bS00MCA4MjRIMjMyVjY4N2g5Ny45YzExLjYgMzIuOCAzMiA2Mi4zIDU5LjEgODQuNyAzNC41IDI4LjUgNzguMiA0NC4zIDEyMyA0NC4zczg4LjUtMTUuNyAxMjMtNDQuM2MyNy4xLTIyLjQgNDcuNS01MS45IDU5LjEtODQuN0g3OTJ2LTYzSDY0My42bC01LjIgMjQuN0M2MjYuNCA3MDguNSA1NzMuMiA3NTIgNTEyIDc1MnMtMTE0LjQtNDMuNS0xMjYuNS0xMDMuM2wtNS4yLTI0LjdIMjMyVjEzNmg1NjB2NzUyek0zMjAgMzQxaDM4NGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOEgzMjBjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDh6bTAgMTYwaDM4NGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOEgzMjBjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDh6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
