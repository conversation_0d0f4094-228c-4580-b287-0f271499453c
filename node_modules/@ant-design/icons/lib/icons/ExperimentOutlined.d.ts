import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![experiment](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA0NzJhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAwem0zNjcgMzUyLjlMNjk2LjMgMzUyVjE3OEg3Njh2LTY4SDI1NnY2OGg3MS43djE3NEwxNDUgODI0LjljLTIuOCA3LjQtNC4zIDE1LjItNC4zIDIzLjEgMCAzNS4zIDI4LjcgNjQgNjQgNjRoNjE0LjZjNy45IDAgMTUuNy0xLjUgMjMuMS00LjMgMzMtMTIuNyA0OS40LTQ5LjggMzYuNi04Mi44ek0zOTUuNyAzNjQuN1YxODBoMjMyLjZ2MTg0LjdMNzE5LjIgNjAwYy0yMC43LTUuMy00Mi4xLTgtNjMuOS04LTYxLjIgMC0xMTkuMiAyMS41LTE2NS4zIDYwYTE4OC43OCAxODguNzggMCAwMS0xMjEuMyA0My45Yy0zMi43IDAtNjQuMS04LjMtOTEuOC0yMy43bDExOC44LTMwNy41ek0yMTAuNSA4NDRsNDEuNy0xMDcuOGMzNS43IDE4LjEgNzUuNCAyNy44IDExNi42IDI3LjggNjEuMiAwIDExOS4yLTIxLjUgMTY1LjMtNjAgMzMuOS0yOC4yIDc2LjMtNDMuOSAxMjEuMy00My45IDM1IDAgNjguNCA5LjUgOTcuNiAyNy4xTDgxMy41IDg0NGgtNjAzeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
