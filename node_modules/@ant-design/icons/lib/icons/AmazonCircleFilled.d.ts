import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![amazon-circle](data:image/svg+xml;base64,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) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
