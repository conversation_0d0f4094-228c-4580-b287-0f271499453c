import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![control](data:image/svg+xml;base64,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) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
