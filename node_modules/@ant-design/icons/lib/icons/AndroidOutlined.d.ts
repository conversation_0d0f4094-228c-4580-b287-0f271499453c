import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![android](data:image/svg+xml;base64,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) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
