{"name": "rc-checkbox", "version": "3.0.1", "description": "checkbox ui component for react", "keywords": ["react", "react-component", "react-checkbox", "checkbox"], "homepage": "http://github.com/react-component/checkbox", "bugs": {"url": "http://github.com/react-component/checkbox/issues"}, "repository": {"type": "git", "url": "**************:react-component/checkbox.git"}, "license": "MIT", "main": "lib/index.js", "module": "es/index.js", "typings": "es/index.d.ts", "files": ["assets/*.css", "es", "lib"], "scripts": {"build": "dumi build", "compile": "father build && lessc assets/index.less assets/index.css", "coverage": "jest --coverage", "gh-pages": "npm run build && father doc deploy -d .docs", "lint": "eslint src/ --ext .tsx,.ts", "prepare": "husky install && dumi setup", "prepublishOnly": "npm run compile && np --yolo --no-publish", "start": "dumi dev", "test": "jest"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.3.2", "rc-util": "^5.25.2"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0 ", "@testing-library/user-event": "^14.4.3", "@types/classnames": "^2.3.1", "@types/jest": "^29.2.4", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@umijs/fabric": "^3.0.0", "@umijs/test": "^4.0.34", "coveralls": "^3.0.6", "dumi": "^2.0.12", "eslint": "^7.0.0", "father": "^4.1.1", "husky": "8.0.2", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "less": "^3.11.1", "lint-staged": "^13.1.0", "np": "^6.2.3", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.1", "typescript": "^4.9.4"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}