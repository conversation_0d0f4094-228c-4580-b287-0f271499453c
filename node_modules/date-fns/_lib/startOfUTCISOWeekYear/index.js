"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = startOfUTCISOWeekYear;
var _index = _interopRequireDefault(require("../getUTCISOWeekYear/index.js"));
var _index2 = _interopRequireDefault(require("../startOfUTCISOWeek/index.js"));
var _index3 = _interopRequireDefault(require("../requiredArgs/index.js"));
function startOfUTCISOWeekYear(dirtyDate) {
  (0, _index3.default)(1, arguments);
  var year = (0, _index.default)(dirtyDate);
  var fourthOfJanuary = new Date(0);
  fourthOfJanuary.setUTCFullYear(year, 0, 4);
  fourthOfJanuary.setUTCHours(0, 0, 0, 0);
  var date = (0, _index2.default)(fourthOfJanuary);
  return date;
}
module.exports = exports.default;