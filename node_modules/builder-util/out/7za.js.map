{"version": 3, "file": "7za.js", "sourceRoot": "", "sources": ["../src/7za.ts"], "names": [], "mappings": ";;AAIA,gCAKC;AAED,8BAGC;AAdD,wCAA0C;AAC1C,yBAAwB;AACxB,uCAAgC;AAEzB,KAAK,UAAU,UAAU;IAC9B,IAAI,EAAE,CAAC,UAAU,CAAC,mBAAO,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAA,gBAAK,EAAC,mBAAO,EAAE,KAAK,CAAC,CAAA;IAC7B,CAAC;IACD,OAAO,mBAAO,CAAA;AAChB,CAAC;AAEM,KAAK,UAAU,SAAS;IAC7B,MAAM,IAAA,gBAAK,EAAC,kBAAM,EAAE,KAAK,CAAC,CAAA;IAC1B,OAAO,kBAAM,CAAA;AACf,CAAC", "sourcesContent": ["import { path7x, path7za } from \"7zip-bin\"\nimport * as fs from \"fs\"\nimport { chmod } from \"fs-extra\"\n\nexport async function getPath7za(): Promise<string> {\n  if (fs.existsSync(path7za)) {\n    await chmod(path7za, 0o755)\n  }\n  return path7za\n}\n\nexport async function getPath7x(): Promise<string> {\n  await chmod(path7x, 0o755)\n  return path7x\n}\n"]}