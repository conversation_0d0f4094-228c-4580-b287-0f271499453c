# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.1](https://github.com/ljharb/defined/compare/v1.0.0...v1.0.1) - 2022-10-12

### Commits

- [eslint] fix indentation [`84801c0`](https://github.com/ljharb/defined/commit/84801c054280c86c433022be64feddf38ac426aa)
- [readme] rename, add badges [`7ccb011`](https://github.com/ljharb/defined/commit/7ccb011e69adda93a923408819730848ed9328cc)
- [actions] add reusable workflows [`03917f0`](https://github.com/ljharb/defined/commit/03917f0a2a1123f71bd6a1e2d48802051614bda4)
- [<PERSON>] update `tape` [`ac57011`](https://github.com/ljharb/defined/commit/ac57011cb753bf1f3937569b16f76a22ea6e9230)
- [eslint] add eslint [`9bf7583`](https://github.com/ljharb/defined/commit/9bf7583959cb02ed2feac6d3781965921d2220f8)
- [meta] add `auto-changelog` [`ec13b5f`](https://github.com/ljharb/defined/commit/ec13b5f04eb2273e6708bb8ac00e29b109880b07)
- [meta] use `npmignore` to autogenerate an npmignore file [`549e1ff`](https://github.com/ljharb/defined/commit/549e1ff99822f86ec6394dcb4540a4ee596e7433)
- Only apps should have lockfiles [`11dfedc`](https://github.com/ljharb/defined/commit/11dfedcd1cf1471fac56763064471f0093aa841f)
- [meta] add `safe-publish-latest` [`efadc76`](https://github.com/ljharb/defined/commit/efadc76397fcfb4435934dab7c8a744bc831c38f)
- [meta] update URLs [`aa9c486`](https://github.com/ljharb/defined/commit/aa9c48684a74611afaa50ae4429832cd39616812)
- [Tests] add `aud` in `posttest` [`a0cf2fb`](https://github.com/ljharb/defined/commit/a0cf2fb66a61870e0657c4f41ba0893abfef77a1)
- [meta] create FUNDING.yml; add `funding` in package.json [`b05fc5e`](https://github.com/ljharb/defined/commit/b05fc5e478baeb12822978e38232c8b8daf60c29)

## [v1.0.0](https://github.com/ljharb/defined/compare/v0.0.0...v1.0.0) - 2015-03-30

### Commits

- using testling-ci [`9f11918`](https://github.com/ljharb/defined/commit/9f11918b7dffb639fc960da7c8a5914d7df67e80)
- Bump to 1.0.0 + newer tape [`3a46c81`](https://github.com/ljharb/defined/commit/3a46c81d39b5f94c0c17c47638939af2528520f3)
- another test [`5c825a7`](https://github.com/ljharb/defined/commit/5c825a710662cab0b8abb37132cae19d0dcf00cb)
- using travis [`4dabaf5`](https://github.com/ljharb/defined/commit/4dabaf53092665b36961a0b82a00d818051d69db)
- use tape 0.2.2 [`1337250`](https://github.com/ljharb/defined/commit/1337250d7f0f7f63ebc864ad509ce1247978b451)
- bump [`07dbbbf`](https://github.com/ljharb/defined/commit/07dbbbfa155c91e9ab09da07af797738340c7338)
- bump [`1a6fde3`](https://github.com/ljharb/defined/commit/1a6fde32136c51b4b8d8664d2b6072d241e5b4ae)

## v0.0.0 - 2012-11-25

### Commits

- docs, example, and code ripped from the tape module [`6bec2cc`](https://github.com/ljharb/defined/commit/6bec2cc9c82f19c2960f344b5141154d6eaa7380)
- package.json whatevs [`ac951cd`](https://github.com/ljharb/defined/commit/ac951cd1dd31b7944fe3b539f091766bfb178e00)
- passing test [`d6e0c87`](https://github.com/ljharb/defined/commit/d6e0c87982c76f4889541d8ed57a463c259fec2c)
- oh right tape [`e3f9596`](https://github.com/ljharb/defined/commit/e3f9596dcc4c5e2a2657fda5f5cad2b9957d705f)
- note about perl 5.10 [`6eba8e6`](https://github.com/ljharb/defined/commit/6eba8e6a2927a5d8b748d422ad7e64b977ab4f94)
- -bin [`fbf0d20`](https://github.com/ljharb/defined/commit/fbf0d20d9cec86266ed06e8fe4f5b9927917a3c5)
