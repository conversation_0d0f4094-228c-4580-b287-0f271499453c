!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom"),require("@floating-ui/react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom","@floating-ui/react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIReact={},e.<PERSON>act,e.ReactDOM,e.FloatingUIReactDOM)}(this,(function(e,t,n,r){"use strict";function o(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var u=o(t),i=o(n);function l(e){const t=u.useRef(void 0),n=u.useCallback((t=>{const n=e.map((e=>{if(null!=e){if("function"==typeof e){const n=e,r=n(t);return"function"==typeof r?r:()=>{n(null)}}return e.current=t,()=>{e.current=null}}}));return()=>{n.forEach((e=>null==e?void 0:e()))}}),e);return u.useMemo((()=>e.every((e=>null==e))?null:e=>{t.current&&(t.current(),t.current=void 0),null!=e&&(t.current=n(e))}),e)}const c={...u},s=c.useInsertionEffect||(e=>e());function a(e){const t=u.useRef((()=>{}));return s((()=>{t.current=e})),u.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}function f(){return"undefined"!=typeof window}function d(e){return v(e)?(e.nodeName||"").toLowerCase():"#document"}function m(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function v(e){return!!f()&&(e instanceof Node||e instanceof m(e).Node)}function p(e){return!!f()&&(e instanceof Element||e instanceof m(e).Element)}function g(e){return!!f()&&(e instanceof HTMLElement||e instanceof m(e).HTMLElement)}function h(e){return!(!f()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof m(e).ShadowRoot)}function y(e){return["html","body","#document"].includes(d(e))}function b(e){return m(e).getComputedStyle(e)}function E(e){if("html"===d(e))return e;const t=e.assignedSlot||e.parentNode||h(e)&&e.host||function(e){var t;return null==(t=(v(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}(e);return h(t)?t.host:t}function w(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function R(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&h(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function x(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function I(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}function k(e){return!(0!==e.mozInputSource||!e.isTrusted)||(O()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function C(e){return!I().includes("jsdom/")&&(!O()&&0===e.width&&0===e.height||O()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}function M(){return/apple/i.test(navigator.vendor)}function O(){const e=/android/i;return e.test(x())||e.test(I())}function S(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function P(e){return(null==e?void 0:e.ownerDocument)||document}function T(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}function L(e){return"composedPath"in e?e.composedPath()[0]:e.target}function A(e){return g(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}function D(e){e.preventDefault(),e.stopPropagation()}function N(e){return!!e&&("combobox"===e.getAttribute("role")&&A(e))}const F=Math.min,j=Math.max,K=Math.round,H=Math.floor;const q="ArrowUp",W="ArrowDown",_="ArrowLeft",B="ArrowRight";function U(e,t,n){return Math.floor(e/t)!==n}function z(e,t){return t<0||t>=e.current.length}function X(e,t){return V(e,{disabledIndices:t})}function Y(e,t){return V(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function V(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:u=1}=void 0===t?{}:t;const i=e.current;let l=n;do{l+=r?-u:u}while(l>=0&&l<=i.length-1&&J(i,l,o));return l}function G(e,t){let{event:n,orientation:r,loop:o,rtl:u,cols:i,disabledIndices:l,minIndex:c,maxIndex:s,prevIndex:a,stopEvent:f=!1}=t,d=a;if(n.key===q){if(f&&D(n),-1===a)d=s;else if(d=V(e,{startingIndex:d,amount:i,decrement:!0,disabledIndices:l}),o&&(a-i<c||d<0)){const e=a%i,t=s%i,n=s-(t-e);d=t===e?s:t>e?n:n-i}z(e,d)&&(d=a)}if(n.key===W&&(f&&D(n),-1===a?d=c:(d=V(e,{startingIndex:a,amount:i,disabledIndices:l}),o&&a+i>s&&(d=V(e,{startingIndex:a%i-i,amount:i,disabledIndices:l}))),z(e,d)&&(d=a)),"both"===r){const t=H(a/i);n.key===(u?_:B)&&(f&&D(n),a%i!=i-1?(d=V(e,{startingIndex:a,disabledIndices:l}),o&&U(d,i,t)&&(d=V(e,{startingIndex:a-a%i-1,disabledIndices:l}))):o&&(d=V(e,{startingIndex:a-a%i-1,disabledIndices:l})),U(d,i,t)&&(d=a)),n.key===(u?B:_)&&(f&&D(n),a%i!=0?(d=V(e,{startingIndex:a,decrement:!0,disabledIndices:l}),o&&U(d,i,t)&&(d=V(e,{startingIndex:a+(i-a%i),decrement:!0,disabledIndices:l}))):o&&(d=V(e,{startingIndex:a+(i-a%i),decrement:!0,disabledIndices:l})),U(d,i,t)&&(d=a));const r=H(s/i)===t;z(e,d)&&(d=o&&r?n.key===(u?B:_)?s:V(e,{startingIndex:a-a%i-1,disabledIndices:l}):a)}return d}function Z(e,t,n){const r=[];let o=0;return e.forEach(((e,u)=>{let{width:i,height:l}=e,c=!1;for(n&&(o=0);!c;){const e=[];for(let n=0;n<i;n++)for(let r=0;r<l;r++)e.push(o+n+r*t);o%t+i<=t&&e.every((e=>null==r[e]))?(e.forEach((e=>{r[e]=u})),c=!0):o++}})),[...r]}function $(e,t,n,r,o){if(-1===e)return-1;const u=n.indexOf(e),i=t[e];switch(o){case"tl":return u;case"tr":return i?u+i.width-1:u;case"bl":return i?u+(i.height-1)*r:u;case"br":return n.lastIndexOf(e)}}function Q(e,t){return t.flatMap(((t,n)=>e.includes(t)?[n]:[]))}function J(e,t,n){if(n)return n.includes(t);const r=e[t];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}var ee="undefined"!=typeof document?t.useLayoutEffect:t.useEffect;function te(e,t){const n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}const ne=u.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function re(e){const{children:t,elementsRef:n,labelsRef:r}=e,[o,i]=u.useState((()=>new Set)),l=u.useCallback((e=>{i((t=>new Set(t).add(e)))}),[]),c=u.useCallback((e=>{i((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),s=u.useMemo((()=>{const e=new Map;return Array.from(o.keys()).sort(te).forEach(((t,n)=>{e.set(t,n)})),e}),[o]);return u.createElement(ne.Provider,{value:u.useMemo((()=>({register:l,unregister:c,map:s,elementsRef:n,labelsRef:r})),[l,c,s,n,r])},t)}function oe(e){void 0===e&&(e={});const{label:t}=e,{register:n,unregister:r,map:o,elementsRef:i,labelsRef:l}=u.useContext(ne),[c,s]=u.useState(null),a=u.useRef(null),f=u.useCallback((e=>{if(a.current=e,null!==c&&(i.current[c]=e,l)){var n;const r=void 0!==t;l.current[c]=r?t:null!=(n=null==e?void 0:e.textContent)?n:null}}),[c,i,l,t]);return ee((()=>{const e=a.current;if(e)return n(e),()=>{r(e)}}),[n,r]),ee((()=>{const e=a.current?o.get(a.current):null;null!=e&&s(e)}),[o]),u.useMemo((()=>({ref:f,index:null==c?-1:c})),[c,f])}function ue(e,t){return"function"==typeof e?e(t):e?u.cloneElement(e,t):u.createElement("div",t)}const ie=u.createContext({activeIndex:0,onNavigate:()=>{}}),le=[_,B],ce=[q,W],se=[...le,...ce],ae=u.forwardRef((function(e,t){const{render:n,orientation:r="both",loop:o=!0,rtl:i=!1,cols:l=1,disabledIndices:c,activeIndex:s,onNavigate:f,itemSizes:d,dense:m=!1,...v}=e,[p,g]=u.useState(0),h=null!=s?s:p,y=a(null!=f?f:g),b=u.useRef([]),E=n&&"function"!=typeof n?n.props:{},w=u.useMemo((()=>({activeIndex:h,onNavigate:y})),[h,y]),R=l>1;const x={...v,...E,ref:t,"aria-orientation":"both"===r?void 0:r,onKeyDown(e){null==v.onKeyDown||v.onKeyDown(e),null==E.onKeyDown||E.onKeyDown(e),function(e){if(!se.includes(e.key))return;let t=h;const n=X(b,c),u=Y(b,c),s=i?_:B,a=i?B:_;if(R){const a=d||Array.from({length:b.current.length},(()=>({width:1,height:1}))),f=Z(a,l,m),v=f.findIndex((e=>null!=e&&!J(b.current,e,c))),p=f.reduce(((e,t,n)=>null==t||J(b.current,t,c)?e:n),-1),g=f[G({current:f.map((e=>e?b.current[e]:null))},{event:e,orientation:r,loop:o,rtl:i,cols:l,disabledIndices:Q([...c||b.current.map(((e,t)=>J(b.current,t)?t:void 0)),void 0],f),minIndex:v,maxIndex:p,prevIndex:$(h>u?n:h,a,f,l,e.key===W?"bl":e.key===s?"tr":"tl")})];null!=g&&(t=g)}const f={horizontal:[s],vertical:[W],both:[s,W]}[r],v={horizontal:[a],vertical:[q],both:[a,q]}[r],p=R?se:{horizontal:le,vertical:ce,both:se}[r];var g;t===h&&[...f,...v].includes(e.key)&&(t=o&&t===u&&f.includes(e.key)?n:o&&t===n&&v.includes(e.key)?u:V(b,{startingIndex:t,decrement:v.includes(e.key),disabledIndices:c})),t===h||z(b,t)||(e.stopPropagation(),p.includes(e.key)&&e.preventDefault(),y(t),null==(g=b.current[t])||g.focus())}(e)}};return u.createElement(ie.Provider,{value:w},u.createElement(re,{elementsRef:b},ue(n,x)))})),fe=u.forwardRef((function(e,t){const{render:n,...r}=e,o=n&&"function"!=typeof n?n.props:{},{activeIndex:i,onNavigate:c}=u.useContext(ie),{ref:s,index:a}=oe(),f=l([s,t,o.ref]),d=i===a;return ue(n,{...r,...o,ref:f,tabIndex:d?0:-1,"data-active":d?"":void 0,onFocus(e){null==r.onFocus||r.onFocus(e),null==o.onFocus||o.onFocus(e),c(a)}})}));function de(){return de=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},de.apply(null,arguments)}let me=!1,ve=0;const pe=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+ve++;const ge=c.useId||function(){const[e,t]=u.useState((()=>me?pe():void 0));return ee((()=>{null==e&&t(pe())}),[]),u.useEffect((()=>{me=!0}),[]),e},he=u.forwardRef((function(e,t){const{context:{placement:n,elements:{floating:r},middlewareData:{arrow:o,shift:i}},width:l=14,height:c=7,tipRadius:s=0,strokeWidth:a=0,staticOffset:f,stroke:d,d:m,style:{transform:v,...p}={},...g}=e,h=ge(),[y,E]=u.useState(!1);if(ee((()=>{if(!r)return;"rtl"===b(r).direction&&E(!0)}),[r]),!r)return null;const[w,R]=n.split("-"),x="top"===w||"bottom"===w;let I=f;(x&&null!=i&&i.x||!x&&null!=i&&i.y)&&(I=null);const k=2*a,C=k/2,M=l/2*(s/-8+1),O=c/2*s/4,S=!!m,P=I&&"end"===R?"bottom":"top";let T=I&&"end"===R?"right":"left";I&&y&&(T="end"===R?"left":"right");const L=null!=(null==o?void 0:o.x)?I||o.x:"",A=null!=(null==o?void 0:o.y)?I||o.y:"",D=m||"M0,0 H"+l+" L"+(l-M)+","+(c-O)+" Q"+l/2+","+c+" "+M+","+(c-O)+" Z",N={top:S?"rotate(180deg)":"",left:S?"rotate(90deg)":"rotate(-90deg)",bottom:S?"":"rotate(180deg)",right:S?"rotate(-90deg)":"rotate(90deg)"}[w];return u.createElement("svg",de({},g,{"aria-hidden":!0,ref:t,width:S?l:l+k,height:l,viewBox:"0 0 "+l+" "+(c>l?c:l),style:{position:"absolute",pointerEvents:"none",[T]:L,[P]:A,[w]:x||S?"100%":"calc(100% - "+k/2+"px)",transform:[N,v].filter((e=>!!e)).join(" "),...p}}),k>0&&u.createElement("path",{clipPath:"url(#"+h+")",fill:"none",stroke:d,strokeWidth:k+(m?0:1),d:D}),u.createElement("path",{stroke:k&&!m?g.fill:"none",d:D}),u.createElement("clipPath",{id:h},u.createElement("rect",{x:-C,y:C*(S?-1:1),width:l+k,height:l})))}));function ye(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.has(t)||e.set(t,new Set),e.get(t).add(n)},off(t,n){var r;null==(r=e.get(t))||r.delete(n)}}}const be=u.createContext(null),Ee=u.createContext(null),we=()=>{var e;return(null==(e=u.useContext(be))?void 0:e.id)||null},Re=()=>u.useContext(Ee);function xe(e){return"data-floating-ui-"+e}function Ie(e){-1!==e.current&&(clearTimeout(e.current),e.current=-1)}function ke(e){const n=t.useRef(e);return ee((()=>{n.current=e})),n}const Ce=xe("safe-polygon");function Me(e,t,n){if(n&&!S(n))return 0;if("number"==typeof e)return e;if("function"==typeof e){const n=e();return"number"==typeof n?n:null==n?void 0:n[t]}return null==e?void 0:e[t]}function Oe(e){return"function"==typeof e?e():e}const Se=()=>{},Pe=u.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:Se,setState:Se,isInstantPhase:!1}),Te=()=>u.useContext(Pe);const Le=u.createContext({hasProvider:!1,timeoutMs:0,delayRef:{current:0},initialDelayRef:{current:0},timeoutIdRef:{current:-1},currentIdRef:{current:null},currentContextRef:{current:null}});
/*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  */
var Ae=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),De="undefined"==typeof Element,Ne=De?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Fe=!De&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},je=function e(t,n){var r;void 0===n&&(n=!0);var o=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert");return""===o||"true"===o||n&&t&&e(t.parentNode)},Ke=function e(t,n,r){for(var o=[],u=Array.from(t);u.length;){var i=u.shift();if(!je(i,!1))if("SLOT"===i.tagName){var l=i.assignedElements(),c=e(l.length?l:i.children,!0,r);r.flatten?o.push.apply(o,c):o.push({scopeParent:i,candidates:c})}else{Ne.call(i,Ae)&&r.filter(i)&&(n||!t.includes(i))&&o.push(i);var s=i.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(i),a=!je(s,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(i));if(s&&a){var f=e(!0===s?i.children:s.children,!0,r);r.flatten?o.push.apply(o,f):o.push({scopeParent:i,candidates:f})}else u.unshift.apply(u,i.children)}}return o},He=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},qe=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!He(e)?0:e.tabIndex},We=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},_e=function(e){return"INPUT"===e.tagName},Be=function(e){return function(e){return _e(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||Fe(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!o||o===e}(e)},Ue=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},ze=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=Ne.call(e,"details>summary:first-of-type")?e.parentElement:e;if(Ne.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return Ue(e)}else{if("function"==typeof r){for(var u=e;e;){var i=e.parentElement,l=Fe(e);if(i&&!i.shadowRoot&&!0===r(i))return Ue(e);e=e.assignedSlot?e.assignedSlot:i||l===e.ownerDocument?i:l.host}e=u}if(function(e){var t,n,r,o,u=e&&Fe(e),i=null===(t=u)||void 0===t?void 0:t.host,l=!1;if(u&&u!==e)for(l=!!(null!==(n=i)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(i)||null!=e&&null!==(o=e.ownerDocument)&&void 0!==o&&o.contains(e));!l&&i;){var c,s,a;l=!(null===(s=i=null===(c=u=Fe(i))||void 0===c?void 0:c.host)||void 0===s||null===(a=s.ownerDocument)||void 0===a||!a.contains(i))}return l}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},Xe=function(e,t){return!(t.disabled||je(t)||function(e){return _e(e)&&"hidden"===e.type}(t)||ze(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!Ne.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},Ye=function(e,t){return!(Be(t)||qe(t)<0||!Xe(e,t))},Ve=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},Ge=function e(t){var n=[],r=[];return t.forEach((function(t,o){var u=!!t.scopeParent,i=u?t.scopeParent:t,l=function(e,t){var n=qe(e);return n<0&&t&&!He(e)?0:n}(i,u),c=u?e(t.candidates):i;0===l?u?n.push.apply(n,c):n.push(i):r.push({documentOrder:o,tabIndex:l,item:t,isScope:u,content:c})})),r.sort(We).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},Ze=function(e,t){var n;return n=(t=t||{}).getShadowRoot?Ke([e],t.includeContainer,{filter:Ye.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:Ve}):function(e,t,n){if(je(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(Ae));return t&&Ne.call(e,Ae)&&r.unshift(e),r.filter(n)}(e,t.includeContainer,Ye.bind(null,t)),Ge(n)};let $e=0;function Qe(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame($e);const u=()=>null==e?void 0:e.focus({preventScroll:n});o?u():$e=requestAnimationFrame(u)}function Je(e,t){var n;let r=[],o=null==(n=e.find((e=>e.id===t)))?void 0:n.parentId;for(;o;){const t=e.find((e=>e.id===o));o=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}function et(e,t){let n=e.filter((e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)})),r=n;for(;r.length;)r=e.filter((e=>{var t;return null==(t=r)?void 0:t.some((t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)}))})),n=n.concat(r);return n}let tt=new WeakMap,nt=new WeakSet,rt={},ot=0;const ut=e=>e&&(e.host||ut(e.parentNode));function it(e,t,n,r){const o="data-floating-ui-inert",u=r?"inert":n?"aria-hidden":null,i=(l=t,e.map((e=>{if(l.contains(e))return e;const t=ut(e);return l.contains(t)?t:null})).filter((e=>null!=e)));var l;const c=new Set,s=new Set(i),a=[];rt[o]||(rt[o]=new WeakMap);const f=rt[o];return i.forEach((function e(t){if(!t||c.has(t))return;c.add(t),t.parentNode&&e(t.parentNode)})),function e(t){if(!t||s.has(t))return;[].forEach.call(t.children,(t=>{if("script"!==d(t))if(c.has(t))e(t);else{const e=u?t.getAttribute(u):null,n=null!==e&&"false"!==e,r=tt.get(t)||0,i=u?r+1:r,l=(f.get(t)||0)+1;tt.set(t,i),f.set(t,l),a.push(t),1===i&&n&&nt.add(t),1===l&&t.setAttribute(o,""),!n&&u&&t.setAttribute(u,"inert"===u?"":"true")}}))}(t),c.clear(),ot++,()=>{a.forEach((e=>{const t=tt.get(e)||0,n=u?t-1:t,r=(f.get(e)||0)-1;tt.set(e,n),f.set(e,r),n||(!nt.has(e)&&u&&e.removeAttribute(u),nt.delete(e)),r||e.removeAttribute(o)})),ot--,ot||(tt=new WeakMap,tt=new WeakMap,nt=new WeakSet,rt={})}}function lt(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);const r=P(e[0]).body;return it(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}const ct=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function st(e,t){const n=Ze(e,ct());"prev"===t&&n.reverse();const r=n.indexOf(w(P(e)));return n.slice(r+1)[0]}function at(e){return st(P(e).body,"next")||e}function ft(e){return st(P(e).body,"prev")||e}function dt(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!R(n,r)}function mt(e){Ze(e,ct()).forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))}function vt(e){e.querySelectorAll("[data-tabindex]").forEach((e=>{const t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))}const pt={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},gt=u.forwardRef((function(e,t){const[n,r]=u.useState();ee((()=>{M()&&r("button")}),[]);const o={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[xe("focus-guard")]:"",style:pt};return u.createElement("span",de({},e,o))})),ht=u.createContext(null),yt=xe("portal");function bt(e){void 0===e&&(e={});const{id:t,root:n}=e,r=ge(),o=Et(),[i,l]=u.useState(null),c=u.useRef(null);return ee((()=>()=>{null==i||i.remove(),queueMicrotask((()=>{c.current=null}))}),[i]),ee((()=>{if(!r)return;if(c.current)return;const e=t?document.getElementById(t):null;if(!e)return;const n=document.createElement("div");n.id=r,n.setAttribute(yt,""),e.appendChild(n),c.current=n,l(n)}),[t,r]),ee((()=>{if(null===n)return;if(!r)return;if(c.current)return;let e=n||(null==o?void 0:o.portalNode);e&&!p(e)&&(e=e.current),e=e||document.body;let u=null;t&&(u=document.createElement("div"),u.id=t,e.appendChild(u));const i=document.createElement("div");i.id=r,i.setAttribute(yt,""),e=u||e,e.appendChild(i),c.current=i,l(i)}),[t,n,r,o]),i}const Et=()=>u.useContext(ht),wt="data-floating-ui-focusable";function Rt(e){return e?e.hasAttribute(wt)?e:e.querySelector("["+wt+"]")||e:null}function xt(e){return u.useMemo((()=>t=>{e.forEach((e=>{e&&(e.current=t)}))}),e)}let It=[];function kt(){return It.slice().reverse().find((e=>e.isConnected))}function Ct(e){const t=ct();return function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==Ne.call(e,Ae)&&Ye(t,e)}(e,t)?e:Ze(e,t)[0]||e}const Mt=u.forwardRef((function(e,t){return u.createElement("button",de({},e,{type:"button",ref:t,tabIndex:-1,style:pt}))}));let Ot=0;let St=()=>{};const Pt=u.forwardRef((function(e,t){const{lockScroll:n=!1,...r}=e;return ee((()=>{if(n)return Ot++,1===Ot&&(St=function(){const e=/iP(hone|ad|od)|iOS/.test(x()),t=document.body.style,n=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",r=window.innerWidth-document.documentElement.clientWidth,o=t.left?parseFloat(t.left):window.scrollX,u=t.top?parseFloat(t.top):window.scrollY;if(t.overflow="hidden",r&&(t[n]=r+"px"),e){var i,l;const e=(null==(i=window.visualViewport)?void 0:i.offsetLeft)||0,n=(null==(l=window.visualViewport)?void 0:l.offsetTop)||0;Object.assign(t,{position:"fixed",top:-(u-Math.floor(n))+"px",left:-(o-Math.floor(e))+"px",right:"0"})}return()=>{Object.assign(t,{overflow:"",[n]:""}),e&&(Object.assign(t,{position:"",top:"",left:"",right:""}),window.scrollTo(o,u))}}()),()=>{Ot--,0===Ot&&St()}}),[n]),u.createElement("div",de({ref:t},r,{style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...r.style}}))}));function Tt(e){return g(e.target)&&"BUTTON"===e.target.tagName}function Lt(e){return A(e)}function At(e){return null!=e&&null!=e.clientX}const Dt={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},Nt={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},Ft=e=>{var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}};function jt(e){const{open:t=!1,onOpenChange:n,elements:r}=e,o=ge(),i=u.useRef({}),[l]=u.useState((()=>ye())),c=null!=we(),[s,f]=u.useState(r.reference),d=a(((e,t,r)=>{i.current.openEvent=e?t:void 0,l.emit("openchange",{open:e,event:t,reason:r,nested:c}),null==n||n(e,t,r)})),m=u.useMemo((()=>({setPositionReference:f})),[]),v=u.useMemo((()=>({reference:s||r.reference||null,floating:r.floating||null,domReference:r.reference})),[s,r.reference,r.floating]);return u.useMemo((()=>({dataRef:i,open:t,onOpenChange:d,elements:v,events:l,floatingId:o,refs:m})),[t,d,v,l,o,m])}function Kt(){return function(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}().includes("jsdom/")}function Ht(){return x().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints&&M()}const qt="active",Wt="selected";function _t(e,t,n){const r=new Map,o="item"===n;let u=e;if(o&&e){const{[qt]:t,[Wt]:n,...r}=e;u=r}return{..."floating"===n&&{tabIndex:-1,[wt]:""},...u,...t.map((t=>{const r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r})).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,u]=t;var i;o&&[qt,Wt].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof u&&(null==(i=r.get(n))||i.push(u),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),u=0;u<t;u++)o[u]=arguments[u];return null==(e=r.get(n))?void 0:e.map((e=>e(...o))).find((e=>void 0!==e))})):e[n]=u)})),e):e),{})}}function Bt(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function Ut(e,t){return Bt(t,e===q||e===W,e===_||e===B)}function zt(e,t,n){return Bt(t,e===W,n?e===_:e===B)||"Enter"===e||" "===e||""===e}function Xt(e,t,n){return Bt(t,n?e===_:e===B,e===W)}function Yt(e,t,n,r){return"both"===t||"horizontal"===t&&r&&r>1?"Escape"===e:Bt(t,n?e===B:e===_,e===q)}const Vt=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);const Gt=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,((e,t)=>(t?"-":"")+e.toLowerCase()));function Zt(e,t){return"function"==typeof e?e(t):e}function $t(e,t){void 0===t&&(t={});const{open:n,elements:{floating:r}}=e,{duration:o=250}=t,l=("number"==typeof o?o:o.close)||0,[c,s]=u.useState("unmounted"),a=function(e,t){const[n,r]=u.useState(e);return e&&!n&&r(!0),u.useEffect((()=>{if(!e&&n){const e=setTimeout((()=>r(!1)),t);return()=>clearTimeout(e)}}),[e,n,t]),n}(n,l);return a||"close"!==c||s("unmounted"),ee((()=>{if(r){if(n){s("initial");const e=requestAnimationFrame((()=>{i.flushSync((()=>{s("open")}))}));return()=>{cancelAnimationFrame(e)}}s("close")}}),[n,r]),{isMounted:a,status:c}}function Qt(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}function Jt(e,t){const[n,r]=e;let o=!1;const u=t.length;for(let e=0,i=u-1;e<u;i=e++){const[u,l]=t[e]||[0,0],[c,s]=t[i]||[0,0];l>=r!=s>=r&&n<=(c-u)*(r-l)/(s-l)+u&&(o=!o)}return o}Object.defineProperty(e,"arrow",{enumerable:!0,get:function(){return r.arrow}}),Object.defineProperty(e,"autoPlacement",{enumerable:!0,get:function(){return r.autoPlacement}}),Object.defineProperty(e,"autoUpdate",{enumerable:!0,get:function(){return r.autoUpdate}}),Object.defineProperty(e,"computePosition",{enumerable:!0,get:function(){return r.computePosition}}),Object.defineProperty(e,"detectOverflow",{enumerable:!0,get:function(){return r.detectOverflow}}),Object.defineProperty(e,"flip",{enumerable:!0,get:function(){return r.flip}}),Object.defineProperty(e,"getOverflowAncestors",{enumerable:!0,get:function(){return r.getOverflowAncestors}}),Object.defineProperty(e,"hide",{enumerable:!0,get:function(){return r.hide}}),Object.defineProperty(e,"inline",{enumerable:!0,get:function(){return r.inline}}),Object.defineProperty(e,"limitShift",{enumerable:!0,get:function(){return r.limitShift}}),Object.defineProperty(e,"offset",{enumerable:!0,get:function(){return r.offset}}),Object.defineProperty(e,"platform",{enumerable:!0,get:function(){return r.platform}}),Object.defineProperty(e,"shift",{enumerable:!0,get:function(){return r.shift}}),Object.defineProperty(e,"size",{enumerable:!0,get:function(){return r.size}}),e.Composite=ae,e.CompositeItem=fe,e.FloatingArrow=he,e.FloatingDelayGroup=function(e){const{children:t,delay:n,timeoutMs:r=0}=e,[o,i]=u.useReducer(((e,t)=>({...e,...t})),{delay:n,timeoutMs:r,initialDelay:n,currentId:null,isInstantPhase:!1}),l=u.useRef(null),c=u.useCallback((e=>{i({currentId:e})}),[]);return ee((()=>{o.currentId?null===l.current?l.current=o.currentId:o.isInstantPhase||i({isInstantPhase:!0}):(o.isInstantPhase&&i({isInstantPhase:!1}),l.current=null)}),[o.currentId,o.isInstantPhase]),u.createElement(Pe.Provider,{value:u.useMemo((()=>({...o,setState:i,setCurrentId:c})),[o,c])},t)},e.FloatingFocusManager=function(e){const{context:t,children:n,disabled:r=!1,order:o=["content"],guards:i=!0,initialFocus:l=0,returnFocus:c=!0,restoreFocus:s=!1,modal:f=!0,visuallyHiddenDismiss:m=!1,closeOnFocusOut:v=!0,outsideElementsInert:p=!1,getInsideElements:h=()=>[]}=e,{open:y,onOpenChange:b,events:E,dataRef:x,elements:{domReference:I,floating:M}}=t,O=a((()=>{var e;return null==(e=x.current.floatingContext)?void 0:e.nodeId})),S=a(h),T="number"==typeof l&&l<0,A=N(I)&&T,F="undefined"!=typeof HTMLElement&&"inert"in HTMLElement.prototype,j=!F||i,K=!j||F&&p,H=ke(o),q=ke(l),W=ke(c),_=Re(),B=Et(),U=u.useRef(null),z=u.useRef(null),X=u.useRef(!1),Y=u.useRef(!1),V=u.useRef(-1),G=null!=B,Z=Rt(M),$=a((function(e){return void 0===e&&(e=Z),e?Ze(e,ct()):[]})),Q=a((e=>{const t=$(e);return H.current.map((e=>I&&"reference"===e?I:Z&&"floating"===e?Z:t)).filter(Boolean).flat()}));u.useEffect((()=>{if(r)return;if(!f)return;function e(e){if("Tab"===e.key){R(Z,w(P(Z)))&&0===$().length&&!A&&D(e);const t=Q(),n=L(e);"reference"===H.current[0]&&n===I&&(D(e),e.shiftKey?Qe(t[t.length-1]):Qe(t[1])),"floating"===H.current[1]&&n===Z&&e.shiftKey&&(D(e),Qe(t[0]))}}const t=P(Z);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}}),[r,I,Z,f,H,A,$,Q]),u.useEffect((()=>{if(!r&&M)return M.addEventListener("focusin",e),()=>{M.removeEventListener("focusin",e)};function e(e){const t=L(e),n=$().indexOf(t);-1!==n&&(V.current=n)}}),[r,M,$]),u.useEffect((()=>{if(!r&&v)return M&&g(I)?(I.addEventListener("focusout",t),I.addEventListener("pointerdown",e),M.addEventListener("focusout",t),()=>{I.removeEventListener("focusout",t),I.removeEventListener("pointerdown",e),M.removeEventListener("focusout",t)}):void 0;function e(){Y.current=!0,setTimeout((()=>{Y.current=!1}))}function t(e){const t=e.relatedTarget;queueMicrotask((()=>{const n=O(),r=!(R(I,t)||R(M,t)||R(t,M)||R(null==B?void 0:B.portalNode,t)||null!=t&&t.hasAttribute(xe("focus-guard"))||_&&(et(_.nodesRef.current,n).find((e=>{var n,r;return R(null==(n=e.context)?void 0:n.elements.floating,t)||R(null==(r=e.context)?void 0:r.elements.domReference,t)}))||Je(_.nodesRef.current,n).find((e=>{var n,r,o;return[null==(n=e.context)?void 0:n.elements.floating,Rt(null==(r=e.context)?void 0:r.elements.floating)].includes(t)||(null==(o=e.context)?void 0:o.elements.domReference)===t}))));if(s&&r&&w(P(Z))===P(Z).body){g(Z)&&Z.focus();const e=V.current,t=$(),n=t[e]||t[t.length-1]||Z;g(n)&&n.focus()}!A&&f||!t||!r||Y.current||t===kt()||(X.current=!0,b(!1,e,"focus-out"))}))}}),[r,I,M,Z,f,_,B,b,v,s,$,A,O]);const J=u.useRef(null),te=u.useRef(null),ne=xt([J,null==B?void 0:B.beforeInsideRef]),re=xt([te,null==B?void 0:B.afterInsideRef]);function oe(e){return!r&&m&&f?u.createElement(Mt,{ref:"start"===e?U:z,onClick:e=>b(!1,e.nativeEvent)},"string"==typeof m?m:"Dismiss"):null}u.useEffect((()=>{var e,t;if(r)return;if(!M)return;const n=Array.from((null==B||null==(e=B.portalNode)?void 0:e.querySelectorAll("["+xe("portal")+"]"))||[]),o=_?Je(_.nodesRef.current,O()):[],u=_&&!f?o.map((e=>{var t;return null==(t=e.context)?void 0:t.elements.floating})):[],i=null==(t=o.find((e=>{var t;return N((null==(t=e.context)?void 0:t.elements.domReference)||null)})))||null==(t=t.context)?void 0:t.elements.domReference,l=[M,i,...n,...u,...S(),U.current,z.current,J.current,te.current,null==B?void 0:B.beforeOutsideRef.current,null==B?void 0:B.afterOutsideRef.current,H.current.includes("reference")||A?I:null].filter((e=>null!=e)),c=f||A?lt(l,!K,K):lt(l);return()=>{c()}}),[r,I,M,f,H,B,A,j,K,_,O,S]),ee((()=>{if(r||!g(Z))return;const e=w(P(Z));queueMicrotask((()=>{const t=Q(Z),n=q.current,r=("number"==typeof n?t[n]:n.current)||Z,o=R(Z,e);T||o||!y||Qe(r,{preventScroll:r===Z})}))}),[r,y,Z,T,Q,q]),ee((()=>{if(r||!Z)return;let e=!1,t=!1;const n=P(Z),o=w(n);let u=x.current.openEvent;var i;function l(n){let{open:r,reason:o,event:i,nested:l}=n;if(r&&(u=i),"escape-key"===o&&(t=!0),["hover","safe-polygon"].includes(o)&&"mouseleave"===i.type&&(X.current=!0),"outside-press"===o)if(l)X.current=!1,e=!0;else if(k(i)||C(i))X.current=!1;else{let t=!1;document.createElement("div").focus({get preventScroll(){return t=!0,!1}}),t?(X.current=!1,e=!0):X.current=!0}}i=o,It=It.filter((e=>e.isConnected)),i&&"body"!==d(i)&&(It.push(i),It.length>20&&(It=It.slice(-20))),E.on("openchange",l);const c=n.createElement("span");return c.setAttribute("tabindex","-1"),c.setAttribute("aria-hidden","true"),Object.assign(c.style,pt),G&&I&&I.insertAdjacentElement("afterend",c),()=>{E.off("openchange",l);const r=w(n),o=R(M,r)||_&&et(_.nodesRef.current,O()).some((e=>{var t;return R(null==(t=e.context)?void 0:t.elements.floating,r)}));(o||u&&["click","mousedown"].includes(u.type))&&(t=!0);const i="boolean"==typeof W.current?t&&I?I:kt()||c:W.current.current||c;queueMicrotask((()=>{const t=Ct(i);W.current&&!X.current&&g(t)&&(t===r||r===n.body||o)&&t.focus({preventScroll:e}),c.remove()}))}}),[r,M,Z,W,x,E,_,G,I,O]),u.useEffect((()=>{queueMicrotask((()=>{X.current=!1}))}),[r]),ee((()=>{if(!r&&B)return B.setFocusManagerState({modal:f,closeOnFocusOut:v,open:y,onOpenChange:b,domReference:I}),()=>{B.setFocusManagerState(null)}}),[r,B,f,y,b,v,I]),ee((()=>{var e;if(r)return;if(!Z)return;if("function"!=typeof MutationObserver)return;if(!(H.current.includes("floating")||null!=(e=Z.getAttribute("role"))&&e.includes("dialog")))return;const t=()=>{const e=Z.getAttribute("tabindex"),t=$(),n=w(P(M)),r=t.indexOf(n);-1!==r&&(V.current=r),H.current.includes("floating")||0===t.length?"0"!==e&&Z.setAttribute("tabindex","0"):"-1"!==e&&Z.setAttribute("tabindex","-1")};t();const n=new MutationObserver(t);return n.observe(Z,{childList:!0,subtree:!0,attributes:!0}),()=>{n.disconnect()}}),[r,M,Z,I,H,$,T]);const ue=!r&&j&&(!f||!A)&&(G||f);return u.createElement(u.Fragment,null,ue&&u.createElement(gt,{"data-type":"inside",ref:ne,onFocus:e=>{if(f){const e=Q();Qe("reference"===o[0]?e[0]:e[e.length-1])}else if(null!=B&&B.preserveTabOrder&&B.portalNode)if(X.current=!1,dt(e,B.portalNode)){const e=at(I);null==e||e.focus()}else{var t;null==(t=B.beforeOutsideRef.current)||t.focus()}}}),!A&&oe("start"),n,oe("end"),ue&&u.createElement(gt,{"data-type":"inside",ref:re,onFocus:e=>{if(f)Qe(Q()[0]);else if(null!=B&&B.preserveTabOrder&&B.portalNode)if(v&&(X.current=!0),dt(e,B.portalNode)){const e=ft(I);null==e||e.focus()}else{var t;null==(t=B.afterOutsideRef.current)||t.focus()}}}))},e.FloatingList=re,e.FloatingNode=function(e){const{children:t,id:n}=e,r=we();return u.createElement(be.Provider,{value:u.useMemo((()=>({id:n,parentId:r})),[n,r])},t)},e.FloatingOverlay=Pt,e.FloatingPortal=function(e){const{children:t,id:n,root:r,preserveTabOrder:o=!0}=e,l=bt({id:n,root:r}),[c,s]=u.useState(null),a=u.useRef(null),f=u.useRef(null),d=u.useRef(null),m=u.useRef(null),v=null==c?void 0:c.modal,p=null==c?void 0:c.open,g=!!c&&!c.modal&&c.open&&o&&!(!r&&!l);return u.useEffect((()=>{if(l&&o&&!v)return l.addEventListener("focusin",e,!0),l.addEventListener("focusout",e,!0),()=>{l.removeEventListener("focusin",e,!0),l.removeEventListener("focusout",e,!0)};function e(e){if(l&&dt(e)){("focusin"===e.type?vt:mt)(l)}}}),[l,o,v]),u.useEffect((()=>{l&&(p||vt(l))}),[p,l]),u.createElement(ht.Provider,{value:u.useMemo((()=>({preserveTabOrder:o,beforeOutsideRef:a,afterOutsideRef:f,beforeInsideRef:d,afterInsideRef:m,portalNode:l,setFocusManagerState:s})),[o,l])},g&&l&&u.createElement(gt,{"data-type":"outside",ref:a,onFocus:e=>{if(dt(e,l)){var t;null==(t=d.current)||t.focus()}else{const e=ft(c?c.domReference:null);null==e||e.focus()}}}),g&&l&&u.createElement("span",{"aria-owns":l.id,style:pt}),l&&i.createPortal(t,l),g&&l&&u.createElement(gt,{"data-type":"outside",ref:f,onFocus:e=>{if(dt(e,l)){var t;null==(t=m.current)||t.focus()}else{const t=at(c?c.domReference:null);null==t||t.focus(),(null==c?void 0:c.closeOnFocusOut)&&(null==c||c.onOpenChange(!1,e.nativeEvent,"focus-out"))}}}))},e.FloatingTree=function(e){const{children:t}=e,n=u.useRef([]),r=u.useCallback((e=>{n.current=[...n.current,e]}),[]),o=u.useCallback((e=>{n.current=n.current.filter((t=>t!==e))}),[]),[i]=u.useState((()=>ye()));return u.createElement(Ee.Provider,{value:u.useMemo((()=>({nodesRef:n,addNode:r,removeNode:o,events:i})),[r,o,i])},t)},e.NextFloatingDelayGroup=function(e){const{children:t,delay:n,timeoutMs:r=0}=e,o=u.useRef(n),i=u.useRef(n),l=u.useRef(null),c=u.useRef(null),s=u.useRef(-1);return u.createElement(Le.Provider,{value:u.useMemo((()=>({hasProvider:!0,delayRef:o,initialDelayRef:i,currentIdRef:l,timeoutMs:r,currentContextRef:c,timeoutIdRef:s})),[r])},t)},e.inner=e=>({name:"inner",options:e,async fn(t){const{listRef:n,overflowRef:o,onFallbackChange:u,offset:l=0,index:c=0,minItemsVisible:s=4,referenceOverflowThreshold:a=0,scrollRef:f,...d}=(v=t,"function"==typeof(m=e)?m(v):m);var m,v;const{rects:p,elements:{floating:g}}=t,h=n.current[c],y=(null==f?void 0:f.current)||g,b=g.clientTop||y.clientTop,E=0!==g.clientTop,w=0!==y.clientTop,R=g===y;if(!h)return{};const x={...t,...await r.offset(-h.offsetTop-g.clientTop-p.reference.height/2-h.offsetHeight/2-l).fn(t)},I=await r.detectOverflow(Qt(x,y.scrollHeight+b+g.clientTop),d),k=await r.detectOverflow(x,{...d,elementContext:"reference"}),C=j(0,I.top),M=x.y+C,O=(y.scrollHeight>y.clientHeight?e=>e:K)(j(0,y.scrollHeight+(E&&R||w?2*b:0)-C-j(0,I.bottom)));if(y.style.maxHeight=O+"px",y.scrollTop=C,u){const e=y.offsetHeight<h.offsetHeight*F(s,n.current.length)-1||k.top>=-a||k.bottom>=-a;i.flushSync((()=>u(e)))}return o&&(o.current=await r.detectOverflow(Qt({...x,y:M},y.offsetHeight+b+g.clientTop),d)),{y:M}}}),e.safePolygon=function(e){void 0===e&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let o,u=!1,i=null,l=null,c=performance.now();const s=e=>{let{x:n,y:s,placement:a,elements:f,onClose:d,nodeId:m,tree:v}=e;return function(e){function g(){clearTimeout(o),d()}if(clearTimeout(o),!f.domReference||!f.floating||null==a||null==n||null==s)return;const{clientX:h,clientY:y}=e,b=[h,y],E=L(e),w="mouseleave"===e.type,x=R(f.floating,E),I=R(f.domReference,E),k=f.domReference.getBoundingClientRect(),C=f.floating.getBoundingClientRect(),M=a.split("-")[0],O=n>C.right-C.width/2,S=s>C.bottom-C.height/2,P=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(b,k),T=C.width>k.width,A=C.height>k.height,D=(T?k:C).left,N=(T?k:C).right,F=(A?k:C).top,j=(A?k:C).bottom;if(x&&(u=!0,!w))return;if(I&&(u=!1),I&&!w)return void(u=!0);if(w&&p(e.relatedTarget)&&R(f.floating,e.relatedTarget))return;if(v&&et(v.nodesRef.current,m).some((e=>{let{context:t}=e;return null==t?void 0:t.open})))return;if("top"===M&&s>=k.bottom-1||"bottom"===M&&s<=k.top+1||"left"===M&&n>=k.right-1||"right"===M&&n<=k.left+1)return g();let K=[];switch(M){case"top":K=[[D,k.top+1],[D,C.bottom-1],[N,C.bottom-1],[N,k.top+1]];break;case"bottom":K=[[D,C.top+1],[D,k.bottom-1],[N,k.bottom-1],[N,C.top+1]];break;case"left":K=[[C.right-1,j],[C.right-1,F],[k.left+1,F],[k.left+1,j]];break;case"right":K=[[k.right-1,j],[k.right-1,F],[C.left+1,F],[C.left+1,j]]}if(!Jt([h,y],K)){if(u&&!P)return g();if(!w&&r){const t=function(e,t){const n=performance.now(),r=n-c;if(null===i||null===l||0===r)return i=e,l=t,c=n,null;const o=e-i,u=t-l,s=Math.sqrt(o*o+u*u);return i=e,l=t,c=n,s/r}(e.clientX,e.clientY);if(null!==t&&t<.1)return g()}Jt([h,y],function(e){let[n,r]=e;switch(M){case"top":return[[T?n+t/2:O?n+4*t:n-4*t,r+t+1],[T?n-t/2:O?n+4*t:n-4*t,r+t+1],...[[C.left,O||T?C.bottom-t:C.top],[C.right,O?T?C.bottom-t:C.top:C.bottom-t]]];case"bottom":return[[T?n+t/2:O?n+4*t:n-4*t,r-t],[T?n-t/2:O?n+4*t:n-4*t,r-t],...[[C.left,O||T?C.top+t:C.bottom],[C.right,O?T?C.top+t:C.bottom:C.top+t]]];case"left":{const e=[n+t+1,A?r+t/2:S?r+4*t:r-4*t],o=[n+t+1,A?r-t/2:S?r+4*t:r-4*t];return[...[[S||A?C.right-t:C.left,C.top],[S?A?C.right-t:C.left:C.right-t,C.bottom]],e,o]}case"right":return[[n-t,A?r+t/2:S?r+4*t:r-4*t],[n-t,A?r-t/2:S?r+4*t:r-4*t],...[[S||A?C.left+t:C.right,C.top],[S?A?C.left+t:C.right:C.left+t,C.bottom]]]}}([n,s]))?!u&&r&&(o=window.setTimeout(g,40)):g()}}};return s.__options={blockPointerEvents:n},s},e.useClick=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,elements:{domReference:i}}=e,{enabled:l=!0,event:c="click",toggle:s=!0,ignoreMouse:a=!1,keyboardHandlers:f=!0,stickIfOpen:d=!0}=t,m=u.useRef(),v=u.useRef(!1),p=u.useMemo((()=>({onPointerDown(e){m.current=e.pointerType},onMouseDown(e){const t=m.current;0===e.button&&"click"!==c&&(S(t,!0)&&a||(!n||!s||o.current.openEvent&&d&&"mousedown"!==o.current.openEvent.type?(e.preventDefault(),r(!0,e.nativeEvent,"click")):r(!1,e.nativeEvent,"click")))},onClick(e){const t=m.current;"mousedown"===c&&m.current?m.current=void 0:S(t,!0)&&a||(!n||!s||o.current.openEvent&&d&&"click"!==o.current.openEvent.type?r(!0,e.nativeEvent,"click"):r(!1,e.nativeEvent,"click"))},onKeyDown(e){m.current=void 0,e.defaultPrevented||!f||Tt(e)||(" "!==e.key||Lt(i)||(e.preventDefault(),v.current=!0),function(e){return g(e.target)&&"A"===e.target.tagName}(e)||"Enter"===e.key&&r(!n||!s,e.nativeEvent,"click"))},onKeyUp(e){e.defaultPrevented||!f||Tt(e)||Lt(i)||" "===e.key&&v.current&&(v.current=!1,r(!n||!s,e.nativeEvent,"click"))}})),[o,i,c,a,f,r,n,d,s]);return u.useMemo((()=>l?{reference:p}:{}),[l,p])},e.useClientPoint=function(e,t){void 0===t&&(t={});const{open:n,dataRef:r,elements:{floating:o,domReference:i},refs:l}=e,{enabled:c=!0,axis:s="both",x:f=null,y:d=null}=t,v=u.useRef(!1),p=u.useRef(null),[g,h]=u.useState(),[y,b]=u.useState([]),E=a(((e,t)=>{v.current||r.current.openEvent&&!At(r.current.openEvent)||l.setPositionReference(function(e,t){let n=null,r=null,o=!1;return{contextElement:e||void 0,getBoundingClientRect(){var u;const i=(null==e?void 0:e.getBoundingClientRect())||{width:0,height:0,x:0,y:0},l="x"===t.axis||"both"===t.axis,c="y"===t.axis||"both"===t.axis,s=["mouseenter","mousemove"].includes((null==(u=t.dataRef.current.openEvent)?void 0:u.type)||"")&&"touch"!==t.pointerType;let a=i.width,f=i.height,d=i.x,m=i.y;return null==n&&t.x&&l&&(n=i.x-t.x),null==r&&t.y&&c&&(r=i.y-t.y),d-=n||0,m-=r||0,a=0,f=0,!o||s?(a="y"===t.axis?i.width:0,f="x"===t.axis?i.height:0,d=l&&null!=t.x?t.x:d,m=c&&null!=t.y?t.y:m):o&&!s&&(f="x"===t.axis?i.height:f,a="y"===t.axis?i.width:a),o=!0,{width:a,height:f,x:d,y:m,top:m,right:d+a,bottom:m+f,left:d}}}}(i,{x:e,y:t,axis:s,dataRef:r,pointerType:g}))})),w=a((e=>{null==f&&null==d&&(n?p.current||b([]):E(e.clientX,e.clientY))})),x=S(g)?o:n,I=u.useCallback((()=>{if(!x||!c||null!=f||null!=d)return;const e=m(o);function t(n){const r=L(n);R(o,r)?(e.removeEventListener("mousemove",t),p.current=null):E(n.clientX,n.clientY)}if(!r.current.openEvent||At(r.current.openEvent)){e.addEventListener("mousemove",t);const n=()=>{e.removeEventListener("mousemove",t),p.current=null};return p.current=n,n}l.setPositionReference(i)}),[x,c,f,d,o,r,l,i,E]);u.useEffect((()=>I()),[I,y]),u.useEffect((()=>{c&&!o&&(v.current=!1)}),[c,o]),u.useEffect((()=>{!c&&n&&(v.current=!0)}),[c,n]),ee((()=>{!c||null==f&&null==d||(v.current=!1,E(f,d))}),[c,f,d,E]);const k=u.useMemo((()=>{function e(e){let{pointerType:t}=e;h(t)}return{onPointerDown:e,onPointerEnter:e,onMouseMove:w,onMouseEnter:w}}),[w]);return u.useMemo((()=>c?{reference:k}:{}),[c,k])},e.useDelayGroup=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,floatingId:o}=e,{id:u,enabled:i=!0}=t,l=null!=u?u:o,c=Te(),{currentId:s,setCurrentId:a,initialDelay:f,setState:d,timeoutMs:m}=c;return ee((()=>{i&&s&&(d({delay:{open:1,close:Me(f,"close")}}),s!==l&&r(!1))}),[i,l,r,d,s,f]),ee((()=>{function e(){r(!1),d({delay:f,currentId:null})}if(i&&s&&!n&&s===l){if(m){const t=window.setTimeout(e,m);return()=>{clearTimeout(t)}}e()}}),[i,n,d,s,l,r,f,m]),ee((()=>{i&&a!==Se&&n&&a(l)}),[i,n,a,l]),c},e.useDelayGroupContext=Te,e.useDismiss=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:o,elements:i,dataRef:l}=e,{enabled:c=!0,escapeKey:s=!0,outsidePress:f=!0,outsidePressEvent:d="pointerdown",referencePress:m=!1,referencePressEvent:v="pointerdown",ancestorScroll:h=!1,bubbles:w,capture:x}=t,I=Re(),k=a("function"==typeof f?f:()=>!1),C="function"==typeof f?k:f,M=u.useRef(!1),O=u.useRef(!1),{escapeKey:S,outsidePress:A}=Ft(w),{escapeKey:D,outsidePress:N}=Ft(x),F=u.useRef(!1),j=a((e=>{var t;if(!n||!c||!s||"Escape"!==e.key)return;if(F.current)return;const r=null==(t=l.current.floatingContext)?void 0:t.nodeId,u=I?et(I.nodesRef.current,r):[];if(!S&&(e.stopPropagation(),u.length>0)){let e=!0;if(u.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__escapeKeyBubbles||(e=!1)})),!e)return}o(!1,function(e){return"nativeEvent"in e}(e)?e.nativeEvent:e,"escape-key")})),K=a((e=>{var t;const n=()=>{var t;j(e),null==(t=L(e))||t.removeEventListener("keydown",n)};null==(t=L(e))||t.addEventListener("keydown",n)})),H=a((e=>{var t;const n=M.current;M.current=!1;const r=O.current;if(O.current=!1,"click"===d&&r)return;if(n)return;if("function"==typeof C&&!C(e))return;const u=L(e),c="["+xe("inert")+"]",s=P(i.floating).querySelectorAll(c);let a=p(u)?u:null;for(;a&&!y(a);){const e=E(a);if(y(e)||!p(e))break;a=e}if(s.length&&p(u)&&!u.matches("html,body")&&!R(u,i.floating)&&Array.from(s).every((e=>!R(a,e))))return;if(g(u)&&_){const t=y(u),n=b(u),r=/auto|scroll/,o=t||r.test(n.overflowX),i=t||r.test(n.overflowY),l=o&&u.clientWidth>0&&u.scrollWidth>u.clientWidth,c=i&&u.clientHeight>0&&u.scrollHeight>u.clientHeight,s="rtl"===n.direction,a=c&&(s?e.offsetX<=u.offsetWidth-u.clientWidth:e.offsetX>u.clientWidth),f=l&&e.offsetY>u.clientHeight;if(a||f)return}const f=null==(t=l.current.floatingContext)?void 0:t.nodeId,m=I&&et(I.nodesRef.current,f).some((t=>{var n;return T(e,null==(n=t.context)?void 0:n.elements.floating)}));if(T(e,i.floating)||T(e,i.domReference)||m)return;const v=I?et(I.nodesRef.current,f):[];if(v.length>0){let e=!0;if(v.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)})),!e)return}o(!1,e,"outside-press")})),q=a((e=>{var t;const n=()=>{var t;H(e),null==(t=L(e))||t.removeEventListener(d,n)};null==(t=L(e))||t.addEventListener(d,n)}));u.useEffect((()=>{if(!n||!c)return;l.current.__escapeKeyBubbles=S,l.current.__outsidePressBubbles=A;let e=-1;function t(e){o(!1,e,"ancestor-scroll")}function u(){window.clearTimeout(e),F.current=!0}function a(){e=window.setTimeout((()=>{F.current=!1}),"undefined"!=typeof CSS&&CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")?5:0)}const f=P(i.floating);s&&(f.addEventListener("keydown",D?K:j,D),f.addEventListener("compositionstart",u),f.addEventListener("compositionend",a)),C&&f.addEventListener(d,N?q:H,N);let m=[];return h&&(p(i.domReference)&&(m=r.getOverflowAncestors(i.domReference)),p(i.floating)&&(m=m.concat(r.getOverflowAncestors(i.floating))),!p(i.reference)&&i.reference&&i.reference.contextElement&&(m=m.concat(r.getOverflowAncestors(i.reference.contextElement)))),m=m.filter((e=>{var t;return e!==(null==(t=f.defaultView)?void 0:t.visualViewport)})),m.forEach((e=>{e.addEventListener("scroll",t,{passive:!0})})),()=>{s&&(f.removeEventListener("keydown",D?K:j,D),f.removeEventListener("compositionstart",u),f.removeEventListener("compositionend",a)),C&&f.removeEventListener(d,N?q:H,N),m.forEach((e=>{e.removeEventListener("scroll",t)})),window.clearTimeout(e)}}),[l,i,s,C,d,n,o,h,c,S,A,j,D,K,H,N,q]),u.useEffect((()=>{M.current=!1}),[C,d]);const W=u.useMemo((()=>({onKeyDown:j,...m&&{[Dt[v]]:e=>{o(!1,e.nativeEvent,"reference-press")},..."click"!==v&&{onClick(e){o(!1,e.nativeEvent,"reference-press")}}}})),[j,o,m,v]),_=u.useMemo((()=>({onKeyDown:j,onMouseDown(){O.current=!0},onMouseUp(){O.current=!0},[Nt[d]]:()=>{M.current=!0}})),[j,d]);return u.useMemo((()=>c?{reference:W,floating:_}:{}),[c,W,_])},e.useFloating=function(e){void 0===e&&(e={});const{nodeId:t}=e,n=jt({...e,elements:{reference:null,floating:null,...e.elements}}),o=e.rootContext||n,i=o.elements,[l,c]=u.useState(null),[s,a]=u.useState(null),f=(null==i?void 0:i.domReference)||l,d=u.useRef(null),m=Re();ee((()=>{f&&(d.current=f)}),[f]);const v=r.useFloating({...e,elements:{...i,...s&&{reference:s}}}),g=u.useCallback((e=>{const t=p(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),getClientRects:()=>e.getClientRects(),contextElement:e}:e;a(t),v.refs.setReference(t)}),[v.refs]),h=u.useCallback((e=>{(p(e)||null===e)&&(d.current=e,c(e)),(p(v.refs.reference.current)||null===v.refs.reference.current||null!==e&&!p(e))&&v.refs.setReference(e)}),[v.refs]),y=u.useMemo((()=>({...v.refs,setReference:h,setPositionReference:g,domReference:d})),[v.refs,h,g]),b=u.useMemo((()=>({...v.elements,domReference:f})),[v.elements,f]),E=u.useMemo((()=>({...v,...o,refs:y,elements:b,nodeId:t})),[v,y,b,t,o]);return ee((()=>{o.dataRef.current.floatingContext=E;const e=null==m?void 0:m.nodesRef.current.find((e=>e.id===t));e&&(e.context=E)})),u.useMemo((()=>({...v,context:E,refs:y,elements:b})),[v,y,b,E])},e.useFloatingNodeId=function(e){const t=ge(),n=Re(),r=we(),o=e||r;return ee((()=>{if(!t)return;const e={id:t,parentId:o};return null==n||n.addNode(e),()=>{null==n||n.removeNode(e)}}),[n,t,o]),t},e.useFloatingParentNodeId=we,e.useFloatingPortalNode=bt,e.useFloatingRootContext=jt,e.useFloatingTree=Re,e.useFocus=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,events:o,dataRef:i,elements:l}=e,{enabled:c=!0,visibleOnly:s=!0}=t,a=u.useRef(!1),f=u.useRef(-1),d=u.useRef(!0);u.useEffect((()=>{if(!c)return;const e=m(l.domReference);function t(){!n&&g(l.domReference)&&l.domReference===w(P(l.domReference))&&(a.current=!0)}function r(){d.current=!0}function o(){d.current=!1}return e.addEventListener("blur",t),Ht()&&(e.addEventListener("keydown",r,!0),e.addEventListener("pointerdown",o,!0)),()=>{e.removeEventListener("blur",t),Ht()&&(e.removeEventListener("keydown",r,!0),e.removeEventListener("pointerdown",o,!0))}}),[l.domReference,n,c]),u.useEffect((()=>{if(c)return o.on("openchange",e),()=>{o.off("openchange",e)};function e(e){let{reason:t}=e;"reference-press"!==t&&"escape-key"!==t||(a.current=!0)}}),[o,c]),u.useEffect((()=>()=>{Ie(f)}),[]);const v=u.useMemo((()=>({onMouseLeave(){a.current=!1},onFocus(e){if(a.current)return;const t=L(e.nativeEvent);if(s&&p(t))if(Ht()&&!e.relatedTarget){if(!d.current&&!A(t))return}else if(!function(e){if(!e||Kt())return!0;try{return e.matches(":focus-visible")}catch(e){return!0}}(t))return;r(!0,e.nativeEvent,"focus")},onBlur(e){a.current=!1;const t=e.relatedTarget,n=e.nativeEvent,o=p(t)&&t.hasAttribute(xe("focus-guard"))&&"outside"===t.getAttribute("data-type");f.current=window.setTimeout((()=>{var e;const u=w(l.domReference?l.domReference.ownerDocument:document);(t||u!==l.domReference)&&(R(null==(e=i.current.floatingContext)?void 0:e.refs.floating.current,u)||R(l.domReference,u)||o||r(!1,n,"focus"))}))}})),[i,l.domReference,r,s]);return u.useMemo((()=>c?{reference:v}:{}),[c,v])},e.useHover=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,events:i,elements:l}=e,{enabled:c=!0,delay:s=0,handleClose:f=null,mouseOnly:d=!1,restMs:m=0,move:v=!0}=t,g=Re(),h=we(),y=ke(f),b=ke(s),E=ke(n),w=ke(m),x=u.useRef(),I=u.useRef(-1),k=u.useRef(),C=u.useRef(-1),M=u.useRef(!0),O=u.useRef(!1),T=u.useRef((()=>{})),L=u.useRef(!1),A=u.useCallback((()=>{var e;const t=null==(e=o.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t}),[o]);u.useEffect((()=>{if(c)return i.on("openchange",e),()=>{i.off("openchange",e)};function e(e){let{open:t}=e;t||(Ie(I),Ie(C),M.current=!0,L.current=!1)}}),[c,i]),u.useEffect((()=>{if(!c)return;if(!y.current)return;if(!n)return;function e(e){A()&&r(!1,e,"hover")}const t=P(l.floating).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}}),[l.floating,n,r,c,y,A]);const D=u.useCallback((function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");const o=Me(b.current,"close",x.current);o&&!k.current?(Ie(I),I.current=window.setTimeout((()=>r(!1,e,n)),o)):t&&(Ie(I),r(!1,e,n))}),[b,r]),N=a((()=>{T.current(),k.current=void 0})),F=a((()=>{if(O.current){const e=P(l.floating).body;e.style.pointerEvents="",e.removeAttribute(Ce),O.current=!1}})),j=a((()=>!!o.current.openEvent&&["click","mousedown"].includes(o.current.openEvent.type)));u.useEffect((()=>{if(c&&p(l.domReference)){const r=l.domReference,o=l.floating;return n&&r.addEventListener("mouseleave",u),v&&r.addEventListener("mousemove",e,{once:!0}),r.addEventListener("mouseenter",e),r.addEventListener("mouseleave",t),o&&(o.addEventListener("mouseleave",u),o.addEventListener("mouseenter",i),o.addEventListener("mouseleave",s)),()=>{n&&r.removeEventListener("mouseleave",u),v&&r.removeEventListener("mousemove",e),r.removeEventListener("mouseenter",e),r.removeEventListener("mouseleave",t),o&&(o.removeEventListener("mouseleave",u),o.removeEventListener("mouseenter",i),o.removeEventListener("mouseleave",s))}}function e(e){if(Ie(I),M.current=!1,d&&!S(x.current)||Oe(w.current)>0&&!Me(b.current,"open"))return;const t=Me(b.current,"open",x.current);t?I.current=window.setTimeout((()=>{E.current||r(!0,e,"hover")}),t):n||r(!0,e,"hover")}function t(e){if(j())return void F();T.current();const t=P(l.floating);if(Ie(C),L.current=!1,y.current&&o.current.floatingContext){n||Ie(I),k.current=y.current({...o.current.floatingContext,tree:g,x:e.clientX,y:e.clientY,onClose(){F(),N(),j()||D(e,!0,"safe-polygon")}});const r=k.current;return t.addEventListener("mousemove",r),void(T.current=()=>{t.removeEventListener("mousemove",r)})}("touch"!==x.current||!R(l.floating,e.relatedTarget))&&D(e)}function u(e){j()||o.current.floatingContext&&(null==y.current||y.current({...o.current.floatingContext,tree:g,x:e.clientX,y:e.clientY,onClose(){F(),N(),j()||D(e)}})(e))}function i(){Ie(I)}function s(e){j()||D(e,!1)}}),[l,c,e,d,v,D,N,F,r,n,E,g,b,y,o,j,w]),ee((()=>{var e;if(c&&n&&null!=(e=y.current)&&e.__options.blockPointerEvents&&A()){O.current=!0;const e=l.floating;if(p(l.domReference)&&e){var t;const n=P(l.floating).body;n.setAttribute(Ce,"");const r=l.domReference,o=null==g||null==(t=g.nodesRef.current.find((e=>e.id===h)))||null==(t=t.context)?void 0:t.elements.floating;return o&&(o.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}}),[c,n,h,l,g,y,A]),ee((()=>{n||(x.current=void 0,L.current=!1,N(),F())}),[n,N,F]),u.useEffect((()=>()=>{N(),Ie(I),Ie(C),F()}),[c,l.domReference,N,F]);const K=u.useMemo((()=>{function e(e){x.current=e.pointerType}return{onPointerDown:e,onPointerEnter:e,onMouseMove(e){const{nativeEvent:t}=e;function o(){M.current||E.current||r(!0,t,"hover")}d&&!S(x.current)||n||0===Oe(w.current)||L.current&&e.movementX**2+e.movementY**2<2||(Ie(C),"touch"===x.current?o():(L.current=!0,C.current=window.setTimeout(o,Oe(w.current))))}}}),[d,r,n,E,w]);return u.useMemo((()=>c?{reference:K}:{}),[c,K])},e.useId=ge,e.useInnerOffset=function(e,t){const{open:n,elements:r}=e,{enabled:o=!0,overflowRef:l,scrollRef:c,onChange:s}=t,f=a(s),d=u.useRef(!1),m=u.useRef(null),v=u.useRef(null);u.useEffect((()=>{if(!o)return;function e(e){if(e.ctrlKey||!t||null==l.current)return;const n=e.deltaY,r=l.current.top>=-.5,o=l.current.bottom>=-.5,u=t.scrollHeight-t.clientHeight,c=n<0?-1:1,s=n<0?"max":"min";t.scrollHeight<=t.clientHeight||(!r&&n>0||!o&&n<0?(e.preventDefault(),i.flushSync((()=>{f((e=>e+Math[s](n,u*c)))}))):/firefox/i.test(I())&&(t.scrollTop+=n))}const t=(null==c?void 0:c.current)||r.floating;return n&&t?(t.addEventListener("wheel",e),requestAnimationFrame((()=>{m.current=t.scrollTop,null!=l.current&&(v.current={...l.current})})),()=>{m.current=null,v.current=null,t.removeEventListener("wheel",e)}):void 0}),[o,n,r.floating,l,c,f]);const p=u.useMemo((()=>({onKeyDown(){d.current=!0},onWheel(){d.current=!1},onPointerMove(){d.current=!1},onScroll(){const e=(null==c?void 0:c.current)||r.floating;if(l.current&&e&&d.current){if(null!==m.current){const t=e.scrollTop-m.current;(l.current.bottom<-.5&&t<-1||l.current.top<-.5&&t>1)&&i.flushSync((()=>f((e=>e+t))))}requestAnimationFrame((()=>{m.current=e.scrollTop}))}}})),[r.floating,f,l,c]);return u.useMemo((()=>o?{floating:p}:{}),[o,p])},e.useInteractions=function(e){void 0===e&&(e=[]);const t=e.map((e=>null==e?void 0:e.reference)),n=e.map((e=>null==e?void 0:e.floating)),r=e.map((e=>null==e?void 0:e.item)),o=u.useCallback((t=>_t(t,e,"reference")),t),i=u.useCallback((t=>_t(t,e,"floating")),n),l=u.useCallback((t=>_t(t,e,"item")),r);return u.useMemo((()=>({getReferenceProps:o,getFloatingProps:i,getItemProps:l})),[o,i,l])},e.useListItem=oe,e.useListNavigation=function(e,t){const{open:n,onOpenChange:r,elements:o,floatingId:i}=e,{listRef:l,activeIndex:c,onNavigate:s=()=>{},enabled:f=!0,selectedIndex:d=null,allowEscape:m=!1,loop:v=!1,nested:p=!1,rtl:h=!1,virtual:y=!1,focusItemOnOpen:b="auto",focusItemOnHover:E=!0,openOnArrowKeyDown:x=!0,disabledIndices:I,orientation:M="vertical",cols:O=1,scrollItemIntoView:S=!0,virtualItemRef:T,itemSizes:L,dense:A=!1}=t,F=ke(Rt(o.floating)),j=we(),K=Re();ee((()=>{e.dataRef.current.orientation=M}),[e,M]);const H=a((()=>{s(-1===te.current?null:te.current)})),q=N(o.domReference),U=u.useRef(b),te=u.useRef(null!=d?d:-1),ne=u.useRef(null),re=u.useRef(!0),oe=u.useRef(H),ue=u.useRef(!!o.floating),ie=u.useRef(n),le=u.useRef(!1),ce=u.useRef(!1),se=ke(I),ae=ke(n),fe=ke(S),de=ke(d),[me,ve]=u.useState(),[pe,ge]=u.useState(),he=a((()=>{function e(e){var t;y?(null!=(t=e.id)&&t.endsWith("-fui-option")&&(e.id=i+"-"+Math.random().toString(16).slice(2,10)),ve(e.id),null==K||K.events.emit("virtualfocus",e),T&&(T.current=e)):Qe(e,{sync:le.current,preventScroll:!0})}const t=l.current[te.current],n=ce.current;t&&e(t);(le.current?e=>e():requestAnimationFrame)((()=>{const r=l.current[te.current]||t;if(!r)return;t||e(r);const o=fe.current;o&&be&&(n||!re.current)&&(null==r.scrollIntoView||r.scrollIntoView("boolean"==typeof o?{block:"nearest",inline:"nearest"}:o))}))}));ee((()=>{f&&(n&&o.floating?U.current&&null!=d&&(ce.current=!0,te.current=d,H()):ue.current&&(te.current=-1,oe.current()))}),[f,n,o.floating,d,H]),ee((()=>{if(f&&n&&o.floating)if(null==c){if(le.current=!1,null!=de.current)return;if(ue.current&&(te.current=-1,he()),(!ie.current||!ue.current)&&U.current&&(null!=ne.current||!0===U.current&&null==ne.current)){let e=0;const t=()=>{if(null==l.current[0]){if(e<2){(e?requestAnimationFrame:queueMicrotask)(t)}e++}else te.current=null==ne.current||zt(ne.current,M,h)||p?X(l,se.current):Y(l,se.current),ne.current=null,H()};t()}}else z(l,c)||(te.current=c,he(),ce.current=!1)}),[f,n,o.floating,c,de,p,l,M,h,H,he,se]),ee((()=>{var e;if(!f||o.floating||!K||y||!ue.current)return;const t=K.nodesRef.current,n=null==(e=t.find((e=>e.id===j)))||null==(e=e.context)?void 0:e.elements.floating,r=w(P(o.floating)),u=t.some((e=>e.context&&R(e.context.elements.floating,r)));n&&!u&&re.current&&n.focus({preventScroll:!0})}),[f,o.floating,K,j,y]),ee((()=>{if(f&&K&&y&&!j)return K.events.on("virtualfocus",e),()=>{K.events.off("virtualfocus",e)};function e(e){ge(e.id),T&&(T.current=e)}}),[f,K,y,j,T]),ee((()=>{oe.current=H,ie.current=n,ue.current=!!o.floating})),ee((()=>{n||(ne.current=null)}),[n]);const ye=null!=c,be=u.useMemo((()=>{function e(e){if(!n)return;const t=l.current.indexOf(e);-1!==t&&te.current!==t&&(te.current=t,H())}return{onFocus(t){let{currentTarget:n}=t;le.current=!0,e(n)},onClick:e=>{let{currentTarget:t}=e;return t.focus({preventScroll:!0})},...E&&{onMouseMove(t){let{currentTarget:n}=t;le.current=!0,ce.current=!1,e(n)},onPointerLeave(e){let{pointerType:t}=e;var n;re.current&&"touch"!==t&&(le.current=!0,te.current=-1,H(),y||null==(n=F.current)||n.focus({preventScroll:!0}))}}}}),[n,F,E,l,H,y]),Ee=u.useCallback((()=>{var e;return null==K||null==(e=K.nodesRef.current.find((e=>e.id===j)))||null==(e=e.context)||null==(e=e.dataRef)?void 0:e.current.orientation}),[j,K]),xe=a((e=>{if(re.current=!1,le.current=!0,229===e.which)return;if(!ae.current&&e.currentTarget===F.current)return;if(p&&Yt(e.key,M,h,O))return Ut(e.key,Ee())||D(e),r(!1,e.nativeEvent,"list-navigation"),void(g(o.domReference)&&(y?null==K||K.events.emit("virtualfocus",o.domReference):o.domReference.focus()));const t=te.current,u=X(l,I),i=Y(l,I);if(q||("Home"===e.key&&(D(e),te.current=u,H()),"End"===e.key&&(D(e),te.current=i,H())),O>1){const t=L||Array.from({length:l.current.length},(()=>({width:1,height:1}))),n=Z(t,O,A),r=n.findIndex((e=>null!=e&&!J(l.current,e,I))),o=n.reduce(((e,t,n)=>null==t||J(l.current,t,I)?e:n),-1),c=n[G({current:n.map((e=>null!=e?l.current[e]:null))},{event:e,orientation:M,loop:v,rtl:h,cols:O,disabledIndices:Q([...I||l.current.map(((e,t)=>J(l.current,t)?t:void 0)),void 0],n),minIndex:r,maxIndex:o,prevIndex:$(te.current>i?u:te.current,t,n,O,e.key===W?"bl":e.key===(h?_:B)?"tr":"tl"),stopEvent:!0})];if(null!=c&&(te.current=c,H()),"both"===M)return}if(Ut(e.key,M)){if(D(e),n&&!y&&w(e.currentTarget.ownerDocument)===e.currentTarget)return te.current=zt(e.key,M,h)?u:i,void H();zt(e.key,M,h)?te.current=v?t>=i?m&&t!==l.current.length?-1:u:V(l,{startingIndex:t,disabledIndices:I}):Math.min(i,V(l,{startingIndex:t,disabledIndices:I})):te.current=v?t<=u?m&&-1!==t?l.current.length:i:V(l,{startingIndex:t,decrement:!0,disabledIndices:I}):Math.max(u,V(l,{startingIndex:t,decrement:!0,disabledIndices:I})),z(l,te.current)&&(te.current=-1),H()}})),Ie=u.useMemo((()=>y&&n&&ye&&{"aria-activedescendant":pe||me}),[y,n,ye,pe,me]),Ce=u.useMemo((()=>({"aria-orientation":"both"===M?void 0:M,...q?{}:Ie,onKeyDown:xe,onPointerMove(){re.current=!0}})),[Ie,xe,M,q]),Me=u.useMemo((()=>{function e(e){"auto"===b&&k(e.nativeEvent)&&(U.current=!0)}function t(e){U.current=b,"auto"===b&&C(e.nativeEvent)&&(U.current=!0)}return{...Ie,onKeyDown(e){re.current=!1;const t=e.key.startsWith("Arrow"),o=["Home","End"].includes(e.key),u=t||o,i=Xt(e.key,M,h),c=Yt(e.key,M,h,O),s=Xt(e.key,Ee(),h),a=Ut(e.key,M),f=(p?s:a)||"Enter"===e.key||""===e.key.trim();if(y&&n){const t=null==K?void 0:K.nodesRef.current.find((e=>null==e.parentId)),n=K&&t?function(e,t){let n,r=-1;return function t(o,u){u>r&&(n=o,r=u),et(e,o).forEach((e=>{t(e.id,u+1)}))}(t,0),e.find((e=>e.id===n))}(K.nodesRef.current,t.id):null;if(u&&n&&T){const t=new KeyboardEvent("keydown",{key:e.key,bubbles:!0});if(i||c){var m,v;const r=(null==(m=n.context)?void 0:m.elements.domReference)===e.currentTarget,o=c&&!r?null==(v=n.context)?void 0:v.elements.domReference:i?l.current.find((e=>(null==e?void 0:e.id)===me)):null;o&&(D(e),o.dispatchEvent(t),ge(void 0))}var g;if((a||o)&&n.context)if(n.context.open&&n.parentId&&e.currentTarget!==n.context.elements.domReference)return D(e),void(null==(g=n.context.elements.domReference)||g.dispatchEvent(t))}return xe(e)}if(n||x||!t){if(f){const t=Ut(e.key,Ee());ne.current=p&&t?null:e.key}p?s&&(D(e),n?(te.current=X(l,se.current),H()):r(!0,e.nativeEvent,"list-navigation")):a&&(null!=d&&(te.current=d),D(e),!n&&x?r(!0,e.nativeEvent,"list-navigation"):xe(e),n&&H())}},onFocus(){n&&!y&&(te.current=-1,H())},onPointerDown:t,onPointerEnter:t,onMouseDown:e,onClick:e}}),[me,Ie,O,xe,se,b,l,p,H,r,n,x,M,Ee,h,d,K,y,T]);return u.useMemo((()=>f?{reference:Me,floating:Ce,item:be}:{}),[f,Me,Ce,be])},e.useMergeRefs=l,e.useNextDelayGroup=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,floatingId:o}=e,{enabled:i=!0}=t,l=u.useContext(Le),{currentIdRef:c,delayRef:s,timeoutMs:a,initialDelayRef:f,currentContextRef:d,hasProvider:m,timeoutIdRef:v}=l,[p,g]=u.useState(!1);return ee((()=>{function e(){var e;g(!1),null==(e=d.current)||e.setIsInstantPhase(!1),c.current=null,d.current=null,s.current=f.current}if(i&&c.current&&!n&&c.current===o){if(g(!1),a)return v.current=window.setTimeout(e,a),()=>{clearTimeout(v.current)};e()}}),[i,n,o,c,s,a,f,d,v]),ee((()=>{if(!i)return;if(!n)return;const e=d.current,t=c.current;d.current={onOpenChange:r,setIsInstantPhase:g},c.current=o,s.current={open:0,close:Me(f.current,"close")},null!==t&&t!==o?(Ie(v),g(!0),null==e||e.setIsInstantPhase(!0),null==e||e.onOpenChange(!1)):(g(!1),null==e||e.setIsInstantPhase(!1))}),[i,n,o,r,c,s,a,f,d,v]),ee((()=>()=>{d.current=null}),[d]),u.useMemo((()=>({hasProvider:m,delayRef:s,isInstantPhase:p})),[m,s,p])},e.useRole=function(e,t){var n,r;void 0===t&&(t={});const{open:o,elements:i,floatingId:l}=e,{enabled:c=!0,role:s="dialog"}=t,a=ge(),f=(null==(n=i.domReference)?void 0:n.id)||a,d=u.useMemo((()=>{var e;return(null==(e=Rt(i.floating))?void 0:e.id)||l}),[i.floating,l]),m=null!=(r=Vt.get(s))?r:s,v=null!=we(),p=u.useMemo((()=>"tooltip"===m||"label"===s?{["aria-"+("label"===s?"labelledby":"describedby")]:o?d:void 0}:{"aria-expanded":o?"true":"false","aria-haspopup":"alertdialog"===m?"dialog":m,"aria-controls":o?d:void 0,..."listbox"===m&&{role:"combobox"},..."menu"===m&&{id:f},..."menu"===m&&v&&{role:"menuitem"},..."select"===s&&{"aria-autocomplete":"none"},..."combobox"===s&&{"aria-autocomplete":"list"}}),[m,d,v,o,f,s]),g=u.useMemo((()=>{const e={id:d,...m&&{role:m}};return"tooltip"===m||"label"===s?e:{...e,..."menu"===m&&{"aria-labelledby":f}}}),[m,d,f,s]),h=u.useCallback((e=>{let{active:t,selected:n}=e;const r={role:"option",...t&&{id:d+"-fui-option"}};switch(s){case"select":return{...r,"aria-selected":t&&n};case"combobox":return{...r,"aria-selected":n}}return{}}),[d,s]);return u.useMemo((()=>c?{reference:p,floating:g,item:h}:{}),[c,p,g,h])},e.useTransitionStatus=$t,e.useTransitionStyles=function(e,t){void 0===t&&(t={});const{initial:n={opacity:0},open:r,close:o,common:i,duration:l=250}=t,c=e.placement,s=c.split("-")[0],a=u.useMemo((()=>({side:s,placement:c})),[s,c]),f="number"==typeof l,d=(f?l:l.open)||0,m=(f?l:l.close)||0,[v,p]=u.useState((()=>({...Zt(i,a),...Zt(n,a)}))),{isMounted:g,status:h}=$t(e,{duration:l}),y=ke(n),b=ke(r),E=ke(o),w=ke(i);return ee((()=>{const e=Zt(y.current,a),t=Zt(E.current,a),n=Zt(w.current,a),r=Zt(b.current,a)||Object.keys(e).reduce(((e,t)=>(e[t]="",e)),{});if("initial"===h&&p((t=>({transitionProperty:t.transitionProperty,...n,...e}))),"open"===h&&p({transitionProperty:Object.keys(r).map(Gt).join(","),transitionDuration:d+"ms",...n,...r}),"close"===h){const r=t||e;p({transitionProperty:Object.keys(r).map(Gt).join(","),transitionDuration:m+"ms",...n,...r})}}),[m,E,y,b,w,d,h,a]),{isMounted:g,styles:v}},e.useTypeahead=function(e,t){var n;const{open:r,dataRef:o}=e,{listRef:i,activeIndex:l,onMatch:c,onTypingChange:s,enabled:f=!0,findMatch:d=null,resetMs:m=750,ignoreKeys:v=[],selectedIndex:p=null}=t,g=u.useRef(-1),h=u.useRef(""),y=u.useRef(null!=(n=null!=p?p:l)?n:-1),b=u.useRef(null),E=a(c),w=a(s),R=ke(d),x=ke(v);ee((()=>{r&&(Ie(g),b.current=null,h.current="")}),[r]),ee((()=>{var e;r&&""===h.current&&(y.current=null!=(e=null!=p?p:l)?e:-1)}),[r,p,l]);const I=a((e=>{e?o.current.typing||(o.current.typing=e,w(e)):o.current.typing&&(o.current.typing=e,w(e))})),k=a((e=>{function t(e,t,n){const r=R.current?R.current(t,n):t.find((e=>0===(null==e?void 0:e.toLocaleLowerCase().indexOf(n.toLocaleLowerCase()))));return r?e.indexOf(r):-1}const n=i.current;if(h.current.length>0&&" "!==h.current[0]&&(-1===t(n,n,h.current)?I(!1):" "===e.key&&D(e)),null==n||x.current.includes(e.key)||1!==e.key.length||e.ctrlKey||e.metaKey||e.altKey)return;r&&" "!==e.key&&(D(e),I(!0));n.every((e=>{var t,n;return!e||(null==(t=e[0])?void 0:t.toLocaleLowerCase())!==(null==(n=e[1])?void 0:n.toLocaleLowerCase())}))&&h.current===e.key&&(h.current="",y.current=b.current),h.current+=e.key,Ie(g),g.current=window.setTimeout((()=>{h.current="",y.current=b.current,I(!1)}),m);const o=y.current,u=t(n,[...n.slice((o||0)+1),...n.slice(0,(o||0)+1)],h.current);-1!==u?(E(u),b.current=u):" "!==e.key&&(h.current="",I(!1))})),C=u.useMemo((()=>({onKeyDown:k})),[k]),M=u.useMemo((()=>({onKeyDown:k,onKeyUp(e){" "===e.key&&I(!1)}})),[k,I]);return u.useMemo((()=>f?{reference:C,floating:M}:{}),[f,C,M])}}));
