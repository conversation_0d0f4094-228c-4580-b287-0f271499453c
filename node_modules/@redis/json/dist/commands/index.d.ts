import * as ARRAPPEND from './ARRAPPEND';
import * as ARRIND<PERSON> from './ARRINDEX';
import * as ARRINSERT from './ARRINSERT';
import * as ARRLEN from './ARRLEN';
import * as ARRPOP from './ARRPOP';
import * as ARRTRIM from './ARRTRIM';
import * as DEBUG_MEMORY from './DEBUG_MEMORY';
import * as DEL from './DEL';
import * as FORGET from './FORGET';
import * as GET from './GET';
import * as MERGE from './MERGE';
import * as MGET from './MGET';
import * as MSET from './MSET';
import * as NUMINCRBY from './NUMINCRBY';
import * as NUMMULTBY from './NUMMULTBY';
import * as OBJKEYS from './OBJKEYS';
import * as OBJLEN from './OBJLEN';
import * as RESP from './RESP';
import * as SET from './SET';
import * as STRAPPEND from './STRAPPEND';
import * as <PERSON>RL<PERSON> from './STRLEN';
import * as TYP<PERSON> from './TYPE';
declare const _default: {
    ARRAPPEND: typeof ARRAPPEND;
    arrAppend: typeof ARRAPPEND;
    ARRINDEX: typeof ARRINDEX;
    arrIndex: typeof ARRINDEX;
    ARRINSERT: typeof ARRINSERT;
    arrInsert: typeof ARRINSERT;
    ARRLEN: typeof ARRLEN;
    arrLen: typeof ARRLEN;
    ARRPOP: typeof ARRPOP;
    arrPop: typeof ARRPOP;
    ARRTRIM: typeof ARRTRIM;
    arrTrim: typeof ARRTRIM;
    DEBUG_MEMORY: typeof DEBUG_MEMORY;
    debugMemory: typeof DEBUG_MEMORY;
    DEL: typeof DEL;
    del: typeof DEL;
    FORGET: typeof FORGET;
    forget: typeof FORGET;
    GET: typeof GET;
    get: typeof GET;
    MERGE: typeof MERGE;
    merge: typeof MERGE;
    MGET: typeof MGET;
    mGet: typeof MGET;
    MSET: typeof MSET;
    mSet: typeof MSET;
    NUMINCRBY: typeof NUMINCRBY;
    numIncrBy: typeof NUMINCRBY;
    NUMMULTBY: typeof NUMMULTBY;
    numMultBy: typeof NUMMULTBY;
    OBJKEYS: typeof OBJKEYS;
    objKeys: typeof OBJKEYS;
    OBJLEN: typeof OBJLEN;
    objLen: typeof OBJLEN;
    RESP: typeof RESP;
    resp: typeof RESP;
    SET: typeof SET;
    set: typeof SET;
    STRAPPEND: typeof STRAPPEND;
    strAppend: typeof STRAPPEND;
    STRLEN: typeof STRLEN;
    strLen: typeof STRLEN;
    TYPE: typeof TYPE;
    type: typeof TYPE;
};
export default _default;
interface RedisJSONArray extends Array<RedisJSON> {
}
interface RedisJSONObject {
    [key: string]: RedisJSON;
    [key: number]: RedisJSON;
}
export type RedisJSON = null | boolean | number | string | Date | RedisJSONArray | RedisJSONObject;
export declare function transformRedisJsonArgument(json: RedisJSON): string;
export declare function transformRedisJsonReply(json: string): RedisJSON;
export declare function transformRedisJsonNullReply(json: string | null): RedisJSON | null;
export declare function transformNumbersReply(reply: string): number | Array<number>;
