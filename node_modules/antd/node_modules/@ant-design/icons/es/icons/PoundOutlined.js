import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PoundOutlinedSvg from "@ant-design/icons-svg/es/asn/PoundOutlined";
import AntdIcon from '../components/AntdIcon';
var PoundOutlined = function PoundOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PoundOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PoundOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PoundOutlined';
}
export default RefIcon;