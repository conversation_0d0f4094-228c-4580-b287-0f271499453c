import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PushpinFilledSvg from "@ant-design/icons-svg/es/asn/PushpinFilled";
import AntdIcon from '../components/AntdIcon';
var PushpinFilled = function PushpinFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PushpinFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PushpinFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PushpinFilled';
}
export default RefIcon;