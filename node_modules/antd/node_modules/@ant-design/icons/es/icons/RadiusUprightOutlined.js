import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RadiusUprightOutlinedSvg from "@ant-design/icons-svg/es/asn/RadiusUprightOutlined";
import AntdIcon from '../components/AntdIcon';
var RadiusUprightOutlined = function RadiusUprightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RadiusUprightOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(RadiusUprightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RadiusUprightOutlined';
}
export default RefIcon;