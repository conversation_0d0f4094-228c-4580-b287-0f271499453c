import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RightOutlinedSvg from "@ant-design/icons-svg/es/asn/RightOutlined";
import AntdIcon from '../components/AntdIcon';
var RightOutlined = function RightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RightOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(RightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RightOutlined';
}
export default RefIcon;