import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ScheduleFilledSvg from "@ant-design/icons-svg/es/asn/ScheduleFilled";
import AntdIcon from '../components/AntdIcon';
var ScheduleFilled = function ScheduleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ScheduleFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ScheduleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ScheduleFilled';
}
export default RefIcon;