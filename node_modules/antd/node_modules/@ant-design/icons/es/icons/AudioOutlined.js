import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AudioOutlinedSvg from "@ant-design/icons-svg/es/asn/AudioOutlined";
import AntdIcon from '../components/AntdIcon';
var AudioOutlined = function AudioOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AudioOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(AudioOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AudioOutlined';
}
export default RefIcon;