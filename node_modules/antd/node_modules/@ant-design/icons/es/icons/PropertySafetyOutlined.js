import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PropertySafetyOutlinedSvg from "@ant-design/icons-svg/es/asn/PropertySafetyOutlined";
import AntdIcon from '../components/AntdIcon';
var PropertySafetyOutlined = function PropertySafetyOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PropertySafetyOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PropertySafetyOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PropertySafetyOutlined';
}
export default RefIcon;