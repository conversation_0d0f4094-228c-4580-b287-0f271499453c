import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AliwangwangOutlinedSvg from "@ant-design/icons-svg/es/asn/<PERSON>wangwangOutlined";
import AntdIcon from '../components/AntdIcon';
var AliwangwangOutlined = function AliwangwangOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: <PERSON>wangwangOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(<PERSON>wangwangOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AliwangwangOutlined';
}
export default RefIcon;