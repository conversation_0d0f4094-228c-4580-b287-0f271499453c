import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SafetyCertificateTwoToneSvg from "@ant-design/icons-svg/es/asn/SafetyCertificateTwoTone";
import AntdIcon from '../components/AntdIcon';
var SafetyCertificateTwoTone = function SafetyCertificateTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SafetyCertificateTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SafetyCertificateTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SafetyCertificateTwoTone';
}
export default RefIcon;