import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StopTwoToneSvg from "@ant-design/icons-svg/es/asn/StopTwoTone";
import AntdIcon from '../components/AntdIcon';
var StopTwoTone = function StopTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StopTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(StopTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'StopTwoTone';
}
export default RefIcon;