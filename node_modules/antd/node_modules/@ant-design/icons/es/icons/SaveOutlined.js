import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SaveOutlinedSvg from "@ant-design/icons-svg/es/asn/SaveOutlined";
import AntdIcon from '../components/AntdIcon';
var SaveOutlined = function SaveOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SaveOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SaveOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SaveOutlined';
}
export default RefIcon;