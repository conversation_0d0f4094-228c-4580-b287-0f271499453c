import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AimOutlinedSvg from "@ant-design/icons-svg/es/asn/AimOutlined";
import AntdIcon from '../components/AntdIcon';
var AimOutlined = function AimOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AimOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(AimOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AimOutlined';
}
export default RefIcon;