import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SignatureOutlinedSvg from "@ant-design/icons-svg/es/asn/SignatureOutlined";
import AntdIcon from '../components/AntdIcon';
var SignatureOutlined = function SignatureOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SignatureOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SignatureOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SignatureOutlined';
}
export default RefIcon;