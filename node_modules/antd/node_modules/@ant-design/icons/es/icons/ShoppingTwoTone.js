import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShoppingTwoToneSvg from "@ant-design/icons-svg/es/asn/ShoppingTwoTone";
import AntdIcon from '../components/AntdIcon';
var ShoppingTwoTone = function ShoppingTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShoppingTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ShoppingTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ShoppingTwoTone';
}
export default RefIcon;