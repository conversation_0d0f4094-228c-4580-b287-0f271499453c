import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlaySquareFilledSvg from "@ant-design/icons-svg/es/asn/PlaySquareFilled";
import AntdIcon from '../components/AntdIcon';
var PlaySquareFilled = function PlaySquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PlaySquareFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PlaySquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PlaySquareFilled';
}
export default RefIcon;