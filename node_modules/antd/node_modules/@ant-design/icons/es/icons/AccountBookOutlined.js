import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AccountBookOutlinedSvg from "@ant-design/icons-svg/es/asn/AccountBookOutlined";
import AntdIcon from '../components/AntdIcon';
var AccountBookOutlined = function AccountBookOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AccountBookOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(AccountBookOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AccountBookOutlined';
}
export default RefIcon;