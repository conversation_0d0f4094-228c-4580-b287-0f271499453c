import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PictureOutlinedSvg from "@ant-design/icons-svg/es/asn/PictureOutlined";
import AntdIcon from '../components/AntdIcon';
var PictureOutlined = function PictureOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PictureOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PictureOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PictureOutlined';
}
export default RefIcon;