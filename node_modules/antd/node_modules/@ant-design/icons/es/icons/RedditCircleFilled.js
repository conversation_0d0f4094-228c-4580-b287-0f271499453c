import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RedditCircleFilledSvg from "@ant-design/icons-svg/es/asn/RedditCircleFilled";
import AntdIcon from '../components/AntdIcon';
var RedditCircleFilled = function RedditCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RedditCircleFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(RedditCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RedditCircleFilled';
}
export default RefIcon;