import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SplitCellsOutlinedSvg from "@ant-design/icons-svg/es/asn/SplitCellsOutlined";
import AntdIcon from '../components/AntdIcon';
var SplitCellsOutlined = function SplitCellsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SplitCellsOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SplitCellsOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SplitCellsOutlined';
}
export default RefIcon;