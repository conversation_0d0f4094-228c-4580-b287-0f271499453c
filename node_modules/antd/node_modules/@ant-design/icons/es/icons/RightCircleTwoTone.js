import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RightCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/RightCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var RightCircleTwoTone = function RightCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RightCircleTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(RightCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RightCircleTwoTone';
}
export default RefIcon;