import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CheckCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/CheckCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var CheckCircleTwoTone = function CheckCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CheckCircleTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(CheckCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CheckCircleTwoTone';
}
export default RefIcon;