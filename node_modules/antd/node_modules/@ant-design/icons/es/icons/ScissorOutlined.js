import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ScissorOutlinedSvg from "@ant-design/icons-svg/es/asn/ScissorOutlined";
import AntdIcon from '../components/AntdIcon';
var ScissorOutlined = function ScissorOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ScissorOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ScissorOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ScissorOutlined';
}
export default RefIcon;