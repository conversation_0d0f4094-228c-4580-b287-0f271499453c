import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PrinterFilledSvg from "@ant-design/icons-svg/es/asn/PrinterFilled";
import AntdIcon from '../components/AntdIcon';
var PrinterFilled = function PrinterFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PrinterFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PrinterFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PrinterFilled';
}
export default RefIcon;