import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SketchSquareFilledSvg from "@ant-design/icons-svg/es/asn/SketchSquareFilled";
import AntdIcon from '../components/AntdIcon';
var SketchSquareFilled = function SketchSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SketchSquareFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SketchSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SketchSquareFilled';
}
export default RefIcon;