import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AlertTwoToneSvg from "@ant-design/icons-svg/es/asn/AlertTwoTone";
import AntdIcon from '../components/AntdIcon';
var AlertTwoTone = function AlertTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlertTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(AlertTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AlertTwoTone';
}
export default RefIcon;