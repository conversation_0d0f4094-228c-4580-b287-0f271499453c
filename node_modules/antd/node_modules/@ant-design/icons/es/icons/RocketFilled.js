import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RocketFilledSvg from "@ant-design/icons-svg/es/asn/RocketFilled";
import AntdIcon from '../components/AntdIcon';
var RocketFilled = function RocketFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RocketFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(RocketFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RocketFilled';
}
export default RefIcon;