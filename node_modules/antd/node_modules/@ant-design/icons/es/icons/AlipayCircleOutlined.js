import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AlipayCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/AlipayCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var AlipayCircleOutlined = function AlipayCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlipayCircleOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(AlipayCircleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AlipayCircleOutlined';
}
export default RefIcon;