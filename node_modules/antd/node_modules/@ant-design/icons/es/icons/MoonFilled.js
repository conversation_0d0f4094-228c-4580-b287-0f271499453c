import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MoonFilledSvg from "@ant-design/icons-svg/es/asn/MoonFilled";
import AntdIcon from '../components/AntdIcon';
var MoonFilled = function MoonFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MoonFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MoonFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MoonFilled';
}
export default RefIcon;