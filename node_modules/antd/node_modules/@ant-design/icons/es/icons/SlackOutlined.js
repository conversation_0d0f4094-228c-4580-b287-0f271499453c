import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SlackOutlinedSvg from "@ant-design/icons-svg/es/asn/SlackOutlined";
import AntdIcon from '../components/AntdIcon';
var SlackOutlined = function SlackOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SlackOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SlackOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SlackOutlined';
}
export default RefIcon;