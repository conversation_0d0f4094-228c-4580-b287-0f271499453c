import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SolutionOutlinedSvg from "@ant-design/icons-svg/es/asn/SolutionOutlined";
import AntdIcon from '../components/AntdIcon';
var SolutionOutlined = function SolutionOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SolutionOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SolutionOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SolutionOutlined';
}
export default RefIcon;