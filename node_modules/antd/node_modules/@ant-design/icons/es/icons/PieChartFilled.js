import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PieChartFilledSvg from "@ant-design/icons-svg/es/asn/PieChartFilled";
import AntdIcon from '../components/AntdIcon';
var PieChartFilled = function PieChartFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PieChartFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PieChartFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PieChartFilled';
}
export default RefIcon;