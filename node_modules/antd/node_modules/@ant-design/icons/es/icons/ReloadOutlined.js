import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ReloadOutlinedSvg from "@ant-design/icons-svg/es/asn/ReloadOutlined";
import AntdIcon from '../components/AntdIcon';
var ReloadOutlined = function ReloadOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ReloadOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ReloadOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ReloadOutlined';
}
export default RefIcon;