import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CodepenOutlinedSvg from "@ant-design/icons-svg/es/asn/CodepenOutlined";
import AntdIcon from '../components/AntdIcon';
var CodepenOutlined = function CodepenOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CodepenOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(CodepenOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CodepenOutlined';
}
export default RefIcon;