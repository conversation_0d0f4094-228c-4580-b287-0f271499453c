import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Pic<PERSON>enterOutlinedSvg from "@ant-design/icons-svg/es/asn/PicCenterOutlined";
import AntdIcon from '../components/AntdIcon';
var PicCenterOutlined = function PicCenterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PicCenterOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PicCenterOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PicCenterOutlined';
}
export default RefIcon;