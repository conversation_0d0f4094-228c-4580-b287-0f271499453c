import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PinterestOutlinedSvg from "@ant-design/icons-svg/es/asn/PinterestOutlined";
import AntdIcon from '../components/AntdIcon';
var PinterestOutlined = function PinterestOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PinterestOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PinterestOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PinterestOutlined';
}
export default RefIcon;