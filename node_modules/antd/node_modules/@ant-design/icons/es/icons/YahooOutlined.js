import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import YahooOutlinedSvg from "@ant-design/icons-svg/es/asn/YahooOutlined";
import AntdIcon from '../components/AntdIcon';
var YahooOutlined = function YahooOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: YahooOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(YahooOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'YahooOutlined';
}
export default RefIcon;