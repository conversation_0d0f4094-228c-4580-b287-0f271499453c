import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CodeOutlinedSvg from "@ant-design/icons-svg/es/asn/CodeOutlined";
import AntdIcon from '../components/AntdIcon';
var CodeOutlined = function CodeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CodeOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(CodeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CodeOutlined';
}
export default RefIcon;