import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import XOutlinedSvg from "@ant-design/icons-svg/es/asn/XOutlined";
import AntdIcon from '../components/AntdIcon';
var XOutlined = function XOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: XOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(XOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'XOutlined';
}
export default RefIcon;