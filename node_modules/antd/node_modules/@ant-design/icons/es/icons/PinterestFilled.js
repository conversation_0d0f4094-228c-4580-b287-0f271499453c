import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PinterestFilledSvg from "@ant-design/icons-svg/es/asn/PinterestFilled";
import AntdIcon from '../components/AntdIcon';
var PinterestFilled = function PinterestFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PinterestFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PinterestFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PinterestFilled';
}
export default RefIcon;