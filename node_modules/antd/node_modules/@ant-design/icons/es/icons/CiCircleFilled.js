import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CiCircleFilledSvg from "@ant-design/icons-svg/es/asn/CiCircleFilled";
import AntdIcon from '../components/AntdIcon';
var CiCircleFilled = function CiCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CiCircleFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(CiCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CiCircleFilled';
}
export default RefIcon;