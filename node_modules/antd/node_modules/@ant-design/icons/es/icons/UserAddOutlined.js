import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UserAddOutlinedSvg from "@ant-design/icons-svg/es/asn/UserAddOutlined";
import AntdIcon from '../components/AntdIcon';
var UserAddOutlined = function UserAddOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UserAddOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(UserAddOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UserAddOutlined';
}
export default RefIcon;