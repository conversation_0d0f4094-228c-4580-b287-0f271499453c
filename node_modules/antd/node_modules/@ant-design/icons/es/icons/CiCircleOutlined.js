import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CiCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/CiCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var CiCircleOutlined = function CiCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CiCircleOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(CiCircleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CiCircleOutlined';
}
export default RefIcon;