import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import NotificationFilledSvg from "@ant-design/icons-svg/es/asn/NotificationFilled";
import AntdIcon from '../components/AntdIcon';
var NotificationFilled = function NotificationFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: NotificationFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(NotificationFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'NotificationFilled';
}
export default RefIcon;