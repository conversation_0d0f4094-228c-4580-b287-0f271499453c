import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UpSquareFilledSvg from "@ant-design/icons-svg/es/asn/UpSquareFilled";
import AntdIcon from '../components/AntdIcon';
var UpSquareFilled = function UpSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UpSquareFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(UpSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UpSquareFilled';
}
export default RefIcon;