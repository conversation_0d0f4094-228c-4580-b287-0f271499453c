import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import TrademarkOutlinedSvg from "@ant-design/icons-svg/es/asn/TrademarkOutlined";
import AntdIcon from '../components/AntdIcon';
var TrademarkOutlined = function TrademarkOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: TrademarkOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(TrademarkOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TrademarkOutlined';
}
export default RefIcon;