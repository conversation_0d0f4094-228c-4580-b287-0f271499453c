import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SubnodeOutlinedSvg from "@ant-design/icons-svg/es/asn/SubnodeOutlined";
import AntdIcon from '../components/AntdIcon';
var SubnodeOutlined = function SubnodeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SubnodeOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SubnodeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SubnodeOutlined';
}
export default RefIcon;