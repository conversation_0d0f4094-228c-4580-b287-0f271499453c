import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AuditOutlinedSvg from "@ant-design/icons-svg/es/asn/AuditOutlined";
import AntdIcon from '../components/AntdIcon';
var AuditOutlined = function AuditOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AuditOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(AuditOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AuditOutlined';
}
export default RefIcon;