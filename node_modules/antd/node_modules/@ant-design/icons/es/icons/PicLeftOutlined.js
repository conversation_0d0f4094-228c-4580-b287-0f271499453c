import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Pic<PERSON>eftOutlinedSvg from "@ant-design/icons-svg/es/asn/PicLeftOutlined";
import AntdIcon from '../components/AntdIcon';
var PicLeftOutlined = function PicLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PicLeftOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PicLeftOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PicLeftOutlined';
}
export default RefIcon;