import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BackwardOutlinedSvg from "@ant-design/icons-svg/es/asn/BackwardOutlined";
import AntdIcon from '../components/AntdIcon';
var BackwardOutlined = function BackwardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BackwardOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(BackwardOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BackwardOutlined';
}
export default RefIcon;