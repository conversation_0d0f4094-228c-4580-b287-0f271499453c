import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import VerticalAlignMiddleOutlinedSvg from "@ant-design/icons-svg/es/asn/VerticalAlignMiddleOutlined";
import AntdIcon from '../components/AntdIcon';
var VerticalAlignMiddleOutlined = function VerticalAlignMiddleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: VerticalAlignMiddleOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(VerticalAlignMiddleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'VerticalAlignMiddleOutlined';
}
export default RefIcon;