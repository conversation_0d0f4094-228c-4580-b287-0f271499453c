import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import UsergroupDeleteOutlinedSvg from "@ant-design/icons-svg/es/asn/UsergroupDeleteOutlined";
import AntdIcon from '../components/AntdIcon';
var UsergroupDeleteOutlined = function UsergroupDeleteOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: UsergroupDeleteOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(UsergroupDeleteOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UsergroupDeleteOutlined';
}
export default RefIcon;