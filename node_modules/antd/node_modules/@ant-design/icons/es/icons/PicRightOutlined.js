import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Pic<PERSON>ightOutlinedSvg from "@ant-design/icons-svg/es/asn/PicRightOutlined";
import AntdIcon from '../components/AntdIcon';
var PicRightOutlined = function PicRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PicRightOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PicRightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PicRightOutlined';
}
export default RefIcon;