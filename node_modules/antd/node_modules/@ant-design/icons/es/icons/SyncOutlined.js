import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SyncOutlinedSvg from "@ant-design/icons-svg/es/asn/SyncOutlined";
import AntdIcon from '../components/AntdIcon';
var SyncOutlined = function SyncOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SyncOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SyncOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SyncOutlined';
}
export default RefIcon;