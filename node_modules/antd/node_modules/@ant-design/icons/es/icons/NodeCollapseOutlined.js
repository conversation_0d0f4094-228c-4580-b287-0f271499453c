import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import NodeCollapseOutlinedSvg from "@ant-design/icons-svg/es/asn/NodeCollapseOutlined";
import AntdIcon from '../components/AntdIcon';
var NodeCollapseOutlined = function NodeCollapseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: NodeCollapseOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(NodeCollapseOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'NodeCollapseOutlined';
}
export default RefIcon;