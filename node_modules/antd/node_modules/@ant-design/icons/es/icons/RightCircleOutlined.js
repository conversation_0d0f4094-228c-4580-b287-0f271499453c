import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RightCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/RightCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var RightCircleOutlined = function RightCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RightCircleOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(RightCircleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RightCircleOutlined';
}
export default RefIcon;