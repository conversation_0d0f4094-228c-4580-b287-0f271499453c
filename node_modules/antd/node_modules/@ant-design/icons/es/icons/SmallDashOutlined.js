import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SmallDashOutlinedSvg from "@ant-design/icons-svg/es/asn/SmallDashOutlined";
import AntdIcon from '../components/AntdIcon';
var SmallDashOutlined = function SmallDashOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SmallDashOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SmallDashOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SmallDashOutlined';
}
export default RefIcon;