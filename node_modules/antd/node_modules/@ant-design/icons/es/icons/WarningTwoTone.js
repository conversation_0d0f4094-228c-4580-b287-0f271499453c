import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WarningTwoToneSvg from "@ant-design/icons-svg/es/asn/WarningTwoTone";
import AntdIcon from '../components/AntdIcon';
var WarningTwoTone = function WarningTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WarningTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(WarningTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'WarningTwoTone';
}
export default RefIcon;