import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RobotOutlinedSvg from "@ant-design/icons-svg/es/asn/RobotOutlined";
import AntdIcon from '../components/AntdIcon';
var RobotOutlined = function RobotOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RobotOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(RobotOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RobotOutlined';
}
export default RefIcon;