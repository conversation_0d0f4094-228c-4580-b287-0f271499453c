import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ClockCircleFilledSvg from "@ant-design/icons-svg/es/asn/ClockCircleFilled";
import AntdIcon from '../components/AntdIcon';
var ClockCircleFilled = function ClockCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ClockCircleFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ClockCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ClockCircleFilled';
}
export default RefIcon;