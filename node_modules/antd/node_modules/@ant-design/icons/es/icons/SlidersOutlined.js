import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SlidersOutlinedSvg from "@ant-design/icons-svg/es/asn/SlidersOutlined";
import AntdIcon from '../components/AntdIcon';
var SlidersOutlined = function SlidersOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SlidersOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SlidersOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SlidersOutlined';
}
export default RefIcon;