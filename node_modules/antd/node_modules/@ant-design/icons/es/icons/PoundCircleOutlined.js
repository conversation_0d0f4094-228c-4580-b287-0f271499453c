import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PoundCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/PoundCircleOutlined";
import AntdIcon from '../components/AntdIcon';
var PoundCircleOutlined = function PoundCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PoundCircleOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PoundCircleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PoundCircleOutlined';
}
export default RefIcon;