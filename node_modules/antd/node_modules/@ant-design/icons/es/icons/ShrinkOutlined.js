import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ShrinkOutlinedSvg from "@ant-design/icons-svg/es/asn/ShrinkOutlined";
import AntdIcon from '../components/AntdIcon';
var ShrinkOutlined = function ShrinkOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ShrinkOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ShrinkOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ShrinkOutlined';
}
export default RefIcon;