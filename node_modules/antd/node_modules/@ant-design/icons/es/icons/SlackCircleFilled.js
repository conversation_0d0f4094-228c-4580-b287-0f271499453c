import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SlackCircleFilledSvg from "@ant-design/icons-svg/es/asn/SlackCircleFilled";
import AntdIcon from '../components/AntdIcon';
var SlackCircleFilled = function SlackCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SlackCircleFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SlackCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SlackCircleFilled';
}
export default RefIcon;