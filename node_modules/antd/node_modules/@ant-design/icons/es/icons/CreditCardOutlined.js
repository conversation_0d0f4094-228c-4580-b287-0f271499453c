import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CreditCardOutlinedSvg from "@ant-design/icons-svg/es/asn/CreditCardOutlined";
import AntdIcon from '../components/AntdIcon';
var CreditCardOutlined = function CreditCardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CreditCardOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(CreditCardOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CreditCardOutlined';
}
export default RefIcon;