import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RadiusSettingOutlinedSvg from "@ant-design/icons-svg/es/asn/RadiusSettingOutlined";
import AntdIcon from '../components/AntdIcon';
var RadiusSettingOutlined = function RadiusSettingOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RadiusSettingOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(RadiusSettingOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RadiusSettingOutlined';
}
export default RefIcon;