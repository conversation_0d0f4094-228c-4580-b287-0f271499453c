import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import RetweetOutlinedSvg from "@ant-design/icons-svg/es/asn/RetweetOutlined";
import AntdIcon from '../components/AntdIcon';
var RetweetOutlined = function RetweetOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RetweetOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(RetweetOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RetweetOutlined';
}
export default RefIcon;