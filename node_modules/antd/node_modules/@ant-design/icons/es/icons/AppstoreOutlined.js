import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AppstoreOutlinedSvg from "@ant-design/icons-svg/es/asn/AppstoreOutlined";
import AntdIcon from '../components/AntdIcon';
var AppstoreOutlined = function AppstoreOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AppstoreOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(AppstoreOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AppstoreOutlined';
}
export default RefIcon;