import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ContainerOutlinedSvg from "@ant-design/icons-svg/es/asn/ContainerOutlined";
import AntdIcon from '../components/AntdIcon';
var ContainerOutlined = function ContainerOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ContainerOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ContainerOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ContainerOutlined';
}
export default RefIcon;