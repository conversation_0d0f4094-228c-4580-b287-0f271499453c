import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CheckCircleFilledSvg from "@ant-design/icons-svg/es/asn/CheckCircleFilled";
import AntdIcon from '../components/AntdIcon';
var CheckCircleFilled = function CheckCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CheckCircleFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(CheckCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CheckCircleFilled';
}
export default RefIcon;