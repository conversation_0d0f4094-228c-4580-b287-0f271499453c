import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Radar<PERSON>hartOutlinedSvg from "@ant-design/icons-svg/es/asn/RadarChartOutlined";
import AntdIcon from '../components/AntdIcon';
var RadarChartOutlined = function RadarChartOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: RadarChartOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(RadarChartOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RadarChartOutlined';
}
export default RefIcon;