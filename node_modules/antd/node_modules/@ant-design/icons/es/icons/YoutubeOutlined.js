import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import YoutubeOutlinedSvg from "@ant-design/icons-svg/es/asn/YoutubeOutlined";
import AntdIcon from '../components/AntdIcon';
var YoutubeOutlined = function YoutubeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: YoutubeOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(YoutubeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'YoutubeOutlined';
}
export default RefIcon;