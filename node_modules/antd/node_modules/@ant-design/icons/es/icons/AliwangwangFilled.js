import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AliwangwangFilledSvg from "@ant-design/icons-svg/es/asn/AliwangwangFilled";
import AntdIcon from '../components/AntdIcon';
var AliwangwangFilled = function AliwangwangFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AliwangwangFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(AliwangwangFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AliwangwangFilled';
}
export default RefIcon;