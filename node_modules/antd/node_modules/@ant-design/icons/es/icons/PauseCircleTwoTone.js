import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PauseCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/PauseCircleTwoTone";
import AntdIcon from '../components/AntdIcon';
var PauseCircleTwoTone = function PauseCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PauseCircleTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PauseCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PauseCircleTwoTone';
}
export default RefIcon;