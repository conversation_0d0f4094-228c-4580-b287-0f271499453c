import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ReconciliationOutlinedSvg from "@ant-design/icons-svg/es/asn/ReconciliationOutlined";
import AntdIcon from '../components/AntdIcon';
var ReconciliationOutlined = function ReconciliationOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ReconciliationOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ReconciliationOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ReconciliationOutlined';
}
export default RefIcon;