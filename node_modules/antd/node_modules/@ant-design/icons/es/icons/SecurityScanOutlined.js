import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SecurityScanOutlinedSvg from "@ant-design/icons-svg/es/asn/SecurityScanOutlined";
import AntdIcon from '../components/AntdIcon';
var SecurityScanOutlined = function SecurityScanOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SecurityScanOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SecurityScanOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SecurityScanOutlined';
}
export default RefIcon;