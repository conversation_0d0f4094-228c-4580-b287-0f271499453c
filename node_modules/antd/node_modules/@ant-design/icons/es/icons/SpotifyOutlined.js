import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SpotifyOutlinedSvg from "@ant-design/icons-svg/es/asn/SpotifyOutlined";
import AntdIcon from '../components/AntdIcon';
var SpotifyOutlined = function SpotifyOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SpotifyOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SpotifyOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SpotifyOutlined';
}
export default RefIcon;