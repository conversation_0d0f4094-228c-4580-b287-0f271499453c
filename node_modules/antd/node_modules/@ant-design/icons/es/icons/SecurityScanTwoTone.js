import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SecurityScanTwoToneSvg from "@ant-design/icons-svg/es/asn/SecurityScanTwoTone";
import AntdIcon from '../components/AntdIcon';
var SecurityScanTwoTone = function SecurityScanTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SecurityScanTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SecurityScanTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SecurityScanTwoTone';
}
export default RefIcon;