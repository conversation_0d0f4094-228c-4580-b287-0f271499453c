import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import OneToOneOutlinedSvg from "@ant-design/icons-svg/es/asn/OneToOneOutlined";
import AntdIcon from '../components/AntdIcon';
var OneToOneOutlined = function OneToOneOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: OneToOneOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(OneToOneOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'OneToOneOutlined';
}
export default RefIcon;