import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PhoneTwoToneSvg from "@ant-design/icons-svg/es/asn/PhoneTwoTone";
import AntdIcon from '../components/AntdIcon';
var PhoneTwoTone = function PhoneTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PhoneTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PhoneTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PhoneTwoTone';
}
export default RefIcon;