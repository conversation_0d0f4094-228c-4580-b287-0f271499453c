import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import CreditCardFilledSvg from "@ant-design/icons-svg/es/asn/CreditCardFilled";
import AntdIcon from '../components/AntdIcon';
var CreditCardFilled = function CreditCardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: CreditCardFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(CreditCardFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CreditCardFilled';
}
export default RefIcon;