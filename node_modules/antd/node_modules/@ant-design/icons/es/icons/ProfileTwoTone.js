import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ProfileTwoToneSvg from "@ant-design/icons-svg/es/asn/ProfileTwoTone";
import AntdIcon from '../components/AntdIcon';
var ProfileTwoTone = function ProfileTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ProfileTwoToneSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ProfileTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ProfileTwoTone';
}
export default RefIcon;