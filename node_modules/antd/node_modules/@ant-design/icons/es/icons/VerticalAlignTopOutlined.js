import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import VerticalAlignTopOutlinedSvg from "@ant-design/icons-svg/es/asn/VerticalAlignTopOutlined";
import AntdIcon from '../components/AntdIcon';
var VerticalAlignTopOutlined = function VerticalAlignTopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: VerticalAlignTopOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(VerticalAlignTopOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'VerticalAlignTopOutlined';
}
export default RefIcon;