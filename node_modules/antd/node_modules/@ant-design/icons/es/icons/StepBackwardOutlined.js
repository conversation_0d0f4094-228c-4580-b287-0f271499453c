import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import StepBackwardOutlinedSvg from "@ant-design/icons-svg/es/asn/StepBackwardOutlined";
import AntdIcon from '../components/AntdIcon';
var StepBackwardOutlined = function StepBackwardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: StepBackwardOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(StepBackwardOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'StepBackwardOutlined';
}
export default RefIcon;