import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ScheduleOutlinedSvg from "@ant-design/icons-svg/es/asn/ScheduleOutlined";
import AntdIcon from '../components/AntdIcon';
var ScheduleOutlined = function ScheduleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ScheduleOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ScheduleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ScheduleOutlined';
}
export default RefIcon;