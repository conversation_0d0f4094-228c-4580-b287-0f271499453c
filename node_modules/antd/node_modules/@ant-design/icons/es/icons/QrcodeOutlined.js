import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import QrcodeOutlinedSvg from "@ant-design/icons-svg/es/asn/QrcodeOutlined";
import AntdIcon from '../components/AntdIcon';
var QrcodeOutlined = function QrcodeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: QrcodeOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(QrcodeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'QrcodeOutlined';
}
export default RefIcon;