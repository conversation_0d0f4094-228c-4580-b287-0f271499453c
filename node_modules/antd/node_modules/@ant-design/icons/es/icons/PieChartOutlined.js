import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import Pie<PERSON>hartOutlinedSvg from "@ant-design/icons-svg/es/asn/PieChartOutlined";
import AntdIcon from '../components/AntdIcon';
var PieChartOutlined = function PieChartOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PieChartOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PieChartOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PieChartOutlined';
}
export default RefIcon;