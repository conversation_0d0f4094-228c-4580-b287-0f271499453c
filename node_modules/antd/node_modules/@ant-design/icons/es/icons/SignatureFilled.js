import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import SignatureFilledSvg from "@ant-design/icons-svg/es/asn/SignatureFilled";
import AntdIcon from '../components/AntdIcon';
var SignatureFilled = function SignatureFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: SignatureFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(SignatureFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SignatureFilled';
}
export default RefIcon;