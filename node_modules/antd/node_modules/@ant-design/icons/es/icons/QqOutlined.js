import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import QqOutlinedSvg from "@ant-design/icons-svg/es/asn/QqOutlined";
import AntdIcon from '../components/AntdIcon';
var QqOutlined = function QqOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: QqOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(QqOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'QqOutlined';
}
export default RefIcon;