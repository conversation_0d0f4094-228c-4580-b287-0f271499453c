import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PauseOutlinedSvg from "@ant-design/icons-svg/es/asn/PauseOutlined";
import AntdIcon from '../components/AntdIcon';
var PauseOutlined = function PauseOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: PauseOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(PauseOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PauseOutlined';
}
export default RefIcon;