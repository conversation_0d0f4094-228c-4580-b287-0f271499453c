import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import BaiduOutlinedSvg from "@ant-design/icons-svg/es/asn/BaiduOutlined";
import AntdIcon from '../components/AntdIcon';
var BaiduOutlined = function BaiduOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: BaiduOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(BaiduOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BaiduOutlined';
}
export default RefIcon;