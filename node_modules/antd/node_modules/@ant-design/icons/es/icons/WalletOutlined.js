import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import WalletOutlinedSvg from "@ant-design/icons-svg/es/asn/WalletOutlined";
import AntdIcon from '../components/AntdIcon';
var WalletOutlined = function WalletOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: WalletOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(WalletOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'WalletOutlined';
}
export default RefIcon;