import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import YuqueFilledSvg from "@ant-design/icons-svg/es/asn/YuqueFilled";
import AntdIcon from '../components/AntdIcon';
var YuqueFilled = function YuqueFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: YuqueFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(YuqueFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'YuqueFilled';
}
export default RefIcon;