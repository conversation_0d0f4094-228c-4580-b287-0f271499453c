import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import MoonOutlinedSvg from "@ant-design/icons-svg/es/asn/MoonOutlined";
import AntdIcon from '../components/AntdIcon';
var MoonOutlined = function MoonOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: MoonOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(MoonOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MoonOutlined';
}
export default RefIcon;