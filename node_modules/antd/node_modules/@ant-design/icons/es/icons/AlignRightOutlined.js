import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import AlignRightOutlinedSvg from "@ant-design/icons-svg/es/asn/AlignRightOutlined";
import AntdIcon from '../components/AntdIcon';
var AlignRightOutlined = function AlignRightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: AlignRightOutlinedSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(AlignRightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AlignRightOutlined';
}
export default RefIcon;