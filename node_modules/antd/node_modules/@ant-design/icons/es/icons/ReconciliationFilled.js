import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ReconciliationFilledSvg from "@ant-design/icons-svg/es/asn/ReconciliationFilled";
import AntdIcon from '../components/AntdIcon';
var ReconciliationFilled = function ReconciliationFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {
    ref: ref,
    icon: ReconciliationFilledSvg
  }));
};
var RefIcon = /*#__PURE__*/React.forwardRef(ReconciliationFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ReconciliationFilled';
}
export default RefIcon;