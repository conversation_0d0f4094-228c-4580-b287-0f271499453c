{"name": "antd", "version": "4.24.16", "description": "An enterprise-class UI design language and React components implementation", "title": "Ant Design", "keywords": ["ant", "component", "components", "design", "framework", "frontend", "react", "react-component", "ui"], "homepage": "https://ant.design", "bugs": {"url": "https://github.com/ant-design/ant-design/issues"}, "repository": {"type": "git", "url": "https://github.com/ant-design/ant-design"}, "license": "MIT", "contributors": ["ant"], "funding": {"type": "opencollective", "url": "https://opencollective.com/ant-design"}, "files": ["dist", "lib", "es"], "sideEffects": ["dist/*", "es/**/style/*", "lib/**/style/*", "*.less"], "main": "lib/index.js", "module": "es/index.js", "unpkg": "dist/antd.min.js", "typings": "lib/index.d.ts", "scripts": {"prepare": "husky install", "api-collection": "antd-tools run api-collection", "authors": "node ./scripts/generate-authors", "build": "npm run compile && NODE_OPTIONS='--max-old-space-size=4096' npm run dist", "bundlesize": "bundlesize --enable-github-checks", "size-limit": "size-limit", "check-commit": "node ./scripts/check-commit", "check-ts-demo": "node ./scripts/check-ts-demo", "clean": "antd-tools run clean && rm -rf es lib coverage dist report.html", "clean-lockfiles": "rm -rf package-lock.json yarn.lock", "prestart": "npm run version", "precompile": "npm run version", "pretest": "npm run version", "predist": "npm run version", "presite": "npm run version", "pretsc": "npm run version", "compile": "npm run clean && antd-tools run compile", "changelog": "node ./scripts/print-changelog", "predeploy": "antd-tools run clean && npm run site && cp CNAME _site && npm run site:test", "deploy": "gh-pages -d _site -r **************:ant-design/4x.ant.design.git -b gh-pages", "deploy:china-mirror": "git checkout gh-pages && git pull origin gh-pages && <NAME_EMAIL>:ant-design/ant-design.git gh-pages", "dist": "antd-tools run dist", "dist:esbuild": "ESBUILD=true npm run dist", "dist:esbuild-no-dup-check": "ESBUILD=true NO_DUP_CHECK=true npm run dist", "lint": "npm run tsc && npm run lint:script && npm run lint:demo && npm run lint:style && npm run lint:deps && npm run lint:md", "lint-fix": "npm run lint-fix:script && npm run lint-fix:demo && npm run lint-fix:style", "lint-fix:demo": "npm run lint:demo -- --fix", "lint-fix:script": "npm run lint:script -- --fix", "lint-fix:style": "npm run lint:style -- --fix", "lint:demo": "eslint components/*/demo/*.md", "lint:deps": "antd-tools run deps-lint", "lint:md": "remark . -f -q", "lint:script": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:style": "stylelint '{site,components}/**/*.less'", "pre-publish": "npm run test-all -- --skip-build && node ./scripts/pre-publish-notice.js", "prettier": "prettier -c --write **/*", "rome:format": "rome format --write .", "pub": "npm run version && antd-tools run pub", "prepublishOnly": "antd-tools run guard", "postpublish": "node ./scripts/post-script.js", "site:theme": "npm run site:theme-dark && npm run site:theme-compact", "site:theme-dark": "cross-env ESBUILD=1 ANT_THEME=dark bisheng build -c ./site/bisheng.config.js", "site:theme-compact": "cross-env ESBUILD=1 ANT_THEME=compact bisheng build -c ./site/bisheng.config.js", "site": "npm run site:theme && cross-env NODE_ICU_DATA=node_modules/full-icu ESBUILD=1 bisheng build --ssr -c ./site/bisheng.config.js", "sort": "npx sort-package-json", "sort-api": "antd-tools run sort-api-table", "start": "antd-tools run clean && cross-env NODE_ENV=development concurrently \"bisheng start -c ./site/bisheng.config.js\"", "test": "jest --config .jest.js --cache=false", "test:update": "jest --config .jest.js --cache=false -u", "test-all": "sh -e ./scripts/test-all.sh", "test-node": "jest --config .jest.node.js --cache=false", "tsc": "tsc --noEmit", "site:test": "jest --config .jest.site.js --cache=false --force-exit", "test-image": "jest --config .jest.image.js --no-cache -i -u", "version": "node ./scripts/generate-version", "install-react-16": "npm i --no-save --legacy-peer-deps react@16 react-dom@16", "install-react-17": "npm i --no-save --legacy-peer-deps react@17 react-dom@17", "install-react-18": "npm i --no-save --legacy-peer-deps react@18 react-dom@18 @testing-library/react@13", "fix-memory-limit": "cross-env LIMIT=10240 increase-memory-limit"}, "browserslist": ["> 0.5%", "last 2 versions", "Firefox ESR", "not dead", "IE 11", "not IE 10"], "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons": "^4.8.2", "@ant-design/react-slick": "~1.0.2", "@babel/runtime": "^7.18.3", "@ctrl/tinycolor": "^3.6.1", "classnames": "^2.2.6", "copy-to-clipboard": "^3.2.0", "lodash": "^4.17.21", "moment": "^2.29.2", "rc-cascader": "~3.7.3", "rc-checkbox": "~3.0.1", "rc-collapse": "~3.4.2", "rc-dialog": "~9.0.2", "rc-drawer": "~6.3.0", "rc-dropdown": "~4.0.1", "rc-field-form": "~1.38.2", "rc-image": "~5.13.0", "rc-input": "~0.1.4", "rc-input-number": "~7.3.11", "rc-mentions": "~1.13.1", "rc-menu": "~9.8.4", "rc-motion": "^2.9.0", "rc-notification": "~4.6.1", "rc-pagination": "~3.2.0", "rc-picker": "~2.7.6", "rc-progress": "~3.4.2", "rc-rate": "~2.9.3", "rc-resize-observer": "^1.3.1", "rc-segmented": "~2.3.0", "rc-select": "~14.1.18", "rc-slider": "~10.0.1", "rc-steps": "~5.0.0", "rc-switch": "~3.2.2", "rc-table": "~7.26.0", "rc-tabs": "~12.5.10", "rc-textarea": "~0.4.7", "rc-tooltip": "~5.2.2", "rc-tree": "~5.7.12", "rc-tree-select": "~5.5.5", "rc-trigger": "^5.3.4", "rc-upload": "~4.3.6", "rc-util": "^5.37.0", "scroll-into-view-if-needed": "^2.2.25"}, "devDependencies": {"@ant-design/bisheng-plugin": "^3.3.0-alpha.4", "@ant-design/hitu": "^0.0.0-alpha.13", "@ant-design/tools": "^15.1.2", "@docsearch/css": "^3.0.0", "@qixian.cs/github-contributors-list": "^1.0.3", "@size-limit/file": "^8.0.0", "@stackblitz/sdk": "~1.8.2", "@testing-library/dom": "^9.0.0", "@testing-library/jest-dom": "^5.16.3", "@testing-library/react": "^12.0.0", "@testing-library/user-event": "^14.4.2", "@types/gtag.js": "^0.0.12", "@types/jest": "^29.0.0", "@types/jest-axe": "^3.5.3", "@types/jest-environment-puppeteer": "^5.0.0", "@types/jest-image-snapshot": "^5.1.0", "@types/jquery": "^3.5.14", "@types/lodash": "^4.14.139", "@types/puppeteer": "^7.0.4", "@types/qs": "^6.9.7", "@types/react": "^18.0.0", "@types/react-color": "^3.0.1", "@types/react-copy-to-clipboard": "^5.0.0", "@types/react-dom": "^18.0.0", "@types/react-highlight-words": "^0.16.4", "@types/react-resizable": "^3.0.0", "@types/react-sticky": "^6.0.4", "@types/react-window": "^1.8.2", "@types/warning": "^3.0.0", "@typescript-eslint/eslint-plugin": "^5.40.0", "@typescript-eslint/parser": "^5.40.0", "antd": "4.x", "antd-img-crop": "^4.2.8", "array-move": "^4.0.0", "babel-plugin-add-react-displayname": "^0.0.5", "bisheng": "^3.7.0-alpha.4", "bisheng-plugin-description": "^0.1.4", "bisheng-plugin-react": "^1.2.0", "bisheng-plugin-toc": "^0.4.4", "bundlesize2": "^0.0.31", "chalk": "^4.0.0", "cheerio": "1.0.0-rc.12", "concurrently": "^7.0.0", "cross-env": "^7.0.0", "css-minimizer-webpack-plugin": "^1.3.0", "dekko": "^0.2.1", "docsearch-react-fork": "^0.0.0-alpha.0", "docsearch.js": "^2.6.3", "duplicate-package-checker-webpack-plugin": "^3.0.0", "enquire-js": "^0.2.1", "esbuild-loader": "^2.13.1", "eslint": "^8.0.0", "eslint-config-airbnb": "^19.0.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-compat": "~4.0.0", "eslint-plugin-import": "^2.21.1", "eslint-plugin-jest": "^27.0.1", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-markdown": "^3.0.0", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "^4.1.2", "eslint-plugin-unicorn": "^47.0.0", "fast-glob": "^3.2.11", "fetch-jsonp": "^1.1.3", "fs-extra": "^10.0.0", "full-icu": "^1.3.0", "gh-pages": "^5.0.0", "glob": "^8.0.1", "http-server": "^14.0.0", "husky": "^8.0.1", "identity-obj-proxy": "^3.0.0", "immer": "^9.0.1", "immutability-helper": "^3.0.0", "increase-memory-limit": "^1.0.7", "inquirer": "^9.1.2", "intersection-observer": "^0.12.0", "isomorphic-fetch": "^3.0.0", "jest": "^29.4.1", "jest-axe": "^7.0.0", "jest-environment-jsdom": "^29.0.1", "jest-environment-node": "^29.0.0", "jest-image-snapshot": "^6.0.0", "jest-puppeteer": "^6.0.0", "jquery": "^3.4.1", "jsdom": "^20.0.0", "jsonml.js": "^0.1.0", "less-vars-to-js": "^1.3.0", "lint-staged": "^13.0.3", "lz-string": "^1.4.4", "mini-css-extract-plugin": "^1.6.2", "mockdate": "^3.0.0", "open": "^8.0.1", "node-notifier": "^10.0.1", "prettier": "^2.3.2", "prettier-plugin-jsdoc": "^0.4.2", "pretty-format": "^29.0.0", "qs": "^6.10.1", "rc-footer": "^0.6.6", "rc-tween-one": "^3.0.3", "rc-virtual-list": "^3.4.11", "react": "^17.0.0", "react-color": "^2.17.3", "react-copy-to-clipboard": "^5.0.1", "react-dnd": "^16.0.0", "react-dnd-html5-backend": "^16.0.0", "react-dom": "^17.0.0", "react-draggable": "^4.4.3", "react-fast-marquee": "^1.2.1", "react-github-button": "^0.1.11", "react-helmet-async": "~1.3.0", "react-highlight-words": "^0.18.0", "react-infinite-scroll-component": "^6.1.0", "react-intl": "^6.0.1", "react-resizable": "^3.0.1", "react-router-dom": "^6.0.2", "react-sortable-hoc": "^2.0.0", "react-sticky": "^6.0.3", "react-window": "^1.8.5", "remark": "^14.0.1", "remark-cli": "^11.0.0", "remark-lint": "^9.0.0", "remark-preset-lint-recommended": "^6.0.0", "remove-files-webpack-plugin": "1.5.0", "rimraf": "^3.0.0", "rome": "^10.0.1", "scrollama": "^3.0.0", "semver": "^7.3.5", "simple-git": "^3.0.0", "size-limit": "^8.0.0", "stylelint": "^14.9.0", "stylelint-config-prettier": "^9.0.2", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^29.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.1.0", "stylelint-order": "^5.0.0", "theme-switcher": "^1.0.2", "typescript": "~4.9.3", "webpack-bundle-analyzer": "^4.1.0", "xhr-mock": "^2.4.1", "yaml-front-matter": "^4.0.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "size-limit": [{"path": "./dist/antd.min.js", "limit": "286 KiB"}, {"path": "./dist/antd.min.css", "limit": "70 KiB"}, {"path": "./dist/antd.dark.min.css", "limit": "70 KiB"}, {"path": "./dist/antd.compact.min.css", "limit": "70 KiB"}, {"path": "./dist/antd.variable.min.css", "limit": "70 KiB"}], "bundlesize": [{"path": "./dist/antd.min.js", "maxSize": "286 kB"}, {"path": "./dist/antd.min.css", "maxSize": "70 kB"}, {"path": "./dist/antd.dark.min.css", "maxSize": "70 kB"}, {"path": "./dist/antd.compact.min.css", "maxSize": "70 kB"}, {"path": "./dist/antd.variable.min.css", "maxSize": "70 kB"}], "tnpm": {"mode": "npm"}, "lint-staged": {"*.{ts,tsx,js}": "rome format --write", "*.{json,less,md}": "prettier --ignore-unknown --write"}}