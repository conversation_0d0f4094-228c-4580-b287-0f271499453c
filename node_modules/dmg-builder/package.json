{"name": "dmg-builder", "version": "26.0.12", "main": "out/dmgUtil.js", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/dmg-builder"}, "bugs": "https://github.com/electron-userland/electron-builder/issues", "homepage": "https://github.com/electron-userland/electron-builder", "files": ["out", "templates", "vendor"], "dependencies": {"fs-extra": "^10.1.0", "iconv-lite": "^0.6.2", "js-yaml": "^4.1.0", "app-builder-lib": "26.0.12", "builder-util": "26.0.11", "builder-util-runtime": "9.3.1"}, "optionalDependencies": {"dmg-license": "^1.0.11"}, "devDependencies": {"@types/fs-extra": "9.0.13", "@types/js-yaml": "4.0.3", "temp-file": "3.4.0"}, "typings": "./out/dmg.d.ts"}