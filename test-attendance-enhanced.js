const mongoose = require('mongoose');

// 连接数据库
mongoose.connect('mongodb://localhost:27017/hrms', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// 导入模型
const Attendance = require('./backend/models/Attendance');
const Employee = require('./backend/models/Employee');

async function testEnhancedAttendance() {
  try {
    console.log('🚀 开始测试增强的考勤系统...\n');

    // 1. 测试新的考勤数据模型
    console.log('1. 测试新的考勤数据模型');
    
    // 查找一个测试员工
    const testEmployee = await Employee.findOne();
    if (!testEmployee) {
      console.log('❌ 没有找到测试员工，请先添加员工数据');
      return;
    }
    
    console.log(`✅ 找到测试员工: ${testEmployee.name} (${testEmployee.employeeId})`);

    // 2. 创建测试考勤记录
    console.log('\n2. 创建测试考勤记录');
    
    const testDate = new Date();
    testDate.setHours(0, 0, 0, 0);
    
    const testAttendance = new Attendance({
      employeeId: testEmployee._id,
      employeeCode: testEmployee.employeeId,
      name: testEmployee.name,
      customId: 'CUSTOM001',
      date: testDate,
      
      // 时间相关字段
      workStartTime: new Date(testDate.getTime() + 8.5 * 60 * 60 * 1000), // 08:30
      workEndTime: new Date(testDate.getTime() + 17.5 * 60 * 60 * 1000), // 17:30
      checkInTime: new Date(testDate.getTime() + 8.75 * 60 * 60 * 1000), // 08:45 (迟到15分钟)
      checkOutTime: new Date(testDate.getTime() + 18 * 60 * 60 * 1000), // 18:00 (加班30分钟)
      
      // 应到实到
      shouldAttend: '1',
      actualAttend: '1',
      
      // 迟到早退
      lateTime: '00:15', // 迟到15分钟
      earlyTime: '00:00', // 没有早退
      
      // 工作时间统计
      workTime: '8.5', // 8.5小时
      attendanceTime: '9.25', // 实际出勤9.25小时
      overtimeTime: '0.5', // 加班0.5小时
      
      // 加班分类
      weekdayOvertime: '0.5',
      weekendOvertime: '0',
      holidayOvertime: '0',
      
      // 其他字段
      isAbsent: false,
      isSmartSchedule: true,
      timeSlot: '标准班次',
      exception: '',
      shouldCheckIn: '08:30',
      shouldCheckOut: '17:30',
      department: testEmployee.department,
      workdayType: '平日',
      
      // 计算字段
      calculatedWorkHours: 8.5,
      calculatedOvertimeHours: 0.5,
      
      // 兼容字段
      status: '迟到'
    });

    await testAttendance.save();
    console.log('✅ 测试考勤记录创建成功');

    // 3. 验证数据完整性
    console.log('\n3. 验证数据完整性');
    
    const savedRecord = await Attendance.findOne({ 
      employeeId: testEmployee._id, 
      date: testDate 
    }).populate('employeeId');
    
    if (savedRecord) {
      console.log('✅ 考勤记录保存成功');
      console.log(`   员工: ${savedRecord.name} (${savedRecord.employeeCode})`);
      console.log(`   日期: ${savedRecord.date.toLocaleDateString()}`);
      console.log(`   签到时间: ${savedRecord.checkInTime ? savedRecord.checkInTime.toLocaleTimeString() : '未签到'}`);
      console.log(`   签退时间: ${savedRecord.checkOutTime ? savedRecord.checkOutTime.toLocaleTimeString() : '未签退'}`);
      console.log(`   工作时长: ${savedRecord.workTime}小时`);
      console.log(`   加班时长: ${savedRecord.overtimeTime}小时`);
      console.log(`   迟到时间: ${savedRecord.lateTime}`);
      console.log(`   考勤状态: ${savedRecord.status}`);
      console.log(`   工作日类型: ${savedRecord.workdayType}`);
      console.log(`   部门: ${savedRecord.department}`);
    } else {
      console.log('❌ 考勤记录保存失败');
    }

    // 4. 测试查询功能
    console.log('\n4. 测试查询功能');
    
    // 按月份查询
    const startOfMonth = new Date(testDate.getFullYear(), testDate.getMonth(), 1);
    const endOfMonth = new Date(testDate.getFullYear(), testDate.getMonth() + 1, 0, 23, 59, 59);
    
    const monthlyRecords = await Attendance.find({
      date: { $gte: startOfMonth, $lte: endOfMonth }
    }).populate('employeeId');
    
    console.log(`✅ 本月考勤记录数量: ${monthlyRecords.length}`);
    
    // 统计分析
    let totalWorkHours = 0;
    let totalOvertimeHours = 0;
    let lateCount = 0;
    let absentCount = 0;
    
    monthlyRecords.forEach(record => {
      if (record.calculatedWorkHours) {
        totalWorkHours += record.calculatedWorkHours;
      }
      if (record.calculatedOvertimeHours) {
        totalOvertimeHours += record.calculatedOvertimeHours;
      }
      if (record.lateTime && record.lateTime !== '00:00' && record.lateTime !== '0') {
        lateCount++;
      }
      if (record.isAbsent) {
        absentCount++;
      }
    });
    
    console.log(`   总工作时长: ${totalWorkHours.toFixed(1)}小时`);
    console.log(`   总加班时长: ${totalOvertimeHours.toFixed(1)}小时`);
    console.log(`   迟到次数: ${lateCount}次`);
    console.log(`   缺勤次数: ${absentCount}次`);

    // 5. 测试新字段的查询
    console.log('\n5. 测试新字段查询');
    
    const smartScheduleRecords = await Attendance.find({ isSmartSchedule: true });
    console.log(`✅ 智能排班记录数量: ${smartScheduleRecords.length}`);
    
    const weekdayRecords = await Attendance.find({ workdayType: '平日' });
    console.log(`✅ 平日考勤记录数量: ${weekdayRecords.length}`);
    
    const overtimeRecords = await Attendance.find({ 
      calculatedOvertimeHours: { $gt: 0 } 
    });
    console.log(`✅ 有加班的记录数量: ${overtimeRecords.length}`);

    console.log('\n🎉 增强的考勤系统测试完成！');
    console.log('\n主要改进:');
    console.log('✅ 1. 增加了签到时间和签退时间字段');
    console.log('✅ 2. 实际出勤天数统计来自上传文件的日期数据');
    console.log('✅ 3. 综合数据统计分析，判定员工考勤情况');
    console.log('✅ 4. 总工时数据来自上传文件的出勤时间统计');
    console.log('✅ 5. 支持所有上传文件的列字段');
    console.log('✅ 6. 前端界面增加了详细考勤记录展开功能');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    mongoose.connection.close();
  }
}

// 运行测试
testEnhancedAttendance(); 