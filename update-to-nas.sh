#!/bin/bash
# 更新指定文件到NAS容器的脚本
# 使用方式: ./update-to-nas.sh frontend/src/components/salary/SalaryPrecalculatorModal.js frontend/src/components/salary/SalaryPrecalculator.css

# 配置信息（与deploy-to-nas.sh相同）
NAS_IP="************"
NAS_USER="yangpeng"
DOCKER_CMD="/share/CACHEDEV1_DATA/.qpkg/container-station/usr/bin/.libs/docker"
NAS_PATH="/share/CACHEDEV2_DATA/Container/container-station-data/application/moco-hr"
APP_CONTAINER_NAME="node-app"

# 检查参数
if [ $# -eq 0 ]; then
    echo "错误: 请指定要更新的文件"
    echo "使用方式: $0 <文件路径1> [文件路径2...]"
    exit 1
fi

echo "=== 开始更新文件到NAS ($NAS_IP) ==="

# 创建临时目录
TMP_DIR=$(mktemp -d)
echo "创建临时目录: $TMP_DIR"

# 对于前端文件，我们需要先构建
if [[ "$*" == *"frontend/src"* ]]; then
    echo "检测到前端文件变更，运行前端构建..."
    (cd frontend && npm run build) || {
        echo "前端构建失败"
        rm -rf "$TMP_DIR"
        exit 1
    }
    echo "✅ 前端构建完成"
fi

# 复制要更新的文件到临时目录，保持目录结构
for file in "$@"; do
    # 如果是前端源文件，我们需要使用构建后的文件
    if [[ $file == frontend/src/* ]]; then
        echo "前端源文件 $file 已通过构建进行处理"
        # 这里不做直接复制，因为我们将复制整个构建目录
    else
        # 为非前端文件创建目录结构
        DIR_PATH=$(dirname "$file")
        mkdir -p "$TMP_DIR/$DIR_PATH"
        cp "$file" "$TMP_DIR/$DIR_PATH/" || {
            echo "复制 $file 失败"
            rm -rf "$TMP_DIR"
            exit 1
        }
        echo "复制 $file 到临时目录"
    fi
done

# 对于前端文件，复制整个构建目录
if [[ "$*" == *"frontend/src"* ]]; then
    mkdir -p "$TMP_DIR/public"
    cp -r frontend/build/* "$TMP_DIR/public/" || {
        echo "复制前端构建文件失败"
        rm -rf "$TMP_DIR"
        exit 1
    }
    echo "复制前端构建文件到临时目录"
fi

# 创建打包文件
TAR_FILE="update-files.tar.gz"
(cd "$TMP_DIR" && tar -czf "$TAR_FILE" .) || {
    echo "打包文件失败"
    rm -rf "$TMP_DIR"
    exit 1
}
echo "创建更新包: $TAR_FILE"

# 上传到NAS
scp "$TMP_DIR/$TAR_FILE" "$NAS_USER@$NAS_IP:$NAS_PATH/" || {
    echo "上传文件失败"
    rm -rf "$TMP_DIR"
    exit 1
}
echo "上传更新包到NAS"

# 在NAS上更新文件
ssh "$NAS_USER@$NAS_IP" << EOF
cd $NAS_PATH || exit 1
tar -xzf $TAR_FILE || exit 1
rm $TAR_FILE

echo "更新Docker容器中的文件..."
$DOCKER_CMD cp public/. $APP_CONTAINER_NAME:/app/public/
# 如果包含后端文件，也复制到容器
for file in \$(find . -type f -not -path "./public/*" -not -name "$TAR_FILE"); do
    echo "更新容器文件: \$file"
    $DOCKER_CMD cp "\$file" "$APP_CONTAINER_NAME:/app/\$file"
done

echo "重启容器..."
$DOCKER_CMD restart $APP_CONTAINER_NAME
EOF

# 清理临时文件
rm -rf "$TMP_DIR"
echo "清理临时文件"

echo "✅ 更新完成！容器已重启，文件已更新。"
echo "更新的文件:"
for file in "$@"; do
    echo "- $file"
done 