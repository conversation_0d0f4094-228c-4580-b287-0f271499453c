const mongoose = require('mongoose');

const connectDB = async () => {
    try {
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/hrmsdb';
        
        console.log(`尝试连接到MongoDB: ${mongoUri}`);
        
        await mongoose.connect(mongoUri, {
            serverSelectionTimeoutMS: 5000,
            socketTimeoutMS: 45000,
            family: 4,
            retryWrites: true,
            retryReads: true
        });

        mongoose.connection.on('error', err => {
            console.error('MongoDB 连接错误:', err);
        });

        mongoose.connection.on('disconnected', () => {
            console.log('MongoDB 连接断开，尝试重新连接...');
            setTimeout(connectDB, 5000);
        });

        console.log('MongoDB 连接成功');
    } catch (error) {
        console.error('MongoDB 连接失败:', error.message);
        console.log('5秒后尝试重新连接...');
        setTimeout(connectDB, 5000);
    }
};

module.exports = connectDB;