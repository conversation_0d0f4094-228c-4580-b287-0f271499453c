function adminMiddleware(req, res, next) {
    // 检查用户是否为管理员
    console.log('[Admin Check] 检查用户权限:', {
        userId: req.user?.userId,
        username: req.user?.username,
        role: req.user?.role,
        path: req.originalUrl,
        method: req.method
    });

    if (req.user && req.user.role === 'admin') {
        console.log(`[Admin Access] 管理员访问成功: ${req.user.username} (${req.user.userId})`);
        next();
    } else {
        console.log(`[Admin Access Denied] 非管理员尝试访问: ${req.user?.username || '未知用户'}`);
        return res.status(403).json({ message: '权限不足，需要管理员权限' });
    }
}

module.exports = adminMiddleware;
