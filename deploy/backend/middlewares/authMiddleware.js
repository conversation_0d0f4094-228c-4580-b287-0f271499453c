const jwt = require('jsonwebtoken');
const User = require('../models/User');

// 认证中间件
const authMiddleware = async (req, res, next) => {
    try {
        // 从请求头中获取 token
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({ message: '未提供认证令牌' });
        }

        const token = authHeader.split(' ')[1];
        if (!token) {
            return res.status(401).json({ message: '未提供认证令牌' });
        }

        // 验证 token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

        // 查找用户
        const user = await User.findById(decoded.userId);
        if (!user) {
            return res.status(401).json({ message: '用户不存在' });
        }

        // 将用户信息添加到请求对象
        req.user = {
            userId: user._id,
            username: user.username,
            role: user.role
        };

        console.log(`[Auth] 用户认证成功: ${user.username} (${user._id}), 角色: ${user.role}`);
        next();
    } catch (error) {
        console.error('[Auth Error]', error);

        // 提供更详细的错误信息
        if (error.name === 'JsonWebTokenError') {
            console.error('[Auth Error] JWT 验证失败:', error.message);
            return res.status(401).json({ message: 'Token 无效', error: error.message });
        } else if (error.name === 'TokenExpiredError') {
            console.error('[Auth Error] JWT 已过期:', error.message);
            return res.status(401).json({ message: 'Token 已过期，请重新登录', error: error.message });
        } else {
            console.error('[Auth Error] 未知认证错误:', error.message);
            return res.status(401).json({ message: '认证失败', error: error.message });
        }
    }
};

module.exports = authMiddleware;
