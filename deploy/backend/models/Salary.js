const mongoose = require('mongoose');

const SalarySchema = new mongoose.Schema({
    employeeId: { type: String, required: true },
    name: { type: String, required: true },
    department: { type: String, required: true },
    subDepartment: { type: String, default: '' },
    positionType: { type: String, required: true },
    positionLevel: { type: String, required: true },
    administrativeLevel: { type: String, required: true},
    // 添加年份和月份字段
    year: { type: Number, required: true, default: () => new Date().getFullYear() },
    month: { type: Number, required: true, default: () => new Date().getMonth() + 1 },
    adjustedBaseSalary: { type: Number, required: true },
    originalBaseSalary: { type: Number },
    educationAdjustment: { type: Number, default: 0 },
    educationCoefficient: { type: Number, default: 1.0 },
    languageAdjustment: { type: Number, default: 0 },
    languageCoefficient: { type: Number, default: 1.0 },
    positionSalary: { type: Number, required: true },
    originalPositionSalary: { type: Number },
    adminSalary: { type: Number, required: true },
    originalAdminSalary: { type: Number },
    performanceBonus: { type: Number, required: true },
    originalPerformanceBonus: { type: Number },
    probationFactor: { type: Number, default: 1.0 },
    performanceCoefficient: { type: Number, required: true },
    actualAttendance: { type: Number, required: true },
    education: { type: String, required: true },
    languageLevel: { type: String, required: true },
    isProbation: { type: Boolean, default: false },
    workType: { type: String, default: '全职' },
    probationEndDate: { type: String, default: '' },
    specialAllowance: {
        remark: { type: String },
        amount: { type: Number, default: 0 }
    },
    specialDeduction: {
        amount: { type: Number, default: 0 }
    },
    calculationResult: {
        mealAllowance: Number,
        communicationAllowance: Number,
        socialInsurance: Number,
        tax: Number,
        totalMonthlySalary: Number,
        netSalary: Number,
        taxableIncome: Number,
        absenceDeduction: Number,
        attendanceAdjustment: Number,
        isProbation: Boolean,
        probationFactor: Number,
        deductions: {
            insurance: {
                pension: Number,
                medical: Number,
                unemployment: Number,
                housingFund: Number
            },
            tax: Number
        },
        companyCost: {
            insuranceCosts: {
                pension: Number,
                medical: Number,
                unemployment: Number,
                injury: Number,
                maternity: Number,
                housingFund: Number
            },
            totalCost: Number
        }
    },
    calculatedAt: { type: Date, default: Date.now }
}, { timestamps: true });
module.exports = mongoose.model('Salary', SalarySchema);