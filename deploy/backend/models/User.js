const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
    username: {
        type: String,
        required: true,
        unique: true,
        minlength: 3,
        maxlength: 20,
        match: /^[a-zA-Z0-9_]+$/ // 只允许字母、数字和下划线
    },
    password: {
        type: String,
        required: true,
        minlength: 6 // 最小长度为6位
    },
    phone: {
        type: String,
        validate: {
            validator: function(v) {
                return /^1[3-9]\d{9}$/.test(v); // 验证中国手机号码格式
            },
            message: props => `${props.value} 不是有效的手机号码!`
        },
        unique: true,
        sparse: true // 允许非必填字段不存在
    },
    resetPasswordToken: String,
    resetPasswordExpires: Date,
    role: {
        type: String,
        enum: ['admin', 'user'],
        default: 'user'
    }
}, {
    timestamps: true // 添加创建和更新时间戳
});

// 密码哈希中间件
userSchema.pre('save', async function(next) {
    if (this.isModified('password')) {
        this.password = await bcrypt.hash(this.password, 10);
    }
    next();
});

// 添加密码验证方法
userSchema.methods.comparePassword = async function(candidatePassword) {
    return bcrypt.compare(candidatePassword, this.password);
};

module.exports = mongoose.model('User', userSchema);