const jwt = require('jsonwebtoken');

// 简化版认证中间件，不查询数据库
// 这个版本直接从token中获取用户信息，不验证用户是否存在于数据库中
function authMiddleware(req, res, next) {
    try {
        // 记录请求路径和方法
        console.log(`[Auth] 收到需要认证的请求: ${req.method} ${req.originalUrl}`);

        // 记录所有请求头（调试用）
        console.log('[Auth] 请求头:', JSON.stringify({
            ...req.headers,
            // 如果有认证头，只显示部分内容
            authorization: req.headers.authorization ?
                req.headers.authorization.substring(0, 15) + '...' :
                undefined
        }, null, 2));

        // 从请求头中获取 token
        const authHeader = req.headers['authorization'];
        if (!authHeader) {
            console.error('[Auth Error] 请求头中没有Authorization字段');
            return res.status(401).json({ message: '未提供认证令牌' });
        }

        if (!authHeader.startsWith('Bearer ')) {
            console.error('[Auth Error] Authorization头格式不正确:', authHeader.substring(0, 15) + '...');
            return res.status(401).json({ message: '认证令牌格式不正确，应为Bearer格式' });
        }

        const token = authHeader.split(' ')[1];
        if (!token) {
            console.error('[Auth Error] Bearer后没有提供token');
            return res.status(401).json({ message: '未提供认证令牌' });
        }

        console.log(`[Auth] 成功提取token: ${token.substring(0, 10)}...`);

        // 验证 token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

        // 将解码后的用户信息添加到请求对象
        req.user = decoded;

        console.log(`[Auth] 用户认证成功: ${decoded.username} (${decoded.userId}), 角色: ${decoded.role}`);
        next();
    } catch (error) {
        console.error('[Auth Error]', error);

        // 提供更详细的错误信息
        if (error.name === 'JsonWebTokenError') {
            console.error('[Auth Error] JWT 验证失败:', error.message);
            return res.status(401).json({ message: 'Token 无效', error: error.message });
        } else if (error.name === 'TokenExpiredError') {
            console.error('[Auth Error] JWT 已过期:', error.message);
            return res.status(401).json({ message: 'Token 已过期，请重新登录', error: error.message });
        } else {
            console.error('[Auth Error] 未知认证错误:', error.message);
            return res.status(401).json({ message: '认证失败', error: error.message });
        }
    }
}

module.exports = authMiddleware;