const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const path = require('path');
const authRoutes = require('./routes/auth');
const employeeRoutes = require('./routes/employees');
const salaryRoutes = require('./routes/salaryRoutes');
const healthRoutes = require('./routes/healthRoutes');
const adminRoutes = require('./routes/adminRoutes');
const { rootHealthCheck } = require('./routes/healthRoutes');
const createInitialUsers = require('./utils/createInitialUsers');
const authMiddleware = require('./authMiddleware');
const adminMiddleware = require('./middlewares/adminMiddleware');

// Load environment variables
require('dotenv').config();

const app = express();

// 中间件配置
app.use(cors());
app.use(express.json());

// 添加根路径的健康检查
app.get('/health', rootHealthCheck);

// API路由（确保在静态文件服务之前）
app.use('/api/auth', authRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/salary', salaryRoutes);
// 临时移除认证要求，方便调试
app.use('/api/admin', adminRoutes);
app.use('/api', healthRoutes);

// 根据环境配置静态文件路径
const staticPath = process.env.NODE_ENV === 'production'
    ? path.join(__dirname, 'public')
    : path.join(__dirname, '../frontend/build');

// 确保API路由在静态文件服务之前定义
app.use('/api/health', (_req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date(),
        environment: process.env.NODE_ENV,
        mongodb_uri: process.env.MONGODB_URI ? process.env.MONGODB_URI.split('@').pop() : 'not_set'
    });
});

app.use(express.static(staticPath));
// 通配路由处理（放在最后）
app.get('*', (_req, res) => {
    const indexPath = process.env.NODE_ENV === 'production'
        ? path.join(__dirname, 'public/index.html')
        : path.join(__dirname, '../frontend/build/index.html');
    res.sendFile(indexPath);
});

// 直接连接到hrmsdb数据库
async function connectToHrmsDb() {
    try {
        // Force connection to hrmsdb
        const mongoUri = 'mongodb://localhost:27017/hrmsdb';
        console.log(`尝试连接到MongoDB: ${mongoUri}`);
        
        await mongoose.connect(mongoUri, {
            serverSelectionTimeoutMS: 5000,
            socketTimeoutMS: 45000,
            family: 4,
            retryWrites: true,
            retryReads: true
        });

        mongoose.connection.on('error', err => {
            console.error('MongoDB 连接错误:', err);
        });

        mongoose.connection.on('disconnected', () => {
            console.log('MongoDB 连接断开，尝试重新连接...');
            setTimeout(connectToHrmsDb, 5000);
        });

        console.log('MongoDB 连接成功');
        console.log('数据库名称:', mongoose.connection.db.databaseName);
        
        return true;
    } catch (error) {
        console.error('MongoDB 连接失败:', error.message);
        console.log('5秒后尝试重新连接...');
        setTimeout(connectToHrmsDb, 5000);
        return false;
    }
}

// 连接数据库并启动服务器
connectToHrmsDb()
.then(async (connected) => {
    if (!connected) {
        console.error('无法连接到数据库，服务器启动失败');
        process.exit(1);
    }
    
    // 创建初始用户
    await createInitialUsers();

    // 数据库连接成功后启动服务器
    const port = process.env.PORT || 5006;
    const host = process.env.HOST || '0.0.0.0';

    // 确保监听所有网络接口，而不仅仅是localhost
    app.listen(port, host, () => {
        console.log(`服务器启动成功，监听端口 ${port}，地址: ${host}`);
        console.log(`运行环境: ${process.env.NODE_ENV || 'development'}`);
        console.log(`健康检查地址: http://${host === '0.0.0.0' ? 'localhost' : host}:${port}/health`);
        console.log(`API地址: http://${host === '0.0.0.0' ? 'localhost' : host}:${port}/api`);
    });
})
.catch(err => {
    console.error('无法启动服务器:', err);
    process.exit(1);
});
