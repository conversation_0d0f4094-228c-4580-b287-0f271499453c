const express = require('express');
const router = express.Router();
const User = require('../models/User');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const { sendVerificationCode, verifyCode } = require('../utils/smsUtils');
const authMiddleware = require('../authMiddleware');

// 添加调试路由
router.get('/test', (_req, res) => {
    res.json({ message: 'Auth routes working' });
});

// 注册路由
router.post('/register', async (req, res) => {
    console.log('[Register Attempt] Received:', req.body);
    const { username, password, phone } = req.body;
    try {
        const existingUser = await User.findOne({ username });
        if (existingUser) {
            console.log(`[Register Failed] Username already exists: ${username}`);
            return res.status(400).json({ message: '用户名已存在' });
        }

        // 如果提供了手机号，检查是否已存在
        if (phone) {
            const existingPhone = await User.findOne({ phone });
            if (existingPhone) {
                console.log(`[Register Failed] Phone already exists: ${phone}`);
                return res.status(400).json({ message: '手机号码已被使用' });
            }
        }

        // 准备创建用户实例
        const userData = {
            username,
            password,
            phone,
            role: 'user'
        };
        console.log('[Register Attempt] Preparing to save user data:', { username: userData.username, role: userData.role });

        const user = new User(userData);
        await user.save();

        console.log(`[Register Success] User registered: ${user.username}, ID: ${user._id}`);
        res.status(201).json({
            message: '注册成功',
            username: user.username,
            role: user.role
        });
    } catch (error) {
        console.error(`[Register Error] Failed for username ${username}:`, error);
        const messages = error.errors ? Object.values(error.errors).map(e => e.message).join(', ') : error.message;
        res.status(400).json({
            message: `注册失败: ${messages}`,
            error: error.name
        });
    }
});

// 移除硬编码的用户数组
// const users = [ ... ];

// 登录路由
router.post('/login', async (req, res) => {
    const { username, password } = req.body;

    console.log(`[Login Attempt] Received for username: ${username}`); // 更清晰的日志头

    try {
        // 始终检查数据库
        const dbUser = await User.findOne({ username });

        // 添加日志：检查是否找到了用户以及用户对象内容
        if (!dbUser) {
            console.log(`[Login Failed] User not found in DB: ${username}`);
            return res.status(401).json({ message: '用户名或密码错误' });
        }
        console.log(`[Login Check] Found user in DB:`, { userId: dbUser._id, username: dbUser.username, role: dbUser.role });
        // 注意：不要直接打印 dbUser.password，避免日志泄露哈希

        // 使用 User 模型中的 comparePassword 方法
        const isMatch = await dbUser.comparePassword(password);

        // 添加日志：记录密码比较结果
        console.log(`[Login Check] Password comparison result for ${username}: ${isMatch}`);

        if (isMatch) {
            console.log(`[Login Success] Password matched for: ${username}`);
            const token = jwt.sign(
                { userId: dbUser._id, username: dbUser.username, role: dbUser.role || 'user' },
                process.env.JWT_SECRET || 'your-secret-key',
                { expiresIn: '24h' }
            );
            return res.json({
                token,
                userId: dbUser._id,
                username: dbUser.username,
                phone: dbUser.phone,
                role: dbUser.role || 'user'
            });
        } else {
            console.log(`[Login Failed] Password mismatch for: ${username}`);
            return res.status(401).json({ message: '用户名或密码错误' });
        }

    } catch (error) {
        console.error('[Login Error] An error occurred:', error); // 改进错误日志
        res.status(500).json({ message: '服务器错误' });
    }
});

// 管理员登录路由
router.post('/admin-login', async (req, res) => {
    const { username, password } = req.body;

    console.log(`[Admin Login Attempt] Received for username: ${username}`);

    try {
        // 查找用户
        const dbUser = await User.findOne({ username });

        // 检查是否找到了用户
        if (!dbUser) {
            console.log(`[Admin Login Failed] User not found in DB: ${username}`);
            return res.status(401).json({ message: '用户名或密码错误' });
        }
        console.log(`[Admin Login Check] Found user in DB:`, { userId: dbUser._id, username: dbUser.username, role: dbUser.role });

        // 验证密码
        const isMatch = await dbUser.comparePassword(password);
        console.log(`[Admin Login Check] Password comparison result for ${username}: ${isMatch}`);

        if (!isMatch) {
            console.log(`[Admin Login Failed] Password mismatch for: ${username}`);
            return res.status(401).json({ message: '用户名或密码错误' });
        }

        // 简化验证逻辑：用户名为 admin 的用户直接允许登录
        if (username === 'admin') {
            console.log(`[Admin Login] 用户名是 admin，允许管理员登录`);

            // 确保用户角色是 admin
            if (String(dbUser.role).toLowerCase() !== 'admin') {
                console.log(`[Admin Login] 更新用户角色: ${dbUser.role} -> admin`);
                dbUser.role = 'admin';
                await dbUser.save();
                console.log(`[Admin Login] 用户角色已更新为 admin`);
            }
        }
        // 对于其他用户，检查角色是否为 admin
        else if (String(dbUser.role).toLowerCase() !== 'admin') {
            console.log(`[Admin Login Failed] User is not admin: ${username}, role: ${dbUser.role}`);
            return res.status(403).json({
                message: '您不是管理员，无法通过此入口登录'
            });
        }

        // 创建 JWT
        const token = jwt.sign(
            { userId: dbUser._id, username: dbUser.username, role: dbUser.role },
            process.env.JWT_SECRET || 'your-secret-key',
            { expiresIn: '24h' }
        );

        console.log(`[Admin Login Success] Admin login successful: ${username}`);

        return res.json({
            message: '管理员登录成功',
            token,
            userId: dbUser._id,
            username: dbUser.username,
            phone: dbUser.phone,
            role: dbUser.role
        });
    } catch (error) {
        console.error('[Admin Login Error] An error occurred:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

// 添加检查用户名是否存在的路由
router.post('/check-username', async (req, res) => {
    const { username } = req.body;
    console.log(`[Username Check] Checking availability for: ${username}`);

    try {
        const existingUser = await User.findOne({ username });
        res.json({ exists: !!existingUser });
        console.log(`[Username Check] Result for ${username}: ${!!existingUser ? 'Taken' : 'Available'}`);
    } catch (error) {
        console.error('[Username Check] Error:', error);
        res.status(500).json({ message: '服务器错误', error: error.message });
    }
});

// 添加检查手机号是否存在的路由
router.post('/check-phone', async (req, res) => {
    const { phone } = req.body;
    console.log(`[Phone Check] Checking availability for: ${phone}`);

    try {
        const existingPhone = await User.findOne({ phone });
        res.json({ exists: !!existingPhone });
        console.log(`[Phone Check] Result for ${phone}: ${!!existingPhone ? 'Taken' : 'Available'}`);
    } catch (error) {
        console.error('[Phone Check] Error:', error);
        res.status(500).json({ message: '服务器错误', error: error.message });
    }
});

// 发送手机验证码路由
router.post('/send-verification-code', async (req, res) => {
    const { phone, username } = req.body;

    if (!phone) {
        return res.status(400).json({ message: '手机号码不能为空' });
    }

    if (!username) {
        return res.status(400).json({ message: '用户名不能为空' });
    }

    try {
        // 检查手机号是否已注册
        const user = await User.findOne({ phone });

        if (!user) {
            return res.status(404).json({ message: '该手机号码未注册' });
        }

        // 检查用户名和手机号是否匹配
        if (user.username !== username) {
            return res.status(400).json({ message: '用户名与手机号码不匹配' });
        }

        // 发送验证码
        const result = await sendVerificationCode(phone);

        res.json(result);
    } catch (error) {
        console.error('发送验证码错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

// 验证码验证路由
router.post('/verify-code', async (req, res) => {
    const { phone, code, username } = req.body;

    if (!phone || !code) {
        return res.status(400).json({ message: '手机号码和验证码不能为空' });
    }

    if (!username) {
        return res.status(400).json({ message: '用户名不能为空' });
    }

    try {
        // 验证码验证
        const isValid = verifyCode(phone, code);

        if (!isValid) {
            return res.status(400).json({ message: '验证码无效或已过期' });
        }

        // 生成重置密码的令牌
        const token = crypto.randomBytes(20).toString('hex');

        // 查找用户并更新重置密码令牌
        const user = await User.findOne({ phone });

        if (!user) {
            return res.status(404).json({ message: '用户不存在' });
        }

        // 检查用户名和手机号是否匹配
        if (user.username !== username) {
            return res.status(400).json({ message: '用户名与手机号码不匹配' });
        }

        user.resetPasswordToken = token;
        user.resetPasswordExpires = Date.now() + 1800000; // 30min内有效

        await user.save();

        res.json({
            message: '验证成功',
            resetToken: token,
            username: user.username
        });
    } catch (error) {
        console.error('验证码验证错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

// 重置密码路由
router.post('/reset-password', async (req, res) => {
    const { resetToken, newPassword } = req.body;

    if (!resetToken || !newPassword) {
        return res.status(400).json({ message: '重置令牌和新密码不能为空' });
    }

    try {
        // 查找具有有效重置令牌的用户
        const user = await User.findOne({
            resetPasswordToken: resetToken,
            resetPasswordExpires: { $gt: Date.now() }
        });

        if (!user) {
            return res.status(400).json({ message: '密码重置令牌无效或已过期' });
        }

        // 更新密码
        user.password = newPassword;
        user.resetPasswordToken = undefined;
        user.resetPasswordExpires = undefined;

        await user.save();

        res.json({ message: '密码重置成功' });
    } catch (error) {
        console.error('密码重置错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

// 更新用户信息路由
router.put('/update-profile', async (req, res) => {
    const { userId, username, phone, currentPassword, newPassword } = req.body;

    if (!userId) {
        return res.status(400).json({ message: '用户ID不能为空' });
    }

    try {
        const user = await User.findById(userId);

        if (!user) {
            return res.status(404).json({ message: '用户不存在' });
        }

        // 如果提供了新用户名，检查是否已存在
        if (username && username !== user.username) {
            const existingUser = await User.findOne({ username });
            if (existingUser) {
                return res.status(400).json({ message: '用户名已被使用' });
            }
            user.username = username;
        }

        // 如果提供了新手机号，检查是否已存在
        if (phone && phone !== user.phone) {
            const existingPhone = await User.findOne({ phone });
            if (existingPhone) {
                return res.status(400).json({ message: '手机号码已被使用' });
            }
            user.phone = phone;
        }

        // 如果要更新密码，验证当前密码
        if (newPassword && currentPassword) {
            const isMatch = await user.comparePassword(currentPassword);

            if (!isMatch) {
                return res.status(400).json({ message: '当前密码不正确' });
            }

            user.password = newPassword;
        }

        await user.save();

        res.json({
            message: '用户信息更新成功',
            user: {
                id: user._id,
                username: user.username,
                phone: user.phone,
                role: user.role
            }
        });
    } catch (error) {
        console.error('更新用户信息错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

// 获取用户信息路由
router.get('/user/:id', authMiddleware, async (req, res) => {
    try {
        console.log(`[User Info] 尝试获取用户信息，ID: ${req.params.id}`);
        console.log(`[User Info] 请求路径: ${req.originalUrl}`);
        console.log(`[User Info] 请求方法: ${req.method}`);
        console.log(`[User Info] 请求头: ${JSON.stringify(req.headers)}`);

        // 尝试使用不同的方法查找用户
        let user;

        try {
            // 方法1: 直接使用ID查找
            user = await User.findById(req.params.id).select('-password -resetPasswordToken -resetPasswordExpires');
            console.log(`[User Info] 方法1 (findById) 结果: ${user ? '找到用户' : '未找到用户'}`);
        } catch (idError) {
            console.log(`[User Info] 方法1 (findById) 错误: ${idError.message}`);
            // 如果ID格式不正确，尝试其他方法
        }

        // 如果方法1失败，尝试方法2
        if (!user) {
            try {
                // 方法2: 使用用户名查找
                const username = req.user.username;
                user = await User.findOne({ username }).select('-password -resetPasswordToken -resetPasswordExpires');
                console.log(`[User Info] 方法2 (findOne by username) 结果: ${user ? '找到用户' : '未找到用户'}`);
            } catch (usernameError) {
                console.log(`[User Info] 方法2 (findOne by username) 错误: ${usernameError.message}`);
            }
        }

        if (!user) {
            console.log(`[User Info] 未找到用户，返回404`);
            return res.status(404).json({ message: '用户不存在' });
        }

        console.log(`[User Info] 成功找到用户: ${user.username}`);
        res.json(user);
    } catch (error) {
        console.error('[User Info] 获取用户信息错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

// 验证 token 路由
router.get('/verify-token', authMiddleware, (req, res) => {
    // 如果能到达这里，说明 token 有效
    res.json({
        message: 'Token 有效',
        user: {
            userId: req.user.userId,
            username: req.user.username,
            role: req.user.role
        }
    });
});

// 获取当前用户信息路由
router.get('/current-user', authMiddleware, async (req, res) => {
    try {
        console.log(`[Current User] 尝试获取当前用户信息，用户ID: ${req.user.userId}`);
        console.log(`[Current User] 用户信息: ${JSON.stringify(req.user)}`);

        // 从数据库中查询用户的完整信息，而不是只使用token中的信息
        const user = await User.findById(req.user.userId).select('-password -resetPasswordToken -resetPasswordExpires');
        
        if (!user) {
            console.log(`[Current User] 在数据库中未找到用户: ${req.user.userId}`);
            return res.status(404).json({ message: '用户不存在' });
        }
        
        console.log(`[Current User] 从数据库获取到完整用户信息: ${user.username}`);
        
        // 返回完整的用户信息
        return res.json({
            _id: user._id,
            username: user.username,
            role: user.role,
            phone: user.phone || '',
            // 添加其他需要的字段
            createdAt: user.createdAt
        });
    } catch (error) {
        console.error('[Current User] 获取当前用户信息错误:', error);
        res.status(500).json({ message: '服务器错误' });
    }
});

module.exports = router;