const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const connectDB = require('../config/db');

// 创建用户的函数
const createUser = async (username, password, role) => {
    try {
        // 检查用户是否已存在
        const existingUser = await User.findOne({ username });
        
        if (existingUser) {
            console.log(`用户 ${username} 已存在`);
            return;
        }
        
        // 创建新用户
        const hashedPassword = await bcrypt.hash(password, 10);
        
        const newUser = new User({
            username,
            password: hashedPassword,
            role
        });
        
        await newUser.save();
        console.log(`用户 ${username} 创建成功，角色: ${role}`);
    } catch (error) {
        console.error('创建用户失败:', error);
    }
};

// 主函数
const main = async () => {
    try {
        // 连接数据库
        await connectDB();
        
        // 创建管理员用户
        await createUser('admin', 'admin123', 'admin');
        
        // 创建普通用户
        await createUser('user', 'user123', 'user');
        
        console.log('用户创建完成');
        process.exit(0);
    } catch (error) {
        console.error('脚本执行失败:', error);
        process.exit(1);
    }
};

// 执行主函数
main();
