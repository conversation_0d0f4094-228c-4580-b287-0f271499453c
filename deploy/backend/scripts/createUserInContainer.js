const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');

// 连接到MongoDB
mongoose.connect('mongodb://mongo:27017/hrmsdb', {
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
    family: 4,
    retryWrites: true,
    retryReads: true
})
.then(() => {
    console.log('MongoDB 连接成功');
    createUsers();
})
.catch(err => {
    console.error('MongoDB 连接失败:', err);
    process.exit(1);
});

// 定义用户模型
const userSchema = new mongoose.Schema({
    username: { 
        type: String, 
        required: true, 
        unique: true,
        minlength: 3,
        maxlength: 20,
        match: /^[a-zA-Z0-9_]+$/ // 只允许字母、数字和下划线
    },
    password: { 
        type: String, 
        required: true,
        minlength: 6 // 最小长度为6位
    },
    role: { 
        type: String, 
        enum: ['admin', 'user'], 
        default: 'user' 
    }
}, {
    timestamps: true // 添加创建和更新时间戳
});

// 密码哈希中间件
userSchema.pre('save', async function(next) {
    if (this.isModified('password')) {
        this.password = await bcrypt.hash(this.password, 10);
    }
    next();
});

// 添加密码验证方法
userSchema.methods.comparePassword = async function(candidatePassword) {
    return bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model('User', userSchema);

// 创建用户的函数
async function createUsers() {
    try {
        // 检查是否已存在管理员用户
        const adminExists = await User.findOne({ username: 'admin' });
        
        if (!adminExists) {
            console.log('创建初始管理员用户...');
            
            await User.create({
                username: 'admin',
                password: 'admin123',
                role: 'admin'
            });
            
            console.log('初始管理员用户创建成功');
        } else {
            console.log('管理员用户已存在，跳过创建');
        }
        
        // 检查是否已存在普通用户
        const userExists = await User.findOne({ username: 'user' });
        
        if (!userExists) {
            console.log('创建初始普通用户...');
            
            await User.create({
                username: 'user',
                password: 'user123',
                role: 'user'
            });
            
            console.log('初始普通用户创建成功');
        } else {
            console.log('普通用户已存在，跳过创建');
        }
        
        console.log('用户创建完成');
        process.exit(0);
    } catch (error) {
        console.error('创建初始用户失败:', error);
        process.exit(1);
    }
}
