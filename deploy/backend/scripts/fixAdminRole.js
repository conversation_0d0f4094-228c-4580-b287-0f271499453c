const mongoose = require('mongoose');
const User = require('../models/User');

async function updateAdmin() {
  try {
    // 连接数据库
    await mongoose.connect('mongodb://localhost:27017/hrmsdb', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    console.log('已连接到数据库');

    // 查找 admin 用户
    let adminUser = await User.findOne({ username: 'admin' });

    if (!adminUser) {
      console.log('未找到 admin 用户，创建新的 admin 用户');

      // 创建新的 admin 用户
      adminUser = new User({
        username: 'admin',
        password: 'admin123',
        role: 'admin'
      });

      await adminUser.save();
      console.log('已创建新的 admin 用户');
    }

    console.log('找到 admin 用户:', {
      id: adminUser._id,
      username: adminUser.username,
      role: adminUser.role,
      roleType: typeof adminUser.role
    });

    // 更新角色
    adminUser.role = 'admin';
    await adminUser.save();

    console.log('已更新 admin 用户角色为 admin');

    // 再次查询确认
    const updatedAdmin = await User.findOne({ username: 'admin' });
    console.log('更新后的 admin 用户:', {
      id: updatedAdmin._id,
      username: updatedAdmin.username,
      role: updatedAdmin.role,
      roleType: typeof updatedAdmin.role
    });

  } catch (error) {
    console.error('更新失败:', error);
  } finally {
    mongoose.connection.close();
    console.log('数据库连接已关闭');
  }
}

updateAdmin();
