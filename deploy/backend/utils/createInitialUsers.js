const User = require('../models/User');
const bcrypt = require('bcryptjs');

/**
 * 创建初始用户
 */
const createInitialUsers = async () => {
    try {
        // 检查是否已存在管理员用户
        const adminExists = await User.findOne({ username: 'admin' });
        
        if (!adminExists) {
            console.log('创建初始管理员用户...');
            const adminPassword = await bcrypt.hash('admin123', 10);
            
            await User.create({
                username: 'admin',
                password: adminPassword,
                role: 'admin'
            });
            
            console.log('初始管理员用户创建成功');
        } else {
            console.log('管理员用户已存在，跳过创建');
        }
        
        // 检查是否已存在普通用户
        const userExists = await User.findOne({ username: 'user' });
        
        if (!userExists) {
            console.log('创建初始普通用户...');
            const userPassword = await bcrypt.hash('user123', 10);
            
            await User.create({
                username: 'user',
                password: userPassword,
                role: 'user'
            });
            
            console.log('初始普通用户创建成功');
        } else {
            console.log('普通用户已存在，跳过创建');
        }
        
    } catch (error) {
        console.error('创建初始用户失败:', error);
    }
};

module.exports = createInitialUsers;
