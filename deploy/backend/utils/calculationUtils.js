const { SALARY_CONFIG: CONFIG } = require('../config/salaryConfig');

// 基础参数验证
const validateSalary = (salary) => {
    if (typeof salary !== 'number' || salary < 0) {
        throw new Error('薪资必须是大于等于0的数字');
    }
};

// 社保计算函数
const calculateSocialInsurance = (salary, employeeData = {}) => {
    validateSalary(salary);  // 保留验证

    console.log('社保计算输入 - 基本工资:', salary);
    console.log('社保计算输入 - 员工数据:', JSON.stringify(employeeData, null, 2));

    const { pension, medical, unemployment, supplementaryMedical } = CONFIG.INSURANCE_RATES;

    // 根据配置确定社保基数
    let socialInsuranceBase = salary; // 默认使用基本工资

    if (CONFIG.INSURANCE_BASE && CONFIG.INSURANCE_BASE.type === 'fixed') {
        socialInsuranceBase = CONFIG.INSURANCE_BASE.fixedAmount || 5000;
        console.log('使用固定社保基数:', socialInsuranceBase);
    } else {
        console.log('使用基本工资作为社保基数:', socialInsuranceBase);
    }

    // 养老保险基数上下限
    const pensionMaxBase = 23194.6;
    const pensionMinBase = 4638.88;

    // 医疗保险基数上下限
    const medicalMaxBase = 24267;
    const medicalMinBase = 4853;

    // 失业保险基数上下限
    const unemploymentMaxBase = 21086;
    const unemploymentMinBase = 4217;

    // 大额医疗补充基数
    const supplementaryMedicalBase = 8;

    // 计算各项社保基数 - 使用配置的社保基数
    const pensionBase = Math.min(Math.max(socialInsuranceBase, pensionMinBase), pensionMaxBase);
    const medicalBase = Math.min(Math.max(socialInsuranceBase, medicalMinBase), medicalMaxBase);
    const unemploymentBase = Math.min(Math.max(socialInsuranceBase, unemploymentMinBase), unemploymentMaxBase);

    // 计算各项社保金额
    const details = {
        pension: roundToTwo(pensionBase * pension),
        medical: roundToTwo(medicalBase * medical),
        unemployment: roundToTwo(unemploymentBase * unemployment),
        supplementaryMedical: roundToTwo(supplementaryMedicalBase * supplementaryMedical)
    };

    // 打印详细的社保计算结果
    console.log('社保计算详情:');
    console.log('- 社保基数类型:', CONFIG.INSURANCE_BASE?.type || 'baseSalary');
    console.log('- 实际使用的社保基数:', socialInsuranceBase);
    console.log('- 养老保险基数:', pensionBase, '费率:', pension, '金额:', details.pension);
    console.log('- 医疗保险基数:', medicalBase, '费率:', medical, '金额:', details.medical);
    console.log('- 失业保险基数:', unemploymentBase, '费率:', unemployment, '金额:', details.unemployment);
    console.log('- 大额医疗补充:', details.supplementaryMedical);

    // 计算社保总额，并保留小数点后2位
    const total = roundToTwo(Object.values(details).reduce((sum, value) => sum + value, 0));
    console.log('社保总额:', total);

    return {
        details,
        total
    };
};

// 个税计算函数
const calculateIncomeTax = (taxableIncome) => {
    // 确保输入是有效数字
    if (typeof taxableIncome !== 'number' || isNaN(taxableIncome)) {
        console.error('无效的应税收入:', taxableIncome);
        return {
            taxableAmount: 0,
            tax: 0,
            effectiveRate: 0
        };
    }

    // 确保非负
    const validTaxableIncome = Math.max(0, taxableIncome);
    const { THRESHOLD } = CONFIG.TAX;
    // 直接使用已扣除专项附加扣除后的应税收入
    const taxableAmount = validTaxableIncome - THRESHOLD;

    if (taxableAmount <= 0) {
        return {
            taxableAmount: 0,
            tax: 0,
            effectiveRate: 0
        };
    }

    // 更新为中国最新的个税税率表
    const TAX_BRACKETS = [
        { threshold: 36000, rate: 0.03, deduction: 0 },      // 不超过36000元的部分，税率3%
        { threshold: 144000, rate: 0.10, deduction: 2520 },  // 超过36000元至144000元的部分，税率10%
        { threshold: 300000, rate: 0.20, deduction: 16920 }, // 超过144000元至300000元的部分，税率20%
        { threshold: 420000, rate: 0.25, deduction: 31920 }, // 超过300000元至420000元的部分，税率25%
        { threshold: 660000, rate: 0.30, deduction: 52920 }, // 超过420000元至660000元的部分，税率30%
        { threshold: 960000, rate: 0.35, deduction: 85920 }, // 超过660000元至960000元的部分，税率35%
        { threshold: Infinity, rate: 0.45, deduction: 181920 } // 超过960000元的部分，税率45%
    ];

    // 查找适用的税率档位
    let tax = 0;
    if (taxableAmount <= 36000) {
        tax = taxableAmount * 0.03;
    } else if (taxableAmount <= 144000) {
        tax = taxableAmount * 0.10 - 2520;
    } else if (taxableAmount <= 300000) {
        tax = taxableAmount * 0.20 - 16920;
    } else if (taxableAmount <= 420000) {
        tax = taxableAmount * 0.25 - 31920;
    } else if (taxableAmount <= 660000) {
        tax = taxableAmount * 0.30 - 52920;
    } else if (taxableAmount <= 960000) {
        tax = taxableAmount * 0.35 - 85920;
    } else {
        tax = taxableAmount * 0.45 - 181920;
    }

    tax = Math.max(0, roundToTwo(tax));

    return {
        taxableAmount,
        tax,
        effectiveRate: validTaxableIncome > 0 ? tax / validTaxableIncome : 0
    };
};

// 数字精度处理
// 1. 添加数字格式化选项
const formatNumber = (value, options = {}) => {
    const {
        minimumFractionDigits = 2,
        maximumFractionDigits = 2,
        currency = '¥',
        useGrouping = true
    } = options;

    if (value === undefined || value === null) {
        return `${currency}0.${'0'.repeat(minimumFractionDigits)}`;
    }

    return `${currency}${value.toLocaleString('zh-CN', {
        minimumFractionDigits,
        maximumFractionDigits,
        useGrouping
    })}`;
};

// 2. 添加计算精度控制
const calculateWithPrecision = (value, precision = 2) => {
    const multiplier = Math.pow(10, precision);
    return Math.floor(value * multiplier) / multiplier;
};
const roundToTwo = (num) => {
    // 保留小数点后2位，使用更精确的方法处理浮点数精度问题
    return Math.round((num + Number.EPSILON) * 100) / 100;
};

// 在文件末尾添加薪资分析工具
const analyzeSalaryStructure = (salary, baseSalary, employeeData = {}) => {
    validateSalary(salary);
    // 如果没有提供基本工资，则使用总薪资作为基本工资
    const validBaseSalary = baseSalary !== undefined && baseSalary !== null ? Math.max(0, baseSalary) : salary;
    validateSalary(validBaseSalary);

    console.log('薪资分析 - 总薪资:', salary);
    console.log('薪资分析 - 基本工资:', validBaseSalary);
    console.log('薪资分析 - 员工数据:', JSON.stringify(employeeData, null, 2));

    // 使用基本工资计算社保，并传递员工数据
    const insurance = calculateSocialInsurance(validBaseSalary, employeeData);
    // 使用总薪资减去社保后计算个税
    const tax = calculateIncomeTax(salary - insurance.total);
    const cost = calculateTotalCost(validBaseSalary, salary, employeeData);

    return {
        grossSalary: salary,
        baseSalary: validBaseSalary,
        netSalary: roundToTwo(salary - insurance.total - tax.tax),
        insuranceRatio: roundToTwo(insurance.total / salary),
        taxRatio: roundToTwo(tax.tax / salary),
        costRatio: roundToTwo(cost.totalCost / salary),
        details: {
            insurance: insurance.details,
            tax: tax,
            companyCost: cost
        }
    };
};

const compareSalaries = (oldSalary, newSalary) => {
    validateSalary(oldSalary);
    validateSalary(newSalary);

    const oldAnalysis = analyzeSalaryStructure(oldSalary);
    const newAnalysis = analyzeSalaryStructure(newSalary);

    return {
        grossIncrease: roundToTwo(newSalary - oldSalary),
        grossIncreaseRate: roundToTwo((newSalary - oldSalary) / oldSalary),
        netIncrease: roundToTwo(newAnalysis.netSalary - oldAnalysis.netSalary),
        netIncreaseRate: roundToTwo((newAnalysis.netSalary - oldAnalysis.netSalary) / oldAnalysis.netSalary),
        costIncrease: roundToTwo(newAnalysis.details.companyCost.totalCost - oldAnalysis.details.companyCost.totalCost),
        monthlyComparison: {
            oldSalary: oldAnalysis,
            newSalary: newAnalysis
        }
    };
};

// 添加公司成本计算函数
const calculateTotalCost = (baseSalary, totalSalary, employeeData = {}) => {
    validateSalary(baseSalary);
    validateSalary(totalSalary);

    const { COMPANY_INSURANCE_RATES } = CONFIG;

    console.log('公司成本计算 - 基本工资:', baseSalary);
    console.log('公司成本计算 - 总薪资:', totalSalary);
    console.log('公司成本计算 - 员工数据:', JSON.stringify(employeeData, null, 2));

    // 养老保险基数上下限
    const pensionMaxBase = 23194.6;
    const pensionMinBase = 4638.88;

    // 医疗保险基数上下限
    const medicalMaxBase = 24267;
    const medicalMinBase = 4853;

    // 失业保险基数上下限
    const unemploymentMaxBase = 21086;
    const unemploymentMinBase = 4217;

    // 工伤保险基数上下限
    const injuryMaxBase = 23194.6;
    const injuryMinBase = 4638.88;

    // 大额医疗补充基数
    const supplementaryMedicalBase = 8;

    // 计算各项社保基数
    const pensionBase = Math.min(Math.max(baseSalary, pensionMinBase), pensionMaxBase);
    const medicalBase = Math.min(Math.max(baseSalary, medicalMinBase), medicalMaxBase);
    const unemploymentBase = Math.min(Math.max(baseSalary, unemploymentMinBase), unemploymentMaxBase);
    const injuryBase = Math.min(Math.max(baseSalary, injuryMinBase), injuryMaxBase);

    // 计算各项公司承担的社保金额
    const companyCosts = {
        pension: roundToTwo(pensionBase * COMPANY_INSURANCE_RATES.pension),
        medical: roundToTwo(medicalBase * COMPANY_INSURANCE_RATES.medical),
        unemployment: roundToTwo(unemploymentBase * COMPANY_INSURANCE_RATES.unemployment),
        injury: roundToTwo(injuryBase * COMPANY_INSURANCE_RATES.injury),
        maternity: roundToTwo(baseSalary * COMPANY_INSURANCE_RATES.maternity),
        supplementaryMedical: roundToTwo(supplementaryMedicalBase * COMPANY_INSURANCE_RATES.supplementaryMedical)
    };

    // 打印详细的公司成本计算结果
    console.log('公司承担的社保计算详情:');
    console.log('- 养老保险:', companyCosts.pension);
    console.log('- 医疗保险:', companyCosts.medical);
    console.log('- 失业保险:', companyCosts.unemployment);
    console.log('- 工伤保险:', companyCosts.injury);
    console.log('- 生育保险:', companyCosts.maternity);
    console.log('- 大额医疗补充:', companyCosts.supplementaryMedical);

    const totalInsuranceCost = roundToTwo(Object.values(companyCosts).reduce((sum, value) => sum + value, 0));
    const totalCost = roundToTwo(totalSalary + totalInsuranceCost);

    console.log('公司承担的社保总额:', totalInsuranceCost);
    console.log('公司总成本:', totalCost);

    return {
        salary: totalSalary,
        insuranceCosts: companyCosts,
        totalInsuranceCost: totalInsuranceCost,
        totalCost: totalCost
    };
};

// 添加工资条生成函数
const generatePayslip = (salaryData) => {
    // 检查薪资数据结构
    const totalMonthlySalary = salaryData.calculationResult?.totalMonthlySalary || salaryData.totalMonthlySalary;

    if (totalMonthlySalary === undefined || totalMonthlySalary === null) {
        console.error('无效的薪资数据结构:', JSON.stringify(salaryData, null, 2));
        throw new Error('薪资数据结构无效: 缺少totalMonthlySalary字段');
    }

    // 获取基本工资作为社保计算基准
    const adjustedBaseSalary = salaryData.adjustedBaseSalary || salaryData.calculationResult?.adjustedBaseSalary;
    if (adjustedBaseSalary === undefined || adjustedBaseSalary === null) {
        console.warn('未找到基本工资(adjustedBaseSalary)，将使用总薪资作为社保计算基准:', JSON.stringify(salaryData, null, 2));
    }

    console.log('工资条生成 - 总薪资:', totalMonthlySalary);
    console.log('工资条生成 - 基本工资:', adjustedBaseSalary);

    // 确保薪资是非负数
    const validTotalSalary = Math.max(0, totalMonthlySalary);
    const validBaseSalary = adjustedBaseSalary !== undefined && adjustedBaseSalary !== null ? Math.max(0, adjustedBaseSalary) : validTotalSalary;

    validateSalary(validTotalSalary);  // 验证总薪资
    validateSalary(validBaseSalary);   // 验证基本工资

    // 使用基本工资计算社保，并传递员工数据
    const insurance = calculateSocialInsurance(validBaseSalary, salaryData);
    // 使用总薪资减去社保后计算个税
    const tax = calculateIncomeTax(validTotalSalary - insurance.total);

    return {
        ...salaryData,
        deductions: {
            insurance: insurance.details,
            tax: tax.tax
        },
        summary: {
            totalDeductions: roundToTwo(insurance.total + tax.tax),
            netSalary: roundToTwo(validTotalSalary - insurance.total - tax.tax)
        }
    };
};module.exports = {
    calculateSocialInsurance,
    calculateIncomeTax,
    formatNumber,
    calculateWithPrecision,
    roundToTwo,
    analyzeSalaryStructure,
    compareSalaries,
    calculateTotalCost,
    generatePayslip
};
