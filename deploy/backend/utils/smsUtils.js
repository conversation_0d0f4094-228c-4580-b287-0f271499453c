/**
 * 短信验证码工具
 * 注意：这是一个模拟实现，实际生产环境中应该使用真实的短信服务
 */

// 存储验证码的内存缓存 (格式: { phone: { code: '123456', expires: Date } })
const verificationCodes = new Map();

/**
 * 生成随机验证码
 * @param {number} length 验证码长度
 * @returns {string} 生成的验证码
 */
const generateVerificationCode = (length = 6) => {
    const digits = '0123456789';
    let code = '';
    for (let i = 0; i < length; i++) {
        code += digits.charAt(Math.floor(Math.random() * digits.length));
    }
    return code;
};

/**
 * 发送验证码到手机
 * @param {string} phone 手机号码
 * @returns {object} 包含验证码和过期时间的对象
 */
const sendVerificationCode = async (phone) => {
    // 生成6位数字验证码
    const code = generateVerificationCode(6);

    // 设置过期时间为10分钟后
    const expires = new Date(Date.now() + 10 * 60 * 1000);

    // 存储验证码和过期时间
    verificationCodes.set(phone, { code, expires });

    // 在实际应用中，这里应该调用短信服务API发送验证码
    console.log(`[模拟] 向手机号 ${phone} 发送验证码: ${code}`);

    // 返回验证码信息
    // 在开发环境中返回验证码，在生产环境中不返回
    const isDevelopment = process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;

    return {
        success: true,
        message: '验证码已发送',
        // 只在开发环境中返回验证码
        ...(isDevelopment ? { code } : {})
    };
};

/**
 * 验证手机验证码
 * @param {string} phone 手机号码
 * @param {string} code 用户提供的验证码
 * @returns {boolean} 验证是否成功
 */
const verifyCode = (phone, code) => {
    const storedData = verificationCodes.get(phone);

    // 检查是否存在验证码记录
    if (!storedData) {
        return false;
    }

    // 检查验证码是否过期
    if (new Date() > storedData.expires) {
        // 删除过期的验证码
        verificationCodes.delete(phone);
        return false;
    }

    // 验证码匹配检查
    const isValid = storedData.code === code;

    // 如果验证成功，删除已使用的验证码
    if (isValid) {
        verificationCodes.delete(phone);
    }

    return isValid;
};

/**
 * 清理过期的验证码
 */
const cleanupExpiredCodes = () => {
    const now = new Date();
    for (const [phone, data] of verificationCodes.entries()) {
        if (now > data.expires) {
            verificationCodes.delete(phone);
        }
    }
};

// 每小时清理一次过期的验证码
setInterval(cleanupExpiredCodes, 60 * 60 * 1000);

module.exports = {
    sendVerificationCode,
    verifyCode
};
