const mongoose = require('mongoose');
const WorkDayService = require('./services/WorkDayService');
require('dotenv').config();

async function testWorkDay() {
    try {
        // 连接数据库
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hrms');
        console.log('已连接到数据库');
        
        // 测试2025年6月的工作日计算
        console.log('测试2025年6月的工作日计算：');
        const june2025 = await WorkDayService.calculateMonthWorkDays(2025, 6, [1, 2, 3, 4, 5]);
        console.log('6月份结果:', june2025.summary);
        
        // 测试2025年5月的工作日计算
        console.log('\n测试2025年5月的工作日计算：');
        const may2025 = await WorkDayService.calculateMonthWorkDays(2025, 5, [1, 2, 3, 4, 5]);
        console.log('5月份结果:', may2025.summary);
        
        // 显示详细的工作日信息
        console.log('\n6月份详细信息:');
        june2025.workDayDetails.forEach(day => {
            if (day.dayType === '节假日' || day.dayType === '调休工作日') {
                console.log(`${day.date}: ${day.dayType} (${day.isWorkDay ? '需要上班' : '放假'})`);
            }
        });
        
        console.log('\n5月份详细信息:');
        may2025.workDayDetails.forEach(day => {
            if (day.dayType === '节假日' || day.dayType === '调休工作日') {
                console.log(`${day.date}: ${day.dayType} (${day.isWorkDay ? '需要上班' : '放假'})`);
            }
        });
        
    } catch (error) {
        console.error('测试失败:', error);
    } finally {
        await mongoose.connection.close();
        console.log('数据库连接已关闭');
    }
}

testWorkDay(); 