/**
 * 薪资数据修复脚本
 *
 * 这个脚本用于修复现有的薪资记录，确保所有字段都正确，特别是：
 * 1. 添加缺失的 educationCoefficient 和 languageCoefficient 字段
 * 2. 确保 adjustedBaseSalary 与 educationAdjustment 和 languageAdjustment 一致
 * 3. 确保试用期相关字段正确
 */

const mongoose = require('mongoose');
const Salary = require('./models/Salary');
const { SALARY_CONFIG } = require('./config/salaryConfig');

// 连接数据库
mongoose.connect('mongodb://localhost:27017/hrmsdb')
    .then(async () => {
        console.log('Connected to MongoDB');

        try {
            // 获取所有薪资记录
            const salaries = await Salary.find({});
            console.log(`Found ${salaries.length} salary records`);

            // 修复计数器
            let fixedCount = 0;

            // 遍历所有薪资记录
            for (const salary of salaries) {
                let needsUpdate = false;
                const updates = {};

                // 1. 检查 educationCoefficient 字段
                const educationCoeff = SALARY_CONFIG.EDUCATION_COEFFICIENT[salary.education] || 1.0;
                updates.educationCoefficient = educationCoeff;
                console.log(`Adding educationCoefficient ${educationCoeff} for employee ${salary.employeeId} (${salary.name})`);
                needsUpdate = true;

                // 2. 检查 languageCoefficient 字段
                const languageCoeff = SALARY_CONFIG.LANGUAGE_COEFFICIENT[salary.languageLevel] || 1.0;
                updates.languageCoefficient = languageCoeff;
                console.log(`Adding languageCoefficient ${languageCoeff} for employee ${salary.employeeId} (${salary.name})`);
                needsUpdate = true;

                // 3. 检查基本工资是否一致
                const baseSalary = SALARY_CONFIG.BASE_SALARY || 3500;
                const educationAdjustment = Number(salary.educationAdjustment) || 0;
                const languageAdjustment = Number(salary.languageAdjustment) || 0;
                const isProbation = salary.isProbation || false;

                // 计算预期的基本工资（不考虑试用期）
                let expectedBaseSalary = baseSalary + educationAdjustment + languageAdjustment;

                // 四舍五入到两位小数
                expectedBaseSalary = Math.round((expectedBaseSalary + Number.EPSILON) * 100) / 100;

                // 试用期员工的基本工资不应该乘以80%，80%折扣只应用于最终应发工资
                const probationFactor = isProbation ? 0.8 : 1.0;
                // 基本工资保持原始值，不乘以试用期系数

                // 获取实际的基本工资
                const actualBaseSalary = Number(salary.adjustedBaseSalary);

                // 计算差异（与预期基本工资比较，不考虑试用期系数）
                const difference = Math.abs(expectedBaseSalary - actualBaseSalary);

                // 如果差异大于0.01，则修复数据
                if (difference >= 0.01) {
                    console.log(`Fixing adjustedBaseSalary for employee ${salary.employeeId} (${salary.name})`);
                    console.log(`  Expected: ${expectedBaseSalary}, Actual: ${actualBaseSalary}, Difference: ${difference}`);
                    console.log(`  Note: 试用期员工的基本工资不乘以80%，80%折扣只应用于最终应发工资`);
                    updates.adjustedBaseSalary = expectedBaseSalary;
                    updates.originalBaseSalary = expectedBaseSalary;
                    needsUpdate = true;
                }

                // 4. 确保试用期相关字段正确
                if (salary.probationFactor === undefined) {
                    updates.probationFactor = probationFactor;
                    needsUpdate = true;
                }

                // 5. 如果需要更新，则更新记录
                if (needsUpdate) {
                    console.log(`Updating salary record for employee ${salary.employeeId} (${salary.name})`);
                    console.log('  Updates:', updates);

                    await Salary.updateOne({ _id: salary._id }, { $set: updates });
                    fixedCount++;
                }
            }

            console.log(`Fixed ${fixedCount} salary records`);
        } catch (error) {
            console.error('Error fixing salary data:', error);
        } finally {
            mongoose.disconnect();
            console.log('Disconnected from MongoDB');
        }
    })
    .catch(err => {
        console.error('Error connecting to MongoDB:', err);
    });
