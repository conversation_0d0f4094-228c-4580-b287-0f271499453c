const Holiday = require('../models/Holiday');

class HolidayAutoService {
  
  /**
   * 自动初始化指定年份的节假日
   * 支持多种策略：固定日期、农历计算、外部API
   */
  static async autoInitializeYear(year) {
    console.log(`开始自动初始化${year}年的节假日配置...`);
    
    // 检查是否已存在配置
    const existingHolidays = await Holiday.find({ year: year });
    if (existingHolidays.length > 0) {
      console.log(`${year}年的节假日配置已存在，跳过初始化`);
      return { success: true, message: '配置已存在' };
    }
    
    try {
      // 1. 固定日期的节假日
      const fixedHolidays = this.generateFixedHolidays(year);
      
      // 2. 农历节假日（需要计算）
      const lunarHolidays = this.generateLunarHolidays(year);
      
      // 3. 尝试从外部API获取官方节假日安排
      const officialHolidays = await this.fetchOfficialHolidays(year);
      
      // 4. 合并所有节假日配置
      const allHolidays = [
        ...fixedHolidays,
        ...lunarHolidays,
        ...officialHolidays
      ];
      
      // 5. 去重并保存
      const uniqueHolidays = this.deduplicateHolidays(allHolidays);
      
      if (uniqueHolidays.length > 0) {
        await Holiday.insertMany(uniqueHolidays);
        console.log(`✅ 成功自动配置${year}年的${uniqueHolidays.length}个节假日`);
      }
      
      return { 
        success: true, 
        count: uniqueHolidays.length,
        holidays: uniqueHolidays.map(h => h.name)
      };
      
    } catch (error) {
      console.error(`自动初始化${year}年节假日失败:`, error);
      return { success: false, error: error.message };
    }
  }
  
  /**
   * 生成固定日期的节假日（阳历）
   */
  static generateFixedHolidays(year) {
    return [
      {
        name: '元旦',
        year: year,
        type: 'national',
        holidayDates: [new Date(year, 0, 1)], // 1月1日
        description: '元旦节'
      },
      {
        name: '劳动节',
        year: year,
        type: 'national',
        holidayDates: [new Date(year, 4, 1)], // 5月1日
        description: '五一劳动节'
      },
      {
        name: '国庆节',
        year: year,
        type: 'national',
        holidayDates: [
          new Date(year, 9, 1), // 10月1日
          new Date(year, 9, 2), // 10月2日
          new Date(year, 9, 3), // 10月3日
        ],
        description: '国庆节假期'
      }
    ];
  }
  
  /**
   * 生成农历节假日
   * 使用农历计算库或查表法
   */
  static generateLunarHolidays(year) {
    // 这里可以集成农历计算库，或者使用预设的农历节日对照表
    const lunarHolidays = [];
    
    // 春节（农历正月初一）- 使用简化的查表法
    const springFestival = this.getSpringFestivalDate(year);
    if (springFestival) {
      lunarHolidays.push({
        name: '春节',
        year: year,
        type: 'traditional',
        holidayDates: this.generateSpringFestivalDates(springFestival),
        description: '春节假期（农历新年）'
      });
    }
    
    // 清明节（通常在4月4-6日之间）
    const qingming = this.getQingmingDate(year);
    if (qingming) {
      lunarHolidays.push({
        name: '清明节',
        year: year,
        type: 'traditional',
        holidayDates: [qingming],
        description: '清明节'
      });
    }
    
    // 端午节（农历五月初五）
    const dragonBoat = this.getDragonBoatDate(year);
    if (dragonBoat) {
      lunarHolidays.push({
        name: '端午节',
        year: year,
        type: 'traditional',
        holidayDates: [dragonBoat],
        description: '端午节'
      });
    }
    
    // 中秋节（农历八月十五）
    const midAutumn = this.getMidAutumnDate(year);
    if (midAutumn) {
      lunarHolidays.push({
        name: '中秋节',
        year: year,
        type: 'traditional',
        holidayDates: [midAutumn],
        description: '中秋节'
      });
    }
    
    return lunarHolidays;
  }
  
  /**
   * 从外部API获取官方节假日安排
   * 包括调休安排等详细信息
   */
  static async fetchOfficialHolidays(year) {
    try {
      // 这里可以调用国务院办公厅的节假日API
      // 或者其他可靠的节假日数据源
      
      // 示例：调用节假日API
      // const response = await fetch(`https://api.holiday.cn/${year}`);
      // const data = await response.json();
      
      // 目前返回空数组，实际使用时可以集成真实的API
      console.log(`尝试从官方API获取${year}年节假日安排...`);
      
      // 模拟API返回的调休安排
      if (year === 2025) {
        return [
          {
            name: '春节调休',
            year: year,
            type: 'national',
            holidayDates: [
              new Date(2025, 0, 28), // 1月28日
              new Date(2025, 0, 29), // 1月29日
              new Date(2025, 0, 30), // 1月30日
              new Date(2025, 0, 31), // 1月31日
              new Date(2025, 1, 1),  // 2月1日
              new Date(2025, 1, 2),  // 2月2日
              new Date(2025, 1, 3),  // 2月3日
            ],
            workDates: [
              new Date(2025, 0, 26), // 1月26日调休
              new Date(2025, 1, 8),  // 2月8日调休
            ],
            description: '春节假期（含调休安排）'
          }
        ];
      }
      
      return [];
      
    } catch (error) {
      console.warn(`获取${year}年官方节假日安排失败:`, error.message);
      return [];
    }
  }
  
  /**
   * 农历节日日期计算（简化版）
   * 实际项目中建议使用专业的农历计算库
   */
  static getSpringFestivalDate(year) {
    // 春节日期对照表（2020-2030）
    const springFestivalDates = {
      2020: new Date(2020, 0, 25),
      2021: new Date(2021, 1, 12),
      2022: new Date(2022, 1, 1),
      2023: new Date(2023, 0, 22),
      2024: new Date(2024, 1, 10),
      2025: new Date(2025, 0, 29),
      2026: new Date(2026, 1, 17),
      2027: new Date(2027, 1, 6),
      2028: new Date(2028, 0, 26),
      2029: new Date(2029, 1, 13),
      2030: new Date(2030, 1, 3),
    };
    
    return springFestivalDates[year];
  }
  
  static generateSpringFestivalDates(springFestivalDate) {
    const dates = [];
    // 春节假期通常是7天（除夕到初六）
    for (let i = -1; i <= 5; i++) {
      const date = new Date(springFestivalDate);
      date.setDate(date.getDate() + i);
      dates.push(date);
    }
    return dates;
  }
  
  static getQingmingDate(year) {
    // 清明节日期对照表
    const qingmingDates = {
      2020: new Date(2020, 3, 4),
      2021: new Date(2021, 3, 4),
      2022: new Date(2022, 3, 5),
      2023: new Date(2023, 3, 5),
      2024: new Date(2024, 3, 4),
      2025: new Date(2025, 3, 4),
      2026: new Date(2026, 3, 5),
      2027: new Date(2027, 3, 4),
      2028: new Date(2028, 3, 4),
      2029: new Date(2029, 3, 4),
      2030: new Date(2030, 3, 5),
    };
    
    return qingmingDates[year];
  }
  
  static getDragonBoatDate(year) {
    // 端午节日期对照表
    const dragonBoatDates = {
      2020: new Date(2020, 5, 25),
      2021: new Date(2021, 5, 14),
      2022: new Date(2022, 5, 3),
      2023: new Date(2023, 5, 22),
      2024: new Date(2024, 5, 10),
      2025: new Date(2025, 4, 31),
      2026: new Date(2026, 5, 19),
      2027: new Date(2027, 5, 9),
      2028: new Date(2028, 5, 26),
      2029: new Date(2029, 5, 16),
      2030: new Date(2030, 5, 5),
    };
    
    return dragonBoatDates[year];
  }
  
  static getMidAutumnDate(year) {
    // 中秋节日期对照表
    const midAutumnDates = {
      2020: new Date(2020, 9, 1),
      2021: new Date(2021, 8, 21),
      2022: new Date(2022, 8, 10),
      2023: new Date(2023, 8, 29),
      2024: new Date(2024, 8, 17),
      2025: new Date(2025, 9, 6),
      2026: new Date(2026, 8, 25),
      2027: new Date(2027, 8, 15),
      2028: new Date(2028, 9, 3),
      2029: new Date(2029, 8, 22),
      2030: new Date(2030, 8, 12),
    };
    
    return midAutumnDates[year];
  }
  
  /**
   * 去重节假日配置
   */
  static deduplicateHolidays(holidays) {
    const seen = new Set();
    return holidays.filter(holiday => {
      const key = `${holiday.name}-${holiday.year}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }
  
  /**
   * 自动检查并初始化当前年份和未来年份的节假日
   */
  static async autoCheckAndInitialize() {
    const currentYear = new Date().getFullYear();
    const years = [currentYear, currentYear + 1]; // 当前年和下一年
    
    const results = [];
    for (const year of years) {
      const result = await this.autoInitializeYear(year);
      results.push({ year, ...result });
    }
    
    return results;
  }
  
  /**
   * 获取节假日配置状态
   */
  static async getHolidayStatus() {
    const currentYear = new Date().getFullYear();
    const years = [currentYear - 1, currentYear, currentYear + 1];
    
    const status = {};
    for (const year of years) {
      const holidays = await Holiday.find({ year: year, isActive: true });
      status[year] = {
        count: holidays.length,
        holidays: holidays.map(h => ({
          name: h.name,
          type: h.type,
          holidayDates: h.holidayDates.length,
          workDates: h.workDates.length
        }))
      };
    }
    
    return status;
  }
  
  /**
   * 智能推荐：基于历史模式预测未来年份的节假日
   */
  static async predictFutureHolidays(targetYear) {
    console.log(`基于历史模式预测${targetYear}年的节假日安排...`);
    
    // 分析过去几年的节假日模式
    const historicalYears = [targetYear - 3, targetYear - 2, targetYear - 1];
    const patterns = {};
    
    for (const year of historicalYears) {
      const holidays = await Holiday.find({ year: year });
      holidays.forEach(holiday => {
        if (!patterns[holiday.name]) {
          patterns[holiday.name] = [];
        }
        patterns[holiday.name].push({
          year: year,
          holidayDates: holiday.holidayDates,
          workDates: holiday.workDates
        });
      });
    }
    
    // 基于模式生成预测
    const predictions = [];
    Object.keys(patterns).forEach(holidayName => {
      const pattern = patterns[holidayName];
      if (pattern.length >= 2) {
        // 有足够的历史数据进行预测
        predictions.push({
          name: holidayName,
          confidence: pattern.length / 3,
          suggestion: `基于${pattern.length}年历史数据的预测`
        });
      }
    });
    
    return predictions;
  }
}

module.exports = HolidayAutoService; 