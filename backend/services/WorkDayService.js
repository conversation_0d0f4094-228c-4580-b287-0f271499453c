const Holiday = require('../models/Holiday');
const HolidayAutoService = require('./HolidayAutoService');

class WorkDayService {
  
  /**
   * 计算指定年月的工作日数量
   * @param {number} year - 年份
   * @param {number} month - 月份 (1-12)
   * @param {Array} workDays - 工作日配置 [1,2,3,4,5] 表示周一到周五
   * @returns {Object} 工作日统计信息
   */
  static async calculateMonthWorkDays(year, month, workDays = [1, 2, 3, 4, 5]) {
    // 自动检查并初始化节假日配置
    await this.ensureHolidayConfiguration(year);
    
    // 获取该年的节假日配置
    const holidays = await Holiday.find({ 
      year: year, 
      isActive: true 
    });
    
    // 构建节假日和调休日期集合
    const holidayDates = new Set();
    const workDates = new Set(); // 调休需要上班的日期
    
    holidays.forEach(holiday => {
      // 添加节假日日期
      holiday.holidayDates.forEach(date => {
        holidayDates.add(this.formatDate(date));
      });
      
      // 添加调休工作日期
      holiday.workDates.forEach(date => {
        workDates.add(this.formatDate(date));
      });
    });
    
    // 计算该月的工作日
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    const totalDays = endDate.getDate();
    
    let workDayCount = 0;
    let weekendWorkDays = 0; // 调休工作日数量
    let holidayCount = 0; // 节假日数量
    const workDayDetails = [];
    
    for (let day = 1; day <= totalDays; day++) {
      const currentDate = new Date(year, month - 1, day);
      const dayOfWeek = currentDate.getDay();
      const dateStr = this.formatDate(currentDate);
      
      let isWorkDay = false;
      let dayType = '';
      
      // 检查是否是节假日
      if (holidayDates.has(dateStr)) {
        dayType = '节假日';
        holidayCount++;
      }
      // 检查是否是调休工作日
      else if (workDates.has(dateStr)) {
        isWorkDay = true;
        dayType = '调休工作日';
        weekendWorkDays++;
      }
      // 检查是否是正常工作日
      else if (workDays.includes(dayOfWeek)) {
        isWorkDay = true;
        dayType = '工作日';
      }
      // 否则是周末
      else {
        dayType = '周末';
      }
      
      if (isWorkDay) {
        workDayCount++;
      }
      
      workDayDetails.push({
        date: dateStr,
        dayOfWeek: dayOfWeek,
        dayType: dayType,
        isWorkDay: isWorkDay
      });
    }
    
    return {
      year,
      month,
      totalDays,
      workDayCount,
      weekendWorkDays,
      holidayCount,
      workDayDetails,
      summary: {
        应出勤天数: workDayCount,
        正常工作日: workDayCount - weekendWorkDays,
        调休工作日: weekendWorkDays,
        节假日: holidayCount,
        周末: totalDays - workDayCount - holidayCount
      }
    };
  }
  
  /**
   * 检查指定日期是否为工作日
   * @param {Date} date - 要检查的日期
   * @param {Array} workDays - 工作日配置
   * @returns {boolean} 是否为工作日
   */
  static async isWorkDay(date, workDays = [1, 2, 3, 4, 5]) {
    const year = date.getFullYear();
    const dateStr = this.formatDate(date);
    const dayOfWeek = date.getDay();
    
    // 自动检查并初始化节假日配置
    await this.ensureHolidayConfiguration(year);
    
    // 获取该年的节假日配置
    const holidays = await Holiday.find({ 
      year: year, 
      isActive: true 
    });
    
    // 检查是否是节假日
    for (const holiday of holidays) {
      for (const holidayDate of holiday.holidayDates) {
        if (this.formatDate(holidayDate) === dateStr) {
          return false; // 是节假日，不是工作日
        }
      }
      
      // 检查是否是调休工作日
      for (const workDate of holiday.workDates) {
        if (this.formatDate(workDate) === dateStr) {
          return true; // 是调休工作日
        }
      }
    }
    
    // 检查是否是正常工作日
    return workDays.includes(dayOfWeek);
  }
  
  /**
   * 获取指定年份的所有节假日
   * @param {number} year - 年份
   * @returns {Array} 节假日列表
   */
  static async getYearHolidays(year) {
    return await Holiday.find({ 
      year: year, 
      isActive: true 
    }).sort({ createdAt: 1 });
  }
  
  /**
   * 初始化指定年份的默认节假日
   * @param {number} year - 年份
   */
  static async initializeDefaultHolidays(year) {
    // 检查是否已经存在该年的节假日配置
    const existingHolidays = await Holiday.find({ year: year });
    if (existingHolidays.length > 0) {
      console.log(`${year}年的节假日配置已存在`);
      return;
    }
    
    // 创建默认的国家法定节假日（需要根据实际情况调整）
    const defaultHolidays = [
      {
        name: '元旦',
        year: year,
        type: 'national',
        holidayDates: [new Date(year, 0, 1)], // 1月1日
        description: '元旦节'
      },
      {
        name: '春节',
        year: year,
        type: 'national',
        holidayDates: [
          // 注意：春节日期每年不同，这里需要根据实际情况调整
          // 示例：假设春节假期是2月10-16日
        ],
        description: '春节假期，具体日期需要根据国务院办公厅通知调整'
      },
      {
        name: '清明节',
        year: year,
        type: 'traditional',
        holidayDates: [],
        description: '清明节，具体日期需要根据农历调整'
      },
      {
        name: '劳动节',
        year: year,
        type: 'national',
        holidayDates: [
          new Date(year, 4, 1), // 5月1日
        ],
        description: '五一劳动节'
      },
      {
        name: '端午节',
        year: year,
        type: 'traditional',
        holidayDates: [],
        description: '端午节，具体日期需要根据农历调整'
      },
      {
        name: '中秋节',
        year: year,
        type: 'traditional',
        holidayDates: [],
        description: '中秋节，具体日期需要根据农历调整'
      },
      {
        name: '国庆节',
        year: year,
        type: 'national',
        holidayDates: [
          new Date(year, 9, 1), // 10月1日
          new Date(year, 9, 2), // 10月2日
          new Date(year, 9, 3), // 10月3日
        ],
        description: '国庆节假期'
      }
    ];
    
    // 只保存有具体日期的节假日
    const holidaysToSave = defaultHolidays.filter(holiday => 
      holiday.holidayDates.length > 0
    );
    
    if (holidaysToSave.length > 0) {
      await Holiday.insertMany(holidaysToSave);
      console.log(`成功初始化${year}年的${holidaysToSave.length}个节假日配置`);
    }
    
    console.log(`注意：${year}年的农历节假日（春节、清明、端午、中秋）需要手动配置具体日期`);
  }
  
  /**
   * 格式化日期为 YYYY-MM-DD 字符串
   * @param {Date} date - 日期对象
   * @returns {string} 格式化的日期字符串
   */
  static formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  
  /**
   * 获取指定月份的工作日详情（用于前端显示）
   * @param {number} year - 年份
   * @param {number} month - 月份
   * @param {Array} workDays - 工作日配置
   * @returns {Object} 月份工作日详情
   */
  static async getMonthWorkDayDetails(year, month, workDays = [1, 2, 3, 4, 5]) {
    const result = await this.calculateMonthWorkDays(year, month, workDays);
    
    // 按周分组显示
    const weeks = [];
    let currentWeek = [];
    
    result.workDayDetails.forEach((day, index) => {
      const date = new Date(year, month - 1, index + 1);
      const dayOfWeek = date.getDay();
      
      // 如果是周日且不是第一天，开始新的一周
      if (dayOfWeek === 0 && currentWeek.length > 0) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
      
      currentWeek.push(day);
      
      // 如果是最后一天，添加当前周
      if (index === result.workDayDetails.length - 1) {
        weeks.push(currentWeek);
      }
    });
    
    return {
      ...result,
      weeks
    };
  }
  
  /**
   * 确保指定年份的节假日配置存在
   * 如果不存在，自动初始化
   */
  static async ensureHolidayConfiguration(year) {
    const existingHolidays = await Holiday.find({ year: year });
    
    if (existingHolidays.length === 0) {
      console.log(`${year}年的节假日配置不存在，正在自动初始化...`);
      
      try {
        const result = await HolidayAutoService.autoInitializeYear(year);
        if (result.success) {
          console.log(`✅ 自动初始化${year}年节假日配置成功: ${result.holidays.join(', ')}`);
        } else {
          console.warn(`⚠️ 自动初始化${year}年节假日配置失败: ${result.error}`);
        }
      } catch (error) {
        console.error(`❌ 自动初始化${year}年节假日配置时发生错误:`, error);
      }
    }
  }
  
  /**
   * 批量确保多年份的节假日配置
   */
  static async ensureMultiYearConfiguration(years) {
    const results = [];
    for (const year of years) {
      try {
        await this.ensureHolidayConfiguration(year);
        results.push({ year, success: true });
      } catch (error) {
        results.push({ year, success: false, error: error.message });
      }
    }
    return results;
  }
  
  /**
   * 系统启动时的自动检查
   */
  static async systemStartupCheck() {
    console.log('🔍 执行系统启动时的节假日配置检查...');
    
    const currentYear = new Date().getFullYear();
    const years = [currentYear - 1, currentYear, currentYear + 1];
    
    const results = await this.ensureMultiYearConfiguration(years);
    
    console.log('📊 节假日配置检查结果:');
    results.forEach(result => {
      if (result.success) {
        console.log(`  ✅ ${result.year}年: 配置正常`);
      } else {
        console.log(`  ❌ ${result.year}年: ${result.error}`);
      }
    });
    
    return results;
  }
}

// 系统启动时自动执行检查
if (require.main === module) {
  WorkDayService.systemStartupCheck().catch(console.error);
}

module.exports = WorkDayService; 