const AttendanceConfig = require('../models/AttendanceConfig');
const WorkDayService = require('../services/WorkDayService');

class AttendanceCalculator {
  constructor(config = null) {
    this.config = config;
  }

  // 获取默认配置
  static async getDefaultConfig() {
    let config = await AttendanceConfig.findOne({ isDefault: true, isActive: true });
    
    if (!config) {
      // 创建默认配置
      config = new AttendanceConfig({
        name: '默认考勤规则',
        isDefault: true,
        isActive: true
      });
      await config.save();
    }
    
    return config;
  }

  // 初始化计算器
  static async create(configId = null) {
    let config;
    if (configId) {
      config = await AttendanceConfig.findById(configId);
    } else {
      config = await AttendanceCalculator.getDefaultConfig();
    }
    
    return new AttendanceCalculator(config);
  }

  // 判断是否为工作日（支持节假日和调休）
  async isWorkDay(date) {
    return await WorkDayService.isWorkDay(date, this.config.workDays);
  }

  // 将时间字符串转换为分钟数
  timeToMinutes(timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  // 将分钟数转换为时间字符串
  minutesToTime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  // 计算两个时间之间的分钟差
  getTimeDifference(startTime, endTime) {
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);
    return endMinutes - startMinutes;
  }

  // 计算考勤状态（异步版本）
  async calculateAttendanceStatus(checkInTime, checkOutTime, date) {
    // 如果不是工作日，返回休息日
    if (!(await this.isWorkDay(date))) {
      return {
        status: '休息日',
        isLate: false,
        isEarly: false,
        lateMinutes: 0,
        earlyMinutes: 0,
        workMinutes: 0,
        overtimeMinutes: 0
      };
    }

    const standardStart = this.config.standardWorkTime.startTime;
    const standardEnd = this.config.standardWorkTime.endTime;
    
    // 如果没有打卡记录，判断为缺勤
    if (!checkInTime && !checkOutTime) {
      return {
        status: '缺勤',
        isLate: false,
        isEarly: false,
        lateMinutes: 0,
        earlyMinutes: 0,
        workMinutes: 0,
        overtimeMinutes: 0
      };
    }

    // 如果只有签到没有签退，判断为异常
    if (checkInTime && !checkOutTime) {
      return {
        status: '异常',
        isLate: false,
        isEarly: false,
        lateMinutes: 0,
        earlyMinutes: 0,
        workMinutes: 0,
        overtimeMinutes: 0,
        note: '只有签到记录，缺少签退记录'
      };
    }

    // 如果只有签退没有签到，判断为异常
    if (!checkInTime && checkOutTime) {
      return {
        status: '异常',
        isLate: false,
        isEarly: false,
        lateMinutes: 0,
        earlyMinutes: 0,
        workMinutes: 0,
        overtimeMinutes: 0,
        note: '只有签退记录，缺少签到记录'
      };
    }

    // 计算迟到和早退
    const checkInMinutes = this.timeToMinutes(checkInTime);
    const checkOutMinutes = this.timeToMinutes(checkOutTime);
    const standardStartMinutes = this.timeToMinutes(standardStart);
    const standardEndMinutes = this.timeToMinutes(standardEnd);

    // 计算迟到分钟数
    let lateMinutes = 0;
    let isLate = false;
    if (checkInMinutes > standardStartMinutes + this.config.lateEarlyRules.lateTolerance) {
      lateMinutes = checkInMinutes - standardStartMinutes;
      isLate = true;
    }

    // 计算早退分钟数
    let earlyMinutes = 0;
    let isEarly = false;
    if (checkOutMinutes < standardEndMinutes - this.config.lateEarlyRules.earlyTolerance) {
      earlyMinutes = standardEndMinutes - checkOutMinutes;
      isEarly = true;
    }

    // 计算实际工作时间
    let workMinutes = checkOutMinutes - checkInMinutes;
    
    // 如果启用了午休，减去午休时间
    if (this.config.lunchBreak.enabled) {
      const lunchStartMinutes = this.timeToMinutes(this.config.lunchBreak.startTime);
      const lunchEndMinutes = this.timeToMinutes(this.config.lunchBreak.endTime);
      
      // 检查是否跨越午休时间
      if (checkInMinutes < lunchEndMinutes && checkOutMinutes > lunchStartMinutes) {
        const lunchOverlap = Math.min(lunchEndMinutes, checkOutMinutes) - 
                            Math.max(lunchStartMinutes, checkInMinutes);
        workMinutes -= Math.max(0, lunchOverlap);
      }
    }

    // 计算加班时间
    let overtimeMinutes = 0;
    if (this.config.overtimeRules.enabled) {
      const overtimeStartMinutes = this.timeToMinutes(this.config.overtimeRules.weekdayOvertimeStart);
      if (checkOutMinutes > overtimeStartMinutes) {
        overtimeMinutes = checkOutMinutes - overtimeStartMinutes;
        // 只有超过最少加班时间才算加班
        if (overtimeMinutes < this.config.overtimeRules.minOvertimeMinutes) {
          overtimeMinutes = 0;
        }
      }
    }

    // 确保工作时间不为负数
    workMinutes = Math.max(0, workMinutes);

    // 判断最终状态
    let status = '正常';
    if (isLate && isEarly) {
      status = '迟到早退';
    } else if (isLate) {
      status = '迟到';
    } else if (isEarly) {
      status = '早退';
    }

    // 如果迟到超过严重迟到时间，标记为严重迟到
    if (lateMinutes >= this.config.lateEarlyRules.severeLateTime) {
      status = status.includes('早退') ? '严重迟到早退' : '严重迟到';
    }

    return {
      status,
      isLate,
      isEarly,
      lateMinutes,
      earlyMinutes,
      workMinutes,
      overtimeMinutes,
      workHours: (workMinutes / 60).toFixed(2),
      overtimeHours: (overtimeMinutes / 60).toFixed(2)
    };
  }

  // 批量计算考勤状态（异步版本）
  async calculateBatchAttendance(attendanceRecords) {
    const results = [];
    for (const record of attendanceRecords) {
      const checkInTime = record.checkIn ? 
        `${record.checkIn.getHours().toString().padStart(2, '0')}:${record.checkIn.getMinutes().toString().padStart(2, '0')}` : 
        null;
      const checkOutTime = record.checkOut ? 
        `${record.checkOut.getHours().toString().padStart(2, '0')}:${record.checkOut.getMinutes().toString().padStart(2, '0')}` : 
        null;
      
      const result = await this.calculateAttendanceStatus(checkInTime, checkOutTime, record.date);
      
      results.push({
        ...record.toObject(),
        calculatedStatus: result
      });
    }
    return results;
  }

  // 获取工作日列表（用于前端显示）
  getWorkDaysLabels() {
    const dayLabels = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return this.config.workDays.map(day => dayLabels[day]);
  }

  // 计算月度工作日数量（支持节假日和调休）
  async calculateMonthWorkDays(year, month) {
    const result = await WorkDayService.calculateMonthWorkDays(
      year, 
      month, 
      this.config.workDays
    );
    return result.workDayCount;
  }

  // 获取月度工作日详细信息
  async getMonthWorkDayDetails(year, month) {
    return await WorkDayService.getMonthWorkDayDetails(
      year, 
      month, 
      this.config.workDays
    );
  }

  // 验证弹性工作时间
  validateFlexibleTime(checkInTime, checkOutTime) {
    if (!this.config.flexibleTime.enabled) {
      return { valid: true };
    }

    const checkInMinutes = this.timeToMinutes(checkInTime);
    const checkOutMinutes = this.timeToMinutes(checkOutTime);
    const earliestStart = this.timeToMinutes(this.config.flexibleTime.earliestStartTime);
    const latestStart = this.timeToMinutes(this.config.flexibleTime.latestStartTime);
    const minWorkMinutes = this.config.flexibleTime.minWorkHours * 60;

    const errors = [];

    // 检查是否在允许的签到时间范围内
    if (checkInMinutes < earliestStart || checkInMinutes > latestStart) {
      errors.push(`签到时间必须在 ${this.config.flexibleTime.earliestStartTime} 到 ${this.config.flexibleTime.latestStartTime} 之间`);
    }

    // 检查是否满足最少工作时长
    const actualWorkMinutes = checkOutMinutes - checkInMinutes;
    if (actualWorkMinutes < minWorkMinutes) {
      errors.push(`工作时长不足，至少需要 ${this.config.flexibleTime.minWorkHours} 小时`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

module.exports = AttendanceCalculator; 