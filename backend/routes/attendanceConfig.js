const express = require('express');
const router = express.Router();
const AttendanceConfig = require('../models/AttendanceConfig');

// 获取所有考勤配置
router.get('/', async (req, res) => {
  try {
    const configs = await AttendanceConfig.find().sort({ isDefault: -1, createdAt: -1 });
    res.json({ success: true, data: configs });
  } catch (error) {
    console.error('获取考勤配置失败:', error);
    res.status(500).json({ success: false, message: '获取考勤配置失败' });
  }
});

// 获取默认考勤配置
router.get('/default', async (req, res) => {
  try {
    let config = await AttendanceConfig.findOne({ isDefault: true, isActive: true });
    
    // 如果没有默认配置，创建一个
    if (!config) {
      config = new AttendanceConfig({
        name: '默认考勤规则',
        isDefault: true,
        isActive: true
      });
      await config.save();
    }
    
    res.json({ success: true, data: config });
  } catch (error) {
    console.error('获取默认考勤配置失败:', error);
    res.status(500).json({ success: false, message: '获取默认考勤配置失败' });
  }
});

// 创建新的考勤配置
router.post('/', async (req, res) => {
  try {
    const config = new AttendanceConfig(req.body);
    await config.save();
    res.json({ success: true, data: config, message: '考勤配置创建成功' });
  } catch (error) {
    console.error('创建考勤配置失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '创建考勤配置失败: ' + error.message 
    });
  }
});

// 更新考勤配置
router.put('/:id', async (req, res) => {
  try {
    const config = await AttendanceConfig.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!config) {
      return res.status(404).json({ success: false, message: '考勤配置不存在' });
    }
    
    res.json({ success: true, data: config, message: '考勤配置更新成功' });
  } catch (error) {
    console.error('更新考勤配置失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '更新考勤配置失败: ' + error.message 
    });
  }
});

// 删除考勤配置
router.delete('/:id', async (req, res) => {
  try {
    const config = await AttendanceConfig.findById(req.params.id);
    
    if (!config) {
      return res.status(404).json({ success: false, message: '考勤配置不存在' });
    }
    
    if (config.isDefault) {
      return res.status(400).json({ success: false, message: '不能删除默认配置' });
    }
    
    await AttendanceConfig.findByIdAndDelete(req.params.id);
    res.json({ success: true, message: '考勤配置删除成功' });
  } catch (error) {
    console.error('删除考勤配置失败:', error);
    res.status(500).json({ success: false, message: '删除考勤配置失败' });
  }
});

// 设置默认配置
router.post('/:id/set-default', async (req, res) => {
  try {
    // 取消所有配置的默认状态
    await AttendanceConfig.updateMany({}, { isDefault: false });
    
    // 设置指定配置为默认
    const config = await AttendanceConfig.findByIdAndUpdate(
      req.params.id,
      { isDefault: true, isActive: true },
      { new: true }
    );
    
    if (!config) {
      return res.status(404).json({ success: false, message: '考勤配置不存在' });
    }
    
    res.json({ success: true, data: config, message: '默认配置设置成功' });
  } catch (error) {
    console.error('设置默认配置失败:', error);
    res.status(500).json({ success: false, message: '设置默认配置失败' });
  }
});

// 切换配置启用状态
router.post('/:id/toggle-active', async (req, res) => {
  try {
    const config = await AttendanceConfig.findById(req.params.id);
    
    if (!config) {
      return res.status(404).json({ success: false, message: '考勤配置不存在' });
    }
    
    if (config.isDefault && config.isActive) {
      return res.status(400).json({ success: false, message: '不能禁用默认配置' });
    }
    
    config.isActive = !config.isActive;
    await config.save();
    
    res.json({ 
      success: true, 
      data: config, 
      message: `配置已${config.isActive ? '启用' : '禁用'}` 
    });
  } catch (error) {
    console.error('切换配置状态失败:', error);
    res.status(500).json({ success: false, message: '切换配置状态失败' });
  }
});

// 验证时间格式的辅助函数
function validateTimeFormat(time) {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
}

// 计算两个时间之间的分钟差
function getTimeDifferenceInMinutes(startTime, endTime) {
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);
  
  const startTotalMinutes = startHour * 60 + startMinute;
  const endTotalMinutes = endHour * 60 + endMinute;
  
  return endTotalMinutes - startTotalMinutes;
}

// 验证配置数据的辅助路由
router.post('/validate', (req, res) => {
  try {
    const {
      standardWorkTime,
      flexibleTime,
      lunchBreak,
      overtimeRules,
      workDays
    } = req.body;
    
    const errors = [];
    
    // 验证工作日
    if (!workDays || workDays.length === 0) {
      errors.push('必须至少选择一个工作日');
    }
    
    // 验证标准工作时间
    if (!validateTimeFormat(standardWorkTime.startTime)) {
      errors.push('标准上班时间格式不正确');
    }
    if (!validateTimeFormat(standardWorkTime.endTime)) {
      errors.push('标准下班时间格式不正确');
    }
    
    if (standardWorkTime.startTime >= standardWorkTime.endTime) {
      errors.push('下班时间必须晚于上班时间');
    }
    
    // 验证弹性工作时间
    if (flexibleTime.enabled) {
      if (!validateTimeFormat(flexibleTime.earliestStartTime)) {
        errors.push('最早上班时间格式不正确');
      }
      if (!validateTimeFormat(flexibleTime.latestStartTime)) {
        errors.push('最晚上班时间格式不正确');
      }
      
      if (flexibleTime.earliestStartTime >= flexibleTime.latestStartTime) {
        errors.push('最晚上班时间必须晚于最早上班时间');
      }
      
      if (flexibleTime.minWorkHours <= 0) {
        errors.push('最少工作时长必须大于0');
      }
    }
    
    // 验证午休时间
    if (lunchBreak.enabled) {
      if (!validateTimeFormat(lunchBreak.startTime)) {
        errors.push('午休开始时间格式不正确');
      }
      if (!validateTimeFormat(lunchBreak.endTime)) {
        errors.push('午休结束时间格式不正确');
      }
      
      if (lunchBreak.startTime >= lunchBreak.endTime) {
        errors.push('午休结束时间必须晚于开始时间');
      }
    }
    
    if (errors.length > 0) {
      return res.status(400).json({ success: false, errors });
    }
    
    res.json({ success: true, message: '配置验证通过' });
  } catch (error) {
    console.error('验证配置失败:', error);
    res.status(500).json({ success: false, message: '验证配置失败' });
  }
});

module.exports = router; 