const express = require('express');
const Holiday = require('../models/Holiday');
const HolidayAutoService = require('../services/HolidayAutoService');
const WorkDayService = require('../services/WorkDayService');
const router = express.Router();

// 获取节假日配置状态
router.get('/status', async (req, res) => {
  try {
    const status = await HolidayAutoService.getHolidayStatus();
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('获取节假日状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取节假日状态失败',
      error: error.message
    });
  }
});

// 自动初始化指定年份的节假日
router.post('/auto-init/:year', async (req, res) => {
  try {
    const year = parseInt(req.params.year);
    
    if (isNaN(year) || year < 2020 || year > 2050) {
      return res.status(400).json({
        success: false,
        message: '年份参数无效，请输入2020-2050之间的年份'
      });
    }
    
    const result = await HolidayAutoService.autoInitializeYear(year);
    
    res.json({
      success: result.success,
      message: result.success ? `成功初始化${year}年节假日配置` : result.error,
      data: result
    });
  } catch (error) {
    console.error('自动初始化节假日失败:', error);
    res.status(500).json({
      success: false,
      message: '自动初始化节假日失败',
      error: error.message
    });
  }
});

// 批量自动初始化多年份节假日
router.post('/auto-init-batch', async (req, res) => {
  try {
    const { years } = req.body;
    
    if (!Array.isArray(years) || years.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的年份数组'
      });
    }
    
    const results = [];
    for (const year of years) {
      if (typeof year === 'number' && year >= 2020 && year <= 2050) {
        const result = await HolidayAutoService.autoInitializeYear(year);
        results.push({ year, ...result });
      } else {
        results.push({ year, success: false, error: '年份无效' });
      }
    }
    
    res.json({
      success: true,
      message: `批量初始化完成`,
      data: results
    });
  } catch (error) {
    console.error('批量初始化节假日失败:', error);
    res.status(500).json({
      success: false,
      message: '批量初始化节假日失败',
      error: error.message
    });
  }
});

// 获取指定年份的节假日详情
router.get('/:year', async (req, res) => {
  try {
    const year = parseInt(req.params.year);
    
    if (isNaN(year)) {
      return res.status(400).json({
        success: false,
        message: '年份参数无效'
      });
    }
    
    const holidays = await Holiday.find({ year: year, isActive: true })
      .sort({ createdAt: 1 });
    
    // 计算该年每个月的工作日统计
    const monthlyStats = {};
    for (let month = 1; month <= 12; month++) {
      const stats = await WorkDayService.calculateMonthWorkDays(year, month);
      monthlyStats[month] = {
        应出勤天数: stats.workDayCount,
        正常工作日: stats.workDayCount - stats.weekendWorkDays,
        调休工作日: stats.weekendWorkDays,
        节假日: stats.holidayCount,
        周末: stats.totalDays - stats.workDayCount - stats.holidayCount
      };
    }
    
    res.json({
      success: true,
      data: {
        year: year,
        holidays: holidays,
        monthlyStats: monthlyStats,
        summary: {
          totalHolidays: holidays.length,
          totalHolidayDays: holidays.reduce((sum, h) => sum + h.holidayDates.length, 0),
          totalWorkDays: holidays.reduce((sum, h) => sum + h.workDates.length, 0)
        }
      }
    });
  } catch (error) {
    console.error('获取节假日详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取节假日详情失败',
      error: error.message
    });
  }
});

// 手动添加节假日
router.post('/', async (req, res) => {
  try {
    const { name, year, type, holidayDates, workDates, description } = req.body;
    
    // 验证必需字段
    if (!name || !year || !holidayDates) {
      return res.status(400).json({
        success: false,
        message: '缺少必需字段：name, year, holidayDates'
      });
    }
    
    // 转换日期字符串为Date对象
    const parsedHolidayDates = holidayDates.map(dateStr => new Date(dateStr));
    const parsedWorkDates = workDates ? workDates.map(dateStr => new Date(dateStr)) : [];
    
    const holiday = new Holiday({
      name,
      year: parseInt(year),
      type: type || 'company',
      holidayDates: parsedHolidayDates,
      workDates: parsedWorkDates,
      description: description || ''
    });
    
    await holiday.save();
    
    res.json({
      success: true,
      message: '节假日添加成功',
      data: holiday
    });
  } catch (error) {
    console.error('添加节假日失败:', error);
    res.status(500).json({
      success: false,
      message: '添加节假日失败',
      error: error.message
    });
  }
});

// 更新节假日
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, holidayDates, workDates, description, isActive } = req.body;
    
    const holiday = await Holiday.findById(id);
    if (!holiday) {
      return res.status(404).json({
        success: false,
        message: '节假日配置不存在'
      });
    }
    
    // 更新字段
    if (name) holiday.name = name;
    if (holidayDates) holiday.holidayDates = holidayDates.map(dateStr => new Date(dateStr));
    if (workDates) holiday.workDates = workDates.map(dateStr => new Date(dateStr));
    if (description !== undefined) holiday.description = description;
    if (isActive !== undefined) holiday.isActive = isActive;
    
    await holiday.save();
    
    res.json({
      success: true,
      message: '节假日更新成功',
      data: holiday
    });
  } catch (error) {
    console.error('更新节假日失败:', error);
    res.status(500).json({
      success: false,
      message: '更新节假日失败',
      error: error.message
    });
  }
});

// 删除节假日
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const holiday = await Holiday.findByIdAndDelete(id);
    if (!holiday) {
      return res.status(404).json({
        success: false,
        message: '节假日配置不存在'
      });
    }
    
    res.json({
      success: true,
      message: '节假日删除成功'
    });
  } catch (error) {
    console.error('删除节假日失败:', error);
    res.status(500).json({
      success: false,
      message: '删除节假日失败',
      error: error.message
    });
  }
});

// 预测未来年份的节假日
router.get('/predict/:year', async (req, res) => {
  try {
    const year = parseInt(req.params.year);
    
    if (isNaN(year) || year <= new Date().getFullYear()) {
      return res.status(400).json({
        success: false,
        message: '请输入未来的年份'
      });
    }
    
    const predictions = await HolidayAutoService.predictFutureHolidays(year);
    
    res.json({
      success: true,
      data: {
        targetYear: year,
        predictions: predictions
      }
    });
  } catch (error) {
    console.error('预测节假日失败:', error);
    res.status(500).json({
      success: false,
      message: '预测节假日失败',
      error: error.message
    });
  }
});

// 系统自检
router.post('/system-check', async (req, res) => {
  try {
    const results = await WorkDayService.systemStartupCheck();
    
    res.json({
      success: true,
      message: '系统自检完成',
      data: results
    });
  } catch (error) {
    console.error('系统自检失败:', error);
    res.status(500).json({
      success: false,
      message: '系统自检失败',
      error: error.message
    });
  }
});

module.exports = router; 