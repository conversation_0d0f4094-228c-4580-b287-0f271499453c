const { calculateMonthlySalary } = require('../utils/SalaryCalculator');
const salaryConfig = require('../config/salaryConfig');

// 测试学历系数计算
const testEducationCoefficient = () => {
    console.log('开始测试学历系数计算...');
    
    const testCases = [
        {
            education: '大专及以下',
            expectedCoefficient: 0.9,
            description: '大专及以下'
        },
        {
            education: '专科',
            expectedCoefficient: 0.9,
            description: '专科'
        },
        {
            education: '大专',
            expectedCoefficient: 0.9,
            description: '大专'
        },
        {
            education: '其他',
            expectedCoefficient: 0.9,
            description: '其他'
        },
        {
            education: '本科（普通院校）',
            expectedCoefficient: 1.0,
            description: '普通本科'
        },
        {
            education: '本科（985/211院校）',
            expectedCoefficient: 1.2,
            description: '985/211本科'
        },
        {
            education: '硕士（普通院校）',
            expectedCoefficient: 1.3,
            description: '普通硕士'
        },
        {
            education: '硕士（985/211院校）',
            expectedCoefficient: 1.5,
            description: '985/211硕士'
        },
        {
            education: '博士（普通院校）',
            expectedCoefficient: 1.6,
            description: '普通博士'
        },
        {
            education: '博士（985/211院校）',
            expectedCoefficient: 1.8,
            description: '985/211博士'
        }
    ];

    testCases.forEach(testCase => {
        const result = calculateMonthlySalary({
            positionLevel: 'A1',
            positionType: '技术',
            education: testCase.education,
            languageLevel: '无',
            administrativeLevel: '无'
        });

        const actualCoefficient = result.educationCoefficient;
        const passed = Math.abs(actualCoefficient - testCase.expectedCoefficient) < 0.001;

        console.log(`测试 ${testCase.description}:`);
        console.log(`  预期系数: ${testCase.expectedCoefficient}`);
        console.log(`  实际系数: ${actualCoefficient}`);
        console.log(`  测试结果: ${passed ? '通过' : '失败'}`);
        console.log('---');
    });
};

// 运行测试
testEducationCoefficient(); 