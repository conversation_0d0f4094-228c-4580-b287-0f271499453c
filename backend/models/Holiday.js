const mongoose = require('mongoose');

const holidaySchema = new mongoose.Schema({
  // 节假日名称
  name: {
    type: String,
    required: true,
    trim: true
  },
  
  // 年份
  year: {
    type: Number,
    required: true
  },
  
  // 节假日类型
  type: {
    type: String,
    enum: ['national', 'traditional', 'company', 'other'],
    default: 'national',
    required: true
  },
  
  // 节假日日期列表（放假的日期）
  holidayDates: [{
    type: Date,
    required: true
  }],
  
  // 调休工作日列表（需要上班的周末）
  workDates: [{
    type: Date
  }],
  
  // 描述
  description: {
    type: String,
    trim: true
  },
  
  // 是否启用
  isActive: {
    type: Boolean,
    default: true
  },
  
  // 创建时间
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // 更新时间
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// 创建复合索引
holidaySchema.index({ year: 1, isActive: 1 });
holidaySchema.index({ year: 1, type: 1 });

// 更新时间中间件
holidaySchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

module.exports = mongoose.model('Holiday', holidaySchema); 