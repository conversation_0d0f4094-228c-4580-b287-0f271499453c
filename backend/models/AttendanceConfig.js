const mongoose = require('mongoose');

const AttendanceConfigSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    default: '默认考勤规则'
  },
  description: {
    type: String,
    default: ''
  },
  // 工作日设置 (0=周日, 1=周一, ..., 6=周六)
  workDays: {
    type: [Number],
    default: [1, 2, 3, 4, 5], // 默认周一到周五
    validate: {
      validator: function(days) {
        return days.every(day => day >= 0 && day <= 6);
      },
      message: '工作日必须在0-6之间'
    }
  },
  // 标准工作时间
  standardWorkTime: {
    startTime: {
      type: String,
      required: true,
      default: '08:30',
      match: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    endTime: {
      type: String,
      required: true,
      default: '17:30',
      match: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    }
  },
  // 弹性工作时间设置
  flexibleTime: {
    enabled: {
      type: Boolean,
      default: false
    },
    // 允许的最早上班时间
    earliestStartTime: {
      type: String,
      default: '08:00',
      match: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    // 允许的最晚上班时间
    latestStartTime: {
      type: String,
      default: '09:00',
      match: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    // 最少工作时长（小时）
    minWorkHours: {
      type: Number,
      default: 8
    }
  },
  // 迟到早退规则
  lateEarlyRules: {
    // 迟到容忍时间（分钟）
    lateTolerance: {
      type: Number,
      default: 0,
      min: 0
    },
    // 早退容忍时间（分钟）
    earlyTolerance: {
      type: Number,
      default: 0,
      min: 0
    },
    // 严重迟到时间（分钟）
    severeLateTime: {
      type: Number,
      default: 30,
      min: 0
    }
  },
  // 午休时间设置
  lunchBreak: {
    enabled: {
      type: Boolean,
      default: true
    },
    startTime: {
      type: String,
      default: '12:00',
      match: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    endTime: {
      type: String,
      default: '13:00',
      match: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    duration: {
      type: Number,
      default: 60 // 分钟
    }
  },
  // 加班规则
  overtimeRules: {
    enabled: {
      type: Boolean,
      default: true
    },
    // 工作日加班开始时间
    weekdayOvertimeStart: {
      type: String,
      default: '17:30',
      match: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    // 最少加班时间才算加班（分钟）
    minOvertimeMinutes: {
      type: Number,
      default: 30,
      min: 0
    },
    // 加班申请是否必需
    requireApproval: {
      type: Boolean,
      default: false
    }
  },
  // 是否为默认配置
  isDefault: {
    type: Boolean,
    default: false
  },
  // 是否启用
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// 更新时自动设置updatedAt
AttendanceConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// 确保只有一个默认配置
AttendanceConfigSchema.pre('save', async function(next) {
  if (this.isDefault) {
    await mongoose.model('AttendanceConfig').updateMany(
      { _id: { $ne: this._id } },
      { isDefault: false }
    );
  }
  next();
});

module.exports = mongoose.model('AttendanceConfig', AttendanceConfigSchema); 