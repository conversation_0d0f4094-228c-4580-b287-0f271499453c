const mongoose = require('mongoose');

const AttendanceSchema = new mongoose.Schema({
  employeeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Employee', required: true },
  employeeCode: { type: String, required: true }, // 考勤号码
  name: { type: String, required: true }, // 姓名
  customId: { type: String }, // 自定义编号
  date: { type: Date, required: true }, // 日期
  
  // 时间相关字段
  workStartTime: { type: Date }, // 上班时间
  workEndTime: { type: Date }, // 下班时间
  checkInTime: { type: Date }, // 签到时间 (实际)
  checkOutTime: { type: Date }, // 签退时间 (实际)
  
  // 应到实到
  shouldAttend: { type: String }, // 应到
  actualAttend: { type: String }, // 实到
  
  // 迟到早退
  lateTime: { type: String }, // 迟到时间
  earlyTime: { type: String }, // 早退时间
  
  // 工作时间统计
  workTime: { type: String }, // 工作时间
  attendanceTime: { type: String }, // 出勤时间
  overtimeTime: { type: String }, // 加班时间
  
  // 加班分类
  weekdayOvertime: { type: String }, // 平日加班
  weekendOvertime: { type: String }, // 周末加班
  holidayOvertime: { type: String }, // 节假日加班
  
  // 其他字段
  isAbsent: { type: Boolean, default: false }, // 是否旷工
  isSmartSchedule: { type: Boolean, default: false }, // 是否智能排班
  timeSlot: { type: String }, // 对应时段
  exception: { type: String }, // 例外情况
  shouldCheckIn: { type: String }, // 应签到
  shouldCheckOut: { type: String }, // 应签退
  department: { type: String }, // 部门
  workdayType: { type: String }, // 平日/周末/节假日
  
  // 原有字段保留兼容性
  checkIn: { type: Date }, // 兼容旧版本
  checkOut: { type: Date }, // 兼容旧版本
  status: { type: String, default: '正常' }, // 考勤状态：正常/迟到/早退/缺勤等
  rawRecords: [String], // 原始打卡记录
  
  // 计算字段
  calculatedWorkHours: { type: Number, default: 0 }, // 计算的工作小时数
  calculatedOvertimeHours: { type: Number, default: 0 }, // 计算的加班小时数
  
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 创建复合索引
AttendanceSchema.index({ employeeId: 1, date: 1 }, { unique: true });
AttendanceSchema.index({ employeeCode: 1, date: 1 });
AttendanceSchema.index({ date: 1 });

// 更新时自动设置updatedAt
AttendanceSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Attendance', AttendanceSchema); 