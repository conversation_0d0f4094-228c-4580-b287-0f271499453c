const mongoose = require('mongoose');
const Holiday = require('../models/Holiday');
const WorkDayService = require('../services/WorkDayService');
require('dotenv').config();

async function initHolidays2025() {
    try {
        // 连接数据库
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hrms');
        console.log('已连接到数据库');
        
        // 删除现有的2025年节假日配置
        await Holiday.deleteMany({ year: 2025 });
        console.log('已清理现有的2025年节假日配置');
        
        // 2025年的实际节假日配置（根据国务院办公厅通知）
        const holidays2025 = [
            {
                name: '元旦',
                year: 2025,
                type: 'national',
                holidayDates: [
                    new Date(2025, 0, 1), // 1月1日
                ],
                description: '元旦节'
            },
            {
                name: '春节',
                year: 2025,
                type: 'national',
                holidayDates: [
                    new Date(2025, 0, 28), // 1月28日（除夕）
                    new Date(2025, 0, 29), // 1月29日（初一）
                    new Date(2025, 0, 30), // 1月30日（初二）
                    new Date(2025, 0, 31), // 1月31日（初三）
                    new Date(2025, 1, 1),  // 2月1日（初四）
                    new Date(2025, 1, 2),  // 2月2日（初五）
                    new Date(2025, 1, 3),  // 2月3日（初六）
                ],
                workDates: [
                    new Date(2025, 0, 26), // 1月26日（周日）调休
                    new Date(2025, 1, 8),  // 2月8日（周六）调休
                ],
                description: '春节假期'
            },
            {
                name: '清明节',
                year: 2025,
                type: 'traditional',
                holidayDates: [
                    new Date(2025, 3, 4), // 4月4日
                    new Date(2025, 3, 5), // 4月5日
                    new Date(2025, 3, 6), // 4月6日
                ],
                description: '清明节'
            },
            {
                name: '劳动节',
                year: 2025,
                type: 'national',
                holidayDates: [
                    new Date(2025, 4, 1), // 5月1日
                    new Date(2025, 4, 2), // 5月2日
                    new Date(2025, 4, 3), // 5月3日
                    new Date(2025, 4, 4), // 5月4日
                    new Date(2025, 4, 5), // 5月5日
                ],
                workDates: [
                    new Date(2025, 3, 27), // 4月27日（周日）调休
                    new Date(2025, 4, 10), // 5月10日（周六）调休
                ],
                description: '五一劳动节'
            },
            {
                name: '端午节',
                year: 2025,
                type: 'traditional',
                holidayDates: [
                    new Date(2025, 4, 31), // 5月31日
                    new Date(2025, 5, 1),  // 6月1日
                    new Date(2025, 5, 2),  // 6月2日
                ],
                description: '端午节'
            },
            {
                name: '中秋节',
                year: 2025,
                type: 'traditional',
                holidayDates: [
                    new Date(2025, 9, 6), // 10月6日
                ],
                description: '中秋节'
            },
            {
                name: '国庆节',
                year: 2025,
                type: 'national',
                holidayDates: [
                    new Date(2025, 9, 1),  // 10月1日
                    new Date(2025, 9, 2),  // 10月2日
                    new Date(2025, 9, 3),  // 10月3日
                    new Date(2025, 9, 4),  // 10月4日
                    new Date(2025, 9, 5),  // 10月5日
                    new Date(2025, 9, 7),  // 10月7日
                    new Date(2025, 9, 8),  // 10月8日
                ],
                workDates: [
                    new Date(2025, 8, 28), // 9月28日（周日）调休
                    new Date(2025, 9, 11), // 10月11日（周六）调休
                ],
                description: '国庆节假期'
            }
        ];
        
        // 保存节假日配置
        await Holiday.insertMany(holidays2025);
        console.log(`✅ 成功创建2025年的${holidays2025.length}个节假日配置`);
        
        // 测试工作日计算
        console.log('\n📊 测试工作日计算：');
        
        // 测试几个月份的工作日计算
        const testMonths = [
            { year: 2025, month: 1, name: '1月（春节）' },
            { year: 2025, month: 5, name: '5月（劳动节+端午）' },
            { year: 2025, month: 6, name: '6月（端午节）' },
            { year: 2025, month: 10, name: '10月（国庆+中秋）' }
        ];
        
        for (const testMonth of testMonths) {
            const result = await WorkDayService.calculateMonthWorkDays(
                testMonth.year, 
                testMonth.month, 
                [1, 2, 3, 4, 5] // 周一到周五
            );
            
            console.log(`${testMonth.name}:`);
            console.log(`  - 总天数: ${result.totalDays}天`);
            console.log(`  - 应出勤天数: ${result.workDayCount}天`);
            console.log(`  - 正常工作日: ${result.workDayCount - result.weekendWorkDays}天`);
            console.log(`  - 调休工作日: ${result.weekendWorkDays}天`);
            console.log(`  - 节假日: ${result.holidayCount}天`);
            console.log(`  - 周末: ${result.totalDays - result.workDayCount - result.holidayCount}天`);
            console.log('');
        }
        
        console.log('🎉 2025年节假日配置初始化完成！');
        console.log('现在考勤系统将正确计算每个月的应出勤天数，包括节假日和调休安排。');
        
    } catch (error) {
        console.error('初始化节假日失败:', error);
    } finally {
        await mongoose.connection.close();
        console.log('数据库连接已关闭');
    }
}

initHolidays2025(); 