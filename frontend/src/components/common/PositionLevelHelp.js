import React from 'react';
import { Typography, Divider } from 'antd';
import './PositionLevelHelp.css';

const { Text, Title } = Typography;

/**
 * 岗位等级说明组件
 * 用于展示各类岗位等级与工作年限的对应关系
 * @param {Object} props
 * @param {boolean} props.showTitle - 是否显示顶部标题
 * @param {boolean} props.showDivider - 是否显示分隔线
 * @param {Array<string>} props.positionTypes - 要显示的岗位类型，默认全部显示
 */
const PositionLevelHelp = ({ 
  showTitle = false, 
  showDivider = false,
  positionTypes = ['tech', 'support', 'other', 'manager']
}) => {
  // 技术岗位工作年限说明
  const techExperienceMap = {
    'A1': '应届毕业生',
    'A2': '1年工作经验',
    'A3': '2年工作经验',
    'A4': '3年工作经验',
    'A5': '4年工作经验',
    'A6': '5-6年工作经验',
    'A7': '7-8年工作经验',
    'A8': '9-10年工作经验',
    'A9': '11-12年工作经验',
    'A10': '13-14年工作经验',
    'A11': '15-16年工作经验',
    'A12': '17-18年工作经验',
    'A13': '19-20年工作经验',
    'A14': '21-22年工作经验',
    'A15': '23-24年工作经验',
    'A16': '25-27年工作经验',
    'A17': '28-30年工作经验',
    'A18': '31-32年工作经验',
    'A19': '33-34年工作经验',
    'A20': '35年及以上工作经验'
  };

  // 支持岗位工作年限说明
  const supportExperienceMap = {
    'C1': '应届毕业生',
    'C2': '1年工作经验',
    'C3': '2年工作经验',
    'C4': '3年工作经验',
    'C5': '4-5年工作经验',
    'C6': '6-7年工作经验',
    'C7': '8-10年工作经验',
    'C8': '11-13年工作经验',
    'C9': '14-16年工作经验',
    'C10': '17-20年工作经验',
    'C11': '21-24年工作经验',
    'C12': '25-28年工作经验',
    'C13': '29-31年工作经验',
    'C14': '32-34年工作经验',
    'C15': '35年及以上工作经验'
  };

  // 其他岗位工作年限说明
  const otherExperienceMap = {
    'D1': '应届毕业生',
    'D2': '1年工作经验',
    'D3': '2年工作经验',
    'D4': '3年工作经验',
    'D5': '4-5年工作经验',
    'D6': '6-7年工作经验',
    'D7': '8-10年工作经验',
    'D8': '11-13年工作经验',
    'D9': '14-16年工作经验',
    'D10': '17-20年工作经验',
    'D11': '21-24年工作经验',
    'D12': '25-28年工作经验',
    'D13': '29-31年工作经验',
    'D14': '32-34年工作经验',
    'D15': '35年及以上工作经验'
  };

  // 高管岗位职位说明
  const managerPositionMap = {
    'B1': '副总监/副总工',
    'B2': '总监/总工',
    'B3': '副总经理',
    'B4': '总经理/董事长'
  };

  return (
    <div className="position-level-help">
      {showTitle && <Title level={4}>以下说明仅供参考，具体薪资以实际为准</Title>}
      
      {positionTypes.includes('tech') && (
        <div className="help-section">
          <Title level={5}>技术岗位等级对应工作年限</Title>
          <ul>
            {Object.entries(techExperienceMap).map(([level, description]) => (
              <li key={level}><Text strong>{level}:</Text> {description}</li>
            ))}
          </ul>
        </div>
      )}

      {positionTypes.includes('support') && (
        <div className="help-section">
          <Title level={5}>支持岗位等级对应工作年限</Title>
          <ul>
            {Object.entries(supportExperienceMap).map(([level, description]) => (
              <li key={level}><Text strong>{level}:</Text> {description}</li>
            ))}
          </ul>
        </div>
      )}

      {positionTypes.includes('other') && (
        <div className="help-section">
          <Title level={5}>其他岗位等级对应工作年限</Title>
          <ul>
            {Object.entries(otherExperienceMap).map(([level, description]) => (
              <li key={level}><Text strong>{level}:</Text> {description}</li>
            ))}
          </ul>
        </div>
      )}

      {showDivider && <Divider />}

      {positionTypes.includes('manager') && (
        <>
          <Title level={4}>高管岗位职位说明</Title>
          <div className="help-section">
            <ul>
              {Object.entries(managerPositionMap).map(([level, description]) => (
                <li key={level}><Text strong>{level}:</Text> {description}</li>
              ))}
            </ul>
          </div>
        </>
      )}
    </div>
  );
};

export default PositionLevelHelp;