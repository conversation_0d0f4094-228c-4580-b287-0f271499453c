import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  TimePicker, 
  Switch, 
  InputNumber, 
  Checkbox, 
  message, 
  Space, 
  Tag, 
  Popconfirm,
  Divider,
  Row,
  Col,
  Tooltip
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SettingOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import './AttendanceConfigManagement.css';
import './AdminDeleteModal.css';

const { TextArea } = Input;

const AttendanceConfigManagement = () => {
  const [configs, setConfigs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [form] = Form.useForm();

  // 调试日志
  const debugLog = (message, data) => {
    console.log(`[AttendanceConfig] ${message}`, data);
  };

  // 工作日选项
  const workDayOptions = [
    { label: '周一', value: 1 },
    { label: '周二', value: 2 },
    { label: '周三', value: 3 },
    { label: '周四', value: 4 },
    { label: '周五', value: 5 },
    { label: '周六', value: 6 },
    { label: '周日', value: 0 },
  ];

  // 表格列定义
  const columns = [
    {
      title: '配置名称',
      dataIndex: 'name',
      key: 'name',
      width: '15%',
      align: 'center',
      render: (text, record) => (
        <div className="config-name">
          <span>{text}</span>
          {record.isDefault && <Tag className="ant-tag-blue">默认</Tag>}
          {!record.isActive && <Tag className="ant-tag-red">已禁用</Tag>}
        </div>
      )
    },
    {
      title: '工作日',
      dataIndex: 'workDays',
      key: 'workDays',
      width: '15%',
      align: 'center',
      render: (workDays) => {
        const dayLabels = ['日', '一', '二', '三', '四', '五', '六'];
        return (
          <span className="work-days">
            {workDays.map(day => `周${dayLabels[day]}`).join(', ')}
          </span>
        );
      }
    },
    {
      title: '标准工作时间',
      key: 'standardWorkTime',
      width: '15%',
      align: 'center',
      render: (text, record) => (
        <span className="time-range">
          {record.standardWorkTime.startTime} - {record.standardWorkTime.endTime}
        </span>
      )
    },
    {
      title: '弹性工作',
      key: 'flexibleTime',
      width: '10%',
      align: 'center',
      render: (text, record) => (
        <Tag className={record.flexibleTime.enabled ? 'ant-tag-green' : 'ant-tag-default'}>
          {record.flexibleTime.enabled ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '午休时间',
      key: 'lunchBreak',
      width: '10%',
      align: 'center',
      render: (text, record) => (
        record.lunchBreak.enabled ? 
        <span className="time-range">{record.lunchBreak.startTime}-{record.lunchBreak.endTime}</span> : 
        <Tag className="ant-tag-default">未设置</Tag>
      )
    },
    {
      title: '迟到容忍',
      key: 'lateTolerance',
      width: '10%',
      align: 'center',
      render: (text, record) => `${record.lateEarlyRules.lateTolerance}分钟`
    },
    {
      title: '操作',
      key: 'actions',
      width: '25%',
      align: 'center',
      render: (text, record) => (
        <div className="config-action-buttons user-action-buttons">
          <span
            className="text-link"
            onClick={() => handleEdit(record)}
          >
            <EditOutlined style={{ marginRight: '4px' }} />
            编辑
          </span>
          
          {!record.isDefault && (
            <span
              className="text-link"
              onClick={() => handleSetDefault(record._id)}
            >
              <SettingOutlined style={{ marginRight: '4px' }} />
              设为默认
            </span>
          )}
          
          <span
            className={`text-link ${record.isDefault && record.isActive ? 'disabled' : ''}`}
            onClick={() => {
              if (!(record.isDefault && record.isActive)) {
                handleToggleActive(record._id);
              }
            }}
            style={{ 
              cursor: record.isDefault && record.isActive ? 'not-allowed' : 'pointer',
              opacity: record.isDefault && record.isActive ? 0.5 : 1
            }}
          >
            {record.isActive ? <CloseOutlined style={{ marginRight: '4px' }} /> : <CheckOutlined style={{ marginRight: '4px' }} />}
            {record.isActive ? '禁用' : '启用'}
          </span>
          
          {!record.isDefault && (
            <span
              className="text-link danger"
              onClick={() => {
                Modal.confirm({
                  title: <span className="delete-modal-title">确认删除</span>,
                  content: (
                    <div className="delete-modal-content">
                      <p>确定要删除配置 {record.name} 吗？</p>
                      <p>此操作不可恢复！</p>
                    </div>
                  ),
                  centered: true,
                  width: 380,
                  className: "admin-delete-modal",
                  maskClosable: true,
                  footer: (_) => (
                    <div className="delete-modal-footer">
                      <Button
                        onClick={() => Modal.destroyAll()}
                        style={{
                          height: '28px',
                          width: '86px',
                          fontSize: '12px',
                          borderRadius: '4px',
                          fontWeight: 500
                        }}
                      >
                        取消
                      </Button>
                      <Button
                        type="primary"
                        danger
                        onClick={() => {
                          handleDelete(record._id);
                          Modal.destroyAll();
                        }}
                        style={{
                          height: '28px',
                          width: '86px',
                          fontSize: '12px',
                          borderRadius: '4px',
                          boxShadow: '0 2px 6px rgba(255, 77, 79, 0.2)',
                          fontWeight: 500
                        }}
                      >
                        确认删除
                      </Button>
                    </div>
                  )
                });
              }}
            >
              <DeleteOutlined style={{ marginRight: '4px' }} />
              删除
            </span>
          )}
        </div>
      )
    }
  ];

  useEffect(() => {
    loadConfigs();
  }, []);

  // 加载配置列表
  const loadConfigs = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/attendance-config');
      const result = await response.json();
      if (result.success) {
        setConfigs(result.data);
      } else {
        message.error('加载配置失败');
      }
    } catch (error) {
      console.error('加载配置失败:', error);
      message.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开新增/编辑模态框
  const handleAdd = () => {
    debugLog('Opening add modal');
    setEditingConfig(null);
    form.resetFields();
    
    const defaultValues = {
      name: '',
      description: '',
      workDays: [1, 2, 3, 4, 5],
      standardWorkTime: {
        startTime: dayjs('08:30', 'HH:mm'),
        endTime: dayjs('17:30', 'HH:mm')
      },
      flexibleTime: {
        enabled: false,
        earliestStartTime: dayjs('08:00', 'HH:mm'),
        latestStartTime: dayjs('09:00', 'HH:mm'),
        minWorkHours: 8
      },
      lateEarlyRules: {
        lateTolerance: 0,
        earlyTolerance: 0,
        severeLateTime: 30
      },
      lunchBreak: {
        enabled: true,
        startTime: dayjs('12:00', 'HH:mm'),
        endTime: dayjs('13:00', 'HH:mm'),
        duration: 60
      },
      overtimeRules: {
        enabled: true,
        weekdayOvertimeStart: dayjs('17:30', 'HH:mm'),
        minOvertimeMinutes: 30,
        requireApproval: false
      },
      isActive: true
    };
    
    debugLog('Setting default values', defaultValues);
    form.setFieldsValue(defaultValues);
    setModalVisible(true);
  };

  // 编辑配置
  const handleEdit = (config) => {
    debugLog('Opening edit modal for config', config);
    setEditingConfig(config);
    
    const editValues = {
      ...config,
      standardWorkTime: {
        startTime: dayjs(config.standardWorkTime.startTime, 'HH:mm'),
        endTime: dayjs(config.standardWorkTime.endTime, 'HH:mm')
      },
      flexibleTime: {
        ...config.flexibleTime,
        earliestStartTime: config.flexibleTime.earliestStartTime ? 
          dayjs(config.flexibleTime.earliestStartTime, 'HH:mm') : dayjs('08:00', 'HH:mm'),
        latestStartTime: config.flexibleTime.latestStartTime ? 
          dayjs(config.flexibleTime.latestStartTime, 'HH:mm') : dayjs('09:00', 'HH:mm')
      },
      lunchBreak: {
        ...config.lunchBreak,
        startTime: config.lunchBreak.startTime ? 
          dayjs(config.lunchBreak.startTime, 'HH:mm') : dayjs('12:00', 'HH:mm'),
        endTime: config.lunchBreak.endTime ? 
          dayjs(config.lunchBreak.endTime, 'HH:mm') : dayjs('13:00', 'HH:mm')
      },
      overtimeRules: {
        ...config.overtimeRules,
        weekdayOvertimeStart: config.overtimeRules.weekdayOvertimeStart ? 
          dayjs(config.overtimeRules.weekdayOvertimeStart, 'HH:mm') : dayjs('17:30', 'HH:mm')
      }
    };
    
    debugLog('Setting edit values', editValues);
    form.setFieldsValue(editValues);
    setModalVisible(true);
  };

  // 安全地转换时间格式
  const formatTime = (timeValue, defaultTime) => {
    if (timeValue && typeof timeValue.format === 'function') {
      return timeValue.format('HH:mm');
    }
    if (typeof timeValue === 'string') {
      return timeValue;
    }
    return defaultTime;
  };

  // 处理表单数据
  const processFormData = (values) => {
    return {
      ...values,
      standardWorkTime: {
        startTime: formatTime(values.standardWorkTime?.startTime, '08:30'),
        endTime: formatTime(values.standardWorkTime?.endTime, '17:30')
      },
      flexibleTime: {
        enabled: values.flexibleTime?.enabled || false,
        earliestStartTime: values.flexibleTime?.enabled ? 
          formatTime(values.flexibleTime?.earliestStartTime, '08:00') : '08:00',
        latestStartTime: values.flexibleTime?.enabled ? 
          formatTime(values.flexibleTime?.latestStartTime, '09:00') : '09:00',
        minWorkHours: values.flexibleTime?.enabled ? 
          (values.flexibleTime?.minWorkHours || 8) : 8
      },
      lunchBreak: {
        enabled: values.lunchBreak?.enabled || false,
        startTime: values.lunchBreak?.enabled ? 
          formatTime(values.lunchBreak?.startTime, '12:00') : '12:00',
        endTime: values.lunchBreak?.enabled ? 
          formatTime(values.lunchBreak?.endTime, '13:00') : '13:00',
        duration: values.lunchBreak?.enabled ? 
          (values.lunchBreak?.duration || 60) : 60
      },
      overtimeRules: {
        enabled: values.overtimeRules?.enabled || false,
        weekdayOvertimeStart: values.overtimeRules?.enabled ? 
          formatTime(values.overtimeRules?.weekdayOvertimeStart, '17:30') : '17:30',
        minOvertimeMinutes: values.overtimeRules?.enabled ? 
          (values.overtimeRules?.minOvertimeMinutes || 30) : 30,
        requireApproval: values.overtimeRules?.requireApproval || false
      }
    };
  };

  // 保存配置
  const handleSave = async () => {
    try {
      debugLog('Starting save process');
      const values = await form.validateFields();
      debugLog('Form values', values);
      
      const configData = processFormData(values);
      debugLog('Processed config data', configData);

      const url = editingConfig ? 
        `/api/attendance-config/${editingConfig._id}` : 
        '/api/attendance-config';
      const method = editingConfig ? 'PUT' : 'POST';

      debugLog('Sending request', { url, method, data: configData });

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(configData)
      });

      const result = await response.json();
      debugLog('Save result', result);
      
      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        form.resetFields();
        setEditingConfig(null);
        loadConfigs();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      debugLog('Save error', error);
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    }
  };

  // 删除配置
  const handleDelete = async (id) => {
    try {
      const response = await fetch(`/api/attendance-config/${id}`, {
        method: 'DELETE'
      });
      const result = await response.json();
      if (result.success) {
        message.success(result.message);
        loadConfigs();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      console.error('删除配置失败:', error);
      message.error('删除配置失败');
    }
  };

  // 设置默认配置
  const handleSetDefault = async (id) => {
    try {
      const response = await fetch(`/api/attendance-config/${id}/set-default`, {
        method: 'POST'
      });
      const result = await response.json();
      if (result.success) {
        message.success(result.message);
        loadConfigs();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      console.error('设置默认配置失败:', error);
      message.error('设置默认配置失败');
    }
  };

  // 切换启用状态
  const handleToggleActive = async (id) => {
    try {
      const response = await fetch(`/api/attendance-config/${id}/toggle-active`, {
        method: 'POST'
      });
      const result = await response.json();
      if (result.success) {
        message.success(result.message);
        loadConfigs();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      console.error('切换状态失败:', error);
      message.error('切换状态失败');
    }
  };

  return (
    <div className="attendance-config-management user-management-container admin-page-container">
      <div className="attendance-config-header user-management-header">
        <h2 className="attendance-config-title user-management-title">考勤配置管理</h2>
        <div className="add-config-button-container add-user-button-container">
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleAdd}
            style={{
              height: '28px',
              width: '86px',
              fontSize: '12px',
              borderRadius: '4px',
              boxShadow: '0 2px 6px rgba(24, 144, 255, 0.2)',
              fontWeight: 500
            }}
          >
            新增配置
          </Button>
        </div>
      </div>

      <Card
        className="config-card attendance-config-table-card"
        title="配置列表"
        type="inner"
        size="small"
      >
        <Table
          columns={columns}
          dataSource={configs}
          loading={loading}
          rowKey="_id"
          className="attendance-config-table"
          bordered
          size="middle"
          scroll={{ x: 1000 }}
          pagination={{
            pageSize: 8,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            size: 'small'
          }}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingConfig ? '编辑考勤配置' : '新增考勤配置'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setEditingConfig(null);
        }}
        footer={null}
        width={900}
        centered
        destroyOnClose={true}
        maskClosable={false}
        styles={{
          body: { maxHeight: '70vh', overflow: 'auto', padding: '20px' },
        }}
      >
        <Form
          form={form}
          layout="vertical"
          scrollToFirstError
          className="attendance-config-form"
        >
          {/* 基本信息 */}
          <Card
            className="config-card"
            title="基本信息"
            type="inner"
            size="small"
            style={{ marginBottom: '16px' }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="配置名称"
                  name="name"
                  rules={[{ required: true, message: '请输入配置名称' }]}
                >
                  <Input placeholder="请输入配置名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="是否启用"
                  name="isActive"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren="启用" 
                    unCheckedChildren="禁用" 
                    style={{ height: '20px', minWidth: '44px' }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              label="描述"
              name="description"
            >
              <TextArea rows={2} placeholder="请输入配置描述" />
            </Form.Item>
            <Form.Item
              label="工作日"
              name="workDays"
              rules={[{ required: true, message: '请选择工作日' }]}
            >
              <Checkbox.Group options={workDayOptions} />
            </Form.Item>
          </Card>

          {/* 标准工作时间 */}
          <Card
            className="config-card"
            title="标准工作时间"
            type="inner"
            size="small"
            style={{ marginBottom: '16px' }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="上班时间"
                  name={['standardWorkTime', 'startTime']}
                  rules={[{ required: true, message: '请选择上班时间' }]}
                >
                  <TimePicker format="HH:mm" placeholder="选择上班时间" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="下班时间"
                  name={['standardWorkTime', 'endTime']}
                  rules={[{ required: true, message: '请选择下班时间' }]}
                >
                  <TimePicker format="HH:mm" placeholder="选择下班时间" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 弹性工作时间 */}
          <Card
            className="config-card"
            title="弹性工作时间"
            type="inner"
            size="small"
            style={{ marginBottom: '16px' }}
          >
            <Form.Item
              label="启用弹性工作时间"
              name={['flexibleTime', 'enabled']}
              valuePropName="checked"
              style={{ marginBottom: '16px' }}
            >
              <Switch style={{ height: '20px', minWidth: '44px' }} />
            </Form.Item>
            
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="最早上班时间"
                  name={['flexibleTime', 'earliestStartTime']}
                >
                  <TimePicker format="HH:mm" placeholder="最早上班时间" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="最晚上班时间"
                  name={['flexibleTime', 'latestStartTime']}
                >
                  <TimePicker format="HH:mm" placeholder="最晚上班时间" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="最少工作时长(小时)"
                  name={['flexibleTime', 'minWorkHours']}
                >
                  <InputNumber min={1} max={12} placeholder="最少工作时长" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 迟到早退规则 */}
          <Card
            className="config-card"
            title="迟到早退规则"
            type="inner"
            size="small"
            style={{ marginBottom: '16px' }}
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="迟到容忍时间(分钟)"
                  name={['lateEarlyRules', 'lateTolerance']}
                >
                  <InputNumber min={0} max={60} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="早退容忍时间(分钟)"
                  name={['lateEarlyRules', 'earlyTolerance']}
                >
                  <InputNumber min={0} max={60} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="严重迟到时间(分钟)"
                  name={['lateEarlyRules', 'severeLateTime']}
                >
                  <InputNumber min={1} max={120} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 午休时间 */}
          <Card
            className="config-card"
            title="午休时间"
            type="inner"
            size="small"
            style={{ marginBottom: '16px' }}
          >
            <Form.Item
              label="启用午休时间"
              name={['lunchBreak', 'enabled']}
              valuePropName="checked"
              style={{ marginBottom: '16px' }}
            >
              <Switch style={{ height: '20px', minWidth: '44px' }} />
            </Form.Item>
            
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="午休开始时间"
                  name={['lunchBreak', 'startTime']}
                >
                  <TimePicker format="HH:mm" placeholder="午休开始时间" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="午休结束时间"
                  name={['lunchBreak', 'endTime']}
                >
                  <TimePicker format="HH:mm" placeholder="午休结束时间" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="午休时长(分钟)"
                  name={['lunchBreak', 'duration']}
                >
                  <InputNumber min={30} max={120} placeholder="午休时长" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 加班规则 */}
          <Card
            className="config-card"
            title="加班规则"
            type="inner"
            size="small"
            style={{ marginBottom: '16px' }}
          >
            <Form.Item
              label="启用加班计算"
              name={['overtimeRules', 'enabled']}
              valuePropName="checked"
              style={{ marginBottom: '16px' }}
            >
              <Switch style={{ height: '20px', minWidth: '44px' }} />
            </Form.Item>
            
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="加班开始时间"
                  name={['overtimeRules', 'weekdayOvertimeStart']}
                >
                  <TimePicker format="HH:mm" placeholder="加班开始时间" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="最少加班时间(分钟)"
                  name={['overtimeRules', 'minOvertimeMinutes']}
                >
                  <InputNumber min={15} max={120} placeholder="最少加班时间" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="需要加班申请"
                  name={['overtimeRules', 'requireApproval']}
                  valuePropName="checked"
                >
                  <Switch style={{ height: '20px', minWidth: '44px' }} />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 表单按钮 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'flex-end', 
            gap: '12px',
            marginTop: '24px',
            paddingTop: '16px',
            borderTop: '1px solid #f0f0f0'
          }}>
            <Button
              onClick={() => {
                setModalVisible(false);
                form.resetFields();
                setEditingConfig(null);
              }}
              style={{
                height: '32px',
                padding: '0 20px',
                fontSize: '14px',
                borderRadius: '6px'
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleSave}
              style={{
                height: '32px',
                padding: '0 20px',
                fontSize: '14px',
                borderRadius: '6px'
              }}
            >
              保存
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default AttendanceConfigManagement; 