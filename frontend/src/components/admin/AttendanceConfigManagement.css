/* 导入用户管理的通用样式 */
@import './AdminCommon.css';
@import './SalaryConfig.css';

.attendance-config-management {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  max-width: 1200px;
  margin: 0 auto;
}

/* 考勤配置表单样式 */
.attendance-config-form {
  width: 100%;
}

.attendance-config-form .config-card {
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
  width: 100%;
}

.attendance-config-form .config-card .ant-card-head {
  padding: 8px 12px;
  border-radius: 4px 4px 0 0;
}

.attendance-config-form .config-card .ant-card-body {
  padding: 16px;
  background-color: #fff;
}

.attendance-config-form .ant-form-item-label > label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.attendance-config-form .ant-input,
.attendance-config-form .ant-input-number,
.attendance-config-form .ant-picker,
.attendance-config-form .ant-select {
  height: 32px;
  font-size: 14px;
}

.attendance-config-form .ant-input-number {
  width: 100%;
}

.attendance-config-form .ant-form-item {
  margin-bottom: 16px;
}

.attendance-config-form .ant-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px 16px;
}

.attendance-config-form .ant-checkbox-wrapper {
  margin: 0;
  font-size: 14px;
}

.attendance-config-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.attendance-config-title {
  margin: 0;
  font-size: 20px;
  color: #333;
  font-weight: 600;
  text-align: center;
}

.add-config-button-container {
  position: absolute;
  right: 0;
  top: 0;
}

.attendance-config-table-card {
  margin: 20px auto;
  width: 100%;
}

.attendance-config-table {
  margin: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
}

.attendance-config-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 500;
  text-align: center;
  padding: 10px 12px;
  font-size: 13px;
}

.attendance-config-table .ant-table-tbody > tr > td {
  text-align: center;
  padding: 10px 12px;
  font-size: 14px;
}

/* 文本链接样式 */
.text-link {
  color: #1890ff;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: inline-flex;
  align-items: center;
}

.text-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.text-link.danger {
  color: #ff4d4f;
}

.text-link.danger:hover {
  color: #ff7875;
  text-decoration: underline;
}

.text-link.disabled {
  color: #bfbfbf;
  cursor: not-allowed;
}

.config-action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.config-name {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.config-name .ant-tag {
  margin: 0;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.work-days {
  font-size: 13px;
  color: #595959;
}

.time-range {
  font-size: 13px;
  color: #595959;
  font-family: 'SF Mono', Monaco, monospace;
}

.attendance-config-management .ant-modal {
  top: 20px;
}

.attendance-config-management .ant-modal-header {
  padding: 16px 24px 12px;
  border-bottom: 1px solid #e8e8e8;
  border-radius: 8px 8px 0 0;
}

.attendance-config-management .ant-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.attendance-config-management .ant-modal-body {
  padding: 20px 24px;
  max-height: 50vh;
  overflow-y: auto;
}

.attendance-config-management .ant-modal-footer {
  padding: 12px 24px 16px;
  border-top: 1px solid #e8e8e8;
  border-radius: 0 0 8px 8px;
  display: flex !important;
  flex-direction: row !important;
  justify-content: flex-end !important;
  align-items: center !important;
  gap: 12px !important;
}

.attendance-config-management .ant-modal-footer .ant-btn {
  height: 32px;
  padding: 0 20px;
  font-size: 14px;
  border-radius: 6px;
  margin: 0 !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.attendance-config-management .ant-form {
  max-width: none;
}

.attendance-config-management .ant-form-item {
  margin-bottom: 16px;
}

.attendance-config-management .ant-form-item-label {
  padding-bottom: 4px;
}

.attendance-config-management .ant-form-item-label > label {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  height: 18px;
  line-height: 18px;
}

.attendance-config-management .ant-form-item-control-input {
  min-height: 32px;
}

.attendance-config-management .ant-input,
.attendance-config-management .ant-input-number,
.attendance-config-management .ant-picker {
  height: 32px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  font-size: 14px;
}

.attendance-config-management .ant-input:focus,
.attendance-config-management .ant-input-number:focus,
.attendance-config-management .ant-picker:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.attendance-config-management .ant-input-number {
  width: 100%;
}

.attendance-config-management .ant-picker {
  width: 100%;
}

.attendance-config-management .ant-select {
  font-size: 14px;
}

.attendance-config-management .ant-select-selector {
  height: 32px !important;
  border-radius: 6px !important;
}

.attendance-config-management .ant-switch {
  height: 20px;
  min-width: 44px;
  line-height: 18px;
  vertical-align: middle;
}

.attendance-config-management .ant-switch-inner {
  font-size: 11px;
  line-height: 18px;
}

.attendance-config-management .ant-switch-handle {
  width: 16px;
  height: 16px;
  top: 2px;
}

.attendance-config-management .ant-switch-handle::before {
  border-radius: 8px;
}

.attendance-config-management .ant-form-item-control-input-content .ant-switch {
  margin-top: 0;
}

/* 统一所有开关按钮的容器样式 */
.attendance-config-management .ant-form-item-control-input-content {
  display: flex;
  align-items: center;
}

/* 针对开关按钮的表单项特殊处理 */
.attendance-config-management .ant-form-item[data-switch="true"] .ant-form-item-control-input {
  min-height: 32px;
  display: flex;
  align-items: center;
}

.attendance-config-management .ant-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px 16px;
}

.attendance-config-management .ant-checkbox-wrapper {
  margin: 0;
  font-size: 14px;
}

.attendance-config-management .ant-checkbox-wrapper .ant-checkbox {
  margin-right: 6px;
}

.attendance-config-management .ant-divider {
  margin: 16px 0 12px;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.attendance-config-management .ant-divider-inner-text {
  padding: 0 16px;
}

.attendance-config-management .ant-row {
  margin-left: -8px;
  margin-right: -8px;
}

.attendance-config-management .ant-col {
  padding-left: 8px;
  padding-right: 8px;
}

.attendance-config-management .ant-input[type="textarea"] {
  min-height: 60px;
  resize: vertical;
  border-radius: 6px;
}

.attendance-config-management .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  margin: 2px 4px 2px 0;
}

.attendance-config-management .ant-tag-blue {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.attendance-config-management .ant-tag-green {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.attendance-config-management .ant-tag-red {
  background-color: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.attendance-config-management .ant-tag-default {
  background-color: #fafafa;
  border-color: #d9d9d9;
  color: #8c8c8c;
}

/* 模态框响应式设计 */
@media (max-width: 1200px) {
  .attendance-config-management {
    max-width: 90%;
  }
  
  .attendance-config-management .ant-modal {
    width: 95vw !important;
    max-width: 800px !important;
  }
}

@media (max-width: 768px) {
  .attendance-config-management {
    padding: 16px;
    max-width: 95%;
  }
  
  .attendance-config-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .add-config-button-container {
    position: static;
    text-align: center;
  }
  
  .attendance-config-management .ant-modal {
    margin: 0;
    top: 0;
    height: 100vh;
    max-width: 100vw;
    width: 100vw !important;
  }

  .attendance-config-management .ant-modal-body {
    padding: 16px !important;
    max-height: calc(100vh - 120px);
  }

  .config-action-buttons {
    gap: 8px;
    flex-direction: column;
  }
  
  .text-link {
    font-size: 12px;
    padding: 4px 0;
  }

  /* 表单在小屏幕上的响应式调整 */
  .attendance-config-form .ant-col {
    margin-bottom: 8px;
  }

  .attendance-config-form .config-card .ant-card-body {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .attendance-config-management .ant-col {
    padding-left: 4px;
    padding-right: 4px;
  }
  
  .attendance-config-management .ant-row {
    margin-left: -4px;
    margin-right: -4px;
  }
  
  .attendance-config-management .ant-form-item {
    margin-bottom: 16px;
  }
}

/* 额外的表单样式优化 */
.attendance-config-form .config-card .ant-card-head-title {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.attendance-config-form .ant-input[type="textarea"] {
  min-height: 60px;
  resize: vertical;
  border-radius: 6px;
}

.attendance-config-form .ant-checkbox-group .ant-checkbox-wrapper {
  margin-right: 16px;
  margin-bottom: 8px;
}