import React, { useState, useEffect } from 'react';
import { message, Tabs, Form, Button, Table, InputNumber, Select, Card, Collapse, Tooltip, Divider, Radio, Row, Col, Input, Switch } from 'antd';
import { InfoCircleOutlined, SaveOutlined, } from '@ant-design/icons';
import config from '../../config';
import './SalaryConfig.css';
import './AdminModals.css';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

const SalaryConfig = () => {
    const [loading, setLoading] = useState(false);
    const [salaryConfig, setSalaryConfig] = useState(null);
    const [form] = Form.useForm();
    const [activeTabKey, setActiveTabKey] = useState(() => {
        // 从本地存储恢复上次选中的选项卡
        const savedTab = localStorage.getItem('salaryConfigActiveTab');
        return savedTab || '1';
    });

    // 获取薪资配置
    const fetchSalaryConfig = async () => {
        setLoading(true);
        try {
            const token = localStorage.getItem('token');
            console.log('使用 token 获取薪资配置:', token ? token.substring(0, 10) + '...' : 'null');

            // 添加时间戳防止缓存
            const timestamp = new Date().getTime();
            const response = await fetch(`${config.apiBaseUrl}/admin/salary-config?t=${timestamp}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Expires': '-1'
                }
            });

            console.log('薪资配置请求状态:', response.status);

            if (!response.ok) {
                // 检查是否是权限问题
                if (response.status === 403) {
                    message.error('您没有权限访问薪资配置');
                    // 可能需要重定向到首页
                    setTimeout(() => {
                        window.location.href = '/#/dashboard';
                    }, 2000);
                    return;
                } else if (response.status === 401) {
                    message.error('登录已过期，请重新登录');
                    // 清除登录信息
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    // 重定向到登录页面
                    setTimeout(() => {
                        window.location.href = '/#/admin-login';
                    }, 2000);
                    return;
                }

                // 尝试获取错误详情
                let errorDetail = '';
                try {
                    const errorData = await response.json();
                    errorDetail = errorData.message || '';
                } catch (e) {
                    // 忽略解析错误
                }

                throw new Error(`获取薪资配置失败: ${response.status}${errorDetail ? ' - ' + errorDetail : ''}`);
            }

            const data = await response.json();
            console.log('获取到的薪资配置:', JSON.stringify(data, null, 2));

            // 保存配置时间戳，用于验证配置是否最新
            localStorage.setItem('salaryConfigTimestamp', timestamp);

            // 详细检查社保基数配置
            if (data.INSURANCE_BASE) {
                console.log('获取到的社保基数类型:', data.INSURANCE_BASE.type);
                console.log('获取到的社保基数配置:', JSON.stringify(data.INSURANCE_BASE, null, 2));
            } else {
                console.warn('获取到的配置中没有社保基数信息');

                // 尝试从本地存储恢复 INSURANCE_BASE 字段
                try {
                    const backupConfig = localStorage.getItem('salaryConfigBackup');
                    if (backupConfig) {
                        const parsedBackup = JSON.parse(backupConfig);
                        if (parsedBackup.INSURANCE_BASE) {
                            console.log('从本地存储恢复社保基数配置:', JSON.stringify(parsedBackup.INSURANCE_BASE, null, 2));
                            data.INSURANCE_BASE = parsedBackup.INSURANCE_BASE;
                        }
                    }
                } catch (e) {
                    console.error('从本地存储恢复社保基数配置失败:', e);
                }

                // 如果仍然没有 INSURANCE_BASE 字段，创建一个默认值
                if (!data.INSURANCE_BASE) {
                    console.log('创建默认社保基数配置');
                    data.INSURANCE_BASE = {
                        type: 'baseSalary',
                        fixedAmount: 5000,
                        limits: {
                            pension: {
                                min: 4638.88,
                                max: 23194.6
                            },
                            medical: {
                                min: 4853,
                                max: 24267
                            },
                            unemployment: {
                                min: 4217,
                                max: 21086
                            }
                        }
                    };
                }
            }

            // 更新全局状态
            setSalaryConfig(data);

            // 如果有表单实例，更新表单值
            if (form) {
                form.setFieldsValue(data);
            }

            // 立即更新所有表单组件
            updateAllFormComponents(data);

            return data;
        } catch (error) {
            console.error('获取薪资配置错误:', error);
            message.error(error.message);

            // 如果是网络错误，可能是后端服务未启动
            if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
                message.error('无法连接到服务器，请检查网络连接或联系管理员');
            }

            return null;
        } finally {
            setLoading(false);
        }
    };

    // 更新所有表单组件
    const updateAllFormComponents = (data) => {
        console.log('更新所有表单组件:', data);

        // 创建一个自定义事件，并附加配置数据
        const event = new CustomEvent('salaryConfigUpdated', { detail: data });
        window.dispatchEvent(event);
    };

    // 更新薪资配置
    const updateSalaryConfig = async (values) => {
        setLoading(true);
        try {
            // 打印要发送的数据
            console.log('发送到服务器的配置数据:', JSON.stringify(values, null, 2));

            const token = localStorage.getItem('token');
            const response = await fetch(`${config.apiBaseUrl}/admin/salary-config`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Expires': '-1'
                },
                body: JSON.stringify(values)
            });

            // 打印响应状态
            console.log('服务器响应状态:', response.status, response.statusText);

            if (!response.ok) {
                throw new Error('更新薪资配置失败');
            }

            // 获取更新后的配置
            const result = await response.json();
            console.log('更新成功，服务器返回的配置:', JSON.stringify(result, null, 2));

            // 检查返回的配置中是否包含社保基数类型
            if (result.updatedConfig && result.updatedConfig.INSURANCE_BASE) {
                console.log('服务器返回的社保基数类型:', result.updatedConfig.INSURANCE_BASE.type);
            } else {
                console.warn('服务器返回的配置中没有社保基数类型信息');
            }

            // 保存服务器返回的时间戳
            if (result.timestamp) {
                localStorage.setItem('salaryConfigTimestamp', result.timestamp);
            }

            // 更新全局状态
            if (result.updatedConfig) {
                // 合并当前配置和更新的配置
                const updatedConfig = {
                    ...salaryConfig,
                    ...result.updatedConfig
                };

                console.log('合并后的完整配置:', JSON.stringify(updatedConfig, null, 2));

                // 确保 INSURANCE_BASE 字段存在
                if (!updatedConfig.INSURANCE_BASE && values.INSURANCE_BASE) {
                    console.log('服务器返回的配置中没有 INSURANCE_BASE 字段，使用本地值');
                    updatedConfig.INSURANCE_BASE = values.INSURANCE_BASE;
                }

                // 确保传入的所有字段都被正确合并
                Object.keys(values).forEach(key => {
                    if (!updatedConfig[key] && values[key]) {
                        console.log(`服务器返回的配置中没有 ${key} 字段，使用本地值`);
                        updatedConfig[key] = values[key];
                    }
                });

                // 更新全局状态
                setSalaryConfig(updatedConfig);

                // 如果有表单实例，更新表单值
                if (form) {
                    form.setFieldsValue(updatedConfig);
                }

                // 立即更新所有表单组件
                updateAllFormComponents(updatedConfig);

                // 保存到本地存储，作为备份
                try {
                    localStorage.setItem('salaryConfigBackup', JSON.stringify(updatedConfig));
                    console.log('配置已保存到本地存储作为备份');
                } catch (e) {
                    console.error('保存配置到本地存储失败:', e);
                }
            }

            // 保存当前活动的选项卡到本地存储
            localStorage.setItem('salaryConfigActiveTab', activeTabKey);

            return result.updatedConfig;
        } catch (error) {
            console.error('更新薪资配置错误:', error);
            message.error(error.message);
            return null;
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchSalaryConfig();
    }, []);

    if (!salaryConfig) {
        return <div>加载中...</div>;
    }

    // 基础薪资配置表单
    const BasicSalaryForm = () => {
        // 使用字符串类型的状态来存储输入值，避免数值转换导致的光标跳动
        const [inputValues, setInputValues] = useState({
            BASE_SALARY: String(salaryConfig.BASE_SALARY || '0'),
            MEAL_ALLOWANCE: String(salaryConfig.MEAL_ALLOWANCE || '0'),
            COMMUNICATION_ALLOWANCE: String(salaryConfig.COMMUNICATION_ALLOWANCE || '0')
        });

        // 真实值用于保存和提交
        const [basicSalary, setBasicSalary] = useState({
            BASE_SALARY: salaryConfig.BASE_SALARY,
            MEAL_ALLOWANCE: salaryConfig.MEAL_ALLOWANCE,
            COMMUNICATION_ALLOWANCE: salaryConfig.COMMUNICATION_ALLOWANCE
        });

        // 当 salaryConfig 更新时，更新本地状态
        useEffect(() => {
            setBasicSalary({
                BASE_SALARY: salaryConfig.BASE_SALARY,
                MEAL_ALLOWANCE: salaryConfig.MEAL_ALLOWANCE,
                COMMUNICATION_ALLOWANCE: salaryConfig.COMMUNICATION_ALLOWANCE
            });

            setInputValues({
                BASE_SALARY: String(salaryConfig.BASE_SALARY || '0'),
                MEAL_ALLOWANCE: String(salaryConfig.MEAL_ALLOWANCE || '0'),
                COMMUNICATION_ALLOWANCE: String(salaryConfig.COMMUNICATION_ALLOWANCE || '0')
            });
        }, [salaryConfig]);

        // 格式化显示值（添加货币符号）
        const formatDisplayValue = (value) => {
            if (value === '' || value === '0') return '¥ 0';
            return `¥ ${value}`;
        };

        // 解析输入值（移除货币符号）
        const parseInputValue = (value) => {
            return value.replace(/[^0-9]/g, '');
        };

        // 处理输入变化（不会立即更新全局状态）
        const handleInputChange = (field, e) => {
            // 防止事件冒泡，避免触发其他事件
            e.stopPropagation();

            // 解析输入值，只保留数字
            const rawValue = parseInputValue(e.target.value);

            // 更新输入值
            setInputValues(prev => ({
                ...prev,
                [field]: rawValue
            }));
        };

        // 处理失去焦点，将输入值转换为数值并更新本地状态（不更新全局状态）
        const handleBlur = (field, e) => {
            // 获取输入值并转换为数值
            const value = parseFloat(e.target.value.replace(/[^0-9.]/g, ''));
            if (!isNaN(value)) {
                // 只更新本地状态
                setBasicSalary(prev => ({
                    ...prev,
                    [field]: value
                }));
            }
        };

        const handleSave = async () => {
            try {
                // 使用统一的更新函数保存基础薪资配置
                await updateSalaryConfig(basicSalary);

                // 保存当前活动的选项卡
                localStorage.setItem('salaryConfigActiveTab', activeTabKey);

                // 成功消息由全局保存函数统一处理
            } catch (error) {
                console.error('保存基础薪资配置失败:', error);
                message.error('保存基础薪资配置失败');
            }
        };

        return (
            <div>
                <Card
                    className="config-card basic-salary-card"
                    title="基础薪资配置"
                    type="inner"
                    size="small"
                >
                    <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap' }}>
                        <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>
                            <div style={{ whiteSpace: 'nowrap', marginRight: '12px' }}>基础工资：</div>
                            <InputNumber
                                min={0}
                                step={100}
                                precision={0}
                                style={{ width: '96px', marginRight: '22px' }}
                                value={inputValues.BASE_SALARY ? parseFloat(inputValues.BASE_SALARY) : 0}
                                formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                                onChange={(value) => {
                                    // 只更新本地输入状态，不更新全局状态
                                    setInputValues(prev => ({
                                        ...prev,
                                        BASE_SALARY: value ? String(value) : '0'
                                    }));

                                    // 同时更新本地保存状态
                                    setBasicSalary(prev => ({
                                        ...prev,
                                        BASE_SALARY: value || 0
                                    }));
                                }}
                                className="admin-input-number uniform-input"
                                placeholder="请输入基础工资"
                            />
                            <div style={{ whiteSpace: 'nowrap', marginRight: '12px' }}>餐补金额：</div>
                            <InputNumber
                                min={0}
                                step={100}
                                precision={0}
                                style={{ width: '96px', marginRight: '22px' }}
                                value={inputValues.MEAL_ALLOWANCE ? parseFloat(inputValues.MEAL_ALLOWANCE) : 0}
                                formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                                onChange={(value) => {
                                    // 只更新本地输入状态，不更新全局状态
                                    setInputValues(prev => ({
                                        ...prev,
                                        MEAL_ALLOWANCE: value ? String(value) : '0'
                                    }));

                                    // 同时更新本地保存状态
                                    setBasicSalary(prev => ({
                                        ...prev,
                                        MEAL_ALLOWANCE: value || 0
                                    }));
                                }}
                                className="admin-input-number uniform-input"
                                placeholder="请输入餐补"
                            />
                            <div style={{ whiteSpace: 'nowrap', marginRight: '12px' }}>通讯补贴：</div>
                            <InputNumber
                                min={0}
                                step={100}
                                precision={0}
                                style={{ width: '96px' }}
                                value={inputValues.COMMUNICATION_ALLOWANCE ? parseFloat(inputValues.COMMUNICATION_ALLOWANCE) : 0}
                                formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                                onChange={(value) => {
                                    // 只更新本地输入状态，不更新全局状态
                                    setInputValues(prev => ({
                                        ...prev,
                                        COMMUNICATION_ALLOWANCE: value ? String(value) : '0'
                                    }));

                                    // 同时更新本地保存状态
                                    setBasicSalary(prev => ({
                                        ...prev,
                                        COMMUNICATION_ALLOWANCE: value || 0
                                    }));
                                }}
                                className="admin-input-number uniform-input"
                                placeholder="请输入通讯补贴"
                            />
                        </div>
                    </div>
                </Card>
            </div>
        );
    };

    // 社保基数类型控制组件（集成到全局保存机制）
    const DirectInsuranceBaseControl = () => {
        // 使用本地状态管理社保基数类型
        const [baseType, setBaseType] = useState(salaryConfig.INSURANCE_BASE?.type || 'baseSalary');
        const [fixedAmount, setFixedAmount] = useState(salaryConfig.INSURANCE_BASE?.fixedAmount || 5000);
        // 使用字符串状态存储固定金额输入值，避免输入中断
        const [inputValue, setInputValue] = useState(String(salaryConfig.INSURANCE_BASE?.fixedAmount || 5000));
        // 添加一个标记，防止无限循环
        const [isInitialized, setIsInitialized] = useState(false);

        // 本地状态，用于保存最终值
        const [localInsuranceBase, setLocalInsuranceBase] = useState({
            type: salaryConfig.INSURANCE_BASE?.type || 'baseSalary',
            fixedAmount: salaryConfig.INSURANCE_BASE?.fixedAmount || 5000,
            limits: salaryConfig.INSURANCE_BASE?.limits || {
                pension: {
                    min: 4638.88,
                    max: 23194.6
                },
                medical: {
                    min: 4853,
                    max: 24267
                },
                unemployment: {
                    min: 4217,
                    max: 21086
                }
            }
        });

        // 初始化本地状态，只执行一次
        useEffect(() => {
            if (!isInitialized && salaryConfig.INSURANCE_BASE) {
                const type = salaryConfig.INSURANCE_BASE.type || 'baseSalary';
                const amount = salaryConfig.INSURANCE_BASE.fixedAmount || 5000;

                setBaseType(type);
                setFixedAmount(amount);
                setInputValue(String(amount));

                setLocalInsuranceBase({
                    type: type,
                    fixedAmount: amount,
                    limits: salaryConfig.INSURANCE_BASE.limits || {
                        pension: {
                            min: 4638.88,
                            max: 23194.6
                        },
                        medical: {
                            min: 4853,
                            max: 24267
                        },
                        unemployment: {
                            min: 4217,
                            max: 21086
                        }
                    }
                });

                setIsInitialized(true);
            }
        }, []);

        // 解析货币输入值
        const parseCurrencyValue = (value) => {
            return value.replace(/[^0-9]/g, '');
        };

        // 处理用户手动更改类型
        const handleTypeChange = (e) => {
            const newType = e.target.value;
            setBaseType(newType);

            // 更新本地保存状态
            setLocalInsuranceBase(prev => ({
                ...prev,
                type: newType
            }));

            // 保存到本地存储作为备份
            localStorage.setItem('insuranceBaseType', newType);
        };

        // 渲染组件
        return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
                <Radio.Group
                    value={baseType}
                    onChange={handleTypeChange}
                    style={{ marginRight: '16px' }}
                    className="uniform-input"
                >
                    <Radio value="baseSalary" className="uniform-input">使用基本工资</Radio>
                    <Radio value="fixed" className="uniform-input">使用固定金额</Radio>
                </Radio.Group>

                {baseType === 'fixed' && (
                    <InputNumber
                        min={0}
                        step={100}
                        precision={0}
                        style={{ width: '126px' }}
                        value={parseFloat(inputValue) || 0}
                        formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                        onChange={(value) => {
                            // 更新本地输入状态
                            setInputValue(value ? String(value) : '0');

                            // 更新固定金额本地状态
                            setFixedAmount(value || 0);

                            // 更新本地保存状态
                            setLocalInsuranceBase(prev => ({
                                ...prev,
                                fixedAmount: value || 0
                            }));

                            // 保存到本地存储作为备份
                            localStorage.setItem('insuranceFixedAmount', value || 0);
                        }}
                        className="admin-input-number uniform-input"
                        placeholder="请输入固定金额"
                    />
                )}
            </div>
        );
    };

    // 保存社保基数类型的函数
    const saveInsuranceBaseType = async () => {
        try {
            // 从本地存储获取当前社保基数类型和固定金额
            const baseType = localStorage.getItem('insuranceBaseType') || salaryConfig.INSURANCE_BASE?.type || 'baseSalary';
            const fixedAmount = localStorage.getItem('insuranceFixedAmount') || salaryConfig.INSURANCE_BASE?.fixedAmount || 5000;

            // 构建社保基数配置
            const insuranceBase = {
                type: baseType,
                fixedAmount: parseInt(fixedAmount) || 5000,
                limits: salaryConfig.INSURANCE_BASE?.limits || {
                    pension: {
                        min: 4638.88,
                        max: 23194.6
                    },
                    medical: {
                        min: 4853,
                        max: 24267
                    },
                    unemployment: {
                        min: 4217,
                        max: 21086
                    }
                }
            };

            // 构建完整配置
            const completeConfig = {
                INSURANCE_RATES: salaryConfig.INSURANCE_RATES || {
                    pension: 0.08,
                    medical: 0.02,
                    unemployment: 0.003,
                    supplementaryMedical: 0.016
                },
                INSURANCE_BASE: insuranceBase
            };

            console.log('保存社保基数类型:', baseType);
            console.log('发送完整配置:', JSON.stringify(completeConfig, null, 2));

            // 使用统一的更新函数
            await updateSalaryConfig(completeConfig);

            return true;
        } catch (error) {
            console.error('保存社保基数类型失败:', error);
            message.error('保存失败: ' + error.message);
            return false;
        }
    };

    // 社保参数配置表单
    const InsuranceRatesForm = () => {
        // 使用字符串类型的状态来存储输入值，避免数值转换导致的光标跳动
        const [inputValues, setInputValues] = useState({
            pension: String(salaryConfig.INSURANCE_RATES.pension || '0'),
            medical: String(salaryConfig.INSURANCE_RATES.medical || '0'),
            unemployment: String(salaryConfig.INSURANCE_RATES.unemployment || '0'),
            supplementaryMedical: String(salaryConfig.INSURANCE_RATES.supplementaryMedical || '0'),
            limits: {
                pension: {
                    min: String(salaryConfig.INSURANCE_BASE?.limits?.pension?.min || '0'),
                    max: String(salaryConfig.INSURANCE_BASE?.limits?.pension?.max || '0')
                },
                medical: {
                    min: String(salaryConfig.INSURANCE_BASE?.limits?.medical?.min || '0'),
                    max: String(salaryConfig.INSURANCE_BASE?.limits?.medical?.max || '0')
                },
                unemployment: {
                    min: String(salaryConfig.INSURANCE_BASE?.limits?.unemployment?.min || '0'),
                    max: String(salaryConfig.INSURANCE_BASE?.limits?.unemployment?.max || '0')
                }
            }
        });

        // 格式化百分比显示值
        const formatPercentValue = (value) => {
            if (!value || value === '') return '0%';

            // 如果值已经是小数形式（如0.08），直接转为百分比
            const numValue = parseFloat(value);
            if (isNaN(numValue)) return '0%';

            // 如果值小于1，认为是小数形式（如0.08），转为百分比
            if (numValue < 1) {
                return `${(numValue * 100).toFixed(1)}%`;
            }

            // 如果值已经是百分比形式（如8），直接添加百分号
            return `${numValue.toFixed(1)}%`;
        };

        // 解析百分比输入值
        const parsePercentValue = (value) => {
            // 移除所有非数字、非小数点字符
            return value.replace(/[^0-9.]/g, '');
        };

        // 格式化金额显示值
        const formatCurrencyValue = (value) => {
            if (value === '' || value === '0') return '¥ 0';
            return `¥ ${value}`;
        };

        // 解析金额输入值
        const parseCurrencyValue = (value) => {
            return value.replace(/[^0-9.]/g, '');
        };

        // 使用本地状态管理社保费率和基数
        const [insuranceConfig, setInsuranceConfig] = useState({
            rates: {
                pension: salaryConfig.INSURANCE_RATES.pension,
                medical: salaryConfig.INSURANCE_RATES.medical,
                unemployment: salaryConfig.INSURANCE_RATES.unemployment,
                supplementaryMedical: salaryConfig.INSURANCE_RATES.supplementaryMedical
            },
            base: {
                type: salaryConfig.INSURANCE_BASE?.type || 'baseSalary',
                fixedAmount: salaryConfig.INSURANCE_BASE?.fixedAmount || 5000,
                limits: {
                    pension: {
                        min: salaryConfig.INSURANCE_BASE?.limits?.pension?.min || 4638.88,
                        max: salaryConfig.INSURANCE_BASE?.limits?.pension?.max || 23194.6
                    },
                    medical: {
                        min: salaryConfig.INSURANCE_BASE?.limits?.medical?.min || 4853,
                        max: salaryConfig.INSURANCE_BASE?.limits?.medical?.max || 24267
                    },
                    unemployment: {
                        min: salaryConfig.INSURANCE_BASE?.limits?.unemployment?.min || 4217,
                        max: salaryConfig.INSURANCE_BASE?.limits?.unemployment?.max || 21086
                    }
                }
            }
        });

        // 当 salaryConfig 更新时，更新本地状态
        useEffect(() => {
            setInsuranceConfig({
                rates: {
                    pension: salaryConfig.INSURANCE_RATES.pension,
                    medical: salaryConfig.INSURANCE_RATES.medical,
                    unemployment: salaryConfig.INSURANCE_RATES.unemployment,
                    supplementaryMedical: salaryConfig.INSURANCE_RATES.supplementaryMedical
                },
                base: {
                    type: salaryConfig.INSURANCE_BASE?.type || 'baseSalary',
                    fixedAmount: salaryConfig.INSURANCE_BASE?.fixedAmount || 5000,
                    limits: {
                        pension: {
                            min: salaryConfig.INSURANCE_BASE?.limits?.pension?.min || 4638.88,
                            max: salaryConfig.INSURANCE_BASE?.limits?.pension?.max || 23194.6
                        },
                        medical: {
                            min: salaryConfig.INSURANCE_BASE?.limits?.medical?.min || 4853,
                            max: salaryConfig.INSURANCE_BASE?.limits?.medical?.max || 24267
                        },
                        unemployment: {
                            min: salaryConfig.INSURANCE_BASE?.limits?.unemployment?.min || 4217,
                            max: salaryConfig.INSURANCE_BASE?.limits?.unemployment?.max || 21086
                        }
                    }
                }
            });

            setInputValues({
                pension: String(salaryConfig.INSURANCE_RATES.pension || '0'),
                medical: String(salaryConfig.INSURANCE_RATES.medical || '0'),
                unemployment: String(salaryConfig.INSURANCE_RATES.unemployment || '0'),
                supplementaryMedical: String(salaryConfig.INSURANCE_RATES.supplementaryMedical || '0'),
                limits: {
                    pension: {
                        min: String(salaryConfig.INSURANCE_BASE?.limits?.pension?.min || '0'),
                        max: String(salaryConfig.INSURANCE_BASE?.limits?.pension?.max || '0')
                    },
                    medical: {
                        min: String(salaryConfig.INSURANCE_BASE?.limits?.medical?.min || '0'),
                        max: String(salaryConfig.INSURANCE_BASE?.limits?.medical?.max || '0')
                    },
                    unemployment: {
                        min: String(salaryConfig.INSURANCE_BASE?.limits?.unemployment?.min || '0'),
                        max: String(salaryConfig.INSURANCE_BASE?.limits?.unemployment?.max || '0')
                    }
                }
            });
        }, [salaryConfig]);

        // 处理费率输入变化
        const handleRateInputChange = (field, e) => {
            // 防止事件冒泡
            e.stopPropagation();

            // 获取原始输入值
            const rawValue = e.target.value;

            // 如果用户正在删除内容，直接更新为空字符串或用户输入的内容
            if (rawValue === '' || rawValue === '%' || rawValue === '0%') {
                setInputValues(prev => ({
                    ...prev,
                    [field]: ''
                }));
                return;
            }

            // 解析百分比值，只保留数字和小数点
            const value = parsePercentValue(rawValue);

            // 更新输入值
            setInputValues(prev => ({
                ...prev,
                [field]: value
            }));
        };

        // 处理费率失去焦点，将输入值转换为数值并更新
        const handleRateBlur = (field, e) => {
            // 获取输入值并转换为数值
            const rawValue = parsePercentValue(e.target.value);
            let value = parseFloat(rawValue);

            // 确保值在合理范围内
            if (isNaN(value)) value = 0;

            // 如果输入的是百分比形式（如8），需要除以100转为小数（如0.08）
            if (value > 1 && value <= 100) {
                value = value / 100;
            }

            // 如果值超过1（可能是误输入），强制转为小数
            if (value > 1) {
                value = value / 100;
            }

            // 确保值不小于0
            if (value < 0) value = 0;

            // 限制小数位数为3位
            value = parseFloat(value.toFixed(3));

            // 更新本地状态
            setInsuranceConfig(prev => ({
                ...prev,
                rates: {
                    ...prev.rates,
                    [field]: value
                }
            }));

            // 更新输入值为规范化后的值
            setInputValues(prev => ({
                ...prev,
                [field]: String(value)
            }));

            // 更新全局状态
            const newConfig = {...salaryConfig};
            newConfig.INSURANCE_RATES[field] = value;
            setSalaryConfig(newConfig);
        };

        const handleSave = async () => {
            try {
                // 使用统一的更新函数保存社保配置
                await updateSalaryConfig({
                    INSURANCE_RATES: insuranceConfig.rates,
                    INSURANCE_BASE: insuranceConfig.base
                });

                // 保存当前活动的选项卡
                localStorage.setItem('salaryConfigActiveTab', activeTabKey);

                // 成功消息由全局保存函数统一处理
            } catch (error) {
                console.error('保存社保参数配置失败:', error);
                message.error('保存社保参数配置失败');
            }
        };

        // 处理社保基数限制变化
        const handleLimitChange = (insuranceType, limitType, value) => {
            setInsuranceConfig(prev => ({
                ...prev,
                base: {
                    ...prev.base,
                    limits: {
                        ...prev.base.limits,
                        [insuranceType]: {
                            ...prev.base.limits[insuranceType],
                            [limitType]: value
                        }
                    }
                }
            }));

            // 更新全局状态
            const newConfig = {...salaryConfig};
            newConfig.INSURANCE_BASE.limits[insuranceType][limitType] = value;
            setSalaryConfig(newConfig);
        };

        // 处理社保基数类型和固定金额变化
        const handleTypeChange = (e) => {
            const newType = e.target.value;
            setInsuranceConfig(prev => ({
                ...prev,
                base: {
                    ...prev.base,
                    type: newType
                }
            }));

            // 更新全局状态
            const newConfig = {...salaryConfig};
            newConfig.INSURANCE_BASE.type = newType;
            setSalaryConfig(newConfig);
        };

        const handleAmountChange = (value) => {
            setInsuranceConfig(prev => ({
                ...prev,
                base: {
                    ...prev.base,
                    fixedAmount: value
                }
            }));

            // 更新全局状态
            const newConfig = {...salaryConfig};
            newConfig.INSURANCE_BASE.fixedAmount = value;
            setSalaryConfig(newConfig);
        };

        // 处理保险基数限制输入变化
        const handleLimitInputChange = (insuranceType, limitType, e) => {
            // 防止事件冒泡
            e.stopPropagation();

            // 解析货币输入值，只保留数字和小数点
            const value = parseCurrencyValue(e.target.value);

            // 更新输入值
            setInputValues(prev => ({
                ...prev,
                limits: {
                    ...prev.limits,
                    [insuranceType]: {
                        ...prev.limits[insuranceType],
                        [limitType]: value
                    }
                }
            }));
        };

        // 处理保险基数限制失去焦点
        const handleLimitBlur = (insuranceType, limitType, e) => {
            // 获取输入值并转换为数值
            const rawValue = parseCurrencyValue(e.target.value);
            const value = parseFloat(rawValue);

            // 确保值合法
            const validValue = isNaN(value) ? 0 : value;

            // 更新本地状态
            setInsuranceConfig(prev => ({
                ...prev,
                base: {
                    ...prev.base,
                    limits: {
                        ...prev.base.limits,
                        [insuranceType]: {
                            ...prev.base.limits[insuranceType],
                            [limitType]: validValue
                        }
                    }
                }
            }));

            // 更新全局状态
            const newConfig = {...salaryConfig};
            newConfig.INSURANCE_BASE.limits[insuranceType][limitType] = validValue;
            setSalaryConfig(newConfig);

            // 更新输入值为规范化后的值
            setInputValues(prev => ({
                ...prev,
                limits: {
                    ...prev.limits,
                    [insuranceType]: {
                        ...prev.limits[insuranceType],
                        [limitType]: String(validValue)
                    }
                }
            }));
        };

        return (
            <div>
                <Card
                    className="config-card basic-salary-card"
                    title="社保费率配置"
                    type="inner"
                    size="small"
                    style={{ marginBottom: '16px' }}
                >
                    <Row gutter={16} style={{ marginBottom: '8px' }}>
                        <Col span={12}>
                            <Row align="middle">
                                <Col span={10}>
                                    <span style={{ whiteSpace: 'nowrap' }}>养老保险费率：</span>
                                </Col>
                                <Col span={14}>
                                    <InputNumber
                                        min={0}
                                        max={100}
                                        step={0.1}
                                        precision={1}
                                        value={insuranceConfig.rates.pension * 100}
                                        formatter={value => `${value}%`}
                                        parser={value => value.replace('%', '')}
                                        className="admin-input-number uniform-input"
                                        style={{ width: '126px' }}
                                        onChange={(value) => {
                                            // 只更新本地状态，不更新全局状态
                                            setInsuranceConfig(prev => ({
                                                ...prev,
                                                rates: {
                                                    ...prev.rates,
                                                    pension: value ? value / 100 : 0
                                                }
                                            }));
                                        }}
                                    />
                                </Col>
                            </Row>
                        </Col>
                        <Col span={10}>
                            <Row align="middle">
                                <Col span={14}>
                                    <span style={{ whiteSpace: 'nowrap' }}>医疗保险费率：</span>
                                </Col>
                                <Col span={10}>
                                    <InputNumber
                                        min={0}
                                        max={100}
                                        step={0.1}
                                        precision={1}
                                        value={insuranceConfig.rates.medical * 100}
                                        formatter={value => `${value}%`}
                                        parser={value => value.replace('%', '')}
                                        className="admin-input-number uniform-input"
                                        style={{ width: '126px' }}
                                        onChange={(value) => {
                                            // 只更新本地状态，不更新全局状态
                                            setInsuranceConfig(prev => ({
                                                ...prev,
                                                rates: {
                                                    ...prev.rates,
                                                    medical: value ? value / 100 : 0
                                                }
                                            }));
                                        }}
                                    />
                                </Col>
                            </Row>
                        </Col>
                    </Row>

                    <Row gutter={14}>
                        <Col span={12}>
                            <Row align="middle">
                                <Col span={10}>
                                    <span style={{ whiteSpace: 'nowrap' }}>失业保险费率：</span>
                                </Col>
                                <Col span={14}>
                                    <InputNumber
                                        min={0}
                                        max={100}
                                        step={0.1}
                                        precision={1}
                                        value={insuranceConfig.rates.unemployment * 100}
                                        formatter={value => `${value}%`}
                                        parser={value => value.replace('%', '')}
                                        className="admin-input-number uniform-input"
                                        style={{ width: '126px' }}
                                        onChange={(value) => {
                                            // 只更新本地状态，不更新全局状态
                                            setInsuranceConfig(prev => ({
                                                ...prev,
                                                rates: {
                                                    ...prev.rates,
                                                    unemployment: value ? value / 100 : 0
                                                }
                                            }));
                                        }}
                                    />
                                </Col>
                            </Row>
                        </Col>
                        <Col span={12}>
                            <Row align="middle">
                                <Col span={12}>
                                    <span style={{ whiteSpace: 'nowrap' }}>大额医疗补充费率：</span>
                                </Col>
                                <Col span={10}>
                                    <InputNumber
                                        min={0}
                                        max={100}
                                        step={0.1}
                                        precision={1}
                                        value={insuranceConfig.rates.supplementaryMedical * 100}
                                        formatter={value => `${value}%`}
                                        parser={value => value.replace('%', '')}
                                        className="admin-input-number uniform-input"
                                        style={{ width: '120px' }}
                                        onChange={(value) => {
                                            // 只更新本地状态，不更新全局状态
                                            setInsuranceConfig(prev => ({
                                                ...prev,
                                                rates: {
                                                    ...prev.rates,
                                                    supplementaryMedical: value ? value / 100 : 0
                                                }
                                            }));
                                        }}
                                    />
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                </Card>

                <Card
                    className="config-card basic-salary-card"
                    title="社保基数上下限"
                    type="inner"
                    size="small"
                >
                    {/* 下限一行 */}
                    <Row gutter={16} style={{ marginBottom: '8px' }}>
                        <Col span={8}>
                            <Form.Item
                                label="养老保险基数下限"
                                rules={[{ required: true, message: '请输入养老保险基数下限' }]}
                            >
                                <InputNumber
                                    min={0}
                                    step={100}
                                    precision={2}
                                    style={{ width: '130px' }}
                                    value={parseFloat(inputValues.limits.pension.min) || 0}
                                    formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                    parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                                    onChange={(value) => {
                                        // 只更新本地状态，不更新全局状态
                                        setInputValues(prev => ({
                                            ...prev,
                                            limits: {
                                                ...prev.limits,
                                                pension: {
                                                    ...prev.limits.pension,
                                                    min: value ? String(value) : '0'
                                                }
                                            }
                                        }));

                                        // 更新本地保存状态
                                        setInsuranceConfig(prev => ({
                                            ...prev,
                                            base: {
                                                ...prev.base,
                                                limits: {
                                                    ...prev.base.limits,
                                                    pension: {
                                                        ...prev.base.limits.pension,
                                                        min: value || 0
                                                    }
                                                }
                                            }
                                        }));
                                    }}
                                    className="admin-input-number uniform-input"
                                    placeholder="请输入养老保险基数下限"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label="医疗保险基数下限"
                                rules={[{ required: true, message: '请输入医疗保险基数下限' }]}
                            >
                                <InputNumber
                                    min={0}
                                    step={100}
                                    precision={2}
                                    style={{ width: '130px' }}
                                    value={parseFloat(inputValues.limits.medical.min) || 0}
                                    formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                    parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                                    onChange={(value) => {
                                        // 更新本地状态
                                        setInputValues(prev => ({
                                            ...prev,
                                            limits: {
                                                ...prev.limits,
                                                medical: {
                                                    ...prev.limits.medical,
                                                    min: value ? String(value) : '0'
                                                }
                                            }
                                        }));

                                        // 更新全局状态
                                        const newConfig = {...salaryConfig};
                                        newConfig.INSURANCE_BASE.limits.medical.min = value || 0;
                                        setSalaryConfig(newConfig);
                                    }}
                                    className="admin-input-number uniform-input"
                                    placeholder="请输入医疗保险基数下限"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label="失业保险基数下限"
                                rules={[{ required: true, message: '请输入失业保险基数下限' }]}
                            >
                                <InputNumber
                                    min={0}
                                    step={100}
                                    precision={2}
                                    style={{ width: '130px' }}
                                    value={parseFloat(inputValues.limits.unemployment.min) || 0}
                                    formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                    parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                                    onChange={(value) => {
                                        // 更新本地状态
                                        setInputValues(prev => ({
                                            ...prev,
                                            limits: {
                                                ...prev.limits,
                                                unemployment: {
                                                    ...prev.limits.unemployment,
                                                    min: value ? String(value) : '0'
                                                }
                                            }
                                        }));

                                        // 更新全局状态
                                        const newConfig = {...salaryConfig};
                                        newConfig.INSURANCE_BASE.limits.unemployment.min = value || 0;
                                        setSalaryConfig(newConfig);
                                    }}
                                    className="admin-input-number uniform-input"
                                    placeholder="请输入失业保险基数下限"
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    {/* 上限一行 */}
                    <Row gutter={16}>
                        <Col span={8}>
                            <Form.Item
                                label="养老保险基数上限"
                                rules={[{ required: true, message: '请输入养老保险基数上限' }]}
                            >
                                <InputNumber
                                    min={0}
                                    step={100}
                                    precision={2}
                                    style={{ width: '130px' }}
                                    value={parseFloat(inputValues.limits.pension.max) || 0}
                                    formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                    parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                                    onChange={(value) => {
                                        // 更新本地状态
                                        setInputValues(prev => ({
                                            ...prev,
                                            limits: {
                                                ...prev.limits,
                                                pension: {
                                                    ...prev.limits.pension,
                                                    max: value ? String(value) : '0'
                                                }
                                            }
                                        }));

                                        // 更新全局状态
                                        const newConfig = {...salaryConfig};
                                        newConfig.INSURANCE_BASE.limits.pension.max = value || 0;
                                        setSalaryConfig(newConfig);
                                    }}
                                    className="admin-input-number uniform-input"
                                    placeholder="请输入养老保险基数上限"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label="医疗保险基数上限"
                                rules={[{ required: true, message: '请输入医疗保险基数上限' }]}
                            >
                                <InputNumber
                                    min={0}
                                    step={100}
                                    precision={2}
                                    style={{ width: '130px' }}
                                    value={parseFloat(inputValues.limits.medical.max) || 0}
                                    formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                    parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                                    onChange={(value) => {
                                        // 更新本地状态
                                        setInputValues(prev => ({
                                            ...prev,
                                            limits: {
                                                ...prev.limits,
                                                medical: {
                                                    ...prev.limits.medical,
                                                    max: value ? String(value) : '0'
                                                }
                                            }
                                        }));

                                        // 更新全局状态
                                        const newConfig = {...salaryConfig};
                                        newConfig.INSURANCE_BASE.limits.medical.max = value || 0;
                                        setSalaryConfig(newConfig);
                                    }}
                                    className="admin-input-number uniform-input"
                                    placeholder="请输入医疗保险基数上限"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label="失业保险基数上限"
                                rules={[{ required: true, message: '请输入失业保险基数上限' }]}
                            >
                                <InputNumber
                                    min={0}
                                    step={100}
                                    precision={2}
                                    style={{ width: '130px' }}
                                    value={parseFloat(inputValues.limits.unemployment.max) || 0}
                                    formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                    parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                                    onChange={(value) => {
                                        // 更新本地状态
                                        setInputValues(prev => ({
                                            ...prev,
                                            limits: {
                                                ...prev.limits,
                                                unemployment: {
                                                    ...prev.limits.unemployment,
                                                    max: value ? String(value) : '0'
                                                }
                                            }
                                        }));

                                        // 更新全局状态
                                        const newConfig = {...salaryConfig};
                                        newConfig.INSURANCE_BASE.limits.unemployment.max = value || 0;
                                        setSalaryConfig(newConfig);
                                    }}
                                    className="admin-input-number uniform-input"
                                    placeholder="请输入失业保险基数上限"
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Card>
            </div>
        );
    };

    // 绩效等级配置表单
    const PerformanceLevelsForm = () => {
        const [performanceLevels, setPerformanceLevels] = useState(
            Object.entries(salaryConfig.PERFORMANCE_LEVELS).map(([level, coefficient]) => ({
                level,
                coefficient
            }))
        );

        // 当 salaryConfig 更新时，更新本地状态
        useEffect(() => {
            setPerformanceLevels(
                Object.entries(salaryConfig.PERFORMANCE_LEVELS).map(([level, coefficient]) => ({
                    level,
                    coefficient
                }))
            );
        }, [salaryConfig]);

        // 监听配置更新事件
        useEffect(() => {
            const handleConfigUpdate = (event) => {
                console.log('绩效等级配置收到更新事件');
                // 使用事件中的数据，而不是依赖 salaryConfig
                const newConfig = event.detail;
                if (newConfig && newConfig.PERFORMANCE_LEVELS) {
                    setPerformanceLevels(
                        Object.entries(newConfig.PERFORMANCE_LEVELS).map(([level, coefficient]) => ({
                            level,
                            coefficient
                        }))
                    );
                }
            };

            window.addEventListener('salaryConfigUpdated', handleConfigUpdate);

            return () => {
                window.removeEventListener('salaryConfigUpdated', handleConfigUpdate);
            };
        }, []);

        const columns = [
            {
                title: '绩效等级',
                dataIndex: 'level',
                key: 'level',
                width: '50%',
                align: 'center'
            },
            {
                title: '系数',
                dataIndex: 'coefficient',
                key: 'coefficient',
                width: '50%',
                align: 'center',
                render: (text, _record, index) => (
                    <InputNumber
                        min={0}
                        max={2}
                        step={0.1}
                        precision={1}
                        value={text}
                        className="admin-input-number uniform-input"
                        style={{ width: '100px' }}
                        onChange={(value) => {
                            const newLevels = [...performanceLevels];
                            newLevels[index].coefficient = value;
                            setPerformanceLevels(newLevels);
                        }}
                    />
                )
            }
        ];

        const handleSave = async () => {
            try {
                const performanceConfig = {};
                performanceLevels.forEach(({ level, coefficient }) => {
                    performanceConfig[level] = coefficient;
                });

                // 使用统一的更新函数
                await updateSalaryConfig({
                    PERFORMANCE_LEVELS: performanceConfig
                });
            } catch (error) {
                console.error('保存绩效等级配置失败:', error);
                message.error('保存绩效等级配置失败');
            }
        };

        return (
            <div className="salary-form">
                <Table
                    dataSource={performanceLevels}
                    columns={columns}
                    rowKey="level"
                    pagination={false}
                    bordered
                    className="performance-table"
                    size="middle"
                />
            </div>
        );
    };

    // 岗位工资配置表单
    const PositionSalaryForm = () => {
        const [techPositions, setTechPositions] = useState(
            Object.entries(salaryConfig.TECH_POSITIONS['技术']).map(([level, salary]) => ({
                level,
                salary
            }))
        );

        const [managerPositions, setManagerPositions] = useState(
            Object.entries(salaryConfig.MANAGER_POSITIONS['高管']).map(([level, salary]) => ({
                level,
                salary
            }))
        );

        const [supportPositions, setSupportPositions] = useState(
            Object.entries(salaryConfig.SUPPORT_POSITIONS['支持']).map(([level, salary]) => ({
                level,
                salary
            }))
        );

        const [otherPositions, setOtherPositions] = useState(
            Object.entries(salaryConfig.OTHER_POSITIONS['其他']).map(([level, salary]) => ({
                level,
                salary
            }))
        );

        // 记录当前选中的岗位类型
        const [activePositionType, setActivePositionType] = useState(() => {
            // 从本地存储恢复上次选中的岗位类型
            const savedType = localStorage.getItem('salaryConfigPositionType');
            return savedType || 'tech';
        });

        // 当 salaryConfig 更新时，更新本地状态
        useEffect(() => {
            setTechPositions(
                Object.entries(salaryConfig.TECH_POSITIONS['技术']).map(([level, salary]) => ({
                    level,
                    salary
                }))
            );

            setManagerPositions(
                Object.entries(salaryConfig.MANAGER_POSITIONS['高管']).map(([level, salary]) => ({
                    level,
                    salary
                }))
            );

            setSupportPositions(
                Object.entries(salaryConfig.SUPPORT_POSITIONS['支持']).map(([level, salary]) => ({
                    level,
                    salary
                }))
            );

            setOtherPositions(
                Object.entries(salaryConfig.OTHER_POSITIONS['其他']).map(([level, salary]) => ({
                    level,
                    salary
                }))
            );
        }, [salaryConfig]);

        // 技术岗位工作年限说明
        const techExperienceMap = {
            'A1': '应届毕业生',
            'A2': '1年工作经验',
            'A3': '2年工作经验',
            'A4': '3年工作经验',
            'A5': '4年工作经验',
            'A6': '5-6年工作经验',
            'A7': '7-8年工作经验',
            'A8': '9-10年工作经验',
            'A9': '11-12年工作经验',
            'A10': '13-14年工作经验',
            'A11': '15-16年工作经验',
            'A12': '17-18年工作经验',
            'A13': '19-20年工作经验',
            'A14': '21-22年工作经验',
            'A15': '23-24年工作经验',
            'A16': '25-27年工作经验',
            'A17': '28-30年工作经验',
            'A18': '31-32年工作经验',
            'A19': '33-34年工作经验',
            'A20': '35年及以上工作经验'
        };

        const techColumns = [
            {
                title: '技术岗位等级',
                dataIndex: 'level',
                key: 'level',
                width: '25%',
                align: 'center'
            },
            {
                title: '说明',
                key: 'description',
                width: '35%',
                align: 'center',
                render: (_, record) => techExperienceMap[record.level] || '-'
            },
            {
                title: '岗位工资',
                dataIndex: 'salary',
                key: 'salary',
                width: '40%',
                align: 'center',
                render: (text, _record, index) => (
                    <InputNumber
                        min={0}
                        step={100}
                        precision={0}
                        value={text}
                        formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                        className="admin-input-number uniform-input"
                        style={{ width: '126px' }}
                        onChange={(value) => {
                            const newPositions = [...techPositions];
                            newPositions[index].salary = value;
                            setTechPositions(newPositions);
                        }}
                    />
                )
            }
        ];

        // 高管岗位职位说明
        const managerPositionMap = {
            'B1': '副总监/副总工',
            'B2': '总监/总工',
            'B3': '副总经理',
            'B4': '总经理/董事长'
        };

        const managerColumns = [
            {
                title: '高管岗位等级',
                dataIndex: 'level',
                key: 'level',
                width: '25%',
                align: 'center'
            },
            {
                title: '说明',
                key: 'description',
                width: '35%',
                align: 'center',
                render: (_, record) => managerPositionMap[record.level] || '-'
            },
            {
                title: '岗位工资',
                dataIndex: 'salary',
                key: 'salary',
                width: '40%',
                align: 'center',
                render: (text, _record, index) => (
                    <InputNumber
                        min={0}
                        step={1000}
                        precision={0}
                        value={text}
                        formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                        className="admin-input-number uniform-input"
                        style={{ width: '126px' }}
                        onChange={(value) => {
                            const newPositions = [...managerPositions];
                            newPositions[index].salary = value;
                            setManagerPositions(newPositions);
                        }}
                    />
                )
            }
        ];

        // 支持岗位工作年限说明 (新增)
        const supportExperienceMap = {
            'C1': '应届毕业生',
            'C2': '1年工作经验',
            'C3': '2年工作经验',
            'C4': '3年工作经验',
            'C5': '4-5年工作经验',
            'C6': '6-7年工作经验',
            'C7': '8-10年工作经验',
            'C8': '11-13年工作经验',
            'C9': '14-16年工作经验',
            'C10': '17-20年工作经验',
            'C11': '21-24年工作经验',
            'C12': '25-28年工作经验',
            'C13': '29-31年工作经验',
            'C14': '32-34年工作经验',
            'C15': '35年及以上工作经验'
        };

        const supportColumns = [
            {
                title: '支持岗位等级',
                dataIndex: 'level',
                key: 'level',
                width: '25%',
                align: 'center'
            },
            {
                title: '说明',
                key: 'description',
                width: '35%',
                align: 'center',
                render: (_, record) => supportExperienceMap[record.level] || '-'
            },
            {
                title: '岗位工资',
                dataIndex: 'salary',
                key: 'salary',
                width: '40%',
                align: 'center',
                render: (text, _record, index) => (
                    <InputNumber
                        min={0}
                        step={100}
                        precision={0}
                        value={text}
                        formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                        className="admin-input-number uniform-input"
                        style={{ width: '126px' }}
                        onChange={(value) => {
                            const newPositions = [...supportPositions];
                            newPositions[index].salary = value;
                            setSupportPositions(newPositions);
                        }}
                    />
                )
            }
        ];

        // 其他岗位工作年限说明
        const otherExperienceMap = {
            'D1': '应届毕业生',
            'D2': '1年工作经验',
            'D3': '2年工作经验',
            'D4': '3年工作经验',
            'D5': '4-5年工作经验',
            'D6': '6-7年工作经验',
            'D7': '8-10年工作经验',
            'D8': '11-13年工作经验',
            'D9': '14-16年工作经验',
            'D10': '17-20年工作经验',
            'D11': '21-24年工作经验',
            'D12': '25-28年工作经验',
            'D13': '29-31年工作经验',
            'D14': '32-34年工作经验',
            'D15': '35年及以上工作经验'
        };

        const otherColumns = [
            {
                title: '其他岗位等级',
                dataIndex: 'level',
                key: 'level',
                width: '25%',
                align: 'center'
            },
            {
                title: '说明',
                key: 'description',
                width: '35%',
                align: 'center',
                render: (_, record) => otherExperienceMap[record.level] || '-'
            },
            {
                title: '岗位工资',
                dataIndex: 'salary',
                key: 'salary',
                width: '40%',
                align: 'center',
                render: (text, _record, index) => (
                    <InputNumber
                        min={0}
                        step={100}
                        precision={0}
                        value={text}
                        formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                        className="admin-input-number uniform-input"
                        style={{ width: '126px' }}
                        onChange={(value) => {
                            const newPositions = [...otherPositions];
                            newPositions[index].salary = value;
                            setOtherPositions(newPositions);
                        }}
                    />
                )
            }
        ];

        // 岗位类型切换处理
        const handlePositionTypeChange = (type) => {
            setActivePositionType(type);
            localStorage.setItem('salaryConfigPositionType', type);
        };

        const handleSave = async () => {
            try {
                // 构建所有岗位类型的配置
                const configToUpdate = {};

                // 技术岗位
                const techConfig = {};
                techPositions.forEach(({ level, salary }) => {
                    techConfig[level] = salary;
                });
                configToUpdate.TECH_POSITIONS = { '技术': techConfig };

                // 高管岗位
                const managerConfig = {};
                managerPositions.forEach(({ level, salary }) => {
                    managerConfig[level] = salary;A
                });
                configToUpdate.MANAGER_POSITIONS = { '高管': managerConfig };

                // 支持岗位
                const supportConfig = {};
                supportPositions.forEach(({ level, salary }) => {
                    supportConfig[level] = salary;
                });
                configToUpdate.SUPPORT_POSITIONS = { '支持': supportConfig };

                // 其他岗位
                const otherConfig = {};
                otherPositions.forEach(({ level, salary }) => {
                    otherConfig[level] = salary;
                });
                configToUpdate.OTHER_POSITIONS = { '其他': otherConfig };

                // 保存当前岗位类型到本地存储
                localStorage.setItem('salaryConfigPositionType', activePositionType);

                // 使用统一的更新函数
                await updateSalaryConfig(configToUpdate);

                // 保存当前活动的选项卡
                localStorage.setItem('salaryConfigActiveTab', activeTabKey);

                message.success('岗位工资配置保存成功');
            } catch (error) {
                console.error('保存岗位工资配置失败:', error);
                message.error('保存岗位工资配置失败');
            }
        };

        return (
            <div className="salary-form">
                <Card
                    className="config-card basic-salary-card"
                    title="岗位工资配置"
                    type="inner"
                    size="small"
                >
                    <Tabs
                        activeKey={activePositionType}
                        onChange={handlePositionTypeChange}
                        tabBarGutter={0}
                        size="small"
                        type="card"
                        style={{ marginBottom: '16px' }}
                    >
                        <TabPane tab="技术岗位" key="tech">
                            <Table
                                dataSource={techPositions}
                                columns={techColumns}
                                rowKey="level"
                                pagination={false}
                                bordered
                                className="salary-table"
                                size="small"
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                        <TabPane tab="高管岗位" key="manager">
                            <Table
                                dataSource={managerPositions}
                                columns={managerColumns}
                                rowKey="level"
                                pagination={false}
                                bordered
                                className="salary-table"
                                size="small"
                            />
                        </TabPane>
                        <TabPane tab="支持岗位" key="support">
                            <Table
                                dataSource={supportPositions}
                                columns={supportColumns}
                                rowKey="level"
                                pagination={false}
                                bordered
                                className="salary-table"
                                size="small"
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                        <TabPane tab="其他岗位" key="other">
                            <Table
                                dataSource={otherPositions}
                                columns={otherColumns}
                                rowKey="level"
                                pagination={false}
                                bordered
                                className="salary-table"
                                size="small"
                                scroll={{ y: 400 }}
                            />
                        </TabPane>
                    </Tabs>
                </Card>
            </div>
        );
    };

    // 系数配置表单
    const CoefficientForm = () => {
        // 记录当前选中的系数类型
        const [activeCoefficientType, setActiveCoefficientType] = useState(() => {
            // 从本地存储恢复上次选中的系数类型
            const savedType = localStorage.getItem('salaryConfigCoefficientType');
            return savedType || 'education';
        });

        // 职称系数启用状态
        const [titleCoefficientEnabled, setTitleCoefficientEnabled] = useState(() => {
            // 从本地存储恢复职称系数启用状态，默认为禁用
            const savedState = localStorage.getItem('titleCoefficientEnabled');
            return savedState === 'true';
        });

        // 字符串输入状态，避免输入中断
        const [inputValues, setInputValues] = useState({
            education: {},
            language: {},
            title: {},
            performance: {}
        });

        // 格式化系数显示值
        const formatCoefficientValue = (value) => {
            if (value === '' || value === undefined) return '';
            if (value === '0') return '0.0';
            // 将小数转为带一位小数的格式
            const numValue = parseFloat(value);
            if (isNaN(numValue)) return '';
            return numValue.toFixed(1) + '%';
        };

        // 解析系数输入值
        const parseCoefficientValue = (value) => {
            return value.replace(/[^0-9.]/g, '');
        };

        // 转换数据为表格所需格式
        const [educationCoefficients, setEducationCoefficients] = useState(
            Object.entries(salaryConfig.EDUCATION_COEFFICIENT).map(([level, coefficient]) => ({
                level,
                coefficient
            }))
        );

        const [languageCoefficients, setLanguageCoefficients] = useState(
            Object.entries(salaryConfig.LANGUAGE_COEFFICIENT).map(([level, coefficient]) => ({
                level,
                coefficient
            }))
        );

        const [titleCoefficients, setTitleCoefficients] = useState(
            Object.entries(salaryConfig.TITLE_COEFFICIENT).map(([level, coefficient]) => ({
                level,
                coefficient
            }))
        );

        const [performanceLevels, setPerformanceLevels] = useState(
            Object.entries(salaryConfig.PERFORMANCE_LEVELS).map(([level, coefficient]) => ({
                level,
                coefficient
            }))
        );

        // 当 salaryConfig 更新时，更新本地状态
        useEffect(() => {
            // 更新职称系数启用状态
            if (salaryConfig.SYSTEM_CONFIG && salaryConfig.SYSTEM_CONFIG.TITLE_COEFFICIENT_ENABLED !== undefined) {
                setTitleCoefficientEnabled(salaryConfig.SYSTEM_CONFIG.TITLE_COEFFICIENT_ENABLED);
                localStorage.setItem('titleCoefficientEnabled', String(salaryConfig.SYSTEM_CONFIG.TITLE_COEFFICIENT_ENABLED));
            }

            setEducationCoefficients(
                Object.entries(salaryConfig.EDUCATION_COEFFICIENT).map(([level, coefficient]) => ({
                    level,
                    coefficient
                }))
            );

            setLanguageCoefficients(
                Object.entries(salaryConfig.LANGUAGE_COEFFICIENT).map(([level, coefficient]) => ({
                    level,
                    coefficient
                }))
            );

            setTitleCoefficients(
                Object.entries(salaryConfig.TITLE_COEFFICIENT).map(([level, coefficient]) => ({
                    level,
                    coefficient
                }))
            );

            setPerformanceLevels(
                Object.entries(salaryConfig.PERFORMANCE_LEVELS).map(([level, coefficient]) => ({
                    level,
                    coefficient
                }))
            );

            // 初始化输入值
            const educationInputs = {};
            Object.entries(salaryConfig.EDUCATION_COEFFICIENT).forEach(([level, coefficient]) => {
                educationInputs[level] = String(coefficient || '');
            });

            const languageInputs = {};
            Object.entries(salaryConfig.LANGUAGE_COEFFICIENT).forEach(([level, coefficient]) => {
                languageInputs[level] = String(coefficient || '');
            });

            const titleInputs = {};
            Object.entries(salaryConfig.TITLE_COEFFICIENT).forEach(([level, coefficient]) => {
                titleInputs[level] = String(coefficient || '');
            });

            const performanceInputs = {};
            Object.entries(salaryConfig.PERFORMANCE_LEVELS).forEach(([level, coefficient]) => {
                performanceInputs[level] = String(coefficient || '');
            });

            setInputValues({
                education: educationInputs,
                language: languageInputs,
                title: titleInputs,
                performance: performanceInputs
            });
        }, [salaryConfig]);

        // 外语水平评定依据说明
        const languageDescriptionMap = {
            '无': '无外语能力或仅掌握基础词汇，无法进行日常交流。',
            '基础': '能够进行简单的日常交流和阅读简单外文文档，掌握基础语法和词汇。',
            '熟练': '能够流利进行日常和工作交流，撰写外文邮件和报告，阅读和理解专业外文文档。',
            '精通': '能够流利进行复杂的工作交流，撰写高质量的外文报告和文档，进行专业外文演讲和谈判。'
        };

        // 绩效等级评定依据说明
        const performanceDescriptionMap = {
            'A': '工作表现远超预期，在所有关键绩效指标上均表现卓越。',
            'B+': '工作表现超过预期，在多数关键绩效指标上表现良好。',
            'B': '工作表现符合预期，能够完成所有基本工作要求。',
            'C': '工作表现低于预期，在某些关键绩效指标上未达标准。',
            'D': '工作表现远低于预期，在多个关键绩效指标上表现不佳。'
        };

        // 系数类型切换处理
        const handleCoefficientTypeChange = (type) => {
            setActiveCoefficientType(type);
            localStorage.setItem('salaryConfigCoefficientType', type);
        };

        // 处理输入变化
        const handleInputChange = (type, level, e) => {
            // 防止事件冒泡
            e.stopPropagation();

            // 获取原始输入值
            const rawValue = e.target.value;

            // 如果用户正在删除内容，直接更新为空字符串
            if (rawValue === '') {
                setInputValues(prev => ({
                    ...prev,
                    [type]: {
                        ...prev[type],
                        [level]: ''
                    }
                }));
                return;
            }

            // 解析输入值，只接受数字和小数点
            const value = parseCoefficientValue(rawValue);

            // 更新输入值
            setInputValues(prev => ({
                ...prev,
                [type]: {
                    ...prev[type],
                    [level]: value
                }
            }));
        };

        // 处理失去焦点
        const handleBlur = (type, level, e) => {
            // 获取输入值并转换为数值
            const rawValue = parseCoefficientValue(e.target.value);
            let value = parseFloat(rawValue);

            // 确保值在合理范围内
            if (isNaN(value)) value = 0;
            if (value > 3) value = 3;
            if (value < 0) value = 0;
            value = Math.round(value * 10) / 10; // 保留一位小数

            // 更新本地状态和全局状态
            if (type === 'education') {
                // 更新本地状态
                setEducationCoefficients(prev => {
                    const newCoefficients = [...prev];
                    const index = newCoefficients.findIndex(item => item.level === level);
                    if (index !== -1) {
                        newCoefficients[index].coefficient = value;
                    }
                    return newCoefficients;
                });

                // 更新全局状态
                const newConfig = {...salaryConfig};
                newConfig.EDUCATION_COEFFICIENT[level] = value;
                setSalaryConfig(newConfig);
            } else if (type === 'language') {
                // 更新本地状态
                setLanguageCoefficients(prev => {
                    const newCoefficients = [...prev];
                    const index = newCoefficients.findIndex(item => item.level === level);
                    if (index !== -1) {
                        newCoefficients[index].coefficient = value;
                    }
                    return newCoefficients;
                });

                // 更新全局状态
                const newConfig = {...salaryConfig};
                newConfig.LANGUAGE_COEFFICIENT[level] = value;
                setSalaryConfig(newConfig);
            } else if (type === 'title') {
                // 更新本地状态
                setTitleCoefficients(prev => {
                    const newCoefficients = [...prev];
                    const index = newCoefficients.findIndex(item => item.level === level);
                    if (index !== -1) {
                        newCoefficients[index].coefficient = value;
                    }
                    return newCoefficients;
                });

                // 更新全局状态
                const newConfig = {...salaryConfig};
                newConfig.TITLE_COEFFICIENT[level] = value;
                setSalaryConfig(newConfig);
            } else if (type === 'performance') {
                // 更新本地状态
                setPerformanceLevels(prev => {
                    const newLevels = [...prev];
                    const index = newLevels.findIndex(item => item.level === level);
                    if (index !== -1) {
                        newLevels[index].coefficient = value;
                    }
                    return newLevels;
                });

                // 更新全局状态
                const newConfig = {...salaryConfig};
                newConfig.PERFORMANCE_LEVELS[level] = value;
                setSalaryConfig(newConfig);
            }

            // 更新输入值为规范化后的值
            setInputValues(prev => ({
                ...prev,
                [type]: {
                    ...prev[type],
                    [level]: String(value)
                }
            }));
        };

        // 通用列定义
        const getColumns = (type) => {
            if (type === 'education') {
                return [
                    {
                        title: '学历等级',
                        dataIndex: 'level',
                        key: 'level',
                        width: 120,
                        align: 'center'
                    },
                    {
                        title: '系数',
                        dataIndex: 'coefficient',
                        key: 'coefficient',
                        width: 75,
                        align: 'center',
                        render: (text, record) => (
                            <InputNumber
                                min={0}
                                max={3}
                                step={0.1}
                                precision={1}
                                value={record.coefficient}
                                formatter={value => `${value}%`}
                                parser={value => value.replace('%', '')}
                                className="admin-input-number uniform-input"
                                style={{ width: '75px' }}
                                onChange={(value) => {
                                    // 只更新本地状态，不更新全局状态
                                    const newCoefficients = [...educationCoefficients];
                                    const index = newCoefficients.findIndex(item => item.level === record.level);
                                    if (index !== -1) {
                                        newCoefficients[index].coefficient = value;
                                        setEducationCoefficients(newCoefficients);
                                    }
                                }}
                            />
                        )
                    },
                    {
                        title: '说明',
                        key: 'description',
                        align: 'center',
                        render: () => '学历为全日制普通高等教育'
                    }
                ];
            } else if (type === 'language') {
                return [
                    {
                        title: '外语水平',
                        dataIndex: 'level',
                        key: 'level',
                        width: 50,
                        align: 'center'
                    },
                    {
                        title: '系数',
                        dataIndex: 'coefficient',
                        key: 'coefficient',
                        width: 50,
                        align: 'center',
                        render: (text, record) => (
                            <InputNumber
                                min={0}
                                max={3}
                                step={0.1}
                                precision={1}
                                value={record.coefficient}
                                formatter={value => `${value}%`}
                                parser={value => value.replace('%', '')}
                                className="admin-input-number uniform-input"
                                style={{ width: '75px' }}
                                onChange={(value) => {
                                    // 只更新本地状态，不更新全局状态
                                    const newCoefficients = [...languageCoefficients];
                                    const index = newCoefficients.findIndex(item => item.level === record.level);
                                    if (index !== -1) {
                                        newCoefficients[index].coefficient = value;
                                        setLanguageCoefficients(newCoefficients);
                                    }
                                }}
                            />
                        )
                    },
                    {
                        title: '评定依据',
                        key: 'description',
                        align: 'left',
                        render: (_, record) => (
                            <div className="language-description">
                                {languageDescriptionMap[record.level] || '-'}
                            </div>
                        )
                    }
                ];
            } else if (type === 'title') {
                return [
                    {
                        title: '职称等级',
                        dataIndex: 'level',
                        key: 'level',
                        width: 69,
                        align: 'center'
                    },
                    {
                        title: '系数',
                        dataIndex: 'coefficient',
                        key: 'coefficient',
                        width: 65,
                        align: 'center',
                        render: (text, record) => (
                            <InputNumber
                                min={0}
                                max={3}
                                step={0.1}
                                precision={1}
                                value={record.coefficient}
                                formatter={value => `${value}%`}
                                parser={value => value.replace('%', '')}
                                className="admin-input-number uniform-input"
                                style={{ width: '75px' }}
                                onChange={(value) => {
                                    // 只更新本地状态，不更新全局状态
                                    const newCoefficients = [...titleCoefficients];
                                    const index = newCoefficients.findIndex(item => item.level === record.level);
                                    if (index !== -1) {
                                        newCoefficients[index].coefficient = value;
                                        setTitleCoefficients(newCoefficients);
                                    }
                                }}
                                disabled={!titleCoefficientEnabled}
                            />
                        )
                    },
                    {
                        title: '说明',
                        key: 'description',
                        align: 'center',
                        render: () => '职称等级影响基本工资计算'
                    }
                ];
            } else if (type === 'performance') {
                return [
                    {
                        title: '绩效等级',
                        dataIndex: 'level',
                        key: 'level',
                        width: 40,
                        align: 'center'
                    },
                    {
                        title: '系数',
                        dataIndex: 'coefficient',
                        key: 'coefficient',
                        width: 45,
                        align: 'center',
                        render: (text, record) => (
                            <InputNumber
                                min={0}
                                max={3}
                                step={0.1}
                                precision={1}
                                value={record.coefficient}
                                formatter={value => `${value}%`}
                                parser={value => value.replace('%', '')}
                                className="admin-input-number uniform-input"
                                style={{ width: '75px' }}
                                onChange={(value) => {
                                    // 只更新本地状态，不更新全局状态
                                    const newLevels = [...performanceLevels];
                                    const index = newLevels.findIndex(item => item.level === record.level);
                                    if (index !== -1) {
                                        newLevels[index].coefficient = value;
                                        setPerformanceLevels(newLevels);
                                    }
                                }}
                            />
                        )
                    },
                    {
                        title: '评定依据',
                        key: 'description',
                        align: 'center',
                        render: (_, record) => (
                            <div className="language-description">
                                {performanceDescriptionMap[record.level] || '-'}
                            </div>
                        )
                    }
                ];
            }

            return [];
        };

        const handleSave = async () => {
            try {
                // 根据当前选中的系数类型构建配置对象
                let configToUpdate = {};

                if (activeCoefficientType === 'education' || activeCoefficientType === 'all') {
                    const educationConfig = {};
                    educationCoefficients.forEach(({ level, coefficient }) => {
                        educationConfig[level] = coefficient;
                    });
                    configToUpdate.EDUCATION_COEFFICIENT = educationConfig;
                }

                if (activeCoefficientType === 'language' || activeCoefficientType === 'all') {
                    const languageConfig = {};
                    languageCoefficients.forEach(({ level, coefficient }) => {
                        languageConfig[level] = coefficient;
                    });
                    configToUpdate.LANGUAGE_COEFFICIENT = languageConfig;
                }

                if (activeCoefficientType === 'title' || activeCoefficientType === 'all') {
                    const titleConfig = {};
                    titleCoefficients.forEach(({ level, coefficient }) => {
                        titleConfig[level] = coefficient;
                    });
                    configToUpdate.TITLE_COEFFICIENT = titleConfig;
                }

                if (activeCoefficientType === 'performance' || activeCoefficientType === 'all') {
                    const performanceConfig = {};
                    performanceLevels.forEach(({ level, coefficient }) => {
                        performanceConfig[level] = coefficient;
                    });
                    configToUpdate.PERFORMANCE_LEVELS = performanceConfig;
                }

                // 保存当前系数类型到本地存储
                localStorage.setItem('salaryConfigCoefficientType', activeCoefficientType);

                // 使用统一的更新函数
                await updateSalaryConfig(configToUpdate);

                // 保存当前活动的选项卡
                localStorage.setItem('salaryConfigActiveTab', activeTabKey);

                message.success('系数配置保存成功');
            } catch (error) {
                console.error('保存系数配置失败:', error);
                message.error('保存系数配置失败');
            }
        };

        // 获取当前选中类型的数据和列
        const getCurrentData = () => {
            switch(activeCoefficientType) {
                case 'education':
                    return { data: educationCoefficients, columns: getColumns('education') };
                case 'language':
                    return { data: languageCoefficients, columns: getColumns('language') };
                case 'title':
                    return { data: titleCoefficients, columns: getColumns('title') };
                case 'performance':
                    return { data: performanceLevels, columns: getColumns('performance') };
                default:
                    return { data: [], columns: [] };
            }
        };

        const { data, columns } = getCurrentData();

        return (
            <div className="salary-form">
                <Card
                    className="config-card basic-salary-card"
                    title="调整系数配置"
                    type="inner"
                    size="small"
                >
                    <Tabs
                        activeKey={activeCoefficientType}
                        onChange={handleCoefficientTypeChange}
                        tabBarGutter={0}
                        size="small"
                        type="card"
                        style={{ marginBottom: '16px' }}
                    >
                        <TabPane tab="学历系数" key="education"/>
                        <TabPane tab="外语水平系数" key="language"/>
                        <TabPane tab="职称系数" key="title"/>
                        <TabPane tab="绩效等级系数" key="performance"/>
                    </Tabs>

                    {activeCoefficientType === 'title' && (
                        <div style={{ marginBottom: '16px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                            <span style={{ marginRight: '8px' }}>职称系数：</span>
                            <Switch
                                checked={titleCoefficientEnabled}
                                onChange={(checked) => {
                                    setTitleCoefficientEnabled(checked);
                                    localStorage.setItem('titleCoefficientEnabled', String(checked));

                                    // 更新全局状态中的标记
                                    const newConfig = {...salaryConfig};
                                    if (!newConfig.SYSTEM_CONFIG) {
                                        newConfig.SYSTEM_CONFIG = {};
                                    }
                                    newConfig.SYSTEM_CONFIG.TITLE_COEFFICIENT_ENABLED = checked;
                                    setSalaryConfig(newConfig);
                                }}
                                checkedChildren="启用"
                                unCheckedChildren="禁用"
                                size="small"
                                style={{ width: '76px' }}
                            />
                        </div>
                    )}

                    <Table
                        dataSource={data}
                        columns={columns}
                        rowKey="level"
                        pagination={false}
                        bordered
                        className="salary-table"
                        size="small"
                        scroll={{ y: 400 }}
                    />
                </Card>
            </div>
        );
    };

    // 管理岗位津贴表单
    const AdminPositionForm = () => {
        // 使用字符串类型的状态来存储输入值，避免数值转换导致的光标跳动
        const [inputValues, setInputValues] = useState({});

        const [adminPositions, setAdminPositions] = useState(
            Object.entries(salaryConfig.ADMIN_POSITIONS).map(([position, allowance]) => ({
                position,
                allowance
            }))
        );

        // 当 salaryConfig 更新时，更新本地状态
        useEffect(() => {
            setAdminPositions(
                Object.entries(salaryConfig.ADMIN_POSITIONS).map(([position, allowance]) => ({
                    position,
                    allowance
                }))
            );

            // 初始化输入值
            const inputs = {};
            Object.entries(salaryConfig.ADMIN_POSITIONS).forEach(([position, allowance]) => {
                inputs[position] = String(allowance || '0');
            });
            setInputValues(inputs);
        }, [salaryConfig]);

        // 格式化货币显示值
        const formatCurrencyValue = (value) => {
            if (value === '' || value === '0') return '¥ 0';
            return `¥ ${value}`;
        };

        // 解析货币输入值
        const parseCurrencyValue = (value) => {
            return value.replace(/[^0-9]/g, '');
        };

        // 处理输入变化
        const handleInputChange = (position, e) => {
            // 防止事件冒泡
            e.stopPropagation();

            // 获取原始输入值
            const rawValue = e.target.value;

            // 如果用户正在删除内容，直接更新为空字符串
            if (rawValue === '' || rawValue === '¥') {
                setInputValues(prev => ({
                    ...prev,
                    [position]: ''
                }));
                return;
            }

            // 解析货币输入值，只保留数字
            const value = parseCurrencyValue(rawValue);

            // 更新输入值
            setInputValues(prev => ({
                ...prev,
                [position]: value
            }));
        };

        // 处理失去焦点
        const handleBlur = (position, e) => {
            // 获取输入值并转换为数值
            const rawValue = parseCurrencyValue(e.target.value);
            const value = parseInt(rawValue, 10);

            // 更新本地状态
            setAdminPositions(prev => {
                const newPositions = [...prev];
                const index = newPositions.findIndex(item => item.position === position);
                if (index !== -1) {
                    newPositions[index].allowance = isNaN(value) ? 0 : value;
                }
                return newPositions;
            });

            // 更新全局状态
            const newConfig = {...salaryConfig};
            newConfig.ADMIN_POSITIONS[position] = isNaN(value) ? 0 : value;
            setSalaryConfig(newConfig);

            // 更新输入值为规范化后的值
            setInputValues(prev => ({
                ...prev,
                [position]: String(isNaN(value) ? 0 : value)
            }));
        };

        const columns = [
            {
                title: '管理岗位',
                dataIndex: 'position',
                key: 'position',
                width: '50%',
                align: 'center'
            },
            {
                title: '津贴金额',
                dataIndex: 'allowance',
                key: 'allowance',
                width: '50%',
                align: 'center',
                render: (text, record) => (
                    <InputNumber
                        min={0}
                        step={100}
                        precision={0}
                        value={record.allowance}
                        formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                        className="admin-input-number uniform-input"
                        style={{ width: '126px' }}
                        onChange={(value) => {
                            // 只更新本地状态，不更新全局状态
                            const newPositions = [...adminPositions];
                            const index = newPositions.findIndex(item => item.position === record.position);
                            if (index !== -1) {
                                newPositions[index].allowance = value;
                                setAdminPositions(newPositions);
                            }
                        }}
                    />
                )
            }
        ];

        const handleSave = async () => {
            try {
                // 构建更新对象
                const adminPositionsConfig = {};
                adminPositions.forEach(({ position, allowance }) => {
                    adminPositionsConfig[position] = allowance;
                });

                // 使用统一的更新函数
                await updateSalaryConfig({
                    ADMIN_POSITIONS: adminPositionsConfig
                });

                // 保存当前活动的选项卡
                localStorage.setItem('salaryConfigActiveTab', activeTabKey);

                message.success('管理岗位津贴配置保存成功');
            } catch (error) {
                console.error('保存管理岗位津贴配置失败:', error);
                message.error('保存管理岗位津贴配置失败');
            }
        };

        return (
            <div className="salary-form">
                <Card
                    className="config-card basic-salary-card"
                    title="管理岗位津贴配置"
                    type="inner"
                    size="small"
                >
                    <Table
                        dataSource={adminPositions}
                        columns={columns}
                        rowKey="position"
                        pagination={false}
                        bordered
                        className="admin-position-table"
                        size="middle"
                    />
                </Card>
            </div>
        );
    };

    // 个税配置表单
    const TaxConfigForm = () => {
        const [taxConfig, setTaxConfig] = useState({
            THRESHOLD: salaryConfig.TAX.THRESHOLD,
            SPECIAL_DEDUCTION: salaryConfig.TAX.SPECIAL_DEDUCTION || {
                housing: 0,
                education: 0,
                medical: 0,
                elderly: 0,
                training: 0
            }
        });

        // 不再需要防抖更新函数，因为我们不再立即更新全局状态
        const handleChange = (field, value) => {
            // 只更新本地状态，不更新全局状态
            setTaxConfig(prev => ({
                ...prev,
                [field]: value
            }));
        };

        const handleSave = async () => {
            try {
                // 使用统一的更新函数
                await updateSalaryConfig({
                    TAX: {
                        THRESHOLD: taxConfig.THRESHOLD,
                        SPECIAL_DEDUCTION: taxConfig.SPECIAL_DEDUCTION
                    }
                });

                // 保存当前活动的选项卡
                localStorage.setItem('salaryConfigActiveTab', activeTabKey);

                // 成功消息由全局保存函数统一处理
            } catch (error) {
                console.error('保存个税配置失败:', error);
                message.error('保存个税配置失败');
            }
        };

        // 当 salaryConfig 更新时，更新本地状态
        useEffect(() => {
            setTaxConfig({
                THRESHOLD: salaryConfig.TAX.THRESHOLD,
                SPECIAL_DEDUCTION: salaryConfig.TAX.SPECIAL_DEDUCTION || {
                    housing: 0,
                    education: 0,
                    medical: 0,
                    elderly: 0,
                    training: 0
                }
            });
        }, [salaryConfig]);

        return (
            <div>
                <Card
                    className="config-card basic-salary-card"
                    title="个税起征点配置"
                    type="inner"
                    size="small"
                >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ width: '120px', flexShrink: 0 }}>个税起征点：</div>
                        <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap' }}>
                            <InputNumber
                                min={0}
                                step={100}
                                precision={0}
                                value={taxConfig.THRESHOLD}
                                onChange={(value) => {
                                    // 只更新本地状态，不更新全局状态
                                    setTaxConfig(prev => ({
                                        ...prev,
                                        THRESHOLD: value
                                    }));
                                }}
                                formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                parser={value => value.replace(/\¥\s?|(,*)/g, '')}
                                className="admin-input-number uniform-input"
                                style={{ width: '126px' }}
                                keyboard={true}
                                inputMode="numeric"
                                controls={true}
                            />
                            <span style={{ marginLeft: '12px', color: '#666', fontSize: '13px', whiteSpace: 'nowrap' }}>
                                （说明：个税起征点按照国家最新标准执行）
                            </span>
                        </div>
                    </div>
                </Card>

                <Card
                    className="config-card basic-salary-card"
                    title="专项附加扣除说明"
                    type="inner"
                    size="small"
                    style={{ marginTop: '16px' }}
                >
                    <div style={{ padding: '10px', color: '#666' }}>
                        <p>专项附加扣除项目包括：住房租金、子女教育、继续教育、大病医疗和赡养老人等。</p>
                        <p>根据国家税务总局规定，专项附加扣除是针对每个纳税人的个人情况而定的，应在计算个人薪资时单独录入。</p>
                        <p>请在为员工计算薪资时，根据员工提供的专项附加扣除信息进行个性化设置。</p>
                    </div>
                </Card>
            </div>
        );
    };

    // 全局保存函数
    const handleSaveClick = async () => {
        try {
            setLoading(true);

            // 根据当前活动的选项卡执行相应的保存逻辑
            if (activeTabKey === '1') {
                // 基础薪资配置 - 使用本地状态
                const basicSalaryForm = document.querySelector('.basic-salary-card');
                if (basicSalaryForm) {
                    // 获取表单中的值
                    const baseInput = basicSalaryForm.querySelector('input[placeholder="请输入基础工资"]');
                    const mealInput = basicSalaryForm.querySelector('input[placeholder="请输入餐补"]');
                    const commInput = basicSalaryForm.querySelector('input[placeholder="请输入通讯补贴"]');

                    const basicSalaryValues = {
                        BASE_SALARY: baseInput ? parseFloat(baseInput.value.replace(/[^0-9.]/g, '')) || 0 : salaryConfig.BASE_SALARY,
                        MEAL_ALLOWANCE: mealInput ? parseFloat(mealInput.value.replace(/[^0-9.]/g, '')) || 0 : salaryConfig.MEAL_ALLOWANCE,
                        COMMUNICATION_ALLOWANCE: commInput ? parseFloat(commInput.value.replace(/[^0-9.]/g, '')) || 0 : salaryConfig.COMMUNICATION_ALLOWANCE
                    };
                    await updateSalaryConfig(basicSalaryValues);
                } else {
                    // 如果找不到表单，使用全局状态
                    const basicSalaryValues = {
                        BASE_SALARY: salaryConfig.BASE_SALARY,
                        MEAL_ALLOWANCE: salaryConfig.MEAL_ALLOWANCE,
                        COMMUNICATION_ALLOWANCE: salaryConfig.COMMUNICATION_ALLOWANCE
                    };
                    await updateSalaryConfig(basicSalaryValues);
                }
            } else if (activeTabKey === '2') {
                // 社保参数配置
                // 先保存社保基数类型
                await saveInsuranceBaseType();

                // 从本地存储获取社保基数类型和固定金额
                const baseType = localStorage.getItem('insuranceBaseType') || salaryConfig.INSURANCE_BASE?.type || 'baseSalary';
                const fixedAmount = localStorage.getItem('insuranceFixedAmount') || salaryConfig.INSURANCE_BASE?.fixedAmount || 5000;

                // 构建社保基数配置
                const insuranceBase = {
                    type: baseType,
                    fixedAmount: parseInt(fixedAmount) || 5000,
                    limits: salaryConfig.INSURANCE_BASE?.limits || {
                        pension: {
                            min: 4638.88,
                            max: 23194.6
                        },
                        medical: {
                            min: 4853,
                            max: 24267
                        },
                        unemployment: {
                            min: 4217,
                            max: 21086
                        }
                    }
                };

                await updateSalaryConfig({
                    INSURANCE_RATES: salaryConfig.INSURANCE_RATES,
                    INSURANCE_BASE: insuranceBase
                });
            } else if (activeTabKey === '5') {
                // 岗位工资配置
                const techConfig = {};
                Object.entries(salaryConfig.TECH_POSITIONS['技术']).forEach(([level, salary]) => {
                    techConfig[level] = salary;
                });

                const managerConfig = {};
                Object.entries(salaryConfig.MANAGER_POSITIONS['高管']).forEach(([level, salary]) => {
                    managerConfig[level] = salary;
                });

                const otherConfig = {};
                Object.entries(salaryConfig.OTHER_POSITIONS['其他']).forEach(([level, salary]) => {
                    otherConfig[level] = salary;
                });

                const positionConfig = {
                    TECH_POSITIONS: {
                        '技术': techConfig
                    },
                    MANAGER_POSITIONS: {
                        '高管': managerConfig
                    },
                    OTHER_POSITIONS: {
                        '其他': otherConfig
                    }
                };

                await updateSalaryConfig(positionConfig);
            } else if (activeTabKey === '6') {
                // 调整系数配置
                const educationConfig = {};
                Object.entries(salaryConfig.EDUCATION_COEFFICIENT).forEach(([level, coefficient]) => {
                    educationConfig[level] = coefficient;
                });

                const languageConfig = {};
                Object.entries(salaryConfig.LANGUAGE_COEFFICIENT).forEach(([level, coefficient]) => {
                    languageConfig[level] = coefficient;
                });

                const titleConfig = {};
                Object.entries(salaryConfig.TITLE_COEFFICIENT).forEach(([level, coefficient]) => {
                    titleConfig[level] = coefficient;
                });

                const performanceConfig = {};
                Object.entries(salaryConfig.PERFORMANCE_LEVELS).forEach(([level, coefficient]) => {
                    performanceConfig[level] = coefficient;
                });

                // 获取职称系数启用状态
                const titleCoefficientEnabled = localStorage.getItem('titleCoefficientEnabled') === 'true';

                const coefficientConfig = {
                    EDUCATION_COEFFICIENT: educationConfig,
                    LANGUAGE_COEFFICIENT: languageConfig,
                    TITLE_COEFFICIENT: titleConfig,
                    PERFORMANCE_LEVELS: performanceConfig,
                    SYSTEM_CONFIG: {
                        ...salaryConfig.SYSTEM_CONFIG,
                        TITLE_COEFFICIENT_ENABLED: titleCoefficientEnabled
                    }
                };

                await updateSalaryConfig(coefficientConfig);
            } else if (activeTabKey === '7') {
                // 管理岗位津贴配置
                const adminPositionsConfig = {};
                Object.entries(salaryConfig.ADMIN_POSITIONS).forEach(([position, allowance]) => {
                    adminPositionsConfig[position] = allowance;
                });

                await updateSalaryConfig({
                    ADMIN_POSITIONS: adminPositionsConfig
                });
            } else if (activeTabKey === '8') {
                // 个税配置 - 获取表单中的值
                const taxForm = document.querySelector('.config-card[title="个税起征点配置"]');
                let threshold = salaryConfig.TAX.THRESHOLD;

                if (taxForm) {
                    const thresholdInput = taxForm.querySelector('input');
                    if (thresholdInput) {
                        threshold = parseFloat(thresholdInput.value.replace(/[^0-9.]/g, '')) || threshold;
                    }
                }

                await updateSalaryConfig({
                    TAX: {
                        THRESHOLD: threshold,
                        // 保留 SPECIAL_DEDUCTION 结构，但不在后台设置具体值
                        SPECIAL_DEDUCTION: {
                            housing: 0,
                            education: 0,
                            medical: 0,
                            elderly: 0,
                            training: 0
                        }
                    }
                });
            }

            message.success('薪资配置保存成功');
        } catch (error) {
            console.error('保存配置失败:', error);
            message.error('保存配置失败: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="salary-config-container admin-page-container" style={{ maxWidth: '700px', margin: '0 auto' }}>
            <div className="admin-page-header" style={{ flexDirection: 'column', alignItems: 'center', position: 'relative', textAlign: 'center' }}>
                <h2 className="admin-page-title" style={{ textAlign: 'center' }}>薪资配置管理</h2>
                <p style={{ marginTop: '8px', color: '#666', fontSize: '12px' }}>
                    特别提醒：所有数据的设定将影响员工薪资计算，请确保数据准确无误。
                </p>
                <Button
                    type="primary"
                    onClick={handleSaveClick}
                    icon={<SaveOutlined />}
                    style={{
                        height: '28px',
                        width: '86px',
                        fontSize: '12px',
                        borderRadius: '4px',
                        boxShadow: '0 2px 6px rgba(24, 144, 255, 0.2)',
                        position: 'absolute',
                        right: '0',
                        top: '0'
                    }}
                >
                    保存配置
                </Button>
            </div>
            <Tabs
                defaultActiveKey="1"
                className="salary-tabs"
                onChange={setActiveTabKey}
                activeKey={activeTabKey}
                size="small"
                tabBarGutter={0}
                centered={false}>
                <TabPane tab="基础薪资" key="1">
                    <div className="salary-tab-content">
                        <BasicSalaryForm />
                    </div>
                </TabPane>
                <TabPane tab="社保参数" key="2">
                    <div className="salary-tab-content">
                        <Card
                            className="config-card basic-salary-card"
                            title="社保基数类型设置"
                            type="inner"
                            size="small"
                            style={{ marginBottom: '16px' }}
                        >
                            <DirectInsuranceBaseControl />
                        </Card>

                        <div style={{
                            marginBottom: '16px',
                            color: '#666',
                            fontSize: '14px',
                            padding: '0 8px'
                        }}>
                            <i>说明：社保费率和上下限基数按照陕西省最新标准执行</i>
                        </div>

                        <InsuranceRatesForm />
                    </div>
                </TabPane>
                {/* 绩效等级配置已经包含在调整系数配置中 */}
                <TabPane tab="岗位工资" key="5">
                    <div className="salary-tab-content">
                        <PositionSalaryForm />
                    </div>
                </TabPane>
                <TabPane tab="调整系数" key="6">
                    <div className="salary-tab-content">
                        <CoefficientForm />
                    </div>
                </TabPane>
                <TabPane tab="管理津贴" key="7">
                    <div className="salary-tab-content">
                        <AdminPositionForm />
                    </div>
                </TabPane>
                <TabPane tab="个税设置" key="8">
                    <div className="salary-tab-content">
                        <TaxConfigForm />
                    </div>
                </TabPane>
            </Tabs>
        </div>
    );
};

export default SalaryConfig;
