# MCHRMS 项目结构说明

## 项目概述
MCHRMS (人力资源管理系统) - 版本 1.2.2
一个基于 React + Node.js + MongoDB 的现代化人力资源管理系统

## 根目录结构
mchrms/
├── .env                    # 根目录环境变量配置
├── .env.production         # 生产环境配置
├── start.js                # 项目启动脚本 (主入口)
├── checkDependencies.js    # 依赖检查脚本
├── mainServer.js           # 主服务器配置
├── package.json            # 项目依赖配置 (版本 1.2.2)
├── package-lock.json       # 依赖版本锁定文件
├── backup.sh               # 备份脚本
├── deploy-to-nas.sh        # NAS 部署脚本
├── Dockerfile              # Docker 配置文件
├── moco-hr.tar.gz          # 项目压缩包
├── PROJECT_STRUCTURE.md    # 项目结构说明 (本文件)
├── switch-env.sh           # 环境切换脚本
│
├── node_modules/           # 根目录依赖包 (npm install 生成)
├── backups/                # 项目备份目录
│   ├── *.tar.gz           # 压缩备份文件
│   └── *.info             # 备份信息文件
│
├── frontend/               # React 前端应用
│   ├── package.json        # 前端依赖配置
│   ├── package-lock.json   # 前端依赖版本锁定
│   ├── README.md           # 前端说明文档
│   ├── node_modules/       # 前端依赖包
│   │
│   ├── public/             # 静态资源目录
│   │   ├── index.html      # HTML 模板
│   │   ├── electron.js     # Electron 相关配置
│   │   ├── favicon.ico     # 网站图标
│   │   ├── logo192.png     # Logo 图标 (192x192)
│   │   ├── logo512.png     # Logo 图标 (512x512)
│   │   ├── manifest.json   # PWA 配置文件
│   │   └── robots.txt      # 爬虫规则文件
│   │
│   ├── src/                # 前端源代码目录
│   │   ├── index.js        # React 应用入口
│   │   ├── App.js          # 主应用组件
│   │   ├── index.css       # 全局样式
│   │   ├── config.js       # 前端配置
│   │   ├── reportWebVitals.js  # Web 性能监控
│   │   │
│   │   ├── api/            # API 请求模块
│   │   ├── components/     # React 组件目录
│   │   │   ├── admin/      # 管理员组件
│   │   │   ├── attendance/ # 考勤管理组件
│   │   │   ├── common/     # 公共组件
│   │   │   ├── employee/   # 员工管理组件
│   │   │   ├── salary/     # 薪资管理组件
│   │   │   └── settings/   # 设置组件
│   │   │
│   │   ├── common/         # 公共模块
│   │   │   ├── DeleteConfirmModal.js # 删除确认模态框
│   │   │   ├── ExportDialog.js       # 导出对话框
│   │   │   ├── ExportUtils.js        # 导出工具
│   │   │   ├── PrintTemplate.js      # 打印模板
│   │   │   ├── PrintUtils.js         # 打印工具
│   │   │   └── TableConfig.js        # 表格配置
│   │   │
│   │   ├── services/       # 服务层
│   │   ├── styles/         # 样式文件
│   │   │   ├── ButtonStyles.css      # 按钮样式
│   │   │   └── GlobalModalStyles.css # 全局模态框样式
│   │   │
│   │   └── utils/          # 工具函数
│   │       ├── clientPort.js         # 客户端端口配置
│   │       ├── educationMapper.js    # 学历映射工具
│   │       ├── eventBus.js           # 事件总线
│   │       ├── pinyinSearch.js       # 拼音搜索工具
│   │       ├── salaryUtils.js        # 薪资计算工具
│   │       ├── testPinyinSearch.js   # 拼音搜索测试
│   │       └── warningUtils.js       # 警告工具
│   │
│   ├── assets/             # 前端资源文件
│   │   └── moco-logo.png   # 公司Logo
│   ├── scripts/            # 前端脚本
│   │   └── convert-icon.js # 图标转换脚本
│   └── build/              # 构建输出目录 (npm run build 生成)
│
├── backend/                # Node.js 后端服务
│   ├── package.json        # 后端依赖配置
│   ├── package-lock.json   # 后端依赖版本锁定
│   ├── node_modules/       # 后端依赖包
│   ├── server.js           # 主服务器入口文件
│   ├── server-hrmsdb.js    # HRMS 数据库服务器
│   ├── authMiddleware.js   # 认证中间件 (旧版)
│   ├── generate-hash.js    # 密码哈希生成工具
│   ├── fix-salary-data.js  # 薪资数据修复脚本
│   │
│   ├── config/             # 配置文件目录
│   │   ├── db.js           # 数据库连接配置
│   │   └── salaryConfig.js # 薪资计算配置
│   │
│   ├── middlewares/        # 中间件目录
│   │   ├── authMiddleware.js   # 认证中间件
│   │   └── adminMiddleware.js  # 管理员权限中间件
│   │
│   ├── routes/             # API 路由目录
│   │   ├── auth.js         # 用户认证路由
│   │   ├── employees.js    # 员工管理路由
│   │   ├── salaryRoutes.js # 薪资管理路由
│   │   ├── adminRoutes.js  # 管理员路由
│   │   └── healthRoutes.js # 健康检查路由
│   │
│   ├── models/             # MongoDB 数据模型
│   │   ├── User.js         # 用户数据模型
│   │   ├── Employee.js     # 员工数据模型
│   │   └── Salary.js       # 薪资数据模型
│   │
│   ├── services/           # 业务服务层
│   │   └── configService.js # 配置服务
│   │
│   ├── scripts/            # 后端脚本
│   │   ├── createUser.js           # 创建用户脚本
│   │   ├── createUserInContainer.js # 容器内创建用户
│   │   └── fixAdminRole.js         # 修复管理员角色
│   │
│   └── utils/              # 后端工具函数
│       ├── calculationUtils.js     # 通用计算工具
│       ├── SalaryCalculator.js     # 薪资计算器核心类
│       ├── createInitialUsers.js   # 初始用户创建
│       └── smsUtils.js             # 短信服务工具
│
├── utils/                  # 根级别工具目录
│   ├── mainPort.js         # 主端口管理工具
│   └── package-lock.json   # 工具目录的依赖锁定
│
├── scripts/                # 项目脚本目录
│   ├── check-frontend-components.js    # 前端组件检查
│   ├── check-frontend-salary-logic.js  # 前端薪资逻辑检查
│   ├── check-salary-consistency.js     # 薪资一致性检查
│   ├── check_salary_module.sh          # 薪资模块检查脚本
│   ├── run-all-checks.sh               # 运行所有检查
│   ├── run-frontend-check.sh           # 前端检查脚本
│   ├── run-frontend-components-check.sh # 前端组件检查脚本
│   ├── run-salary-check.sh             # 薪资检查脚本
│   └── salary_check.js                 # 薪资检查工具
│
├── data/                   # 数据存储目录
│   ├── db/                 # MongoDB 数据库文件目录
│   │   └── (WiredTiger 内部文件)
│   ├── mongodb.log         # MongoDB 当前日志
│   ├── mongodb.log.2025-02-12T02-18-50  # 历史日志文件
│   ├── mongodb.log.2025-02-12T09-46-36  # 历史日志文件
│   ├── mongodb.log.2025-02-13T06-51-38  # 历史日志文件
│   └── mongodb.log.2025-02-13T10-12-39  # 历史日志文件
│
├── docs/                   # 项目文档目录
│   └── salary_module_check_list.md     # 薪资模块检查清单
│
├── extracted/              # 提取的文件目录
│   └── database_complete/  # 完整数据库备份
│
├── deploy/                 # 部署相关文件
│   ├── backend/            # 部署用后端代码副本
│   │   ├── (完整后端代码结构)
│   ├── public/             # 部署用前端静态文件
│   │   ├── asset-manifest.json  # 资源清单
│   │   ├── index.html           # 主页面
│   │   ├── favicon.ico          # 网站图标
│   │   ├── manifest.json        # PWA 配置
│   │   └── static/              # 静态资源 (CSS, JS, 图片)
│   ├── Dockerfile          # Docker 部署配置
│   └── start.sh            # 部署启动脚本

## 技术栈
- **前端**: React 18 + Ant Design + CSS3
- **后端**: Node.js + Express.js
- **数据库**: MongoDB + Mongoose ODM
- **缓存**: Redis
- **构建工具**: Create React App
- **部署**: Docker + NAS 容器

## 核心功能模块
1. **用户管理**: 登录认证、权限控制
2. **员工管理**: 员工信息CRUD、部门管理
3. **薪资管理**: 薪资计算、试用期处理、导出打印
4. **考勤管理**: 出勤记录、请假管理
5. **系统设置**: 配置管理、数据备份

## 启动方式
```bash
# 完整启动 (推荐)
node start.js

# 分别启动
cd backend && npm start
cd frontend && npm start
```

## 备份说明
- 项目支持完整备份和精简备份
- 备份文件存储在 `/Users/<USER>/backups/` 目录
- 包含数据库备份和项目文件备份