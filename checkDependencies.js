const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);
const fs = require('fs');
const path = require('path');
const { checkPort } = require('./utils/mainPort');
const { execSync } = require('child_process');

// 添加环境变量检查
function checkEnvironmentVariables() {
    const required = [
        'NODE_ENV',
        'MONGODB_URI',
        'REDIS_URL'
    ];
    
    const missing = required.filter(key => !process.env[key]);
    if (missing.length > 0) {
        console.error('❌ 缺少必要的环境变量:', missing.join(', '));
        return false;
    }
    console.log('✅ 环境变量检查通过');
    return true;
}

// 原有的依赖检查函数
function checkDependency(command, name) {
    try {
        execSync(command, { stdio: 'ignore' });
        console.log(`✅ ${name} 已安装`);
        return true;
    } catch (error) {
        console.error(`❌ ${name} 未安装或版本不正确`);
        return false;
    }
}

function compareVersions(version1, version2) {
    const v1 = version1.split('.').map(Number);
    const v2 = version2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
        const num1 = v1[i] || 0;
        const num2 = v2[i] || 0;
        if (num1 > num2) return 1;
        if (num1 < num2) return -1;
    }
    return 0;
}

function checkDependencyVersion(command, name, minVersion) {
    try {
        const version = execSync(command, { encoding: 'utf8' }).trim();
        let cleanVersion;
        
        // 特殊处理 Redis CLI 版本
        if (name === 'Redis CLI' && version.includes('redis-cli')) {
            cleanVersion = version.split(' ')[1] || version.split(' ')[0];
        } else {
            cleanVersion = version.replace(/^v/, '').split(' ')[0];
        }
        
        if (compareVersions(cleanVersion, minVersion) >= 0) {
            console.log(`✅ ${name} 版本 ${cleanVersion} 符合要求`);
            return true;
        } else {
            console.error(`❌ ${name} 版本 ${cleanVersion} 过低，需要 ${minVersion} 或更高版本`);
            return false;
        }
    } catch (error) {
        console.error(`❌ ${name} 版本检查失败:`, error.message);
        return false;
    }
}

async function checkDependencies() {
    let allPassed = true;
    
    if (!checkEnvironmentVariables()) {
        allPassed = false;
    }

    const checks = [
        { command: 'node -v', name: 'Node.js', minVersion: '14.0.0' },
        { command: 'npm -v', name: 'NPM', minVersion: '6.0.0' },
        { command: 'mongosh --version', name: 'MongoDB Shell', minVersion: '1.0.0' },
        { command: 'redis-cli -v', name: 'Redis CLI', minVersion: '6.0.0' }
    ];
    
    for (const check of checks) {
        if (!checkDependencyVersion(check.command, check.name, check.minVersion)) {
            allPassed = false;
        }
    }

    if (!allPassed) {
        process.exit(1);
    }

    process.exit(0);
}

checkDependencies();