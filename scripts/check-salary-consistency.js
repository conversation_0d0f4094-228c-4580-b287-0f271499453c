/**
 * 薪资计算一致性检查脚本
 * 
 * 此脚本用于检查薪资计算相关文件的数值是否一致，是否能正确显示
 * 从数据库中读取指定员工的信息，并进行薪资计算和一致性检查
 */

const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const { calculateMonthlySalary } = require('../backend/utils/SalaryCalculator');
const { SALARY_CONFIG } = require('../backend/config/salaryConfig');

// 连接数据库
async function connectDB() {
    try {
        const dbUrl = process.env.MONGODB_URI || 'mongodb://localhost:27017/mchrms';
        await mongoose.connect(dbUrl, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('数据库连接成功');
    } catch (error) {
        console.error('数据库连接失败:', error);
        process.exit(1);
    }
}

// 定义员工模型
const employeeSchema = new mongoose.Schema({}, { strict: false });
const Employee = mongoose.model('Employee', employeeSchema, 'employees');

// 定义薪资模型
const salarySchema = new mongoose.Schema({}, { strict: false });
const Salary = mongoose.model('Salary', salarySchema, 'salaries');

// 检查员工薪资计算一致性
async function checkSalaryConsistency(employeeIds) {
    console.log('开始检查薪资计算一致性...');
    console.log('='.repeat(80));
    
    for (const empId of employeeIds) {
        console.log(`\n检查员工 ${empId} 的薪资计算一致性:`);
        console.log('-'.repeat(80));
        
        // 从数据库获取员工信息
        const employee = await Employee.findOne({ employeeId: empId });
        if (!employee) {
            console.log(`未找到员工 ${empId} 的信息`);
            continue;
        }
        
        // 从数据库获取员工薪资信息
        const salary = await Salary.findOne({ employeeId: empId });
        if (!salary) {
            console.log(`未找到员工 ${empId} 的薪资信息`);
            continue;
        }
        
        // 打印员工基本信息
        console.log('员工基本信息:');
        console.log(`  工号: ${employee.employeeId}`);
        console.log(`  姓名: ${employee.name}`);
        console.log(`  部门: ${employee.department || '未设置'}`);
        console.log(`  职位: ${employee.position || '未设置'}`);
        console.log(`  学历: ${employee.education || '未设置'}`);
        console.log(`  语言等级: ${employee.languageLevel || '未设置'}`);
        console.log(`  是否试用期: ${employee.isProbation ? '是' : '否'}`);
        
        // 打印数据库中的薪资信息
        console.log('\n数据库中的薪资信息:');
        console.log(`  基本工资: ${salary.adjustedBaseSalary || 0}`);
        console.log(`  学历系数: ${salary.educationCoefficient || 1}`);
        console.log(`  学历调整值: ${salary.educationAdjustment || 0}`);
        console.log(`  语言系数: ${salary.languageCoefficient || 1}`);
        console.log(`  语言调整值: ${salary.languageAdjustment || 0}`);
        
        // 使用SalaryCalculator重新计算薪资
        const calculationParams = {
            positionLevel: salary.positionLevel || employee.positionLevel,
            positionType: salary.positionType || employee.positionType || '技术',
            education: salary.education || employee.education,
            languageLevel: salary.languageLevel || employee.languageLevel,
            administrativeLevel: salary.administrativeLevel || employee.administrativeLevel,
            performanceCoefficient: salary.performanceCoefficient || 1.0,
            actualAttendance: salary.actualAttendance || 22,
            specialAllowance: salary.specialAllowance || { amount: 0 },
            specialDeduction: salary.specialDeduction || { amount: 0 },
            isProbation: salary.isProbation || employee.isProbation || false,
            workType: salary.workType || employee.workType || '全职',
            probationEndDate: salary.probationEndDate || employee.probationEndDate || ''
        };
        
        // 执行薪资计算
        const calculatedSalary = calculateMonthlySalary(calculationParams);
        
        // 打印计算结果
        console.log('\n重新计算的薪资信息:');
        console.log(`  基本工资: ${calculatedSalary.adjustedBaseSalary}`);
        console.log(`  学历系数: ${calculatedSalary.educationCoefficient}`);
        console.log(`  学历调整值: ${calculatedSalary.educationAdjustment}`);
        console.log(`  语言系数: ${calculatedSalary.languageCoefficient}`);
        console.log(`  语言调整值: ${calculatedSalary.languageAdjustment}`);
        
        // 检查数据一致性
        console.log('\n数据一致性检查:');
        
        // 检查基本工资
        checkConsistency('基本工资', salary.adjustedBaseSalary, calculatedSalary.adjustedBaseSalary);
        
        // 检查学历系数
        checkConsistency('学历系数', salary.educationCoefficient, calculatedSalary.educationCoefficient);
        
        // 检查学历调整值
        checkConsistency('学历调整值', salary.educationAdjustment, calculatedSalary.educationAdjustment);
        
        // 检查语言系数
        checkConsistency('语言系数', salary.languageCoefficient, calculatedSalary.languageCoefficient);
        
        // 检查语言调整值
        checkConsistency('语言调整值', salary.languageAdjustment, calculatedSalary.languageAdjustment);
        
        // 检查总薪资
        checkConsistency('应发工资', salary.calculationResult?.totalMonthlySalary, calculatedSalary.calculationResult.totalMonthlySalary);
        
        // 检查净薪资
        checkConsistency('实发工资', salary.calculationResult?.netSalary, calculatedSalary.calculationResult.netSalary);
        
        console.log('-'.repeat(80));
    }
    
    console.log('\n薪资计算一致性检查完成');
    console.log('='.repeat(80));
}

// 检查两个值是否一致
function checkConsistency(fieldName, dbValue, calculatedValue) {
    const dbVal = Number(dbValue) || 0;
    const calcVal = Number(calculatedValue) || 0;
    
    // 计算差异（允许0.01的误差）
    const difference = Math.abs(dbVal - calcVal);
    const isConsistent = difference < 0.01;
    
    console.log(`  ${fieldName}: ${isConsistent ? '✓ 一致' : '✗ 不一致'} (数据库: ${dbVal}, 计算值: ${calcVal}, 差异: ${difference.toFixed(2)})`);
    
    return isConsistent;
}

// 主函数
async function main() {
    try {
        await connectDB();
        
        // 要检查的员工ID列表
        const employeeIds = ['M001', '002', '003', '006'];
        
        // 检查薪资计算一致性
        await checkSalaryConsistency(employeeIds);
        
        // 断开数据库连接
        await mongoose.disconnect();
        console.log('数据库连接已关闭');
    } catch (error) {
        console.error('执行过程中出错:', error);
    }
}

// 执行主函数
main();
