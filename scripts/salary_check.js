/**
 * 薪资计算功能检查脚本
 * 
 * 本脚本用于检查薪资计算功能的正确性，特别关注以下几点：
 * 1. 当用户首次使用薪资模块时，所有薪资相关金额应该显示其是默认，比如金额为0，系数为1等等
 * 2. 薪资详单，薪资表单中关于学历和语言调整系数和金额的一致性显示
 * 3. 根据学历和语言系数调整的基本工资值的正确性（分为三种情况，调整系数大于1，等于1和小于1）
 */

const fs = require('fs');
const path = require('path');
const { MongoClient } = require('mongodb');

// 数据库连接配置
const dbConfig = {
  url: 'mongodb://localhost:27017',
  dbName: 'mchrms',
  employeeCollection: 'employees',
  salaryCollection: 'salaries'
};

// 测试用例配置
const testCases = [
  {
    name: '首次使用薪资模块',
    description: '检查首次使用薪资模块时，所有薪资相关金额是否显示为默认值',
    setup: async (db) => {
      // 清空薪资数据
      await db.collection(dbConfig.salaryCollection).deleteMany({});
      console.log('已清空薪资数据，准备测试首次使用薪资模块');
    },
    verify: (employee) => {
      // 验证薪资相关金额是否为默认值
      const isDefault = 
        employee.adjustedBaseSalary === 0 &&
        employee.positionSalary === 0 &&
        employee.adminSalary === 0 &&
        employee.performanceBonus === 0 &&
        (!employee.calculationResult || 
          (employee.calculationResult.totalMonthlySalary === 0 && 
           employee.calculationResult.netSalary === 0));
      
      return {
        passed: isDefault,
        message: isDefault ? 
          '首次使用薪资模块测试通过：所有薪资相关金额显示为默认值' : 
          `首次使用薪资模块测试失败：存在非默认值 ${JSON.stringify({
            adjustedBaseSalary: employee.adjustedBaseSalary,
            positionSalary: employee.positionSalary,
            adminSalary: employee.adminSalary,
            performanceBonus: employee.performanceBonus,
            totalMonthlySalary: employee.calculationResult?.totalMonthlySalary,
            netSalary: employee.calculationResult?.netSalary
          })}`
      };
    }
  },
  {
    name: '学历系数大于1',
    description: '检查学历系数大于1时，基本工资的计算是否正确',
    setup: async (db) => {
      // 设置测试数据
      const testEmployee = {
        employeeId: 'TEST001',
        name: '测试员工1',
        education: '硕士（985/211 院校）',
        languageLevel: '一般',
        educationCoefficient: 1.2,
        languageCoefficient: 1.0,
        educationAdjustment: 700, // 假设基本工资为3500，则调整值为3500 * (1.2 - 1) = 700
        languageAdjustment: 0,
        originalBaseSalary: 3500,
        adjustedBaseSalary: 4200 // 3500 + 700 + 0 = 4200
      };
      
      await db.collection(dbConfig.employeeCollection).updateOne(
        { employeeId: 'TEST001' },
        { $set: testEmployee },
        { upsert: true }
      );
      
      console.log('已设置学历系数大于1的测试数据');
    },
    verify: (employee) => {
      // 验证基本工资计算是否正确
      const expectedBaseSalary = 4200; // 3500 + 700 + 0 = 4200
      const isCorrect = employee.adjustedBaseSalary === expectedBaseSalary;
      
      return {
        passed: isCorrect,
        message: isCorrect ? 
          '学历系数大于1测试通过：基本工资计算正确' : 
          `学历系数大于1测试失败：基本工资计算错误，期望${expectedBaseSalary}，实际${employee.adjustedBaseSalary}`
      };
    }
  },
  {
    name: '学历系数等于1',
    description: '检查学历系数等于1时，基本工资的计算是否正确',
    setup: async (db) => {
      // 设置测试数据
      const testEmployee = {
        employeeId: 'TEST002',
        name: '测试员工2',
        education: '本科（普通院校）',
        languageLevel: '一般',
        educationCoefficient: 1.0,
        languageCoefficient: 1.0,
        educationAdjustment: 0, // 3500 * (1.0 - 1) = 0
        languageAdjustment: 0,
        originalBaseSalary: 3500,
        adjustedBaseSalary: 3500 // 3500 + 0 + 0 = 3500
      };
      
      await db.collection(dbConfig.employeeCollection).updateOne(
        { employeeId: 'TEST002' },
        { $set: testEmployee },
        { upsert: true }
      );
      
      console.log('已设置学历系数等于1的测试数据');
    },
    verify: (employee) => {
      // 验证基本工资计算是否正确
      const expectedBaseSalary = 3500; // 3500 + 0 + 0 = 3500
      const isCorrect = employee.adjustedBaseSalary === expectedBaseSalary;
      
      return {
        passed: isCorrect,
        message: isCorrect ? 
          '学历系数等于1测试通过：基本工资计算正确' : 
          `学历系数等于1测试失败：基本工资计算错误，期望${expectedBaseSalary}，实际${employee.adjustedBaseSalary}`
      };
    }
  },
  {
    name: '学历系数小于1',
    description: '检查学历系数小于1时，基本工资的计算是否正确',
    setup: async (db) => {
      // 设置测试数据
      const testEmployee = {
        employeeId: 'TEST003',
        name: '测试员工3',
        education: '大专及以下',
        languageLevel: '一般',
        educationCoefficient: 0.8,
        languageCoefficient: 1.0,
        educationAdjustment: -700, // 3500 * (0.8 - 1) = -700
        languageAdjustment: 0,
        originalBaseSalary: 3500,
        adjustedBaseSalary: 2800 // 3500 - 700 + 0 = 2800
      };
      
      await db.collection(dbConfig.employeeCollection).updateOne(
        { employeeId: 'TEST003' },
        { $set: testEmployee },
        { upsert: true }
      );
      
      console.log('已设置学历系数小于1的测试数据');
    },
    verify: (employee) => {
      // 验证基本工资计算是否正确
      const expectedBaseSalary = 2800; // 3500 - 700 + 0 = 2800
      const isCorrect = employee.adjustedBaseSalary === expectedBaseSalary;
      
      return {
        passed: isCorrect,
        message: isCorrect ? 
          '学历系数小于1测试通过：基本工资计算正确' : 
          `学历系数小于1测试失败：基本工资计算错误，期望${expectedBaseSalary}，实际${employee.adjustedBaseSalary}`
      };
    }
  }
];

// 主函数
async function main() {
  let client;
  try {
    // 连接数据库
    client = new MongoClient(dbConfig.url);
    await client.connect();
    console.log('已连接到数据库');
    
    const db = client.db(dbConfig.dbName);
    
    // 运行测试用例
    for (const testCase of testCases) {
      console.log(`\n开始测试：${testCase.name}`);
      console.log(`描述：${testCase.description}`);
      
      // 设置测试环境
      await testCase.setup(db);
      
      // 获取测试数据
      const employee = await db.collection(dbConfig.employeeCollection).findOne({ 
        employeeId: testCase.name === '首次使用薪资模块' ? { $exists: true } : `TEST00${testCases.indexOf(testCase)}`
      });
      
      if (!employee) {
        console.log(`测试失败：未找到测试员工数据`);
        continue;
      }
      
      // 验证测试结果
      const result = testCase.verify(employee);
      console.log(result.message);
    }
    
    console.log('\n所有测试完成');
  } catch (error) {
    console.error('测试过程中发生错误：', error);
  } finally {
    // 关闭数据库连接
    if (client) {
      await client.close();
      console.log('已关闭数据库连接');
    }
  }
}

// 运行主函数
main();
