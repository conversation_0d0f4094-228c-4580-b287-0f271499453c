#!/bin/bash

# 薪资计算一致性检查批处理脚本
# 此脚本用于运行薪资计算一致性检查

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始执行薪资计算一致性检查...${NC}"
echo

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo -e "${RED}错误: Node.js未安装，请先安装Node.js${NC}"
    exit 1
fi

# 检查MongoDB是否运行
echo -e "${YELLOW}检查MongoDB连接...${NC}"
if ! nc -z localhost 27017 &> /dev/null; then
    echo -e "${RED}错误: MongoDB未运行，请先启动MongoDB${NC}"
    exit 1
fi
echo -e "${GREEN}MongoDB连接正常${NC}"
echo

# 检查脚本文件是否存在
if [ ! -f "scripts/check-salary-consistency.js" ]; then
    echo -e "${RED}错误: 检查脚本不存在，请确保scripts/check-salary-consistency.js文件存在${NC}"
    exit 1
fi

# 运行检查脚本
echo -e "${YELLOW}运行薪资计算一致性检查脚本...${NC}"
echo
node scripts/check-salary-consistency.js

# 检查脚本执行结果
if [ $? -eq 0 ]; then
    echo
    echo -e "${GREEN}薪资计算一致性检查完成${NC}"
else
    echo
    echo -e "${RED}薪资计算一致性检查失败${NC}"
fi

# 提示用户查看结果
echo
echo -e "${YELLOW}检查完成。请查看上面的输出结果，确认薪资计算是否一致。${NC}"
echo -e "${YELLOW}如果发现不一致的地方，请检查相关代码并修复问题。${NC}"
