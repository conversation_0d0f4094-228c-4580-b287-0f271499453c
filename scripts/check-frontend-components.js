/**
 * 前端组件一致性检查脚本
 * 
 * 此脚本用于检查SalaryList、SalaryForm和SalaryDetail组件之间的计算结果和显示值是否一致
 * 模拟这些组件的渲染逻辑，并比较它们的输出
 */

const fs = require('fs');
const path = require('path');
const { SALARY_CONFIG } = require('../backend/config/salaryConfig');

// 模拟SalaryList组件的渲染逻辑
function simulateSalaryList(employeeData) {
    console.log('模拟SalaryList组件渲染...');
    
    // 获取基本参数
    const baseSalary = SALARY_CONFIG.BASE_SALARY;
    const educationCoeff = getEducationCoefficient(employeeData.education);
    const languageCoeff = getLanguageCoefficient(employeeData.languageLevel);
    const isProbation = employeeData.isProbation || false;
    
    // 计算调整值
    const educationAdjustment = Math.round((baseSalary * (educationCoeff - 1) + Number.EPSILON) * 100) / 100;
    const languageAdjustment = Math.round((baseSalary * (languageCoeff - 1) + Number.EPSILON) * 100) / 100;
    
    // 计算调整后的基本工资
    let adjustedBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
    
    // 如果是试用期，应用80%的系数
    const probationFactor = isProbation ? 0.8 : 1.0;
    const probationAdjustedBaseSalary = Math.round((adjustedBaseSalary * probationFactor + Number.EPSILON) * 100) / 100;
    
    // 生成SalaryList组件中显示的薪资说明
    const salaryExplanation = generateSalaryExplanation(baseSalary, educationAdjustment, languageAdjustment, isProbation);
    
    return {
        component: 'SalaryList',
        originalBaseSalary: baseSalary,
        educationCoefficient: educationCoeff,
        educationAdjustment: educationAdjustment,
        languageCoefficient: languageCoeff,
        languageAdjustment: languageAdjustment,
        adjustedBaseSalary: probationAdjustedBaseSalary,
        isProbation: isProbation,
        probationFactor: probationFactor,
        salaryExplanation: salaryExplanation
    };
}

// 模拟SalaryForm组件的渲染逻辑
function simulateSalaryForm(employeeData) {
    console.log('模拟SalaryForm组件渲染...');
    
    // 获取基本参数
    const baseSalary = SALARY_CONFIG.BASE_SALARY;
    const educationCoeff = getEducationCoefficient(employeeData.education);
    const languageCoeff = getLanguageCoefficient(employeeData.languageLevel);
    const isProbation = employeeData.isProbation || false;
    
    // 计算调整值
    const educationAdjustment = Math.round((baseSalary * (educationCoeff - 1) + Number.EPSILON) * 100) / 100;
    const languageAdjustment = Math.round((baseSalary * (languageCoeff - 1) + Number.EPSILON) * 100) / 100;
    
    // 计算调整后的基本工资
    let adjustedBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
    
    // 如果是试用期，应用80%的系数
    const probationFactor = isProbation ? 0.8 : 1.0;
    const probationAdjustedBaseSalary = Math.round((adjustedBaseSalary * probationFactor + Number.EPSILON) * 100) / 100;
    
    // 生成SalaryForm组件中显示的薪资说明
    const salaryExplanation = generateSalaryExplanation(baseSalary, educationAdjustment, languageAdjustment, isProbation);
    
    // 生成试用期薪资说明
    const probationExplanation = isProbation ? 
        "该员工处于试用期，系统将自动计算其税前应发工资为正常工资的80%。" : "";
    
    return {
        component: 'SalaryForm',
        originalBaseSalary: baseSalary,
        educationCoefficient: educationCoeff,
        educationAdjustment: educationAdjustment,
        languageCoefficient: languageCoeff,
        languageAdjustment: languageAdjustment,
        adjustedBaseSalary: probationAdjustedBaseSalary,
        isProbation: isProbation,
        probationFactor: probationFactor,
        salaryExplanation: salaryExplanation,
        probationExplanation: probationExplanation
    };
}

// 模拟SalaryDetail组件的渲染逻辑
function simulateSalaryDetail(employeeData) {
    console.log('模拟SalaryDetail组件渲染...');
    
    // 获取基本参数
    const baseSalary = SALARY_CONFIG.BASE_SALARY;
    const educationCoeff = getEducationCoefficient(employeeData.education);
    const languageCoeff = getLanguageCoefficient(employeeData.languageLevel);
    const isProbation = employeeData.isProbation || false;
    
    // 计算调整值
    const educationAdjustment = Math.round((baseSalary * (educationCoeff - 1) + Number.EPSILON) * 100) / 100;
    const languageAdjustment = Math.round((baseSalary * (languageCoeff - 1) + Number.EPSILON) * 100) / 100;
    
    // 计算调整后的基本工资
    let adjustedBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
    
    // 如果是试用期，应用80%的系数
    const probationFactor = isProbation ? 0.8 : 1.0;
    const probationAdjustedBaseSalary = Math.round((adjustedBaseSalary * probationFactor + Number.EPSILON) * 100) / 100;
    
    // 生成SalaryDetail组件中显示的薪资说明
    const salaryExplanation = generateSalaryExplanation(baseSalary, educationAdjustment, languageAdjustment, isProbation);
    
    // 生成试用期薪资说明
    const probationExplanation = isProbation ? 
        "该员工处于试用期，系统将自动计算其税前应发工资为正常工资的80%。" : "";
    
    return {
        component: 'SalaryDetail',
        originalBaseSalary: baseSalary,
        educationCoefficient: educationCoeff,
        educationAdjustment: educationAdjustment,
        languageCoefficient: languageCoeff,
        languageAdjustment: languageAdjustment,
        adjustedBaseSalary: probationAdjustedBaseSalary,
        isProbation: isProbation,
        probationFactor: probationFactor,
        salaryExplanation: salaryExplanation,
        probationExplanation: probationExplanation
    };
}

// 生成薪资计算说明
function generateSalaryExplanation(baseSalary, educationAdjustment, languageAdjustment, isProbation) {
    let explanation = `${formatCurrency(baseSalary)}`;
    
    if (educationAdjustment >= 0) {
        explanation += ` + ${formatCurrency(educationAdjustment)}`;
    } else {
        explanation += ` - ${formatCurrency(Math.abs(educationAdjustment))}`;
    }
    
    if (languageAdjustment >= 0) {
        explanation += ` + ${formatCurrency(languageAdjustment)}`;
    } else {
        explanation += ` - ${formatCurrency(Math.abs(languageAdjustment))}`;
    }
    
    if (isProbation) {
        explanation += ' × 80%';
    }
    
    return explanation;
}

// 格式化货币
function formatCurrency(value) {
    if (value === undefined || value === null) return '0.00';
    return value.toFixed(2);
}

// 模拟前端获取教育系数的逻辑
function getEducationCoefficient(education) {
    if (!education) return 1.0;
    
    // 映射教育水平
    const mappedEducation = mapEducation(education);
    
    // 获取对应的系数
    const coefficient = SALARY_CONFIG.EDUCATION_COEFFICIENT[mappedEducation];
    if (coefficient !== undefined) {
        return coefficient;
    }
    
    return 1.0; // 默认值
}

// 模拟前端获取语言系数的逻辑
function getLanguageCoefficient(languageLevel) {
    if (!languageLevel) return 1.0;
    
    // 获取对应的系数
    const coefficient = SALARY_CONFIG.LANGUAGE_COEFFICIENT[languageLevel];
    if (coefficient !== undefined) {
        return coefficient;
    }
    
    return 1.0; // 默认值
}

// 模拟前端的学历映射逻辑
function mapEducation(edu) {
    // 映射规则
    const educationMap = {
        '专科': '大专及以下',
        '本科': '本科（普通院校）',
        '硕士': '硕士（普通院校）',
        '博士': '博士（普通）'
    };
    
    // 如果有明确的映射，使用映射后的值
    if (educationMap[edu]) {
        return educationMap[edu];
    }
    
    // 如果没有明确的映射，但包含某些关键词，进行模糊匹配
    if (edu && typeof edu === 'string') {
        if (edu.includes('专科') || edu.includes('大专')) {
            return '大专及以下';
        }
        if (edu.includes('本科')) {
            return edu.includes('985') || edu.includes('211') ?
                '本科（985/211 院校）' : '本科（普通院校）';
        }
        if (edu.includes('硕士')) {
            return edu.includes('985') || edu.includes('211') ?
                '硕士（985/211 院校）' : '硕士（普通院校）';
        }
        if (edu.includes('博士')) {
            return edu.includes('985') || edu.includes('211') ?
                '博士（985/211 院校）' : '博士（普通）';
        }
    }
    
    // 如果无法映射，返回原值
    return edu;
}

// 检查组件之间的一致性
function checkComponentConsistency(salaryList, salaryForm, salaryDetail) {
    console.log('\n组件之间的一致性检查:');
    
    // 检查基本工资
    checkFieldConsistency('基本工资', [
        { component: 'SalaryList', value: salaryList.adjustedBaseSalary },
        { component: 'SalaryForm', value: salaryForm.adjustedBaseSalary },
        { component: 'SalaryDetail', value: salaryDetail.adjustedBaseSalary }
    ]);
    
    // 检查学历系数
    checkFieldConsistency('学历系数', [
        { component: 'SalaryList', value: salaryList.educationCoefficient },
        { component: 'SalaryForm', value: salaryForm.educationCoefficient },
        { component: 'SalaryDetail', value: salaryDetail.educationCoefficient }
    ]);
    
    // 检查学历调整值
    checkFieldConsistency('学历调整值', [
        { component: 'SalaryList', value: salaryList.educationAdjustment },
        { component: 'SalaryForm', value: salaryForm.educationAdjustment },
        { component: 'SalaryDetail', value: salaryDetail.educationAdjustment }
    ]);
    
    // 检查语言系数
    checkFieldConsistency('语言系数', [
        { component: 'SalaryList', value: salaryList.languageCoefficient },
        { component: 'SalaryForm', value: salaryForm.languageCoefficient },
        { component: 'SalaryDetail', value: salaryDetail.languageCoefficient }
    ]);
    
    // 检查语言调整值
    checkFieldConsistency('语言调整值', [
        { component: 'SalaryList', value: salaryList.languageAdjustment },
        { component: 'SalaryForm', value: salaryForm.languageAdjustment },
        { component: 'SalaryDetail', value: salaryDetail.languageAdjustment }
    ]);
    
    // 检查薪资计算说明
    checkFieldConsistency('薪资计算说明', [
        { component: 'SalaryList', value: salaryList.salaryExplanation },
        { component: 'SalaryForm', value: salaryForm.salaryExplanation },
        { component: 'SalaryDetail', value: salaryDetail.salaryExplanation }
    ]);
    
    // 检查试用期薪资说明
    if (salaryList.isProbation) {
        checkFieldConsistency('试用期薪资说明', [
            { component: 'SalaryForm', value: salaryForm.probationExplanation },
            { component: 'SalaryDetail', value: salaryDetail.probationExplanation }
        ]);
    }
}

// 检查字段一致性
function checkFieldConsistency(fieldName, values) {
    console.log(`  ${fieldName}:`);
    
    // 打印每个组件的值
    values.forEach(item => {
        console.log(`    ${item.component}: ${item.value}`);
    });
    
    // 检查所有值是否一致
    const firstValue = values[0].value;
    const isConsistent = values.every(item => {
        if (typeof item.value === 'number' && typeof firstValue === 'number') {
            return Math.abs(item.value - firstValue) < 0.01;
        }
        return item.value === firstValue;
    });
    
    console.log(`    结果: ${isConsistent ? '✓ 一致' : '✗ 不一致'}`);
    
    return isConsistent;
}

// 主函数
function main() {
    console.log('开始检查前端组件一致性...');
    console.log('='.repeat(80));
    
    // 测试数据
    const testEmployees = [
        {
            employeeId: 'TEST001',
            name: '测试员工1',
            education: '本科（普通院校）',
            languageLevel: '熟练',
            isProbation: false
        },
        {
            employeeId: 'TEST002',
            name: '测试员工2',
            education: '硕士（985/211 院校）',
            languageLevel: '精通',
            isProbation: true
        },
        {
            employeeId: 'TEST003',
            name: '测试员工3',
            education: '大专及以下',
            languageLevel: '无',
            isProbation: false
        },
        {
            employeeId: 'TEST004',
            name: '测试员工4',
            education: '博士（普通）',
            languageLevel: '基础',
            isProbation: true
        }
    ];
    
    // 对每个测试员工进行检查
    for (const employee of testEmployees) {
        console.log(`\n检查员工 ${employee.employeeId} (${employee.name}) 的前端组件一致性:`);
        console.log('-'.repeat(80));
        
        // 打印员工基本信息
        console.log('员工基本信息:');
        console.log(`  学历: ${employee.education}`);
        console.log(`  语言等级: ${employee.languageLevel}`);
        console.log(`  是否试用期: ${employee.isProbation ? '是' : '否'}`);
        
        // 模拟各组件渲染
        const salaryListResult = simulateSalaryList(employee);
        const salaryFormResult = simulateSalaryForm(employee);
        const salaryDetailResult = simulateSalaryDetail(employee);
        
        // 检查组件之间的一致性
        checkComponentConsistency(salaryListResult, salaryFormResult, salaryDetailResult);
        
        console.log('-'.repeat(80));
    }
    
    console.log('\n前端组件一致性检查完成');
    console.log('='.repeat(80));
}

// 执行主函数
main();
