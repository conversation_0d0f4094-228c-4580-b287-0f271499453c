#!/bin/bash

# 薪资模块检查批处理脚本
# 此脚本用于检查薪资计算功能的正确性

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}===== 薪资模块检查工具 =====${NC}"
echo "此工具将检查薪资计算功能的正确性，特别关注以下几点："
echo "1. 当用户首次使用薪资模块时，所有薪资相关金额应该显示为默认值"
echo "2. 薪资详单，薪资表单中关于学历和语言调整系数和金额的一致性显示"
echo "3. 根据学历和语言系数调整的基本工资值的正确性（分为三种情况，调整系数大于1，等于1和小于1）"
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo -e "${RED}错误: Node.js未安装，请先安装Node.js${NC}"
    exit 1
fi

# 检查MongoDB是否运行
if ! pgrep -x "mongod" > /dev/null; then
    echo -e "${YELLOW}警告: MongoDB可能未运行，请确保MongoDB已启动${NC}"
    read -p "是否继续? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查检查脚本是否存在
if [ ! -f "scripts/salary_check.js" ]; then
    echo -e "${RED}错误: 检查脚本不存在，请确保scripts/salary_check.js文件存在${NC}"
    exit 1
fi

# 运行自动化检查脚本
echo -e "${BLUE}开始运行自动化检查...${NC}"
node scripts/salary_check.js

echo ""
echo -e "${YELLOW}===== 手动检查项目 =====${NC}"
echo "以下项目需要手动检查："

echo -e "${GREEN}1. 首次使用薪资模块检查:${NC}"
echo "   - 打开薪资列表页面，确认所有员工的薪资相关金额是否显示为0"
echo "   - 打开薪资详情页面，确认所有薪资相关金额是否显示为0，所有系数是否显示为1.00"
echo "   - 打开薪资表单，确认所有薪资相关金额是否显示为0，所有系数是否显示为1.00"

echo -e "${GREEN}2. 学历和语言系数调整检查:${NC}"
echo "   - 选择一个员工，设置学历为'硕士（985/211 院校）'，语言水平为'一般'"
echo "   - 计算薪资，确认学历系数是否大于1，学历调整金额是否为正数"
echo "   - 确认基本工资计算公式是否正确显示：基本工资 + 学历调整 + 语言调整 = 调整后基本工资"
echo "   - 确认薪资详情页面中的学历系数、语言系数和基本工资计算公式是否与薪资表单一致"

echo "   - 选择另一个员工，设置学历为'本科（普通院校）'，语言水平为'一般'"
echo "   - 计算薪资，确认学历系数是否等于1，学历调整金额是否为0"
echo "   - 确认基本工资计算公式是否正确显示：基本工资 + 0 + 0 = 基本工资"
echo "   - 确认薪资详情页面中的学历系数、语言系数和基本工资计算公式是否与薪资表单一致"

echo "   - 选择第三个员工，设置学历为'大专及以下'，语言水平为'一般'"
echo "   - 计算薪资，确认学历系数是否小于1，学历调整金额是否为负数"
echo "   - 确认基本工资计算公式是否正确显示：基本工资 - |学历调整| + 0 = 调整后基本工资"
echo "   - 确认薪资详情页面中的学历系数、语言系数和基本工资计算公式是否与薪资表单一致"

echo -e "${GREEN}3. 重置薪资检查:${NC}"
echo "   - 选择一个已计算薪资的员工，点击重置薪资"
echo "   - 确认薪资列表中该员工的所有薪资相关金额是否显示为0"
echo "   - 打开该员工的薪资详情页面，确认所有薪资相关金额是否显示为0，所有系数是否显示为1.00"
echo "   - 打开该员工的薪资表单，确认所有薪资相关金额是否显示为0，所有系数是否显示为1.00"

echo -e "${BLUE}===== 检查完成 =====${NC}"
echo "请根据上述检查项目，确认薪资计算功能的正确性"
