/**
 * 前端薪资计算逻辑检查脚本
 * 
 * 此脚本用于检查前端组件中的薪资计算逻辑是否正确
 * 模拟前端组件中的计算逻辑，并与后端计算结果进行比较
 */

const fs = require('fs');
const path = require('path');
const { calculateMonthlySalary } = require('../backend/utils/SalaryCalculator');
const { SALARY_CONFIG } = require('../backend/config/salaryConfig');

// 模拟前端的薪资计算逻辑
function simulateFrontendCalculation(employeeData) {
    console.log('模拟前端薪资计算逻辑...');
    
    // 获取基本参数
    const baseSalary = SALARY_CONFIG.BASE_SALARY;
    const educationCoeff = getEducationCoefficient(employeeData.education);
    const languageCoeff = getLanguageCoefficient(employeeData.languageLevel);
    const isProbation = employeeData.isProbation || false;
    
    // 计算调整值
    const educationAdjustment = Math.round((baseSalary * (educationCoeff - 1) + Number.EPSILON) * 100) / 100;
    const languageAdjustment = Math.round((baseSalary * (languageCoeff - 1) + Number.EPSILON) * 100) / 100;
    
    // 计算调整后的基本工资
    let adjustedBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
    
    // 如果是试用期，应用80%的系数
    const probationFactor = isProbation ? 0.8 : 1.0;
    const probationAdjustedBaseSalary = Math.round((adjustedBaseSalary * probationFactor + Number.EPSILON) * 100) / 100;
    
    return {
        originalBaseSalary: baseSalary,
        educationCoefficient: educationCoeff,
        educationAdjustment: educationAdjustment,
        languageCoefficient: languageCoeff,
        languageAdjustment: languageAdjustment,
        adjustedBaseSalary: probationAdjustedBaseSalary,
        isProbation: isProbation,
        probationFactor: probationFactor
    };
}

// 模拟前端获取教育系数的逻辑
function getEducationCoefficient(education) {
    if (!education) return 1.0;
    
    // 映射教育水平
    const mappedEducation = mapEducation(education);
    
    // 获取对应的系数
    const coefficient = SALARY_CONFIG.EDUCATION_COEFFICIENT[mappedEducation];
    if (coefficient !== undefined) {
        return coefficient;
    }
    
    return 1.0; // 默认值
}

// 模拟前端获取语言系数的逻辑
function getLanguageCoefficient(languageLevel) {
    if (!languageLevel) return 1.0;
    
    // 获取对应的系数
    const coefficient = SALARY_CONFIG.LANGUAGE_COEFFICIENT[languageLevel];
    if (coefficient !== undefined) {
        return coefficient;
    }
    
    return 1.0; // 默认值
}

// 模拟前端的学历映射逻辑
function mapEducation(edu) {
    // 映射规则
    const educationMap = {
        '专科': '大专及以下',
        '本科': '本科（普通院校）',
        '硕士': '硕士（普通院校）',
        '博士': '博士（普通）'
    };
    
    // 如果有明确的映射，使用映射后的值
    if (educationMap[edu]) {
        return educationMap[edu];
    }
    
    // 如果没有明确的映射，但包含某些关键词，进行模糊匹配
    if (edu && typeof edu === 'string') {
        if (edu.includes('专科') || edu.includes('大专')) {
            return '大专及以下';
        }
        if (edu.includes('本科')) {
            return edu.includes('985') || edu.includes('211') ?
                '本科（985/211 院校）' : '本科（普通院校）';
        }
        if (edu.includes('硕士')) {
            return edu.includes('985') || edu.includes('211') ?
                '硕士（985/211 院校）' : '硕士（普通院校）';
        }
        if (edu.includes('博士')) {
            return edu.includes('985') || edu.includes('211') ?
                '博士（985/211 院校）' : '博士（普通）';
        }
    }
    
    // 如果无法映射，返回原值
    return edu;
}

// 检查前端和后端计算结果的一致性
function checkCalculationConsistency(frontendResult, backendResult) {
    console.log('\n前端和后端计算结果一致性检查:');
    
    // 检查基本工资
    checkConsistency('基本工资', frontendResult.adjustedBaseSalary, backendResult.adjustedBaseSalary);
    
    // 检查学历系数
    checkConsistency('学历系数', frontendResult.educationCoefficient, backendResult.educationCoefficient);
    
    // 检查学历调整值
    checkConsistency('学历调整值', frontendResult.educationAdjustment, backendResult.educationAdjustment);
    
    // 检查语言系数
    checkConsistency('语言系数', frontendResult.languageCoefficient, backendResult.languageCoefficient);
    
    // 检查语言调整值
    checkConsistency('语言调整值', frontendResult.languageAdjustment, backendResult.languageAdjustment);
}

// 检查两个值是否一致
function checkConsistency(fieldName, frontendValue, backendValue) {
    const frontVal = Number(frontendValue) || 0;
    const backVal = Number(backendValue) || 0;
    
    // 计算差异（允许0.01的误差）
    const difference = Math.abs(frontVal - backVal);
    const isConsistent = difference < 0.01;
    
    console.log(`  ${fieldName}: ${isConsistent ? '✓ 一致' : '✗ 不一致'} (前端: ${frontVal}, 后端: ${backVal}, 差异: ${difference.toFixed(2)})`);
    
    return isConsistent;
}

// 主函数
function main() {
    console.log('开始检查前端薪资计算逻辑...');
    console.log('='.repeat(80));
    
    // 测试数据
    const testEmployees = [
        {
            employeeId: 'TEST001',
            name: '测试员工1',
            education: '本科（普通院校）',
            languageLevel: '熟练',
            isProbation: false
        },
        {
            employeeId: 'TEST002',
            name: '测试员工2',
            education: '硕士（985/211 院校）',
            languageLevel: '精通',
            isProbation: true
        },
        {
            employeeId: 'TEST003',
            name: '测试员工3',
            education: '大专及以下',
            languageLevel: '无',
            isProbation: false
        },
        {
            employeeId: 'TEST004',
            name: '测试员工4',
            education: '博士（普通）',
            languageLevel: '基础',
            isProbation: true
        }
    ];
    
    // 对每个测试员工进行检查
    for (const employee of testEmployees) {
        console.log(`\n检查员工 ${employee.employeeId} (${employee.name}) 的薪资计算:`);
        console.log('-'.repeat(80));
        
        // 打印员工基本信息
        console.log('员工基本信息:');
        console.log(`  学历: ${employee.education}`);
        console.log(`  语言等级: ${employee.languageLevel}`);
        console.log(`  是否试用期: ${employee.isProbation ? '是' : '否'}`);
        
        // 模拟前端计算
        const frontendResult = simulateFrontendCalculation(employee);
        
        // 打印前端计算结果
        console.log('\n前端计算结果:');
        console.log(`  基本工资: ${frontendResult.originalBaseSalary}`);
        console.log(`  学历系数: ${frontendResult.educationCoefficient}`);
        console.log(`  学历调整值: ${frontendResult.educationAdjustment}`);
        console.log(`  语言系数: ${frontendResult.languageCoefficient}`);
        console.log(`  语言调整值: ${frontendResult.languageAdjustment}`);
        console.log(`  调整后基本工资: ${frontendResult.adjustedBaseSalary}`);
        
        // 使用后端计算
        const calculationParams = {
            education: employee.education,
            languageLevel: employee.languageLevel,
            isProbation: employee.isProbation,
            positionLevel: 'A1',  // 默认值
            positionType: '技术',  // 默认值
            administrativeLevel: '无',  // 默认值
            performanceCoefficient: 1.0,  // 默认值
            actualAttendance: 22  // 默认值
        };
        
        const backendResult = calculateMonthlySalary(calculationParams);
        
        // 打印后端计算结果
        console.log('\n后端计算结果:');
        console.log(`  基本工资: ${backendResult.originalBaseSalary}`);
        console.log(`  学历系数: ${backendResult.educationCoefficient}`);
        console.log(`  学历调整值: ${backendResult.educationAdjustment}`);
        console.log(`  语言系数: ${backendResult.languageCoefficient}`);
        console.log(`  语言调整值: ${backendResult.languageAdjustment}`);
        console.log(`  调整后基本工资: ${backendResult.adjustedBaseSalary}`);
        
        // 检查一致性
        checkCalculationConsistency(frontendResult, backendResult);
        
        console.log('-'.repeat(80));
    }
    
    console.log('\n前端薪资计算逻辑检查完成');
    console.log('='.repeat(80));
}

// 执行主函数
main();
