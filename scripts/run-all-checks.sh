#!/bin/bash

# 薪资计算全面检查批处理脚本
# 此脚本用于运行所有薪资计算相关的检查

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}====================================================${NC}"
echo -e "${BLUE}           薪资计算全面检查批处理脚本              ${NC}"
echo -e "${BLUE}====================================================${NC}"
echo

# 检查脚本文件是否存在
if [ ! -f "scripts/run-salary-check.sh" ] || [ ! -f "scripts/run-frontend-check.sh" ]; then
    echo -e "${RED}错误: 检查脚本不存在，请确保所有检查脚本文件存在${NC}"
    exit 1
fi

# 确保脚本有执行权限
chmod +x scripts/run-salary-check.sh
chmod +x scripts/run-frontend-check.sh

# 运行数据库薪资一致性检查
echo -e "${YELLOW}步骤 1: 运行数据库薪资一致性检查${NC}"
echo -e "${YELLOW}---------------------------------------------------${NC}"
./scripts/run-salary-check.sh
echo

# 运行前端薪资计算逻辑检查
echo -e "${YELLOW}步骤 2: 运行前端薪资计算逻辑检查${NC}"
echo -e "${YELLOW}---------------------------------------------------${NC}"
./scripts/run-frontend-check.sh
echo

# 运行前端组件一致性检查
echo -e "${YELLOW}步骤 3: 运行前端组件一致性检查${NC}"
echo -e "${YELLOW}---------------------------------------------------${NC}"
./scripts/run-frontend-components-check.sh
echo

# 总结
echo -e "${BLUE}====================================================${NC}"
echo -e "${GREEN}所有薪资计算检查已完成${NC}"
echo -e "${BLUE}====================================================${NC}"
echo
echo -e "${YELLOW}请查看上面的输出结果，确认所有薪资计算是否正确。${NC}"
echo -e "${YELLOW}如果发现任何问题，请检查相关代码并修复。${NC}"
echo
echo -e "${BLUE}检查项目:${NC}"
echo -e "  1. 数据库中的薪资数据与重新计算的结果是否一致"
echo -e "  2. 前端薪资计算逻辑与后端计算结果是否一致"
echo -e "  3. 前端组件(SalaryList、SalaryForm、SalaryDetail)之间的计算结果和显示值是否一致"
echo
echo -e "${BLUE}检查的员工:${NC}"
echo -e "  - 数据库检查: M001, 002, 003, 006"
echo -e "  - 前端逻辑检查: 使用了4个测试用例，覆盖不同学历、语言等级和试用期状态"
echo -e "  - 前端组件检查: 使用了相同的4个测试用例，检查组件间的一致性"
